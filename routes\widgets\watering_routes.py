"""
Watering Widget API Routes
Handles watering plan and schedule API endpoints
"""

from flask import Blueprint, request, jsonify
from phase_logic.widgets.watering_logic import WateringLogic

# Create blueprint
watering_bp = Blueprint('watering', __name__, url_prefix='/api/watering')

@watering_bp.route('/plan/<phase>', methods=['GET'])
def watering_plan(phase):
    """
    Bewässerungsplan für eine bestimmte Phase
    """
    try:
        # Parameter aus Query-String holen
        pot_size = request.args.get('pot_size_l', type=float, default=11.0)
        substrate = request.args.get('substrate', default='coco')
        strain_type = request.args.get('strain_type', default='photoperiodic')
        
        # Plant ID aus Query-String holen (falls vorhanden)
        plant_id = request.args.get('plant_id')
        
        # Strain-Type aus Datenbank laden falls nicht übergeben und Plant ID vorhanden
        if strain_type == 'photoperiodic' and plant_id:
            try:
                from database_basic import GrowDiaryBasicDB
                db = GrowDiaryBasicDB()
                conn = db.get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT strain_type, strain FROM plants WHERE id = ?", (plant_id,))
                row = cursor.fetchone()
                if row:
                    # Erst prüfen ob strain_type explizit gesetzt ist
                    if row[0]:  # strain_type
                        strain_type_db = row[0].lower()
                        # Prüfe auf Autoflower-Werte im strain_type Feld
                        auto_type_keywords = ['auto', 'autoflower', 'autoflowering', 'automatic']
                        if any(keyword in strain_type_db for keyword in auto_type_keywords):
                            strain_type = 'autoflowering'
                            print(f"Watering API - Detected autoflower from strain_type: {row[0]}")
                        else:
                            strain_type = strain_type_db
                            print(f"Watering API - Loaded strain_type from database: {strain_type}")
                    # Dann nach Autoflower-Keywords in strain suchen
                    elif row[1]:  # strain
                        strain_text = row[1].lower()
                        auto_keywords = ['auto', 'autoflower', 'automatic', 'ruderalis']
                        if any(keyword in strain_text for keyword in auto_keywords):
                            strain_type = 'autoflowering'
                            print(f"Watering API - Detected autoflower from strain: {row[1]}")
            except Exception as e:
                print(f"Watering API - Could not load strain_type from database: {e}")
        
        # Debug: Parameter loggen
        print(f"Watering API - Received parameters: strain_type={strain_type}, pot_size={pot_size}, substrate={substrate}")
        
        # Watering-Logik initialisieren
        watering_logic = WateringLogic()
        
        # Bewässerungsplan mit Guidelines berechnen
        result = watering_logic.calculate_watering_with_guidelines(phase, pot_size, substrate, strain_type)
        
        return jsonify({
            'success': True,
            'phase': phase,
            **result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden des Bewässerungsplans: {str(e)}'
        }), 500

@watering_bp.route('/schedule/<plant_id>', methods=['GET'])
def watering_schedule(plant_id):
    """
    Bewässerungsplan für eine spezifische Pflanze
    """
    try:
        from database_basic import db
        
        # Pflanzendaten laden
        plant = db.get_plant(plant_id)
        if not plant:
            return jsonify({'success': False, 'message': 'Pflanze nicht gefunden'}), 404
        
        # Watering-Logik initialisieren
        watering_logic = WateringLogic()
        
        # Bewässerungsplan für Pflanze berechnen
        result = watering_logic.calculate_plant_watering_schedule(plant)
        
        return jsonify({
            'success': True,
            'plant_id': plant_id,
            'schedule': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden des Bewässerungsplans: {str(e)}'
        }), 500

@watering_bp.route('/substrates', methods=['GET'])
def watering_substrates():
    """
    Verfügbare Substrate für Bewässerungsplan
    """
    try:
        watering_logic = WateringLogic()
        substrates = watering_logic.get_available_substrates()
        
        return jsonify({
            'success': True,
            'substrates': substrates
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Substrate: {str(e)}'
        }), 500

@watering_bp.route('/calculate', methods=['POST'])
def watering_calculate():
    """
    Bewässerungsberechnung mit benutzerdefinierten Werten
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not data.get('pot_size'):
            return jsonify({'success': False, 'message': 'Topfgröße ist erforderlich'}), 400
        
        if not data.get('phase'):
            return jsonify({'success': False, 'message': 'Phase ist erforderlich'}), 400
        
        pot_size = float(data['pot_size'])
        phase = data['phase']
        substrate = data.get('substrate', 'coco')
        strain_type = data.get('strain_type', 'photoperiodic')
        
        # Watering-Logik initialisieren
        watering_logic = WateringLogic()
        
        # Bewässerung mit Guidelines berechnen
        result = watering_logic.calculate_watering_with_guidelines(phase, pot_size, substrate, strain_type)
        
        return jsonify({
            'success': True,
            'watering_amount': result['amount'],
            'frequency': result['frequency'],
            'schedule': result['schedule']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der Bewässerungsberechnung: {str(e)}'
        }), 500

@watering_bp.route('/guidelines', methods=['GET'])
def watering_guidelines():
    """
    Bewässerungsrichtlinien und Faustregeln laden
    """
    try:
        import json
        import os
        
        # JSON-Datei mit Guidelines laden
        guidelines_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'static', 'data', 'watering-guidelines.json'
        )
        
        if not os.path.exists(guidelines_path):
            return jsonify({
                'success': False,
                'message': 'Bewässerungsrichtlinien nicht gefunden'
            }), 404
        
        with open(guidelines_path, 'r', encoding='utf-8') as f:
            guidelines = json.load(f)
        
        return jsonify({
            'success': True,
            'guidelines': guidelines
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Bewässerungsrichtlinien: {str(e)}'
        }), 500

@watering_bp.route('/guidelines/<pot_size>/<phase>', methods=['GET'])
def watering_guidelines_for_pot_phase(pot_size, phase):
    """
    Spezifische Bewässerungsrichtlinien für Topfgröße und Phase
    """
    try:
        import json
        import os
        
        # JSON-Datei mit Guidelines laden
        guidelines_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'static', 'data', 'watering-guidelines.json'
        )
        
        if not os.path.exists(guidelines_path):
            return jsonify({
                'success': False,
                'message': 'Bewässerungsrichtlinien nicht gefunden'
            }), 404
        
        with open(guidelines_path, 'r', encoding='utf-8') as f:
            guidelines_data = json.load(f)
        
        # Topfgröße und Phase finden
        pot_size_float = float(pot_size)
        guidelines = guidelines_data.get('wateringGuidelines', {})
        pot_sizes = guidelines.get('potSizes', [])
        
        # Passende Topfgröße finden
        matching_pot = None
        for pot in pot_sizes:
            if pot.get('potSizeLiters') == pot_size_float:
                matching_pot = pot
                break
        
        if not matching_pot:
            return jsonify({
                'success': False,
                'message': f'Keine Richtlinien für Topfgröße {pot_size}L gefunden'
            }), 404
        
        # Passende Phase finden
        stages = matching_pot.get('stages', [])
        matching_stage = None
        for stage in stages:
            if stage.get('phase') == phase:
                matching_stage = stage
                break
        
        if not matching_stage:
            return jsonify({
                'success': False,
                'message': f'Keine Richtlinien für Phase {phase} gefunden'
            }), 404
        
        return jsonify({
            'success': True,
            'pot_size': pot_size_float,
            'phase': phase,
            'guidelines': matching_stage,
            'rules': guidelines.get('rules', {}),
            'meta': guidelines.get('meta', {})
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Bewässerungsrichtlinien: {str(e)}'
        }), 500 