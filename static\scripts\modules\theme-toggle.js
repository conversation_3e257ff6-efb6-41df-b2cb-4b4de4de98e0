/**
 * Theme Toggle Module
 * Handles theme switching functionality
 */

class ThemeToggle {
    constructor() {
        this.themeToggle = document.getElementById('theme-toggle');
        this.themeIcon = document.querySelector('.theme-icon');
        this.init();
    }
    
    init() {
        if (this.themeToggle) {
            this.loadTheme();
            this.setupEventListeners();
        }
    }
    
    loadTheme() {
        // Theme aus LocalStorage oder System laden
        let savedTheme = localStorage.getItem('growDiaryTheme');
        if (!savedTheme) {
            savedTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    }
    
    setupEventListeners() {
        this.themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });
    }
    
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('growDiaryTheme', newTheme);
        this.updateThemeIcon(newTheme);
    }
    
    updateThemeIcon(theme) {
        if (this.themeIcon) {
            this.themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
            this.themeToggle.title = theme === 'dark' ? 'Wechsel zu Light Mode' : 'Wechsel zu Dark Mode';
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new ThemeToggle();
});

// Export for use in other modules
window.ThemeToggle = ThemeToggle; 