# 🎨 Widget CSS-Style-Guide

## 1. BEM-Methodologie
- Klassen nach dem Schema: `.widget-name__element--modifier`
- Beispiel:
  ```css
  .lighting-widget__slider { ... }
  .lighting-widget__label--highlight { ... }
  ```

## 2. Struktur der CSS-Dateien
- Jede Widget-Style-Datei liegt in `static/styles/widgets/<widget-name>-widget.css`
- Nur Styles für das jeweilige Widget in der Datei
- Gemeinsame Utilities in `static/styles/utilities/`

## 3. Utility-Klassen
- Für Farben, Abstände, Flexbox etc. gibt es zentrale Utility-Klassen
- Beispiel:
  ```html
  <div class="mb-3 d-flex align-items-center"></div>
  <span class="icon-success"></span>
  ```

## 4. Responsive Design
- Nutze Bootstrap-Grid oder eigene Media Queries
- Beispiel:
  ```css
  @media (max-width: 768px) {
    .lighting-widget__slider { width: 100%; }
  }
  ```

## 5. Dark Mode & Barrierefreiheit
- Farben immer über CSS-Variablen oder Utility-Klassen
- Kontraste beachten, keine festen Farben inline
- Fokus-Styles und ARIA-Attribute für wichtige Interaktionen

---
*Letzte Aktualisierung: {{ "now" | date("%d.%m.%Y %H:%M") }}* 