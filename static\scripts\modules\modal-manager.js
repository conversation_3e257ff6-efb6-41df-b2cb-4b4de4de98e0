/**
 * Modal Manager Module
 * Handles general modal functionality
 */

class ModalManager {
    constructor() {
        this.modals = {};
        this.init();
    }
    
    init() {
        // Initialize existing modals
        this.setupModals();
    }
    
    setupModals() {
        // Find all modals on the page
        const modalElements = document.querySelectorAll('.modal');
        modalElements.forEach(modal => {
            const modalId = modal.id;
            if (modalId) {
                this.modals[modalId] = new bootstrap.Modal(modal);
            }
        });
    }
    
    open(modalId) {
        const modal = this.modals[modalId];
        if (modal) {
            modal.show();
        } else {
            console.error(`Modal with id '${modalId}' not found`);
        }
    }
    
    close(modalId) {
        const modal = this.modals[modalId];
        if (modal) {
            modal.hide();
        }
    }
    
    getModal(modalId) {
        return this.modals[modalId];
    }
}

// Export for use in other modules
window.ModalManager = ModalManager; 