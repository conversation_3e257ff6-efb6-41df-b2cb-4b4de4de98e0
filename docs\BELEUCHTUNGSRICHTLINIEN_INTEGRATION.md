# Beleuchtungsrichtlinien Integration

## Übersicht

Die Beleuchtungsrichtlinien wurden erfolgreich in das Grow-Tagebuch-System integriert. Diese basieren auf wissenschaftlichen Erkenntnissen und LED-optimierten Growräumen und werden sowohl für die automatische Berechnung als auch für die Benutzerinformation verwendet.

## Implementierte Features

### 1. JSON-Datenstruktur
- **Datei**: `static/data/lighting-guidelines.json`
- **Struktur**: Vollständige Beleuchtungsrichtlinien mit Phasen, PPFD/DLI-Werten und Tent-Empfehlungen
- **Quellen**: Migro Light, Royal Queen Seeds, Coco for Cannabis, Lux Light Tech, Kind LED

### 2. Frontend-Integration
- **Info-Button**: "Richtlinien"-Button im Beleuchtungsplan-Widget
- **Vorschau**: Wichtige PPFD/DLI-Empfehlungen direkt im Widget sichtbar
- **Modal**: Detaillierte Richtlinien mit phasenspezifischen Empfehlungen
- **Responsive Design**: Optimiert für alle Bildschirmgrößen

### 3. Backend-API
- **Endpoint**: `/api/lighting/guidelines` - Alle Richtlinien laden
- **Endpoint**: `/api/lighting/guidelines/<phase>` - Spezifische Richtlinien für Phase
- **Endpoint**: `/api/lighting/calculate-dli` - DLI-Berechnung
- **Integration**: Automatische Berechnung basierend auf JSON-Daten

## Datenstruktur

### Phasenspezifische Empfehlungen
```json
{
  "phases": [
    {
      "phase": "flowering_peak",
      "description": "Volle Blüte – Trichomentwicklung, Budaufbau",
      "ppfd": {
        "min": 800,
        "max": 1000
      },
      "dli": {
        "min": 35,
        "max": 45
      },
      "lightHours": {
        "photoperiod": 12,
        "autoflower": 18
      },
      "recommendedKelvin": "2700–3500K",
      "spectrum": ["deep_red_660nm", "far_red_730nm", "warm_white"],
      "additives": ["uv_a_385nm", "hyper_red", "infrared_730nm"]
    }
  ]
}
```

### Tent-Empfehlungen
```json
{
  "tentExamples": [
    {
      "tentSize": "80x80cm",
      "recommendedWatt": {
        "veg": 120,
        "flower": 200
      },
      "ppfdTarget": {
        "veg": 350,
        "flower": 800
      }
    }
  ]
}
```

### DLI-Berechnung
- **Formel**: DLI = PPFD × Photoperiode × 0,0036
- **Beispiel**: 600 PPFD × 12h × 0,0036 = 26 mol/m²/Tag
- **Zielbereich**: 12-45 mol/m²/Tag für optimales Wachstum

## Technische Implementierung

### Frontend (JavaScript)
```javascript
// Guidelines-Modal erstellen
createGuidelinesModal() {
    // Modal mit vollständigen Richtlinien
    // Bootstrap-Modal mit strukturiertem Inhalt
    // Phasenspezifische Empfehlungen
    // Tent-Empfehlungen
    // DLI-Berechnung
}

// Guidelines-Vorschau rendern
renderGuidelinesPreview() {
    // Kompakte Anzeige der wichtigsten PPFD/DLI-Empfehlungen
    // Button zum Öffnen des vollständigen Modals
}
```

### Backend (Python)
```python
@lighting_bp.route('/guidelines', methods=['GET'])
def lighting_guidelines():
    # JSON-Datei laden und als API-Response zurückgeben
    
@lighting_bp.route('/guidelines/<phase>', methods=['GET'])
def lighting_guidelines_for_phase(phase):
    # Spezifische Richtlinien für Phase
    
@lighting_bp.route('/calculate-dli', methods=['POST'])
def calculate_dli():
    # DLI-Berechnung mit Formel
```

### CSS-Styling
```css
/* Guidelines-spezifische Styles */
.lighting-guidelines-content {
    background: rgba(156, 39, 176, 0.05);
    border: 1px solid rgba(156, 39, 176, 0.2);
}

.lighting-phase-item {
    background: #f8f9fa;
    border-left: 4px solid #9c27b0;
    padding: 15px;
    margin-bottom: 15px;
}
```

## Benutzer-Nutzen

### 1. Sofortige Information
- **Vorschau**: Wichtigste PPFD/DLI-Empfehlungen direkt im Widget sichtbar
- **Schnellzugriff**: "Richtlinien"-Button für detaillierte Informationen
- **Kontext**: Phasenspezifische Empfehlungen mit Spektrum-Informationen

### 2. Automatische Berechnung
- **Präzise PPFD-Werte**: Basierend auf wissenschaftlichen Daten
- **DLI-Berechnung**: Automatische Berechnung des Daily Light Integral
- **Phasen-Berücksichtigung**: Unterschiedliche Bedürfnisse je Wachstumsphase
- **Strain-Type-Anpassung**: Photoperiodic vs. Autoflowering

### 3. Lernmöglichkeiten
- **PPFD-Bereiche**: Verständnis der optimalen Lichtintensität
- **DLI-Ziele**: Daily Light Integral für optimales Wachstum
- **Spektrum-Empfehlungen**: Blau (Veg) → Rot (Blüte) Anpassung
- **Tent-Empfehlungen**: Watt-Leistung und PPFD-Ziele für verschiedene Tent-Größen

## Wartung und Erweiterung

### JSON-Datei aktualisieren
1. Neue Phasen hinzufügen
2. PPFD/DLI-Werte anpassen
3. Tent-Empfehlungen erweitern
4. Quellen aktualisieren

### API erweitern
1. Neue Endpoints für spezielle Anwendungsfälle
2. Caching für bessere Performance
3. Validierung der Eingabeparameter
4. Erweiterte DLI-Berechnungen

### Frontend anpassen
1. Neue Widget-Features
2. Verbesserte Benutzerführung
3. Zusätzliche Visualisierungen
4. Interaktive PPFD/DLI-Rechner

## Qualitätssicherung

### Datenvalidierung
- JSON-Schema-Validierung
- Bereichsprüfungen für PPFD/DLI-Werte
- Konsistenzprüfungen zwischen Phasen

### Benutzerfreundlichkeit
- Klare, verständliche Formulierungen
- Logische Gruppierung der Informationen
- Responsive Design für alle Geräte
- Intuitive DLI-Berechnung

### Performance
- Effiziente JSON-Ladung
- Caching-Strategien
- Minimale API-Aufrufe

## Fazit

Die Integration der Beleuchtungsrichtlinien bietet einen erheblichen Mehrwert für die Benutzer des Grow-Tagebuchs. Durch die Kombination aus automatischer Berechnung und umfassender Information wird sowohl die Benutzerfreundlichkeit als auch die Qualität der Beleuchtungsempfehlungen deutlich verbessert.

Die wissenschaftlich fundierten PPFD/DLI-Werte und phasenspezifischen Empfehlungen ermöglichen eine professionelle Beleuchtungsoptimierung für maximale Erträge und Pflanzenqualität. 