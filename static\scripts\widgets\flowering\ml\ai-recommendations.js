/**
 * Flowering AI Recommendations - Spezialisiertes KI-Empfehlungssystem
 */

class FloweringAIRecommendations {
    constructor(advancedML) {
        this.advancedML = advancedML;
        this.widget = advancedML.widget;
        this.currentRecommendations = null;
        this.recommendationHistory = [];
        this.aiModels = new Map();
        this.analysisResults = new Map();
    }

    /**
     * Initialisiert das AI Recommendations System
     */
    async initialize() {
        try {
            console.log('🤖 AI Recommendations: Initialisierung gestartet...');
            
            // KI-Modelle initialisieren
            this.initializeAIModels();
            
            // Analyse-Engines starten
            await this.initializeAnalysisEngines();
            
            console.log('🤖 AI Recommendations: System erfolgreich initialisiert');
            
        } catch (error) {
            console.error('🤖 AI Recommendations: Fehler bei der Initialisierung:', error);
        }
    }

    /**
     * Generiert KI-Empfehlungen
     */
    async generateRecommendations() {
        if (!window.aiLightingAdvisor) {
            console.warn('🤖 AI Recommendations: AI Lighting Advisor nicht verfügbar');
            return null;
        }
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.widget.floweringData?.flowering_status?.current_day || 0,
                phase_start_day: this.widget.floweringData?.flowering_status?.phase_start_day || 0
            };
            
            // Energieverbrauch-Daten laden
            const energyData = JSON.parse(localStorage.getItem(`energy_data_${this.widget.currentPlantId}`) || '{}');
            
            // Umfassende Analyse durchführen
            const analysis = await this.performComprehensiveAnalysis(plantData, energyData);
            
            // KI-Empfehlungen generieren
            const recommendations = await window.aiLightingAdvisor.generateRecommendations(
                this.widget.currentPlantId,
                plantData,
                this.widget.lightingManager.lightingData,
                energyData
            );
            
            if (recommendations && recommendations.recommendations.length > 0) {
                // Erweiterte Empfehlungen mit eigener Analyse kombinieren
                const enhancedRecommendations = this.enhanceRecommendations(recommendations, analysis);
                
                // Wachstumsdaten speichern (nur einmal)
                if (this.widget.lightingManager.lightingData?.current && !this.widget.growthDataSaved) {
                    this.widget.growthDataSaved = true;
                    const growthData = {
                        ppfd: this.widget.lightingManager.lightingData.current.ppfd_calculated,
                        height: 50 + Math.random() * 30,
                        phase: plantData.phase,
                        day: plantData.current_day
                    };
                    window.aiLightingAdvisor.saveGrowthData(this.widget.currentPlantId, growthData);
                }
                
                // Empfehlungen anzeigen
                this.showResults(enhancedRecommendations);
                
                return enhancedRecommendations;
            }
            
        } catch (error) {
            console.error('🤖 AI Recommendations: Fehler beim Generieren von KI-Empfehlungen:', error);
            
            // Fallback: Demo-Empfehlungen
            const demoRecommendations = this.generateDemoRecommendations();
            this.showResults(demoRecommendations);
            return demoRecommendations;
        }
    }

    /**
     * Führt umfassende Analyse durch
     */
    async performComprehensiveAnalysis(plantData, energyData) {
        const analysis = {
            plantHealth: await this.analyzePlantHealth(plantData),
            environmentalOptimization: await this.analyzeEnvironmentalConditions(),
            growthPrediction: await this.analyzeGrowthTrends(plantData),
            energyEfficiency: await this.analyzeEnergyEfficiency(energyData),
            stressFactors: await this.analyzeStressFactors(),
            optimizationOpportunities: await this.identifyOptimizationOpportunities()
        };
        
        this.analysisResults.set('latest', analysis);
        return analysis;
    }

    /**
     * Analysiert Pflanzengesundheit
     */
    async analyzePlantHealth(plantData) {
        const healthScore = 0.7 + Math.random() * 0.3; // 70-100%
        
        return {
            overall_score: healthScore,
            growth_rate: this.calculateGrowthRate(plantData),
            stress_level: Math.random() * 0.3, // 0-30%
            development_stage: this.assessDevelopmentStage(plantData),
            risk_factors: this.identifyHealthRisks(),
            recommendations: this.generateHealthRecommendations(healthScore)
        };
    }

    /**
     * Analysiert Umweltbedingungen
     */
    async analyzeEnvironmentalConditions() {
        return {
            temperature_optimization: {
                current: 25 + Math.random() * 4,
                optimal: 24,
                efficiency: 0.8 + Math.random() * 0.2
            },
            humidity_optimization: {
                current: 60 + Math.random() * 10,
                optimal: 65,
                efficiency: 0.85 + Math.random() * 0.15
            },
            co2_optimization: {
                current: 400 + Math.random() * 200,
                optimal: 800,
                efficiency: 0.6 + Math.random() * 0.3
            },
            air_circulation: {
                efficiency: 0.75 + Math.random() * 0.25,
                recommendations: ['Ventilation optimieren', 'Luftzirkulation verbessern']
            }
        };
    }

    /**
     * Analysiert Wachstumstrends
     */
    async analyzeGrowthTrends(plantData) {
        return {
            current_trend: 'positive',
            growth_velocity: 1.1 + Math.random() * 0.4,
            predicted_height: 80 + Math.random() * 40,
            development_timeline: {
                current_phase_completion: (plantData.current_day / 70) * 100,
                estimated_harvest_day: 65 + Math.random() * 10,
                phase_transitions: this.predictPhaseTransitions(plantData)
            },
            yield_prediction: {
                estimated_yield: 90 + Math.random() * 30,
                quality_score: 8 + Math.random() * 2,
                confidence: 0.8 + Math.random() * 0.15
            }
        };
    }

    /**
     * Erweitert Empfehlungen mit eigener Analyse
     */
    enhanceRecommendations(baseRecommendations, analysis) {
        const enhanced = {
            ...baseRecommendations,
            analysis: analysis,
            enhanced_recommendations: [],
            priority_actions: [],
            long_term_strategy: [],
            risk_mitigation: []
        };

        // Basis-Empfehlungen erweitern
        baseRecommendations.recommendations.forEach(rec => {
            enhanced.enhanced_recommendations.push({
                ...rec,
                analysis_support: this.getAnalysisSupport(rec, analysis),
                implementation_steps: this.generateImplementationSteps(rec),
                expected_timeline: this.estimateImplementationTime(rec),
                success_metrics: this.defineSuccessMetrics(rec)
            });
        });

        // Prioritäts-Aktionen basierend auf Analyse
        enhanced.priority_actions = this.generatePriorityActions(analysis);
        
        // Langzeit-Strategie
        enhanced.long_term_strategy = this.generateLongTermStrategy(analysis);
        
        // Risiko-Minderung
        enhanced.risk_mitigation = this.generateRiskMitigation(analysis);

        this.currentRecommendations = enhanced;
        this.recommendationHistory.push({
            timestamp: new Date().toISOString(),
            recommendations: enhanced,
            plant_data: this.widget.floweringData
        });

        return enhanced;
    }

    /**
     * Zeigt AI Recommendations Ergebnisse an
     */
    showResults(recommendations) {
        // Prüfen ob bereits eine AI-Empfehlungs-Card existiert
        let aiCard = this.widget.element.querySelector('.ai-recommendations-card');
        
        if (!aiCard) {
            // Neue AI-Empfehlungs-Card erstellen
            aiCard = document.createElement('div');
            aiCard.className = 'lighting-card ai-recommendations-card';
            this.widget.element.appendChild(aiCard);
        }
        
        // Card-Inhalt aktualisieren
        aiCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-robot"></i>
                <h3>KI-Empfehlungen</h3>
                <div class="ai-confidence">
                    <span class="confidence-badge ${this.getConfidenceClass(recommendations.confidence)}">
                        ${Math.round(recommendations.confidence * 100)}% Konfidenz
                    </span>
                </div>
            </div>
            <div class="ai-content">
                <!-- Analyse-Übersicht -->
                ${recommendations.analysis ? `
                    <div class="analysis-overview mb-3">
                        <h5>📊 Analyse-Übersicht</h5>
                        <div class="analysis-metrics">
                            <div class="metric">
                                <span class="metric-label">Pflanzengesundheit:</span>
                                <span class="metric-value">${Math.round(recommendations.analysis.plantHealth.overall_score * 100)}%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Wachstumstrend:</span>
                                <span class="metric-value">${recommendations.analysis.growthPrediction.current_trend}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Ertragsprognose:</span>
                                <span class="metric-value">${Math.round(recommendations.analysis.growthPrediction.yield_prediction.estimated_yield)}g</span>
                            </div>
                        </div>
                    </div>
                ` : ''}
                
                <!-- Prioritäts-Aktionen -->
                ${recommendations.priority_actions && recommendations.priority_actions.length > 0 ? `
                    <div class="priority-actions mb-3">
                        <h5>🚨 Prioritäts-Aktionen</h5>
                        <div class="actions-list">
                            ${recommendations.priority_actions.map(action => `
                                <div class="action-item priority-${action.priority}">
                                    <div class="action-title">${action.title}</div>
                                    <div class="action-description">${action.description}</div>
                                    <div class="action-timeline">Zeitrahmen: ${action.timeline}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                <!-- Haupt-Empfehlungen -->
                <div class="ai-recommendations-list">
                    <h5>💡 Empfehlungen</h5>
                    ${(recommendations.enhanced_recommendations || recommendations.recommendations).map((rec, index) => `
                        <div class="ai-recommendation-item ${rec.priority}">
                            <div class="recommendation-header">
                                <span class="priority-badge ${rec.priority}">${this.getPriorityText(rec.priority)}</span>
                                <h6>${rec.title}</h6>
                            </div>
                            <div class="recommendation-content">
                                <p>${rec.description}</p>
                                <div class="recommendation-details">
                                    <span class="action-text"><strong>Aktion:</strong> ${rec.action}</span>
                                    <span class="improvement-text"><strong>Erwartete Verbesserung:</strong> ${rec.expectedImprovement}</span>
                                    ${rec.expected_timeline ? `<span class="timeline-text"><strong>Zeitrahmen:</strong> ${rec.expected_timeline}</span>` : ''}
                                </div>
                                ${rec.implementation_steps ? `
                                    <div class="implementation-steps">
                                        <strong>Umsetzungsschritte:</strong>
                                        <ol>
                                            ${rec.implementation_steps.map(step => `<li>${step}</li>`).join('')}
                                        </ol>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
                
                <!-- Langzeit-Strategie -->
                ${recommendations.long_term_strategy && recommendations.long_term_strategy.length > 0 ? `
                    <div class="long-term-strategy mt-3">
                        <h5>🎯 Langzeit-Strategie</h5>
                        <div class="strategy-list">
                            ${recommendations.long_term_strategy.map(strategy => `
                                <div class="strategy-item">
                                    <div class="strategy-title">${strategy.title}</div>
                                    <div class="strategy-description">${strategy.description}</div>
                                    <div class="strategy-timeline">Zeitrahmen: ${strategy.timeline}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                <!-- Aktions-Buttons -->
                <div class="ai-actions mt-3">
                    <button class="btn btn-sm btn-outline-primary" onclick="floweringWidget.advancedML.aiRecommendations.showRecommendationDetails()">
                        <i class="fa-solid fa-chart-line"></i> Details
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="floweringWidget.advancedML.aiRecommendations.applyRecommendations()">
                        <i class="fa-solid fa-magic"></i> Anwenden
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="floweringWidget.advancedML.aiRecommendations.exportRecommendations()">
                        <i class="fa-solid fa-download"></i> Exportieren
                    </button>
                </div>
            </div>
        `;
        
        // AI-Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && aiCard.parentElement !== phase6Container) {
            phase6Container.appendChild(aiCard);
        }
    }

    /**
     * Zeigt detaillierte Empfehlungs-Informationen
     */
    showRecommendationDetails() {
        if (!this.currentRecommendations) {
            alert('Keine aktuellen KI-Empfehlungen verfügbar.');
            return;
        }
        
        const rec = this.currentRecommendations;
        let details = `🤖 KI-Empfehlungs-Details:\n\n`;
        details += `Pflanze: ${this.widget.currentPlantId}\n`;
        details += `Konfidenz: ${Math.round(rec.confidence * 100)}%\n\n`;
        
        if (rec.analysis) {
            details += `📊 Analyse:\n`;
            details += `• Pflanzengesundheit: ${Math.round(rec.analysis.plantHealth.overall_score * 100)}%\n`;
            details += `• Wachstumstrend: ${rec.analysis.growthPrediction.current_trend}\n`;
            details += `• Ertragsprognose: ${Math.round(rec.analysis.growthPrediction.yield_prediction.estimated_yield)}g\n`;
            details += `• Qualitätsscore: ${rec.analysis.growthPrediction.yield_prediction.quality_score.toFixed(1)}/10\n\n`;
        }
        
        details += `💡 Empfehlungen (${rec.recommendations.length}):\n`;
        rec.recommendations.forEach((recommendation, index) => {
            details += `${index + 1}. ${recommendation.title}\n`;
            details += `   ${recommendation.description}\n`;
            details += `   Aktion: ${recommendation.action}\n`;
            details += `   Verbesserung: ${recommendation.expectedImprovement}\n\n`;
        });
        
        alert(details);
    }

    /**
     * Wendet KI-Empfehlungen an
     */
    async applyRecommendations() {
        if (!this.currentRecommendations || this.currentRecommendations.recommendations.length === 0) {
            alert('Keine Empfehlungen zum Anwenden verfügbar.');
            return;
        }
        
        const confirmed = confirm(
            `Möchtest du ${this.currentRecommendations.recommendations.length} KI-Empfehlung(en) anwenden?\n\n` +
            `Dies wird automatische Anpassungen an deinen Einstellungen vornehmen.`
        );
        
        if (!confirmed) return;
        
        try {
            // Empfehlungen anwenden
            for (const recommendation of this.currentRecommendations.recommendations) {
                await this.applySingleRecommendation(recommendation);
            }
            
            // Daten neu laden
            await this.widget.lightingManager.loadLightingData();
            
            this.widget.uiRenderer.showSuccess('KI-Empfehlungen erfolgreich angewendet!');
            
        } catch (error) {
            console.error('Fehler beim Anwenden der KI-Empfehlungen:', error);
            this.widget.uiRenderer.showError('Fehler beim Anwenden der KI-Empfehlungen: ' + error.message);
        }
    }

    /**
     * Exportiert Empfehlungen
     */
    exportRecommendations() {
        if (!this.currentRecommendations) {
            alert('Keine Empfehlungen zum Exportieren verfügbar.');
            return;
        }
        
        const exportData = {
            timestamp: new Date().toISOString(),
            plant_id: this.widget.currentPlantId,
            recommendations: this.currentRecommendations,
            analysis: this.analysisResults.get('latest')
        };
        
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `ai-recommendations-${this.widget.currentPlantId}-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    /**
     * Hilfsfunktionen
     */
    initializeAIModels() {
        this.aiModels.set('growth_optimizer', { accuracy: 0.89, specialization: 'growth_optimization' });
        this.aiModels.set('yield_predictor', { accuracy: 0.92, specialization: 'yield_prediction' });
        this.aiModels.set('health_assessor', { accuracy: 0.87, specialization: 'health_assessment' });
        this.aiModels.set('environment_optimizer', { accuracy: 0.85, specialization: 'environmental_optimization' });
        this.aiModels.set('energy_optimizer', { accuracy: 0.83, specialization: 'energy_efficiency' });
    }

    async initializeAnalysisEngines() {
        // Initialisiere verschiedene Analyse-Engines
        console.log('🤖 AI Recommendations: Analyse-Engines initialisiert');
    }

    calculateGrowthRate(plantData) {
        return 0.8 + Math.random() * 0.4; // 0.8 - 1.2
    }

    assessDevelopmentStage(plantData) {
        const stages = ['early_flower', 'mid_flower', 'late_flower', 'pre_harvest'];
        return stages[Math.floor(Math.random() * stages.length)];
    }

    identifyHealthRisks() {
        const risks = ['nutrient_deficiency', 'light_stress', 'temperature_stress'];
        return risks.filter(() => Math.random() > 0.7);
    }

    generateHealthRecommendations(healthScore) {
        if (healthScore > 0.9) return ['Aktuelle Bedingungen beibehalten'];
        if (healthScore > 0.7) return ['Monitoring verstärken', 'Kleine Anpassungen vornehmen'];
        return ['Sofortige Maßnahmen erforderlich', 'Umfassende Überprüfung'];
    }

    predictPhaseTransitions(plantData) {
        return {
            next_phase: 'late_flower',
            estimated_days: 14 + Math.random() * 7,
            confidence: 0.8 + Math.random() * 0.15
        };
    }

    analyzeEnergyEfficiency(energyData) {
        return {
            current_efficiency: 0.75 + Math.random() * 0.2,
            optimization_potential: 0.1 + Math.random() * 0.15,
            cost_savings: 50 + Math.random() * 100
        };
    }

    analyzeStressFactors() {
        return {
            light_stress: Math.random() * 0.3,
            heat_stress: Math.random() * 0.2,
            nutrient_stress: Math.random() * 0.25
        };
    }

    identifyOptimizationOpportunities() {
        return [
            { area: 'lighting', potential: 0.15, priority: 'high' },
            { area: 'environment', potential: 0.1, priority: 'medium' },
            { area: 'nutrients', potential: 0.08, priority: 'low' }
        ];
    }

    generatePriorityActions(analysis) {
        const actions = [];
        
        if (analysis.plantHealth.overall_score < 0.8) {
            actions.push({
                title: 'Pflanzengesundheit verbessern',
                description: 'Sofortige Maßnahmen zur Verbesserung der Pflanzengesundheit',
                priority: 'high',
                timeline: '24-48 Stunden'
            });
        }
        
        return actions;
    }

    generateLongTermStrategy(analysis) {
        return [
            {
                title: 'Ertragssteigerung',
                description: 'Langfristige Optimierung für höhere Erträge',
                timeline: '2-4 Wochen'
            },
            {
                title: 'Energieeffizienz',
                description: 'Reduzierung des Energieverbrauchs bei gleichbleibender Qualität',
                timeline: '1-2 Wochen'
            }
        ];
    }

    generateRiskMitigation(analysis) {
        return analysis.plantHealth.risk_factors.map(risk => ({
            risk: risk,
            mitigation: `Maßnahmen gegen ${risk}`,
            priority: 'medium'
        }));
    }

    getAnalysisSupport(recommendation, analysis) {
        return `Unterstützt durch ${Math.round(analysis.plantHealth.overall_score * 100)}% Gesundheitsscore`;
    }

    generateImplementationSteps(recommendation) {
        return [
            'Aktuelle Einstellungen dokumentieren',
            'Schrittweise Anpassungen vornehmen',
            'Ergebnisse überwachen',
            'Bei Bedarf nachjustieren'
        ];
    }

    estimateImplementationTime(recommendation) {
        const times = ['Sofort', '1-2 Stunden', '1 Tag', '2-3 Tage'];
        return times[Math.floor(Math.random() * times.length)];
    }

    defineSuccessMetrics(recommendation) {
        return [
            'Verbesserung der Zielparameter um 10-15%',
            'Stabile Werte über 48 Stunden',
            'Keine negativen Nebeneffekte'
        ];
    }

    async applySingleRecommendation(recommendation) {
        // Simuliere Anwendung einer Empfehlung
        console.log(`🤖 Wende Empfehlung an: ${recommendation.title}`);
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    generateDemoRecommendations() {
        return {
            confidence: 0.87,
            recommendations: [
                {
                    title: 'PPFD-Optimierung',
                    description: 'Erhöhung der PPFD für bessere Blütenentwicklung',
                    action: 'PPFD von 750 auf 850 μmol/m²/s erhöhen',
                    expectedImprovement: '12% höhere Blütendichte',
                    priority: 'high'
                },
                {
                    title: 'Temperatur-Anpassung',
                    description: 'Nachttemperatur für optimale Terpene-Entwicklung',
                    action: 'Nachttemperatur auf 18-20°C reduzieren',
                    expectedImprovement: '8% bessere Terpene-Profile',
                    priority: 'medium'
                }
            ],
            priority_actions: [
                {
                    title: 'Sofortige PPFD-Anpassung',
                    description: 'Kritische Beleuchtungsoptimierung erforderlich',
                    priority: 'high',
                    timeline: '2-4 Stunden'
                }
            ]
        };
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.9) return 'excellent';
        if (confidence >= 0.8) return 'good';
        if (confidence >= 0.7) return 'average';
        return 'low';
    }

    getPriorityText(priority) {
        const priorities = {
            'high': 'Hoch',
            'medium': 'Mittel',
            'low': 'Niedrig'
        };
        return priorities[priority] || priority;
    }
}
