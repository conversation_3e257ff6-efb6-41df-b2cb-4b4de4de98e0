/**
 * Watering Widget Module
 * Handles watering plan functionality
 */

class WateringWidget {
    constructor(containerId = 'watering-plan-box', options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        // Stelle sicher, dass currentPhase ein String ist
        let phase = options.currentPhase || window.currentPhaseKey || 'vegetative_early';
        if (typeof phase === 'object' && phase !== null) {
            phase = 'flowering_late'; // Fallback wenn es ein Objekt ist
        }
        this.currentPhase = phase;
        this.substrate = 'erde';
        this.potSize = 11;
        this.currentWaterAmount = 0;
        
        if (this.container) {
            this.init();
        } else {
            console.warn(`Watering Widget: Container ${containerId} nicht gefunden`);
        }
    }

    init() {
        // Watering Widget wird initialisiert...
        this.loadWateringPlan();
        this.setupEventListeners();
    }

    // Hilfsfunktion für benutzerfreundliche Phase-Namen
    getFriendlyPhaseName(phaseKey) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phaseKey] || phaseKey.replace('_', ' ');
    }

    // Event-Listener für Widget-Interaktionen
    setupEventListeners() {
        // Event-Delegation für dynamisch erstellte Elemente
        this.container.addEventListener('click', (e) => {
            if (e.target.id === 'watering-calc-btn') {
                this.calculateWatering();
            }
        });

        // Input-Event-Listener für Live-Updates
        this.container.addEventListener('input', (e) => {
            if (e.target.id === 'substrate-input' || e.target.id === 'pot-size-input' || e.target.id === 'water-amount-input') {
                this.updateInputValues();
            }
        });

        // Event-Listener für Änderungen der Pflanzendaten (z.B. Topfgröße)
        document.addEventListener('plantDataUpdated', () => {
            // Watering Widget: Pflanzendaten wurden aktualisiert, lade Daten neu...
            this.loadPlantDataFromDOM();
            this.loadWateringPlan();
        });

        // Event-Listener für spezifische Topfgrößen-Änderungen
        document.addEventListener('potSizeChanged', (event) => {
            // Watering Widget: Topfgröße wurde geändert:
            this.potSize = event.detail.newSize;
            this.loadWateringPlan();
        });
    }

    // Aktuelle Input-Werte aktualisieren
    updateInputValues() {
        const substrateInput = document.getElementById('substrate-input');
        const potSizeInput = document.getElementById('pot-size-input');
        const waterAmountInput = document.getElementById('water-amount-input');
        
        if (substrateInput) this.substrate = substrateInput.value;
        if (potSizeInput) this.potSize = parseFloat(potSizeInput.value) || 11;
        if (waterAmountInput) this.currentWaterAmount = parseFloat(waterAmountInput.value) || 0;
    }

    // Bewässerungs-Berechnung auslösen
    async calculateWatering() {
        // Zuerst Input-Werte aktualisieren
        this.updateInputValues();
        
        // Dann DOM-Daten laden (falls Input leer ist)
        this.loadPlantDataFromDOM();
        
        // Bewässerungsplan mit aktuellen Werten laden
        await this.loadWateringPlan();
    }

    // Hauptfunktion: Bewässerungsplan laden
    async loadWateringPlan() {
        if (!this.container) return;

        // Guidelines laden, falls noch nicht geschehen
        if (!WateringWidget.wateringGuidelinesLoaded) {
            await WateringWidget.loadWateringGuidelines();
        }

        // Bewässerungsplan wird geladen...
        // Aktuelle Werte vor API-Call: {substrate: 'erde', potSize: 11, currentWaterAmount: 0}
        
        this.container.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Lade Bewässerungsplan...</div>';

        try {
            // Plant ID aus DOM oder URL holen
            let plantId = null;
            const plantIdElement = document.querySelector('[data-plant-id]');
            if (plantIdElement) {
                plantId = plantIdElement.getAttribute('data-plant-id');
            } else {
                // Aus URL holen
                const urlParams = new URLSearchParams(window.location.search);
                plantId = urlParams.get('plant_id');
            }
            
            const url = `/api/watering/plan/${encodeURIComponent(this.currentPhase)}?substrate=${encodeURIComponent(this.substrate)}&pot_size_l=${encodeURIComponent(this.potSize)}&water_amount_ml=${encodeURIComponent(this.currentWaterAmount)}${plantId ? `&plant_id=${plantId}` : ''}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error('Bewässerungsplan konnte nicht geladen werden');
            }
            
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            
            this.renderWateringWidget(data);
            
        } catch (err) {
            this.container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler beim Laden des Bewässerungsplans: ${err.message}
                </div>
            `;
        }
    }

    // Pflanzen-Daten aus DOM laden (nur als Fallback wenn Input-Werte leer sind)
    loadPlantDataFromDOM() {
        // Nur laden wenn Input-Werte nicht gesetzt sind
        const shouldLoadSubstrate = !this.substrate || this.substrate === '';
        const shouldLoadPotSize = !this.potSize || this.potSize === 11;
        
        if (!shouldLoadSubstrate && !shouldLoadPotSize) {
            // Watering Widget: Input-Werte bereits gesetzt, überspringe DOM-Laden
            return;
        }
        
        // Verschiedene Selektoren für Substrat und Topfgröße probieren
        const substrateSelectors = [
            '[data-plant-substrate]',
            '.plant-substrate',
            '#plant-substrate'
        ];
        
        const potSizeSelectors = [
            '[data-plant-pot-size]',
            '.plant-pot-size',
            '#plant-pot-size'
        ];
        
        // Substrat laden (nur wenn nicht bereits gesetzt)
        if (shouldLoadSubstrate) {
            for (const selector of substrateSelectors) {
                const substrateElement = document.querySelector(selector);
                if (substrateElement) {
                    const substrateText = substrateElement.textContent.trim();
                    if (substrateText && substrateText !== '-') {
                        if (substrateText.toLowerCase().includes('erde')) this.substrate = 'erde';
                        else if (substrateText.toLowerCase().includes('coco')) this.substrate = 'coco';
                        else if (substrateText.toLowerCase().includes('hydro')) this.substrate = 'hydro';
                        else this.substrate = substrateText.toLowerCase();
                        // Watering Widget: Substrat aus DOM geladen:
                        break;
                    }
                }
            }
        }
        
        // Topfgröße laden (nur wenn nicht bereits gesetzt)
        if (shouldLoadPotSize) {
            for (const selector of potSizeSelectors) {
                const potSizeElement = document.querySelector(selector);
                if (potSizeElement) {
                    const potSizeText = potSizeElement.textContent.trim();
                    if (potSizeText && potSizeText !== '-') {
                        const match = potSizeText.match(/(\d+(?:\.\d+)?)/);
                        if (match) {
                            this.potSize = parseFloat(match[1]);
                            // Watering Widget: Topfgröße aus DOM geladen:
                            break;
                        }
                    }
                }
            }
        }
        
        // Fallback: Topfgröße aus versteckten dat-Attributen (nur wenn noch nicht gesetzt)
        if (shouldLoadPotSize && (!this.potSize || this.potSize === 11)) {
            const plantCard = document.querySelector('.plant-card, [data-plant-id], #strainTypeContainer');
            if (plantCard) {
                const potSizeData = plantCard.getAttribute('data-pot-size');
                if (potSizeData) {
                    const match = potSizeData.match(/(\d+(?:\.\d+)?)/);
                    if (match) {
                        this.potSize = parseFloat(match[1]);
                        // Watering Widget: Topfgröße aus data-Attribut geladen:
                    }
                }
                
                // Auch Substrat aus data-Attribut laden (nur wenn noch nicht gesetzt)
                if (shouldLoadSubstrate) {
                    const substrateData = plantCard.getAttribute('data-substrate');
                    if (substrateData && !this.substrate) {
                        this.substrate = substrateData.toLowerCase();
                        // Watering Widget: Substrat aus data-Attribut geladen:
                    }
                }
            }
        }
    }

    // Bewässerungs-Widget rendern
    renderWateringWidget(data) {
        const rec = data.recommendations;
        const phaseName = this.getFriendlyPhaseName(data.phase);
        
        const html = `
            <div class="watering-widget-card">
                <div class="watering-widget-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fa-solid fa-tint"></i>
                        Bewässerungsplan <span class="watering-phase-name">${phaseName}</span>
                    </div>
                    <div class="watering-widget-actions">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-info me-2" 
                            data-bs-toggle="modal" 
                            data-bs-target="#watering-guidelines-modal"
                            title="Bewässerungsrichtlinien und Faustregeln anzeigen"
                        >
                            <i class="fas fa-book"></i> Richtlinien
                        </button>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-light" 
                            data-bs-toggle="tooltip" 
                            data-bs-placement="left"
                            title="Optimale Bewässerungsmengen basierend auf Substrat, Topfgröße und Wachstumsphase. Verhindert Über- und Unterbewässerung und fördert gesundes Wurzelwachstum."
                        >
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                </div>
                <div class="watering-widget-body">
                    ${this.renderRecommendedWatering(data)}
                    ${this.renderPhaseSpecificGuidelines(data)}
                    ${this.renderPotSizeExamples(data)}
                    ${this.renderPlantSettings()}
                    ${this.renderWateringNotes(rec)}
                    ${this.renderWaterAmountComparison(data)}
                    ${this.renderDrainGuidelines(data)}
                    ${this.renderGuidelinesPreview()}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        
        // Tooltips nach dem Rendern initialisieren
        this.initTooltips();
        
        // Guidelines-Modal erstellen falls nicht vorhanden
        this.createGuidelinesModal();
    }
    
    // Tooltips für dynamisch erstellte Elemente initialisieren
    initTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            // Watering Widget Tooltips initialisiert:
        } else {
            // Fallback: Warten und erneut versuchen
            setTimeout(() => this.initTooltips(), 500);
        }
    }

    // Empfohlene Bewässerung rendern
    renderRecommendedWatering(data) {
        const amountPerPot = Math.round(data.amount_ml_per_liter * this.potSize);
        
        // Strain-Type aus dem Template oder Widget holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        // Strain-spezifische Hinweise
        let strainSpecificNote = '';
        if (data.strain_specific) {
            strainSpecificNote = `
                <div class="watering-strain-specific">
                    <i class="fa fa-leaf me-2"></i>
                    <strong>Sortentyp-spezifische Empfehlungen:</strong> ${strainTypeDisplay}
                    ${isAutoflower ? 
                        '<br><small class="text-muted">Autoflowers benötigen weniger Wasser pro Gießvorgang für optimales Wachstum.</small>' : 
                        '<br><small class="text-muted">Photoperiodische Sorten können höhere Wassermengen vertragen.</small>'
                    }
                </div>
            `;
        }
        
        return `
            <div class="watering-section-title watering-recommended">
                <i class="fa-solid fa-check-circle me-2"></i>Empfohlene Bewässerung
                ${data.strain_specific ? '<span class="badge bg-info ms-2">Sortentyp-spezifisch</span>' : ''}
                <span class="badge bg-success ms-2">Empfehlung für ${strainTypeDisplay}</span>
            </div>
            <div class="watering-section-content watering-recommended-content">
                ${strainSpecificNote}
                <div class="watering-recommendation-type">
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>Empfehlungstyp:</strong> 
                    ${data.strain_specific ? 
                        `<span class="text-success">Sortentyp-spezifisch (${strainTypeDisplay})</span>` : 
                        `<span class="text-warning">Allgemeine Empfehlung</span>`
                    }
                </div>
                <div><span class="watering-label">Bewässerungsmenge:</span> <span class="watering-amount">${data.amount_ml_per_liter} ml pro Liter Substrat</span></div>
                <div><span class="watering-label">Häufigkeit:</span> <span class="watering-frequency">${data.frequency}</span></div>
                <div><span class="watering-label">Menge pro Topf:</span> <span class="watering-amount">${amountPerPot} ml</span></div>
                ${data.watering_factor_min ? `<div><span class="watering-label">Faktor:</span> <span class="watering-factor">${(data.watering_factor_min * 100).toFixed(1)}-${(data.watering_factor_max * 100).toFixed(1)}% des Topfvolumens</span></div>` : ''}
            </div>
        `;
    }

    // Phasen-spezifische Guidelines rendern
    renderPhaseSpecificGuidelines(data) {
        const guidelines = WateringWidget.wateringGuidelines;
        if (!guidelines) return '';

        const phaseGuidelines = guidelines.phases.find(p => p.phase === this.currentPhase);
        if (!phaseGuidelines) return '';

        const isAutoflower = this.getStrainType().toLowerCase().includes('auto');
        const strainType = isAutoflower ? 'autoflower' : 'photoperiod';
        const factors = (phaseGuidelines.wateringFactors && phaseGuidelines.wateringFactors[strainType]) ? phaseGuidelines.wateringFactors[strainType] : null;

        return `
            <div class="watering-section-title watering-guidelines-phase">
                <i class="fa-solid fa-leaf me-2"></i>Phasen-spezifische Hinweise
                <span class="badge bg-info ms-2">${this.getFriendlyPhaseName(this.currentPhase)}</span>
            </div>
            <div class="watering-section-content watering-guidelines-phase-content">
                <div class="watering-phase-description">
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>Phase:</strong> ${phaseGuidelines.description}
                </div>
                <div><span class="watering-label">Bewässerungsfaktor:</span> <span class="watering-factor">${factors ? `${(factors.min * 100).toFixed(1)}-${(factors.max * 100).toFixed(1)}% des Topfvolumens` : 'Keine Daten vorhanden'}</span></div>
                <div class="watering-phase-notes">
                    <strong>Wichtige Hinweise:</strong>
                    <ul class="watering-phase-notes-list">
                        ${(Array.isArray(phaseGuidelines.notes) ? phaseGuidelines.notes : []).map(note => `<li>${note}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    // Topfgrößen-Beispiele rendern
    renderPotSizeExamples(data) {
        const guidelines = WateringWidget.wateringGuidelines;
        if (!guidelines || !guidelines.topfGroessenBeispiel) return '';

        const currentPhaseExamples = guidelines.topfGroessenBeispiel.filter(ex => ex.phase === this.currentPhase);
        if (currentPhaseExamples.length === 0) return '';

        const isAutoflower = this.getStrainType().toLowerCase().includes('auto');
        const strainType = isAutoflower ? 'autoflower' : 'photoperiod';

        return `
            <div class="watering-section-title watering-examples">
                <i class="fa-solid fa-ruler me-2"></i>Topfgrößen-Beispiele
                <span class="badge bg-info ms-2">${this.getFriendlyPhaseName(this.currentPhase)}</span>
            </div>
            <div class="watering-section-content watering-examples-content">
                <div class="watering-examples-grid">
                    ${currentPhaseExamples.map(ex => {
                        const values = ex[strainType];
                        return `
                            <div class="watering-example-item">
                                <div class="watering-example-pot-size">
                                    <i class="fa fa-flask me-2"></i>
                                    <strong>${ex.potSizeLiters}L Topf:</strong>
                                </div>
                                <div class="watering-example-amount">
                                    ${values.minMl}-${values.maxMl} ml
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
                <div class="watering-examples-note">
                    <small class="text-muted">
                        <i class="fa fa-info-circle me-1"></i>
                        Beispiele für ${isAutoflower ? 'Autoflower' : 'Photoperiodische'} Sorten in der ${this.getFriendlyPhaseName(this.currentPhase)}
                    </small>
                </div>
            </div>
        `;
    }

    // Drain-Guidelines rendern
    renderDrainGuidelines(data) {
        const guidelines = WateringWidget.wateringGuidelines;
        if (!guidelines) return '';

        const amountPerPot = Math.round(data.amount_ml_per_liter * this.potSize);
        const drainTarget = Math.round(amountPerPot * 0.15); // 15% Drain-Ziel
        const drainMin = Math.round(amountPerPot * 0.10); // 10% Minimum
        const drainMax = Math.round(amountPerPot * 0.20); // 20% Maximum

        return `
            <div class="watering-section-title watering-drain">
                <i class="fa-solid fa-tint me-2"></i>Drainage-Ziele
                <span class="badge bg-info ms-2">Wichtig für Nährstoffaufnahme</span>
            </div>
            <div class="watering-section-content watering-drain-content">
                <div class="watering-drain-target">
                    <i class="fa fa-bullseye me-2"></i>
                    <strong>Drain-Ziel:</strong> ${drainTarget} ml (15% der Gießmenge)
                </div>
                <div class="watering-drain-range">
                    <span class="watering-label">Akzeptabler Bereich:</span> ${drainMin}-${drainMax} ml (10-20%)
                </div>
                <div class="watering-drain-warnings">
                    <div class="watering-drain-warning">
                        <i class="fa fa-exclamation-triangle text-warning me-2"></i>
                        <strong>Zu wenig Drain (&lt; 10%):</strong> Salzansammlung, Nährstoffblockaden
                    </div>
                    <div class="watering-drain-warning">
                        <i class="fa fa-exclamation-triangle text-danger me-2"></i>
                        <strong>Zu viel Drain (&gt; 20%):</strong> Nährstoffverlust, ineffiziente Bewässerung
                    </div>
                </div>
            </div>
        `;
    }

    // Strain-Type ermitteln
    getStrainType() {
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        return strainType;
    }

    // Pflanzen-Einstellungen rendern
    renderPlantSettings() {
        return `
            <div class="watering-section-title watering-current">
                <i class="fa-solid fa-cog me-2"></i>Pflanzen-Einstellungen
            </div>
            <div class="watering-section-content watering-current-content">
                <div class="watering-input-grid">
                    <div class="watering-input-group">
                        <label for="substrate-input">Substrat</label>
                        <select id="substrate-input" class="watering-input">
                            <option value="erde" ${this.substrate === 'erde' ? 'selected' : ''}>Erde</option>
                            <option value="coco" ${this.substrate === 'coco' ? 'selected' : ''}>Coco</option>
                            <option value="hydro" ${this.substrate === 'hydro' ? 'selected' : ''}>Hydro</option>
                        </select>
                    </div>
                    <div class="watering-input-group">
                        <label for="pot-size-input">Topfgröße (L)</label>
                        <input type="number" min="1" max="50" step="0.5" 
                               id="pot-size-input" value="${this.potSize}" 
                               class="watering-input">
                    </div>
                </div>
                <div class="watering-input-group">
                    <label for="water-amount-input">Gießwasser-Menge (ml)</label>
                    <input type="number" min="0" step="10" 
                           id="water-amount-input" placeholder="z.B. 500" 
                           value="${this.currentWaterAmount > 0 ? this.currentWaterAmount : ''}" 
                           class="watering-input">
                    <small class="watering-input-hint">
                        <i class="fa fa-info-circle"></i> (Optional: Prüft ob die Menge ausreicht)
                    </small>
                </div>
                <div class="watering-calc-section">
                    <button id="watering-calc-btn" class="btn btn-sm btn-primary">
                        <i class="fa fa-calculator"></i> Bewässerung Berechnen
                    </button>
                    <span class="watering-input-hint">
                        <i class="fa fa-info-circle"></i> (Anpassung der Einstellungen)
                    </span>
                </div>
            </div>
        `;
    }

    // Bewässerungs-Hinweise rendern
    renderWateringNotes(rec) {
        return `
            <div class="watering-section-title watering-notes">
                <i class="fa-solid fa-info-circle me-2"></i>Hinweise
            </div>
            <div class="watering-section-content watering-notes-content">
                <div class="watering-note">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    ${rec.note}
                </div>
            </div>
        `;
    }

    // Wasser-Mengen-Vergleich rendern
    renderWaterAmountComparison(data) {
        const recommendedAmount = Math.round(data.amount_ml_per_liter * this.potSize);
        if (this.currentWaterAmount <= 0) return '';
        const difference = this.currentWaterAmount - recommendedAmount;
        const percentage = Math.round((this.currentWaterAmount / recommendedAmount) * 100);
        
        let statusClass = 'text-success';
        let statusIcon = 'fa-check-circle';
        let statusText = 'Perfekt!';
        
        if (percentage < 80) {
            statusClass = 'text-warning';
            statusIcon = 'fa-exclamation-triangle';
            statusText = 'Zu wenig';
        } else if (percentage > 120) {
            statusClass = 'text-danger';
            statusIcon = 'fa-exclamation-circle';
            statusText = 'Zu viel';
        }
        
        return `
            <div class="watering-section-title watering-comparison">
                <i class="fa-solid fa-balance-scale me-2"></i>Mengen-Vergleich
            </div>
            <div class="watering-section-content watering-comparison-content">
                <div><span class="watering-label">Deine Menge:</span> <span class="watering-amount">${this.currentWaterAmount} ml</span></div>
                <div><span class="watering-label">Empfohlen:</span> <span class="watering-amount">${recommendedAmount} ml</span></div>
                <div><span class="watering-label">Status:</span> <span class="${statusClass}"><i class="fa-solid ${statusIcon} me-1"></i>${statusText} (${percentage}%)</span></div>
                ${difference !== 0 ? `<div><span class="watering-label">Differenz:</span> <span class="${difference > 0 ? 'text-danger' : 'text-warning'}">${difference > 0 ? '+' : ''}${difference} ml</span></div>` : ''}
            </div>
        `;
    }

    // Phase aktualisieren (für externe Aufrufe)
    updatePhase(newPhase) {
        this.currentPhase = newPhase;
        this.loadWateringPlan();
    }

    // Guidelines-Vorschau rendern
    renderGuidelinesPreview() {
        // Strain-Type aus dem Template oder Widget holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        return `
            <div class="watering-section-title watering-guidelines">
                <i class="fa-solid fa-lightbulb me-2"></i>Wichtige Faustregeln
                <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
            </div>
            <div class="watering-section-content watering-guidelines-content">
                <div class="watering-guidelines-preview">
                    <ul class="watering-guidelines-list">
                        <li><strong>Pro Gießvorgang:</strong> ca. 1/4 bis 1/3 des Topfvolumens</li>
                        <li><strong>Drainage:</strong> Immer auf ca. 10–20% Drain abzielen</li>
                        <li><strong>Trocknung:</strong> Zwischen Gießvorgängen vollständig trocknen lassen</li>
                        <li><strong>Gießweise:</strong> Besser selten und durchdringend als häufig und flach</li>
                        ${isAutoflower ? '<li><strong>Autoflower-spezifisch:</strong> Weniger Wasser pro Gießvorgang für optimales Wachstum</li>' : ''}
                    </ul>
                    <div class="watering-guidelines-more">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#watering-guidelines-modal"
                        >
                            <i class="fas fa-book-open"></i> Alle Richtlinien anzeigen
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Guidelines-Modal erstellen
    createGuidelinesModal() {
        // Prüfen ob Modal bereits existiert
        if (document.getElementById('watering-guidelines-modal')) {
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="watering-guidelines-modal" tabindex="-1" aria-labelledby="watering-guidelines-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="watering-guidelines-modal-label">
                                <i class="fa-solid fa-tint me-2"></i>Bewässerungsrichtlinien & Faustregeln
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="watering-guidelines-content">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fa-solid fa-list-check me-2"></i>Faustregeln</h6>
                                        <ul class="watering-guidelines-list">
                                            <li>Pro Gießvorgang ca. 1/4 bis 1/3 des Topfvolumens verwenden.</li>
                                            <li>Immer auf ca. 10–20% Drain abzielen.</li>
                                            <li>Zwischen Gießvorgängen vollständig trocknen lassen (Topfgewicht prüfen).</li>
                                            <li>Besser selten und durchdringend als häufig und flach gießen.</li>
                                            <li>In Light Mix früher mit Dünger starten, da weniger vorgedüngt.</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fa-solid fa-chart-line me-2"></i>Phase-Einfluss</h6>
                                        <div class="watering-phase-influence">
                                            <div class="watering-phase-item">
                                                <strong>Vegetation:</strong> Fokus auf Wurzelaufbau, geringerer Wasserverbrauch. Anfangs kleinere Mengen, dann schrittweise steigern.
                                            </div>
                                            <div class="watering-phase-item">
                                                <strong>Blüte:</strong> Steigender Wasserbedarf, besonders ab Blütetag 25–30. Gießmenge sollte spätestens ab Woche 5 angepasst sein.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6><i class="fa-solid fa-leaf me-2"></i>Sortentyp-spezifische Empfehlungen</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="watering-strain-section">
                                            <h6 class="text-primary">
                                                <i class="fa-solid fa-sun me-2"></i>Photoperiodische Sorten
                                            </h6>
                                            <ul class="watering-strain-list">
                                                <li>Höhere Wassermengen pro Gießvorgang möglich</li>
                                                <li>Längere Vegetationsphase erlaubt schrittweise Steigerung</li>
                                                <li>Kann in Blüte mehr Wasser vertragen</li>
                                                <li>Bessere Kontrolle über Wachstumsphasen</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="watering-strain-section">
                                            <h6 class="text-success">
                                                <i class="fa-solid fa-clock me-2"></i>Autoflower-Sorten
                                            </h6>
                                            <ul class="watering-strain-list">
                                                <li>Weniger Wasser pro Gießvorgang für optimales Wachstum</li>
                                                <li>Kürzere Lebensdauer erfordert effizientere Bewässerung</li>
                                                <li>Empfindlicher gegen Überwässerung</li>
                                                <li>Fokus auf konstante, moderate Bewässerung</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>Einflussfaktoren</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="watering-influence-list">
                                            <li><strong>Substratstruktur:</strong> Lockere Erde mit Perlite trocknet schneller, häufiger gießen nötig.</li>
                                            <li><strong>Temperatur/Luftfeuchtigkeit:</strong> Hohe Temperaturen und niedrige Luftfeuchte erhöhen den Wasserbedarf.</li>
                                            <li><strong>Topfform:</strong> Breite Töpfe trocknen schneller aus als hohe, schmale.</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="watering-influence-list">
                                            <li><strong>Lichtintensität:</strong> Mehr PPFD führt zu stärkerer Verdunstung über die Blätter.</li>
                                            <li><strong>Drainage & Bodenleben:</strong> Staunässe vermeiden – gut drainiertes Substrat ist essenziell für konstante Wasseraufnahme.</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="watering-sources">
                                    <h6><i class="fa-solid fa-link me-2"></i>Quellen</h6>
                                    <p class="text-muted small">
                                        Diese Empfehlungen basieren auf Erfahrungswerten und wissenschaftlichen Erkenntnissen von:
                                    </p>
                                    <ul class="watering-sources-list">
                                        <li>Royal Queen Seeds - Die ultimative Anleitung zum Gießen</li>
                                        <li>BioBizz Light Mix Produktinformationen</li>
                                        <li>GrowDiaries Community Erfahrungen</li>
                                        <li>Sensi Seeds Grow-Tipps</li>
                                    </ul>
                                    <p class="text-muted small mt-2">
                                        <i class="fa-solid fa-calendar me-1"></i>Stand: 12.07.2025 | 
                                        <i class="fa-solid fa-user me-1"></i>Erstellt von: Grow-Experte (GPT)
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Widget zerstören (Cleanup)
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Guidelines laden und cachen
WateringWidget.wateringGuidelines = null;
WateringWidget.wateringGuidelinesLoaded = false;
WateringWidget.loadWateringGuidelines = async function() {
    if (WateringWidget.wateringGuidelinesLoaded) return WateringWidget.wateringGuidelines;
    try {
        const response = await fetch('/static/data/watering-guidelines.json');
        if (response.ok) {
            const json = await response.json();
            WateringWidget.wateringGuidelines = json.wateringGuidelines;
            WateringWidget.wateringGuidelinesLoaded = true;
            return WateringWidget.wateringGuidelines;
        }
    } catch (e) {}
    return null;
};

// Automatische Initialisierung entfernt - wird jetzt über Widget Manager verwaltet

// Global verfügbar machen für Widget Manager
window.WateringWidget = WateringWidget;

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WateringWidget;
} 