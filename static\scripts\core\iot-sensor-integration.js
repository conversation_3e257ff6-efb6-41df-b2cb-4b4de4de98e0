/**
 * 🌐 IoT Sensor Integration System
 * 
 * Automatische Sensor-Netzwerk Integration
 * Remote-Monitoring und Daten-Synchronisation
 */

class IoTSensorIntegration {
    constructor() {
        this.sensors = new Map();
        this.sensorNetworks = new Map();
        this.dataStreams = new Map();
        this.remoteConnections = new Map();
        this.syncQueue = [];
        
        // IoT-Konfiguration
        this.iotConfig = {
            syncInterval: 30 * 1000, // 30 Sekunden
            retryAttempts: 3,
            connectionTimeout: 10000, // 10 Sekunden
            maxDataPoints: 1000,
            compressionEnabled: true
        };
        
        // Sensor-Typen
        this.sensorTypes = {
            'temperature': { unit: '°C', range: [-10, 50] },
            'humidity': { unit: '%', range: [0, 100] },
            'ppfd': { unit: 'μmol/m²/s', range: [0, 2000] },
            'co2': { unit: 'ppm', range: [300, 2000] },
            'ph': { unit: 'pH', range: [5.5, 8.5] },
            'ec': { unit: 'mS/cm', range: [0, 5] },
            'soil_moisture': { unit: '%', range: [0, 100] },
            'air_flow': { unit: 'm³/h', range: [0, 1000] }
        };
    }
    
    /**
     * Sensor registrieren
     */
    registerSensor(sensorId, sensorType, location, config = {}) {
        try {
            if (!this.sensorTypes[sensorType]) {
                throw new Error(`Unbekannter Sensor-Typ: ${sensorType}`);
            }
            
            const sensor = {
                id: sensorId,
                type: sensorType,
                location: location,
                config: {
                    ...this.sensorTypes[sensorType],
                    ...config
                },
                status: 'offline',
                lastReading: null,
                lastUpdate: null,
                dataPoints: [],
                calibration: {
                    offset: 0,
                    multiplier: 1,
                    lastCalibrated: null
                }
            };
            
            this.sensors.set(sensorId, sensor);
            
            // Daten-Stream erstellen
            this.createDataStream(sensorId);
            

            
            return sensor;
            
        } catch (error) {
            console.error('🌐 IoTSensorIntegration: Fehler beim Registrieren des Sensors:', error);
            return null;
        }
    }
    
    /**
     * Sensor-Netzwerk erstellen
     */
    createSensorNetwork(networkId, plantId, sensors = []) {
        try {
            const network = {
                id: networkId,
                plantId: plantId,
                sensors: new Set(sensors),
                status: 'active',
                lastSync: null,
                dataAggregation: {
                    temperature: { min: null, max: null, avg: null },
                    humidity: { min: null, max: null, avg: null },
                    ppfd: { min: null, max: null, avg: null },
                    co2: { min: null, max: null, avg: null }
                },
                alerts: []
            };
            
            this.sensorNetworks.set(networkId, network);
            

            
            return network;
            
        } catch (error) {
            console.error('🌐 IoTSensorIntegration: Fehler beim Erstellen des Sensor-Netzwerks:', error);
            return null;
        }
    }
    
    /**
     * Sensor zu Netzwerk hinzufügen
     */
    addSensorToNetwork(networkId, sensorId) {
        const network = this.sensorNetworks.get(networkId);
        if (!network) {
            console.warn(`🌐 IoTSensorIntegration: Netzwerk ${networkId} nicht gefunden`);
            return false;
        }
        
        const sensor = this.sensors.get(sensorId);
        if (!sensor) {
            console.warn(`🌐 IoTSensorIntegration: Sensor ${sensorId} nicht gefunden`);
            return false;
        }
        
        network.sensors.add(sensorId);

        
        return true;
    }
    
    /**
     * Daten-Stream erstellen
     */
    createDataStream(sensorId) {
        const stream = {
            sensorId: sensorId,
            dataPoints: [],
            isActive: true,
            lastUpdate: null,
            compression: {
                enabled: this.iotConfig.compressionEnabled,
                algorithm: 'delta_encoding',
                threshold: 0.1
            }
        };
        
        this.dataStreams.set(sensorId, stream);
        
        // Automatische Daten-Synchronisation starten
        this.startDataSync(sensorId);
        
        return stream;
    }
    
    /**
     * Sensor-Daten empfangen
     */
    async receiveSensorData(sensorId, data) {
        try {
            const sensor = this.sensors.get(sensorId);
            if (!sensor) {
                console.warn(`🌐 IoTSensorIntegration: Sensor ${sensorId} nicht gefunden`);
                return false;
            }
            
            // Daten validieren
            const validatedData = this.validateSensorData(sensor, data);
            if (!validatedData) {
                console.warn(`🌐 IoTSensorIntegration: Ungültige Daten für Sensor ${sensorId}`);
                return false;
            }
            
            // Kalibrierung anwenden
            const calibratedData = this.applyCalibration(sensor, validatedData);
            
            // Sensor-Status aktualisieren
            sensor.status = 'online';
            sensor.lastReading = calibratedData;
            sensor.lastUpdate = new Date().toISOString();
            
            // Daten-Stream aktualisieren
            const stream = this.dataStreams.get(sensorId);
            if (stream) {
                stream.dataPoints.push({
                    ...calibratedData,
                    timestamp: new Date().toISOString()
                });
                
                // Daten-Stream begrenzen
                if (stream.dataPoints.length > this.iotConfig.maxDataPoints) {
                    stream.dataPoints.splice(0, stream.dataPoints.length - this.iotConfig.maxDataPoints);
                }
                
                stream.lastUpdate = new Date().toISOString();
            }
            
            // Netzwerk-Daten aggregieren
            this.updateNetworkData(sensorId, calibratedData);
            
            // Alerts prüfen
            this.checkAlerts(sensorId, calibratedData);
            
            console.log(`🌐 IoTSensorIntegration: Daten von Sensor ${sensorId} empfangen:`, calibratedData.value, calibratedData.unit);
            
            return true;
            
        } catch (error) {
            console.error('🌐 IoTSensorIntegration: Fehler beim Empfangen der Sensor-Daten:', error);
            return false;
        }
    }
    
    /**
     * Sensor-Daten validieren
     */
    validateSensorData(sensor, data) {
        if (!data || typeof data.value !== 'number') {
            return null;
        }
        
        const { range } = sensor.config;
        if (data.value < range[0] || data.value > range[1]) {
            console.warn(`🌐 IoTSensorIntegration: Sensor ${sensor.id} Wert außerhalb des Bereichs: ${data.value} ${sensor.config.unit}`);
            return null;
        }
        
        return {
            value: data.value,
            unit: sensor.config.unit,
            quality: data.quality || 1.0,
            timestamp: data.timestamp || new Date().toISOString()
        };
    }
    
    /**
     * Kalibrierung anwenden
     */
    applyCalibration(sensor, data) {
        const { offset, multiplier } = sensor.calibration;
        
        return {
            ...data,
            value: (data.value + offset) * multiplier,
            rawValue: data.value,
            calibration: {
                offset: offset,
                multiplier: multiplier
            }
        };
    }
    
    /**
     * Netzwerk-Daten aktualisieren
     */
    updateNetworkData(sensorId, data) {
        for (const [networkId, network] of this.sensorNetworks) {
            if (network.sensors.has(sensorId)) {
                const sensor = this.sensors.get(sensorId);
                if (sensor && network.dataAggregation[sensor.type]) {
                    const aggregation = network.dataAggregation[sensor.type];
                    
                    if (aggregation.min === null || data.value < aggregation.min) {
                        aggregation.min = data.value;
                    }
                    
                    if (aggregation.max === null || data.value > aggregation.max) {
                        aggregation.max = data.value;
                    }
                    
                    // Durchschnitt berechnen
                    const networkSensors = Array.from(network.sensors)
                        .map(id => this.sensors.get(id))
                        .filter(s => s && s.type === sensor.type && s.lastReading);
                    
                    if (networkSensors.length > 0) {
                        const sum = networkSensors.reduce((total, s) => total + s.lastReading.value, 0);
                        aggregation.avg = sum / networkSensors.length;
                    }
                }
                
                network.lastSync = new Date().toISOString();
            }
        }
    }
    
    /**
     * Alerts prüfen
     */
    checkAlerts(sensorId, data) {
        const sensor = this.sensors.get(sensorId);
        if (!sensor) return;
        
        const { range } = sensor.config;
        const alerts = [];
        
        // Grenzwert-Alerts
        if (data.value < range[0] * 1.1) {
            alerts.push({
                type: 'low_value',
                severity: 'warning',
                message: `${sensor.type} zu niedrig: ${data.value} ${data.unit}`,
                sensorId: sensorId,
                timestamp: new Date().toISOString()
            });
        }
        
        if (data.value > range[1] * 0.9) {
            alerts.push({
                type: 'high_value',
                severity: 'warning',
                message: `${sensor.type} zu hoch: ${data.value} ${data.unit}`,
                sensorId: sensorId,
                timestamp: new Date().toISOString()
            });
        }
        
        // Qualitäts-Alerts
        if (data.quality < 0.8) {
            alerts.push({
                type: 'low_quality',
                severity: 'info',
                message: `Niedrige Datenqualität: ${(data.quality * 100).toFixed(0)}%`,
                sensorId: sensorId,
                timestamp: new Date().toISOString()
            });
        }
        
        // Alerts zu Netzwerken hinzufügen
        for (const [networkId, network] of this.sensorNetworks) {
            if (network.sensors.has(sensorId)) {
                network.alerts.push(...alerts);
                
                // Alerts begrenzen
                if (network.alerts.length > 100) {
                    network.alerts.splice(0, network.alerts.length - 100);
                }
            }
        }
    }
    
    /**
     * Daten-Synchronisation starten
     */
    startDataSync(sensorId) {
        // Automatische Synchronisation deaktiviert - keine Logs
    }
    
    /**
     * Update-Hinweis anzeigen
     */
    showUpdateNotice(message) {
        // Update-Hinweis deaktiviert - keine Logs
    }
    
    /**
     * Sensor-Daten synchronisieren
     */
    async syncSensorData(sensorId) {
        try {
            const stream = this.dataStreams.get(sensorId);
            if (!stream || stream.dataPoints.length === 0) {
                return false;
            }
            
            const sensor = this.sensors.get(sensorId);
            if (!sensor) {
                return false;
            }
            
            // Daten komprimieren
            const compressedData = this.compressData(stream.dataPoints);
            
            // Remote-Synchronisation
            const syncResult = await this.syncToRemote(sensorId, compressedData);
            
            if (syncResult) {
                // Erfolgreich synchronisiert - Daten löschen
                stream.dataPoints = [];
                console.log(`🌐 IoTSensorIntegration: Sensor ${sensorId} erfolgreich synchronisiert`);
            }
            
            return syncResult;
            
        } catch (error) {
            console.error('🌐 IoTSensorIntegration: Fehler bei Daten-Synchronisation:', error);
            return false;
        }
    }
    
    /**
     * Daten komprimieren
     */
    compressData(dataPoints) {
        if (!this.iotConfig.compressionEnabled || dataPoints.length < 10) {
            return dataPoints;
        }
        
        const compressed = [];
        const threshold = 0.1;
        
        for (let i = 0; i < dataPoints.length; i++) {
            if (i === 0 || i === dataPoints.length - 1) {
                // Ersten und letzten Punkt immer behalten
                compressed.push(dataPoints[i]);
            } else {
                // Delta-Encoding für Zwischenpunkte
                const prev = dataPoints[i - 1];
                const current = dataPoints[i];
                const next = dataPoints[i + 1];
                
                const delta = Math.abs(current.value - prev.value);
                const nextDelta = Math.abs(next.value - current.value);
                
                // Nur signifikante Änderungen behalten
                if (delta > threshold || nextDelta > threshold) {
                    compressed.push(current);
                }
            }
        }
        
        return compressed;
    }
    
    /**
     * Remote-Synchronisation
     */
    async syncToRemote(sensorId, data) {
        try {
            // Simulierte Remote-Synchronisation
            const syncData = {
                sensorId: sensorId,
                data: data,
                timestamp: new Date().toISOString(),
                checksum: this.calculateChecksum(data)
            };
            
            // Remote-Verbindung simulieren
            const connection = await this.getRemoteConnection(sensorId);
            if (!connection) {
                console.warn(`🌐 IoTSensorIntegration: Keine Remote-Verbindung für Sensor ${sensorId}`);
                return false;
            }
            
            // Daten senden
            const response = await this.sendToRemote(connection, syncData);
            
            return response.success;
            
        } catch (error) {
            console.error('🌐 IoTSensorIntegration: Fehler bei Remote-Synchronisation:', error);
            return false;
        }
    }
    
    /**
     * Remote-Verbindung herstellen
     */
    async getRemoteConnection(sensorId) {
        // Simulierte Remote-Verbindung
        const connection = {
            id: `remote_${sensorId}`,
            url: 'https://api.grow-tagebuch.com/iot/sync',
            status: 'connected',
            lastPing: new Date().toISOString()
        };
        
        this.remoteConnections.set(sensorId, connection);
        
        return connection;
    }
    
    /**
     * Daten an Remote senden
     */
    async sendToRemote(connection, data) {
        // Simulierte Remote-Kommunikation
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: Math.random() > 0.1, // 90% Erfolgsrate
                    timestamp: new Date().toISOString(),
                    message: 'Daten erfolgreich synchronisiert'
                });
            }, Math.random() * 1000 + 500); // 500-1500ms
        });
    }
    
    /**
     * Checksum berechnen
     */
    calculateChecksum(data) {
        const dataString = JSON.stringify(data);
        let checksum = 0;
        
        for (let i = 0; i < dataString.length; i++) {
            checksum += dataString.charCodeAt(i);
        }
        
        return checksum.toString(16);
    }
    
    /**
     * Sensor kalibrieren
     */
    calibrateSensor(sensorId, referenceValue, measuredValue) {
        const sensor = this.sensors.get(sensorId);
        if (!sensor) {
            console.warn(`🌐 IoTSensorIntegration: Sensor ${sensorId} nicht gefunden`);
            return false;
        }
        
        // Kalibrierung berechnen
        const offset = referenceValue - measuredValue;
        const multiplier = referenceValue / measuredValue;
        
        sensor.calibration.offset = offset;
        sensor.calibration.multiplier = multiplier;
        sensor.calibration.lastCalibrated = new Date().toISOString();
        
        console.log(`🌐 IoTSensorIntegration: Sensor ${sensorId} kalibriert (Offset: ${offset.toFixed(3)}, Multiplier: ${multiplier.toFixed(3)})`);
        
        return true;
    }
    
    /**
     * Sensor-Status abrufen
     */
    getSensorStatus(sensorId) {
        const sensor = this.sensors.get(sensorId);
        if (!sensor) {
            return { status: 'not_found' };
        }
        
        const stream = this.dataStreams.get(sensorId);
        
        return {
            id: sensor.id,
            type: sensor.type,
            status: sensor.status,
            location: sensor.location,
            lastReading: sensor.lastReading,
            lastUpdate: sensor.lastUpdate,
            dataPoints: stream ? stream.dataPoints.length : 0,
            calibration: sensor.calibration
        };
    }
    
    /**
     * Netzwerk-Status abrufen
     */
    getNetworkStatus(networkId) {
        const network = this.sensorNetworks.get(networkId);
        if (!network) {
            return { status: 'not_found' };
        }
        
        const sensors = Array.from(network.sensors).map(id => this.getSensorStatus(id));
        const onlineSensors = sensors.filter(s => s.status === 'online');
        
        return {
            id: network.id,
            plantId: network.plantId,
            status: network.status,
            sensorCount: network.sensors.size,
            onlineSensors: onlineSensors.length,
            lastSync: network.lastSync,
            dataAggregation: network.dataAggregation,
            alerts: network.alerts.slice(-10) // Letzte 10 Alerts
        };
    }
    
    /**
     * Alle Sensoren abrufen
     */
    getAllSensors() {
        return Array.from(this.sensors.values()).map(sensor => ({
            id: sensor.id,
            type: sensor.type,
            status: sensor.status,
            location: sensor.location,
            lastUpdate: sensor.lastUpdate
        }));
    }
    
    /**
     * Alle Netzwerke abrufen
     */
    getAllNetworks() {
        return Array.from(this.sensorNetworks.values()).map(network => ({
            id: network.id,
            plantId: network.plantId,
            status: network.status,
            sensorCount: network.sensors.size,
            lastSync: network.lastSync
        }));
    }
    
    /**
     * Sensor entfernen
     */
    removeSensor(sensorId) {
        const sensor = this.sensors.get(sensorId);
        if (!sensor) {
            return false;
        }
        
        // Aus allen Netzwerken entfernen
        for (const [networkId, network] of this.sensorNetworks) {
            network.sensors.delete(sensorId);
        }
        
        // Daten-Stream stoppen
        const stream = this.dataStreams.get(sensorId);
        if (stream) {
            stream.isActive = false;
        }
        
        // Sync-Intervall stoppen
        const syncItem = this.syncQueue.find(item => item.sensorId === sensorId);
        if (syncItem) {
            clearInterval(syncItem.interval);
            this.syncQueue = this.syncQueue.filter(item => item.sensorId !== sensorId);
        }
        
        // Sensor löschen
        this.sensors.delete(sensorId);
        this.dataStreams.delete(sensorId);
        this.remoteConnections.delete(sensorId);
        
        console.log(`🌐 IoTSensorIntegration: Sensor ${sensorId} entfernt`);
        
        return true;
    }
    
    /**
     * Netzwerk entfernen
     */
    removeNetwork(networkId) {
        const network = this.sensorNetworks.get(networkId);
        if (!network) {
            return false;
        }
        
        this.sensorNetworks.delete(networkId);
        
        console.log(`🌐 IoTSensorIntegration: Netzwerk ${networkId} entfernt`);
        
        return true;
    }
    
    /**
     * Debug-Informationen ausgeben
     */
    debug() {
        console.log('🌐 IoTSensorIntegration Debug Info:');
        console.log('Sensors:', this.sensors.size);
        console.log('Networks:', this.sensorNetworks.size);
        console.log('Data Streams:', this.dataStreams.size);
        console.log('Remote Connections:', this.remoteConnections.size);
        console.log('Sync Queue:', this.syncQueue.length);
        
        // Sensor-Status
        for (const [sensorId, sensor] of this.sensors) {
            console.log(`Sensor ${sensorId}: ${sensor.status}, Last Update: ${sensor.lastUpdate}`);
        }
        
        // Netzwerk-Status
        for (const [networkId, network] of this.sensorNetworks) {
            console.log(`Network ${networkId}: ${network.sensors.size} sensors, Last Sync: ${network.lastSync}`);
        }
    }
}

// Globale Instanz erstellen
window.iotSensorIntegration = new IoTSensorIntegration();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IoTSensorIntegration;
} 