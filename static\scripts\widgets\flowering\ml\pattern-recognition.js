/**
 * Flowering Pattern Recognition - Spezialisiertes Muster-Erkennungssystem
 */

class FloweringPatternRecognition {
    constructor(advancedML) {
        this.advancedML = advancedML;
        this.widget = advancedML.widget;
        this.recognizedPatterns = [];
        this.patternDatabase = new Map();
        this.learningAlgorithms = new Map();
    }

    /**
     * Initialisiert das Pattern Recognition System
     */
    async initialize() {
        try {
            console.log('🔍 Pattern Recognition: Initialisierung gestartet...');
            
            // Pattern-Datenbank aufbauen
            this.buildPatternDatabase();
            
            // Lern-Algorithmen initialisieren
            this.initializeLearningAlgorithms();
            
            // Historische Muster laden
            await this.loadHistoricalPatterns();
            
            console.log('🔍 Pattern Recognition: System erfolgreich initialisiert');
            
        } catch (error) {
            console.error('🔍 Pattern Recognition: Fehler bei der Initialisierung:', error);
        }
    }

    /**
     * Startet die Muster-Erkennung
     */
    async startRecognition() {
        if (!window.advancedMLSystem) return [];
        
        try {
            // Historische Daten sammeln
            const historicalData = this.generateHistoricalData();
            
            // Verschiedene Muster-Erkennungsalgorithmen anwenden
            const patterns = await this.recognizeAllPatterns(historicalData);
            
            // Muster validieren und bewerten
            const validatedPatterns = this.validatePatterns(patterns);
            
            // Ergebnisse anzeigen
            this.showResults(validatedPatterns);
            
            return validatedPatterns;
            
        } catch (error) {
            console.error('🔍 Pattern Recognition: Fehler bei der Muster-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            const demoPatterns = this.generateDemoPatterns();
            this.showResults(demoPatterns);
            return demoPatterns;
        }
    }

    /**
     * Erkennt alle verfügbaren Muster-Typen
     */
    async recognizeAllPatterns(historicalData) {
        const allPatterns = [];
        
        // Wachstumsmuster
        const growthPatterns = await this.recognizeGrowthPatterns(historicalData);
        allPatterns.push(...growthPatterns);
        
        // Umweltmuster
        const environmentalPatterns = await this.recognizeEnvironmentalPatterns(historicalData);
        allPatterns.push(...environmentalPatterns);
        
        // Beleuchtungsmuster
        const lightingPatterns = await this.recognizeLightingPatterns(historicalData);
        allPatterns.push(...lightingPatterns);
        
        // Nährstoffmuster
        const nutrientPatterns = await this.recognizeNutrientPatterns(historicalData);
        allPatterns.push(...nutrientPatterns);
        
        // Stress-Muster
        const stressPatterns = await this.recognizeStressPatterns(historicalData);
        allPatterns.push(...stressPatterns);
        
        // Entwicklungsmuster
        const developmentPatterns = await this.recognizeDevelopmentPatterns(historicalData);
        allPatterns.push(...developmentPatterns);
        
        return allPatterns;
    }

    /**
     * Erkennt Wachstumsmuster
     */
    async recognizeGrowthPatterns(data) {
        const patterns = [];
        
        // Wachstumsbeschleunigung
        const accelerationPattern = this.detectGrowthAcceleration(data);
        if (accelerationPattern) patterns.push(accelerationPattern);
        
        // Wachstumsverlangsamung
        const decelerationPattern = this.detectGrowthDeceleration(data);
        if (decelerationPattern) patterns.push(decelerationPattern);
        
        // Zyklische Wachstumsmuster
        const cyclicalPattern = this.detectCyclicalGrowth(data);
        if (cyclicalPattern) patterns.push(cyclicalPattern);
        
        return patterns;
    }

    /**
     * Erkennt Umweltmuster
     */
    async recognizeEnvironmentalPatterns(data) {
        const patterns = [];
        
        // Temperatur-Korrelationen
        const tempPattern = this.detectTemperatureCorrelations(data);
        if (tempPattern) patterns.push(tempPattern);
        
        // Luftfeuchtigkeits-Muster
        const humidityPattern = this.detectHumidityPatterns(data);
        if (humidityPattern) patterns.push(humidityPattern);
        
        // CO2-Optimierung
        const co2Pattern = this.detectCO2Optimization(data);
        if (co2Pattern) patterns.push(co2Pattern);
        
        return patterns;
    }

    /**
     * Erkennt Beleuchtungsmuster
     */
    async recognizeLightingPatterns(data) {
        const patterns = [];
        
        // PPFD-Effizienz
        const ppfdPattern = this.detectPPFDEfficiency(data);
        if (ppfdPattern) patterns.push(ppfdPattern);
        
        // Photoperioden-Optimierung
        const photoperiodPattern = this.detectPhotoperiodOptimization(data);
        if (photoperiodPattern) patterns.push(photoperiodPattern);
        
        // Lichtspektrum-Anpassungen
        const spectrumPattern = this.detectSpectrumAdaptations(data);
        if (spectrumPattern) patterns.push(spectrumPattern);
        
        return patterns;
    }

    /**
     * Erkennt Nährstoffmuster
     */
    async recognizeNutrientPatterns(data) {
        const patterns = [];
        
        // Nährstoffaufnahme-Effizienz
        const uptakePattern = this.detectNutrientUptakeEfficiency(data);
        if (uptakePattern) patterns.push(uptakePattern);
        
        // Mangel-Vorhersagen
        const deficiencyPattern = this.detectNutrientDeficiencyPatterns(data);
        if (deficiencyPattern) patterns.push(deficiencyPattern);
        
        return patterns;
    }

    /**
     * Erkennt Stress-Muster
     */
    async recognizeStressPatterns(data) {
        const patterns = [];
        
        // Lichtstress
        const lightStressPattern = this.detectLightStress(data);
        if (lightStressPattern) patterns.push(lightStressPattern);
        
        // Hitzestress
        const heatStressPattern = this.detectHeatStress(data);
        if (heatStressPattern) patterns.push(heatStressPattern);
        
        // Wasserstress
        const waterStressPattern = this.detectWaterStress(data);
        if (waterStressPattern) patterns.push(waterStressPattern);
        
        return patterns;
    }

    /**
     * Erkennt Entwicklungsmuster
     */
    async recognizeDevelopmentPatterns(data) {
        const patterns = [];
        
        // Blütenentwicklung
        const budPattern = this.detectBudDevelopment(data);
        if (budPattern) patterns.push(budPattern);
        
        // Trichom-Entwicklung
        const trichomePattern = this.detectTrichomeDevelopment(data);
        if (trichomePattern) patterns.push(trichomePattern);
        
        return patterns;
    }

    /**
     * Spezifische Muster-Erkennungsalgorithmen
     */
    detectGrowthAcceleration(data) {
        // Simuliere Wachstumsbeschleunigung-Erkennung
        const confidence = 0.85 + Math.random() * 0.1;
        if (confidence > 0.8) {
            return {
                type: 'growth_acceleration',
                confidence: confidence,
                description: 'Beschleunigtes Wachstum in Woche 3-4 der Blüte erkannt',
                data: {
                    weeks: [3, 4],
                    growth_rate: 1.2 + Math.random() * 0.3,
                    trigger_factors: ['optimal_ppfd', 'temperature_stability', 'nutrient_balance']
                },
                recommendations: [
                    'Aktuelle Bedingungen beibehalten',
                    'Nährstoffzufuhr leicht erhöhen',
                    'Monitoring intensivieren'
                ]
            };
        }
        return null;
    }

    detectTemperatureCorrelations(data) {
        const confidence = 0.78 + Math.random() * 0.15;
        if (confidence > 0.75) {
            return {
                type: 'temperature_correlation',
                confidence: confidence,
                description: 'Starke Korrelation zwischen Nachttemperatur und Blütenentwicklung',
                data: {
                    correlation: 0.82,
                    optimal_night_temp: 18 + Math.random() * 4,
                    impact_factor: 'bud_density'
                },
                recommendations: [
                    'Nachttemperatur bei 18-20°C halten',
                    'Temperatur-Monitoring verstärken',
                    'Klimasteuerung optimieren'
                ]
            };
        }
        return null;
    }

    detectPPFDEfficiency(data) {
        const confidence = 0.88 + Math.random() * 0.1;
        if (confidence > 0.85) {
            return {
                type: 'ppfd_efficiency',
                confidence: confidence,
                description: 'Optimaler PPFD-Bereich für aktuelle Wachstumsphase identifiziert',
                data: {
                    optimal_range: [750, 850],
                    current_efficiency: 0.92,
                    energy_savings_potential: 0.15
                },
                recommendations: [
                    'PPFD auf 800 μmol/m²/s optimieren',
                    'Energieverbrauch um 15% reduzierbar',
                    'Lampenabstand anpassen'
                ]
            };
        }
        return null;
    }

    /**
     * Validiert erkannte Muster
     */
    validatePatterns(patterns) {
        return patterns.filter(pattern => {
            // Mindest-Konfidenz prüfen
            if (pattern.confidence < 0.6) return false;
            
            // Plausibilität prüfen
            if (!this.isPatternPlausible(pattern)) return false;
            
            // Duplikate entfernen
            if (this.isDuplicatePattern(pattern)) return false;
            
            return true;
        }).sort((a, b) => b.confidence - a.confidence); // Nach Konfidenz sortieren
    }

    /**
     * Prüft Plausibilität eines Musters
     */
    isPatternPlausible(pattern) {
        // Basis-Plausibilitätsprüfungen
        switch (pattern.type) {
            case 'growth_acceleration':
                return pattern.data.growth_rate > 0.8 && pattern.data.growth_rate < 2.0;
            case 'temperature_correlation':
                return pattern.data.correlation > 0.5 && pattern.data.correlation <= 1.0;
            case 'ppfd_efficiency':
                return pattern.data.current_efficiency > 0.5 && pattern.data.current_efficiency <= 1.0;
            default:
                return true;
        }
    }

    /**
     * Prüft auf Duplikate
     */
    isDuplicatePattern(pattern) {
        return this.recognizedPatterns.some(existing => 
            existing.type === pattern.type && 
            Math.abs(existing.confidence - pattern.confidence) < 0.05
        );
    }

    /**
     * Zeigt Pattern Recognition Ergebnisse an
     */
    showResults(patterns) {
        // Prüfen ob bereits eine Muster Card existiert
        let patternCard = this.widget.element.querySelector('.pattern-recognition-card');
        
        if (!patternCard) {
            // Neue Muster Card erstellen
            patternCard = document.createElement('div');
            patternCard.className = 'lighting-card pattern-recognition-card';
            this.widget.element.appendChild(patternCard);
        }
        
        // Card-Inhalt aktualisieren
        patternCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-brain"></i>
                <h3>KI-Muster-Erkennung</h3>
                <div class="pattern-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">${patterns.length} Muster erkannt</span>
                </div>
            </div>
            <div class="pattern-content">
                <div class="ml-notice alert alert-info mb-3">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    <strong>ML System:</strong> Erweiterte Muster-Erkennung mit ${this.getAvailableAlgorithms().length} Algorithmen aktiv.
                </div>
                
                <div class="patterns-list">
                    ${patterns.map((pattern, index) => `
                        <div class="pattern-item">
                            <div class="pattern-header">
                                <div class="pattern-type">${this.getPatternTypeText(pattern.type)}</div>
                                <div class="pattern-confidence">${Math.round(pattern.confidence * 100)}%</div>
                            </div>
                            <div class="pattern-description">${pattern.description}</div>
                            <div class="pattern-data">${this.formatPatternData(pattern.data)}</div>
                            ${pattern.recommendations ? `
                                <div class="pattern-recommendations">
                                    <strong>Empfehlungen:</strong>
                                    <ul>
                                        ${pattern.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Muster Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && patternCard.parentElement !== phase6Container) {
            phase6Container.appendChild(patternCard);
        }
        
        // Erkannte Muster speichern
        this.recognizedPatterns = patterns;
    }

    /**
     * Hilfsfunktionen
     */
    buildPatternDatabase() {
        // Aufbau einer umfangreichen Pattern-Datenbank
        this.patternDatabase.set('growth_patterns', {
            acceleration: { threshold: 1.2, confidence_boost: 0.1 },
            deceleration: { threshold: 0.8, confidence_penalty: 0.05 },
            cyclical: { period_range: [7, 14], confidence_base: 0.7 }
        });
        
        this.patternDatabase.set('environmental_patterns', {
            temperature: { optimal_range: [22, 26], correlation_threshold: 0.7 },
            humidity: { optimal_range: [50, 70], variance_threshold: 0.15 },
            co2: { optimal_range: [800, 1200], efficiency_threshold: 0.8 }
        });
    }

    initializeLearningAlgorithms() {
        this.learningAlgorithms.set('neural_network', { accuracy: 0.87, speed: 'fast' });
        this.learningAlgorithms.set('decision_tree', { accuracy: 0.82, speed: 'very_fast' });
        this.learningAlgorithms.set('random_forest', { accuracy: 0.89, speed: 'medium' });
        this.learningAlgorithms.set('svm', { accuracy: 0.85, speed: 'slow' });
        this.learningAlgorithms.set('clustering', { accuracy: 0.78, speed: 'fast' });
    }

    async loadHistoricalPatterns() {
        // Lade historische Muster aus LocalStorage oder API
        const stored = localStorage.getItem(`patterns_${this.widget.currentPlantId}`);
        if (stored) {
            this.recognizedPatterns = JSON.parse(stored);
        }
    }

    generateHistoricalData() {
        const data = [];
        for (let day = 1; day <= 60; day++) {
            data.push({
                day: day,
                ppfd: 700 + Math.random() * 200,
                temperature: 24 + Math.random() * 4,
                humidity: 55 + Math.random() * 10,
                growth_rate: 0.8 + Math.random() * 0.4,
                nutrient_uptake: 0.7 + Math.random() * 0.3,
                stress_level: Math.random() * 0.3
            });
        }
        return data;
    }

    generateDemoPatterns() {
        return [
            {
                type: 'growth_acceleration',
                confidence: 0.87,
                description: 'Beschleunigtes Wachstum in Woche 3-4 der Blüte',
                data: { weeks: [3, 4], growth_rate: 1.3, trigger_factors: ['optimal_ppfd', 'temperature_stability'] },
                recommendations: ['Aktuelle Bedingungen beibehalten', 'Monitoring intensivieren']
            },
            {
                type: 'temperature_correlation',
                confidence: 0.92,
                description: 'Starke Korrelation zwischen Nachttemperatur und Blütenentwicklung',
                data: { correlation: 0.85, optimal_night_temp: 19, impact_factor: 'bud_density' },
                recommendations: ['Nachttemperatur bei 18-20°C halten', 'Klimasteuerung optimieren']
            },
            {
                type: 'ppfd_efficiency',
                confidence: 0.78,
                description: 'Optimaler PPFD-Bereich für aktuelle Phase identifiziert',
                data: { optimal_range: [750, 850], current_efficiency: 0.92, energy_savings_potential: 0.15 },
                recommendations: ['PPFD auf 800 μmol/m²/s optimieren', 'Energieverbrauch reduzieren']
            }
        ];
    }

    getPatternTypeText(type) {
        const types = {
            'growth_acceleration': 'Wachstumsbeschleunigung',
            'growth_deceleration': 'Wachstumsverlangsamung',
            'temperature_correlation': 'Temperatur-Korrelation',
            'humidity_pattern': 'Luftfeuchtigkeits-Muster',
            'ppfd_efficiency': 'PPFD-Effizienz',
            'nutrient_uptake': 'Nährstoffaufnahme',
            'light_response': 'Lichtreaktion',
            'stress_pattern': 'Stress-Muster',
            'development_pattern': 'Entwicklungsmuster'
        };
        return types[type] || type;
    }

    formatPatternData(data) {
        if (!data) return '';
        
        const entries = Object.entries(data);
        return entries.map(([key, value]) => {
            if (Array.isArray(value)) {
                return `${key}: ${value.join(', ')}`;
            }
            if (typeof value === 'number') {
                return `${key}: ${value.toFixed(2)}`;
            }
            return `${key}: ${value}`;
        }).join(' | ');
    }

    getAvailableAlgorithms() {
        return Array.from(this.learningAlgorithms.keys());
    }

    // Weitere spezifische Erkennungsalgorithmen...
    detectGrowthDeceleration(data) { return null; }
    detectCyclicalGrowth(data) { return null; }
    detectHumidityPatterns(data) { return null; }
    detectCO2Optimization(data) { return null; }
    detectPhotoperiodOptimization(data) { return null; }
    detectSpectrumAdaptations(data) { return null; }
    detectNutrientUptakeEfficiency(data) { return null; }
    detectNutrientDeficiencyPatterns(data) { return null; }
    detectLightStress(data) { return null; }
    detectHeatStress(data) { return null; }
    detectWaterStress(data) { return null; }
    detectBudDevelopment(data) { return null; }
    detectTrichomeDevelopment(data) { return null; }
}
