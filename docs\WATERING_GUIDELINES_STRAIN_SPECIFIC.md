# Sortentyp-spezifische Bewässerungsrichtlinien

## Übersicht

Das Grow-Tagebuch unterstützt jetzt sortentyp-spezifische Bewässerungsrichtlinien, die zwischen Autoflower- und Photoperiod-Sorten unterscheiden. Diese Funktion basiert auf wissenschaftlich fundierten Guidelines und bietet präzise Empfehlungen für optimale Bewässerung.

## Implementierung

### 1. Backend-Logic (`phase_logic/widgets/watering_logic.py`)

#### Erweiterte Methoden:

- **`load_guidelines_from_json(phase)`**: Lädt die neuen JSON-Guidelines für eine spezifische Phase
- **`calculate_watering_with_guidelines()`**: Berechnet Bewässerung mit strain-spezifischen Faktoren

#### Strain-Type-Erkennung:

```python
# Strain-Type aus Datenbank laden
strain_key = 'photoperiod' if strain_type == 'photoperiodic' else 'autoflower'

# Strain-spezifische Faktoren aus JSON
watering_factors = guidelines.get('wateringFactors', {})
if isinstance(watering_factors, dict) and strain_key in watering_factors:
    strain_factors = watering_factors[strain_key]
    min_factor = strain_factors.get('min', 0)
    max_factor = strain_factors.get('max', 0)
```

### 2. API-Routes (`routes/widgets/watering_routes.py`)

#### Automatische Strain-Type-Erkennung:

```python
# Strain-Type aus Datenbank laden falls nicht übergeben
if strain_type == 'photoperiodic' and plant_id:
    cursor.execute("SELECT strain_type, strain FROM plants WHERE id = ?", (plant_id,))
    row = cursor.fetchone()
    if row:
        # Prüfe auf Autoflower-Keywords
        auto_keywords = ['auto', 'autoflower', 'automatic', 'ruderalis']
        if any(keyword in strain_text.lower() for keyword in auto_keywords):
            strain_type = 'autoflowering'
```

### 3. Frontend-Widget (`static/scripts/widgets/watering-widget.js`)

#### Strain-spezifische Anzeige:

```javascript
// Strain-Type aus DOM oder Widget holen
let strainType = 'photoperiodic';
const strainTypeElement = document.querySelector('[data-plant-strain-type]');
if (strainTypeElement) {
    strainType = strainTypeElement.getAttribute('data-plant-strain-type');
}

const isAutoflower = strainType.toLowerCase().includes('auto');
const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
```

#### Visuelle Kennzeichnung:

- **Badge**: "Sortentyp-spezifisch" für strain-spezifische Empfehlungen
- **Empfehlungstyp**: Farbkodierte Anzeige (grün für strain-spezifisch, gelb für allgemein)
- **Strain-spezifische Hinweise**: Erklärende Texte für Autoflower vs. Photoperiod

## JSON-Struktur

### Bewässerungsrichtlinien (`static/data/watering-guidelines.json`)

```json
{
  "wateringGuidelines": {
    "phases": [
      {
        "phase": "vegetative_early",
        "description": "Frühe Wachstumsphase - Fokus auf Wurzelaufbau",
        "wateringFactors": {
          "photoperiod": {
            "min": 0.15,
            "max": 0.25
          },
          "autoflower": {
            "min": 0.12,
            "max": 0.20
          }
        },
        "notes": [
          "Substrat nicht zu nass halten",
          "Langsam bewässern"
        ]
      }
    ]
  }
}
```

## Strain-spezifische Unterschiede

### Autoflower-Sorten

- **Wassermengen**: 20-30% weniger pro Gießvorgang
- **Frequenz**: Häufiger, aber kleinere Mengen
- **Empfindlichkeit**: Höhere Empfindlichkeit gegen Überwässerung
- **Lebensdauer**: Kürzere Vegetationsphase erfordert effizientere Bewässerung

### Photoperiodische Sorten

- **Wassermengen**: Höhere Mengen pro Gießvorgang möglich
- **Frequenz**: Weniger häufig, aber größere Mengen
- **Kontrolle**: Bessere Kontrolle über Wachstumsphasen
- **Anpassung**: Längere Vegetationsphase erlaubt schrittweise Steigerung

## Frontend-Features

### 1. Strain-spezifische Anzeige

- **Badge-System**: Visuelle Kennzeichnung des Empfehlungstyps
- **Tooltips**: Erklärende Hinweise für jeden Sortentyp
- **Farbkodierung**: Grün für strain-spezifisch, Gelb für allgemein

### 2. Guidelines-Modal

- **Strain-spezifische Sektion**: Separate Bereiche für Autoflower und Photoperiod
- **Vergleichende Darstellung**: Gegenüberstellung der Unterschiede
- **Praktische Tipps**: Anwendbare Empfehlungen für jeden Sortentyp

### 3. Automatische Erkennung

- **DOM-Parsing**: Automatische Erkennung aus Pflanzendaten
- **Keyword-Matching**: Erkennung von Autoflower-Keywords
- **Fallback-System**: Allgemeine Empfehlungen als Fallback

## Technische Details

### Datenfluss

1. **Frontend**: Widget lädt Pflanzendaten und Strain-Type
2. **API-Call**: Plant ID wird an Backend gesendet
3. **Backend**: Strain-Type wird aus Datenbank geladen/validiert
4. **JSON-Loading**: Strain-spezifische Faktoren werden geladen
5. **Berechnung**: Bewässerung wird mit strain-spezifischen Faktoren berechnet
6. **Response**: Strain-spezifische Daten werden an Frontend gesendet
7. **Rendering**: Frontend zeigt strain-spezifische Anzeige

### Fallback-Mechanismus

- **Strain-Type unbekannt**: Verwendet allgemeine Empfehlungen
- **JSON nicht verfügbar**: Fallback auf ursprüngliche Berechnungslogik
- **Datenbank-Fehler**: Verwendet Standard-Werte

## Nutzer-Nutzen

### Für Autoflower-Grower

- **Präzise Empfehlungen**: Angepasst an kürzere Lebensdauer
- **Überwässerung-Schutz**: Reduzierte Wassermengen verhindern Probleme
- **Effiziente Bewässerung**: Optimiert für schnelles Wachstum

### Für Photoperiod-Grower

- **Flexibilität**: Höhere Wassermengen für bessere Kontrolle
- **Phasen-Anpassung**: Schrittweise Steigerung möglich
- **Maximale Erträge**: Optimiert für längere Wachstumsphasen

### Allgemein

- **Wissenschaftliche Basis**: Fundiert auf bewährten Praktiken
- **Benutzerfreundlich**: Klare visuelle Kennzeichnung
- **Automatisch**: Keine manuelle Konfiguration nötig

## Wartung und Updates

### JSON-Updates

- **Neue Phasen**: Einfach in JSON-Datei hinzufügen
- **Faktor-Anpassungen**: Wissenschaftliche Updates möglich
- **Strain-Types**: Erweiterung um weitere Sortentypen möglich

### Code-Wartung

- **Modular**: Separate Logic-Module für einfache Updates
- **Dokumentiert**: Klare Struktur und Kommentare
- **Testbar**: Einzelne Komponenten testbar

## Zusammenfassung

Die sortentyp-spezifischen Bewässerungsrichtlinien bieten:

✅ **Wissenschaftlich fundierte Empfehlungen** für Autoflower und Photoperiod-Sorten  
✅ **Automatische Erkennung** des Sortentyps aus Pflanzendaten  
✅ **Visuelle Kennzeichnung** mit Badges und Tooltips  
✅ **Umfassende Dokumentation** im Guidelines-Modal  
✅ **Fallback-System** für unbekannte Sortentypen  
✅ **Modulare Architektur** für einfache Wartung und Erweiterung  

Das System verbessert die Bewässerungseffizienz und reduziert das Risiko von Überwässerung, besonders bei Autoflower-Sorten. 