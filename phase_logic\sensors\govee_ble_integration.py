"""
Govee BLE (Bluetooth Low Energy) Integration
Handles local Bluetooth communication with Govee hygrometers
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import json

# Try to import BLE libraries
try:
    from govee_ble import GoveeBle
    BLE_AVAILABLE = True
except ImportError:
    BLE_AVAILABLE = False
    print("WARNUNG: govee-ble nicht installiert. BLE-Integration nicht verfügbar.")
    print("Installation: pip install govee-ble")

class GoveeBleIntegration:
    """Local Bluetooth integration for Govee hygrometers"""
    
    def __init__(self):
        self.ble_available = BLE_AVAILABLE
        self.govee_ble = None
        self.devices = []
        self.last_readings = {}
        self.scanning = False
        
        if self.ble_available:
            try:
                self.govee_ble = GoveeBle()
                print("Govee BLE Integration initialisiert")
            except Exception as e:
                print(f"Fehler bei BLE-Initialisierung: {e}")
                self.ble_available = False
    
    def is_available(self) -> bool:
        """Check if BLE integration is available"""
        return self.ble_available and self.govee_ble is not None
    
    async def scan_devices(self) -> List[Dict[str, Any]]:
        """Scan for available Govee BLE devices"""
        if not self.is_available():
            return []
        
        try:
            self.scanning = True
            print("Scanne nach Govee BLE-Geräten...")
            
            # Scan for devices (timeout: 10 seconds)
            devices = await self.govee_ble.scan(timeout=10)
            
            found_devices = []
            for device in devices:
                device_info = {
                    'address': device.address,
                    'name': device.name or f"Govee-{device.address[-6:]}",
                    'model': device.model,
                    'rssi': device.rssi,
                    'type': 'hygrometer' if 'H5' in device.model else 'unknown'
                }
                found_devices.append(device_info)
            
            self.devices = found_devices
            print(f"Gefunden: {len(found_devices)} Govee-Geräte")
            return found_devices
            
        except Exception as e:
            print(f"Fehler beim Scannen: {e}")
            return []
        finally:
            self.scanning = False
    
    async def get_device_reading(self, device_address: str) -> Optional[Dict[str, Any]]:
        """Get current reading from a specific device"""
        if not self.is_available():
            return None
        
        try:
            # Connect to device
            device = await self.govee_ble.connect(device_address)
            
            # Get reading
            reading = await device.get_reading()
            
            if reading:
                sensor_data = {
                    'temperature': reading.temperature,
                    'humidity': reading.humidity,
                    'battery': reading.battery,
                    'timestamp': datetime.now().isoformat(),
                    'device_address': device_address,
                    'model': device.model
                }
                
                # Store last reading
                self.last_readings[device_address] = sensor_data
                
                # Disconnect
                await device.disconnect()
                
                return sensor_data
            
        except Exception as e:
            print(f"Fehler beim Auslesen von {device_address}: {e}")
            return None
    
    async def get_all_readings(self) -> List[Dict[str, Any]]:
        """Get readings from all discovered devices"""
        if not self.devices:
            await self.scan_devices()
        
        readings = []
        for device in self.devices:
            reading = await self.get_device_reading(device['address'])
            if reading:
                readings.append(reading)
        
        return readings
    
    def get_last_reading(self, device_address: str) -> Optional[Dict[str, Any]]:
        """Get last known reading for a device"""
        return self.last_readings.get(device_address)
    
    def get_discovered_devices(self) -> List[Dict[str, Any]]:
        """Get list of discovered devices"""
        return self.devices
    
    def get_status(self) -> Dict[str, Any]:
        """Get integration status"""
        return {
            'ble_available': self.ble_available,
            'govee_ble_initialized': self.govee_ble is not None,
            'devices_discovered': len(self.devices),
            'scanning': self.scanning,
            'last_readings_count': len(self.last_readings)
        }

# Global instance
govee_ble_integration = GoveeBleIntegration()

# Helper function for non-async usage
def scan_devices_sync():
    """Synchronous wrapper for device scanning"""
    if not govee_ble_integration.is_available():
        return []
    
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(govee_ble_integration.scan_devices())
    except RuntimeError:
        # No event loop, create new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(govee_ble_integration.scan_devices())
        finally:
            loop.close()

def get_device_reading_sync(device_address: str):
    """Synchronous wrapper for device reading"""
    if not govee_ble_integration.is_available():
        return None
    
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(govee_ble_integration.get_device_reading(device_address))
    except RuntimeError:
        # No event loop, create new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(govee_ble_integration.get_device_reading(device_address))
        finally:
            loop.close() 