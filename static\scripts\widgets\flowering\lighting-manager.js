/**
 * Flowering Lighting Manager - Verwaltet alle Beleuchtungs-bezogenen Funktionen
 */

class FloweringLightingManager {
    constructor(widget) {
        this.widget = widget;
        this.lightingData = null;
        this.lightingManagerInitialized = false;
        this.aiRecommendationsGenerated = false;
        this.growthDataSaved = false;
    }

    /**
     * Initialisiert das Beleuchtungs-Management
     */
    setupLightingManager() {
        if (!window.lightingManager) {
            console.warn('🌸 Blüte-Widget: LightingManager nicht verfügbar');
            return;
        }
        
        // Lighting Manager nur beim ersten Laden initialisieren
        if (!this.lightingManagerInitialized) {
            this.lightingManagerInitialized = true;
            
            // Beleuchtungs-Daten mit Verzögerung laden
            setTimeout(() => {
                this.loadLightingData();
            }, 1000);
        }
    }

    /**
     * Lädt Beleuchtungs-Daten
     */
    async loadLightingData() {
        try {
            // Aktuelle Phase aus den Flowering-Daten holen
            const currentPhase = this.widget.floweringData?.flowering_status?.phase || 'flowering_middle';
            const strainType = this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic';
            
            // Beleuchtungs-Daten vom LightingManager laden
            if (window.lightingManager) {
                this.lightingData = window.lightingManager.getLightingData(this.widget.currentPlantId);
                
                if (!this.lightingData) {
                    // Standard-Beleuchtungsdaten erstellen
                    this.lightingData = this.createDefaultLightingData(currentPhase, strainType);
                    window.lightingManager.setLightingData(this.widget.currentPlantId, this.lightingData);
                }
                
                // UI aktualisieren
                this.updateLightingOverview();
                this.updateLightingRecommendations();
                this.loadEnergyData();
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Laden der Beleuchtungs-Daten:', error);
        }
    }

    /**
     * Erstellt Standard-Beleuchtungsdaten
     */
    createDefaultLightingData(phase, strainType) {
        const baseData = {
            current: {
                lamp_power_w: 600,
                lamp_distance_cm: 50,
                photoperiod_hours: phase === 'flowering_middle' ? 12 : 18,
                ppfd_calculated: 800,
                color_temperature_k: 3000
            },
            recommendations: {
                ppfd_target: this.getPPFDTarget(phase),
                photoperiod_target: this.getPhotoperiodTarget(phase, strainType),
                distance_target: 45
            },
            phase: phase,
            strain_type: strainType
        };
        
        return baseData;
    }

    /**
     * Aktualisiert die Beleuchtungs-Übersicht
     */
    updateLightingOverview() {
        if (!this.lightingData) return;

        const current = this.lightingData.current;
        const recommendations = this.lightingData.recommendations;

        // Aktuelle Werte
        const lampPowerEl = this.widget.getElementById('flowering-lamp-power');
        if (lampPowerEl) lampPowerEl.value = current.lamp_power_w;

        const lampDistanceEl = this.widget.getElementById('flowering-lamp-distance');
        if (lampDistanceEl) lampDistanceEl.value = current.lamp_distance_cm;

        const photoperiodEl = this.widget.getElementById('flowering-photoperiod');
        if (photoperiodEl) photoperiodEl.value = current.photoperiod_hours;

        // PPFD-Anzeige
        const ppfdValueEl = this.widget.getElementById('flowering-ppfd-value');
        if (ppfdValueEl) ppfdValueEl.textContent = `${current.ppfd_calculated} μmol/m²/s`;

        // Empfehlungen
        const ppfdRecommendationEl = this.widget.getElementById('flowering-ppfd-recommendation');
        if (ppfdRecommendationEl) {
            ppfdRecommendationEl.textContent = `Empfohlen: ${recommendations.ppfd_target} μmol/m²/s`;
        }

        // Status-Indikator
        this.updateLightingStatus();
    }

    /**
     * Aktualisiert den Beleuchtungs-Status
     */
    updateLightingStatus() {
        if (!this.lightingData) return;

        const current = this.lightingData.current;
        const recommendations = this.lightingData.recommendations;
        
        const statusIndicator = this.widget.getElementById('flowering-lighting-status');
        const statusText = this.widget.getElementById('flowering-lighting-status-text');
        
        if (!statusIndicator || !statusText) return;

        // PPFD-Abweichung prüfen
        const ppfdDifference = Math.abs(current.ppfd_calculated - recommendations.ppfd_target);
        const ppfdTolerance = recommendations.ppfd_target * 0.1; // 10% Toleranz

        if (ppfdDifference <= ppfdTolerance) {
            statusIndicator.className = 'status-indicator';
            statusText.textContent = 'Optimal';
        } else if (ppfdDifference <= ppfdTolerance * 2) {
            statusIndicator.className = 'status-indicator warning';
            statusText.textContent = 'Anpassung empfohlen';
        } else {
            statusIndicator.className = 'status-indicator error';
            statusText.textContent = 'Korrektur erforderlich';
        }
    }

    /**
     * Aktualisiert Beleuchtungs-Empfehlungen
     */
    updateLightingRecommendations() {
        if (!this.lightingData) return;

        const current = this.lightingData.current;
        const recommendations = this.lightingData.recommendations;
        const recommendationsList = this.widget.getElementById('flowering-lighting-recommendations');
        
        if (!recommendationsList) return;

        const recommendationsHtml = [];

        // PPFD-Empfehlung
        const ppfdDifference = recommendations.ppfd_target - current.ppfd_calculated;
        if (Math.abs(ppfdDifference) > 50) {
            if (ppfdDifference > 0) {
                recommendationsHtml.push(`
                    <div class="recommendation-item">
                        <i class="fa-solid fa-arrow-up text-success"></i>
                        PPFD um ${ppfdDifference} μmol/m²/s erhöhen
                    </div>
                `);
            } else {
                recommendationsHtml.push(`
                    <div class="recommendation-item">
                        <i class="fa-solid fa-arrow-down text-warning"></i>
                        PPFD um ${Math.abs(ppfdDifference)} μmol/m²/s reduzieren
                    </div>
                `);
            }
        }

        // Abstand-Empfehlung
        const distanceDifference = recommendations.distance_target - current.lamp_distance_cm;
        if (Math.abs(distanceDifference) > 5) {
            if (distanceDifference > 0) {
                recommendationsHtml.push(`
                    <div class="recommendation-item">
                        <i class="fa-solid fa-arrow-up text-info"></i>
                        Lampenabstand um ${distanceDifference} cm vergrößern
                    </div>
                `);
            } else {
                recommendationsHtml.push(`
                    <div class="recommendation-item">
                        <i class="fa-solid fa-arrow-down text-info"></i>
                        Lampenabstand um ${Math.abs(distanceDifference)} cm verkleinern
                    </div>
                `);
            }
        }

        // Photoperiode-Empfehlung
        if (current.photoperiod_hours !== recommendations.photoperiod_target) {
            recommendationsHtml.push(`
                <div class="recommendation-item">
                    <i class="fa-solid fa-clock text-primary"></i>
                    Photoperiode auf ${recommendations.photoperiod_target}h anpassen
                </div>
            `);
        }

        if (recommendationsHtml.length === 0) {
            recommendationsList.innerHTML = `
                <div class="recommendation-item text-success">
                    <i class="fa-solid fa-check"></i>
                    Beleuchtung ist optimal eingestellt
                </div>
            `;
        } else {
            recommendationsList.innerHTML = recommendationsHtml.join('');
        }
    }

    /**
     * Aktualisiert Beleuchtungs-Einstellungen
     */
    async updateLightingSettings() {
        try {
            const lampPower = parseInt(this.widget.getElementById('flowering-lamp-power').value);
            const lampDistance = parseInt(this.widget.getElementById('flowering-lamp-distance').value);
            const photoperiod = parseInt(this.widget.getElementById('flowering-photoperiod').value);

            // PPFD neu berechnen (vereinfachte Formel)
            const ppfdCalculated = this.calculatePPFD(lampPower, lampDistance);

            // Daten aktualisieren
            this.lightingData.current = {
                ...this.lightingData.current,
                lamp_power_w: lampPower,
                lamp_distance_cm: lampDistance,
                photoperiod_hours: photoperiod,
                ppfd_calculated: ppfdCalculated
            };

            // An LightingManager weiterleiten
            if (window.lightingManager) {
                window.lightingManager.setLightingData(this.widget.currentPlantId, this.lightingData);
            }

            // UI aktualisieren
            this.updateLightingOverview();
            
            this.widget.uiRenderer.showSuccess('Beleuchtungs-Einstellungen aktualisiert');

        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Aktualisieren der Beleuchtungs-Einstellungen:', error);
            this.widget.uiRenderer.showError('Fehler beim Aktualisieren der Einstellungen');
        }
    }

    /**
     * Lädt Energieverbrauch-Daten
     */
    async loadEnergyData() {
        try {
            const current = this.lightingData?.current;
            if (!current) return;

            // Energieverbrauch berechnen
            const dailyConsumption = (current.lamp_power_w * current.photoperiod_hours) / 1000; // kWh
            const monthlyCost = dailyConsumption * 30 * 0.30; // Annahme: 0.30€/kWh

            // Energiedaten anzeigen
            const dailyConsumptionEl = this.widget.getElementById('flowering-daily-consumption');
            if (dailyConsumptionEl) {
                dailyConsumptionEl.textContent = `${dailyConsumption.toFixed(2)} kWh/Tag`;
            }

            const monthlyCostEl = this.widget.getElementById('flowering-monthly-cost');
            if (monthlyCostEl) {
                monthlyCostEl.textContent = `${monthlyCost.toFixed(2)} €/Monat`;
            }

            // Effizienz-Bewertung
            const efficiency = this.calculateEfficiency(current.ppfd_calculated, current.lamp_power_w);
            const efficiencyEl = this.widget.getElementById('flowering-efficiency');
            if (efficiencyEl) {
                efficiencyEl.textContent = `${efficiency.toFixed(2)} μmol/J`;
            }

        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Laden der Energiedaten:', error);
        }
    }

    /**
     * Hilfsfunktionen
     */
    calculatePPFD(lampPower, distance) {
        // Vereinfachte PPFD-Berechnung
        // In der Realität würde dies komplexere Faktoren berücksichtigen
        const basePPFD = lampPower * 1.5; // Basis-PPFD pro Watt
        const distanceFactor = Math.pow(50 / distance, 2); // Inverse Square Law
        return Math.round(basePPFD * distanceFactor);
    }

    calculateEfficiency(ppfd, power) {
        // Effizienz in μmol/J
        return ppfd / power;
    }

    getPPFDTarget(phase) {
        const targets = {
            'preflower': 600,
            'stretch': 700,
            'flowering_middle': 800,
            'flowering_late': 750,
            'flush': 600
        };
        return targets[phase] || 700;
    }

    getPhotoperiodTarget(phase, strainType) {
        if (strainType === 'autoflowering') {
            return 18; // Autoflowers können 18h Licht vertragen
        }
        
        const targets = {
            'preflower': 12,
            'stretch': 12,
            'flowering_middle': 12,
            'flowering_late': 12,
            'flush': 12
        };
        return targets[phase] || 12;
    }
}
