/**
 * Edit Plant Form Module
 * Handles edit plant form functionality with dynamic dropdowns and external data
 */

class EditPlantForm {
    constructor() {
        this.init();
    }
    
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupDropdowns();
            this.setupExternalData();
            this.setupFormHandling();
        });
    }
    
    setupDropdowns() {
        // Autopot-Optionen anzeigen/verstecken
        const wateringSelect = document.getElementById('plant-watering');
        const autopotOptions = document.getElementById('autopot-options');
        
        if (wateringSelect && autopotOptions) {
            wateringSelect.addEventListener('change', () => {
                if (wateringSelect.value === 'Autopot') {
                    autopotOptions.classList.remove('autopot-options-section');
                } else {
                    autopotOptions.classList.add('autopot-options-section');
                }
            });
            
            // Initial prüfen
            if (wateringSelect.value === 'Autopot') {
                autopotOptions.classList.remove('autopot-options-section');
            }
        }
    }
    
    setupExternalData() {
        const addExternalDataBtn = document.getElementById('add-external-data');
        const externalDataContainer = document.getElementById('external-data-container');
        
        if (addExternalDataBtn && externalDataContainer) {
            addExternalDataBtn.addEventListener('click', () => {
                const newRow = document.createElement('div');
                newRow.className = 'row mb-2 external-data-row';
                newRow.innerHTML = `
                    <div class="col-md-4">
                        <input type="text" name="external_key[]" class="form-control" 
                               placeholder="z.B. Marke, THC, etc.">
                    </div>
                    <div class="col-md-6">
                        <input type="text" name="external_value[]" class="form-control" 
                               placeholder="Wert">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-danger btn-sm remove-external-data">
                            <i class="fa-solid fa-trash"></i>
                        </button>
                    </div>
                `;
                externalDataContainer.appendChild(newRow);
            });
        }
        
        // Externe Daten entfernen
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-external-data') || 
                e.target.closest('.remove-external-data')) {
                const row = e.target.closest('.external-data-row');
                if (row) {
                    row.remove();
                }
            }
        });
    }
    
    setupFormHandling() {
        const form = document.querySelector('form');
        if (!form) return;
        
        form.addEventListener('submit', (e) => {
            // Form validation and processing can be added here
        });
    }
}

// Initialize immediately (Script wird am Ende des <body> geladen)
new EditPlantForm();

// Export for use in other modules
window.EditPlantForm = EditPlantForm; 