/**
 * Phase-Warnungen Widget
 * Zeigt automatische Warnungen und ermöglicht deren Verwaltung
 */

class PhaseWarningsWidget {
    constructor(containerId, plantId) {
        this.containerId = containerId;
        this.plantId = plantId;
        this.currentPhase = null;
        this.warnings = [];
        this.init();
    }
    
    init() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            console.error(`PhaseWarningsWidget: Container ${this.containerId} nicht gefunden`);
            return;
        }
        
        this.render();
        this.loadWarnings();
    }
    
    setCurrentPhase(phaseData) {
        this.currentPhase = phaseData;
        this.checkForNewWarnings();
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="card phase-warnings-card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Phase-Warnungen
                    </h5>
                    <button 
                        type="button" 
                        class="btn btn-sm btn-outline-light" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="left"
                        title="Automatische Warnungen basierend auf Phasenfortschritt, Klimabedingungen und Analyse-Daten. Hilft dabei, rechtzeitig auf wichtige Entwicklungen zu reagieren und optimale Bedingungen zu schaffen."
                    >
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div id="phase-warnings-content">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">Lade...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Tooltip initialisieren
        const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    async loadWarnings() {
        try {
            const response = await fetch(`/api/phase-warnings/${this.plantId}`);
            const data = await response.json();
            
            if (data.success) {
                this.warnings = data.warnings;
                this.renderWarnings();
            }
        } catch (error) {
            console.error('Fehler beim Laden der Warnungen:', error);
            this.showError('Fehler beim Laden der Warnungen');
        }
    }
    
    async checkForNewWarnings() {
        if (!this.currentPhase) {
            return;
        }
        
        // Erst aktuelle Warnungen laden
        await this.loadWarnings();
        
        // Automatische Warnungen basierend auf aktueller Phase generieren
        const newWarnings = this.generateAutomaticWarnings();
        
        // Neue Warnungen hinzufügen (nur wenn sie noch nicht existieren)
        for (const warning of newWarnings) {
            // Prüfen ob diese Warnung bereits existiert
            const warningExists = this.warnings.some(existingWarning => 
                existingWarning.warning_type === warning.warning_type &&
                existingWarning.warning_message === warning.warning_message
            );
            
            if (!warningExists) {
                await this.addWarning(warning);
            }
        }
        
        // Warnungen neu laden
        await this.loadWarnings();
    }
    
    generateAutomaticWarnings() {
        if (!this.currentPhase) {
            return [];
        }
        
        const warnings = [];
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        const daysInPhase = this.currentPhase.sub_stage_day || 0;
        const daysToNext = this.currentPhase.days_to_next || 0;
        
        // Phase-spezifische Warnungen
        if ((phaseName === 'vegetative_late' || phaseName === 'Späte Wachstumsphase') && daysInPhase >= 5) {
            warnings.push({
                warning_type: 'phase_transition',
                warning_message: 'Blütephase beginnt bald - Lichtzyklus vorbereiten!',
                warning_level: 'warning'
            });
        }
        
        if ((phaseName === 'flowering_early' || phaseName === 'Frühe Blüte') && daysInPhase <= 3) {
            warnings.push({
                warning_type: 'phase_start',
                warning_message: 'Blütephase hat begonnen - Dünger anpassen!',
                warning_level: 'info'
            });
        }
        
        if ((phaseName === 'flowering_middle' || phaseName === 'Mittlere Blüte') && daysInPhase >= 20) {
            warnings.push({
                warning_type: 'trichome_check',
                warning_message: 'Trichome-Entwicklung überprüfen - Ernte nähert sich!',
                warning_level: 'warning'
            });
        }
        
        // Countdown-Warnungen für Phasenwechsel
        if (daysToNext <= 1) {
            const nextPhase = this.getPhaseDisplayName(this.currentPhase.next_phase || this.currentPhase.next_phase_name || 'nächste Phase');
            warnings.push({
                warning_type: 'phase_countdown',
                warning_message: `Morgen beginnt: ${nextPhase}`,
                warning_level: 'info'
            });
        } else if (daysToNext <= 3) {
            const nextPhase = this.getPhaseDisplayName(this.currentPhase.next_phase || this.currentPhase.next_phase_name || 'nächste Phase');
            warnings.push({
                warning_type: 'phase_countdown',
                warning_message: `In ${daysToNext} Tagen beginnt: ${nextPhase}`,
                warning_level: 'info'
            });
        }
        
        return warnings;
    }
    
    async addWarning(warningData) {
        try {
            const response = await fetch(`/api/phase-warnings/${this.plantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(warningData)
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('API-Fehler:', response.status, errorText);
                return false;
            }
            
            const data = await response.json();
            return data.success;
        } catch (error) {
            console.error('Fehler beim Hinzufügen der Warnung:', error);
            return false;
        }
    }
    
    renderWarnings() {
        const contentDiv = document.getElementById('phase-warnings-content');
        if (!contentDiv) return;
        
        if (this.warnings.length === 0) {
            contentDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    Keine aktiven Warnungen vorhanden
                </div>
            `;
            return;
        }
        
        const warningsHtml = this.warnings.map(warning => `
            <div class="alert alert-${this.getAlertClass(warning.warning_level)} alert-dismissible fade show mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <i class="fas ${this.getWarningIcon(warning.warning_type)} me-2"></i>
                        <strong>${this.getWarningTypeDisplay(warning.warning_type)}:</strong>
                        ${warning.warning_message}
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${this.formatDate(warning.triggered_at)}
                        </small>
                    </div>
                    <div class="ms-2">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-secondary"
                            onclick="phaseWarningsWidget.acknowledgeWarning('${warning.id}')"
                            title="Als gelesen markieren"
                        >
                            <i class="fas fa-check"></i>
                        </button>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-danger"
                            onclick="phaseWarningsWidget.deleteWarning('${warning.id}')"
                            title="Warnung löschen"
                        >
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
        
        contentDiv.innerHTML = `
            <div class="mb-3">
                <h6 class="text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Aktive Warnungen (${this.warnings.length})
                </h6>
            </div>
            
            <div id="warnings-list">
                ${warningsHtml}
            </div>
            
            <div class="mt-3">
                <button 
                    type="button" 
                    class="btn btn-outline-warning btn-sm"
                    onclick="phaseWarningsWidget.acknowledgeAll()"
                >
                    <i class="fas fa-check-double me-1"></i>
                    Alle als gelesen markieren
                </button>
                
                <button 
                    type="button" 
                    class="btn btn-outline-danger btn-sm ms-2"
                    onclick="phaseWarningsWidget.deleteAll()"
                >
                    <i class="fas fa-trash me-1"></i>
                    Alle löschen
                </button>
            </div>
        `;
    }
    
    async acknowledgeWarning(warningId) {
        try {
            const response = await fetch(`/api/phase-warnings/acknowledge/${warningId}`, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Warnung als gelesen markiert');
                await this.loadWarnings();
            } else {
                this.showError(data.message || 'Fehler beim Markieren');
            }
        } catch (error) {
            console.error('Fehler beim Bestätigen der Warnung:', error);
            this.showError('Fehler beim Bestätigen der Warnung');
        }
    }
    
    async deleteWarning(warningId) {
        // Sichere Funktion zum Entfernen des Modals
        const safeRemoveModal = (modalId) => {
            const element = document.getElementById(modalId);
            if (element && element.parentNode) {
                element.remove();
            }
        };
        
        // Modal für Bestätigung anzeigen
        const modalHtml = `
            <div class="modal fade" id="deleteWarningModal" tabindex="-1" aria-labelledby="deleteWarningModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteWarningModalLabel">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                Warnung löschen
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Möchtest du diese Warnung wirklich löschen?</p>
                            <p class="text-muted small">Diese Aktion kann nicht rückgängig gemacht werden.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Abbrechen
                            </button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteWarning">
                                <i class="fas fa-trash me-1"></i>
                                Löschen
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Modal zum DOM hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Prüfen ob Bootstrap verfügbar ist, sonst Fallback zu confirm()
        let modal = null;
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            modal = new bootstrap.Modal(document.getElementById('deleteWarningModal'));
            modal.show();
        } else {
            // Fallback zu confirm() wenn Bootstrap nicht verfügbar
            if (confirm('Möchtest du diese Warnung wirklich löschen?')) {
                try {
                    const response = await fetch(`/api/phase-warnings/delete/${warningId}`, {
                        method: 'DELETE'
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        this.showSuccess('Warnung erfolgreich gelöscht');
                        await this.loadWarnings();
                    } else {
                        this.showError(data.message || 'Fehler beim Löschen');
                    }
                } catch (error) {
                    console.error('Fehler beim Löschen der Warnung:', error);
                    this.showError('Fehler beim Löschen der Warnung');
                }
            }
            // Modal aus DOM entfernen
            safeRemoveModal('deleteWarningModal');
            return;
        }
        
        // Event-Listener für Bestätigung
        document.getElementById('confirmDeleteWarning').addEventListener('click', async () => {
            if (modal) modal.hide();
            
            try {
                const response = await fetch(`/api/phase-warnings/delete/${warningId}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showSuccess('Warnung erfolgreich gelöscht');
                    await this.loadWarnings();
                } else {
                    this.showError(data.message || 'Fehler beim Löschen');
                }
            } catch (error) {
                console.error('Fehler beim Löschen der Warnung:', error);
                this.showError('Fehler beim Löschen der Warnung');
            }
            
            // Modal aus DOM entfernen
            safeRemoveModal('deleteWarningModal');
        });
        
        // Modal aus DOM entfernen wenn geschlossen
        const modalElement = document.getElementById('deleteWarningModal');
        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', () => {
                safeRemoveModal('deleteWarningModal');
            });
        }
    }
    
    async acknowledgeAll() {
        // Sichere Funktion zum Entfernen des Modals
        const safeRemoveModal = (modalId) => {
            const element = document.getElementById(modalId);
            if (element && element.parentNode) {
                element.remove();
            }
        };
        
        // Modal für Bestätigung anzeigen
        const modalHtml = `
            <div class="modal fade" id="acknowledgeAllWarningsModal" tabindex="-1" aria-labelledby="acknowledgeAllWarningsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="acknowledgeAllWarningsModalLabel">
                                <i class="fas fa-check-double text-success me-2"></i>
                                Alle Warnungen als gelesen markieren
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Möchtest du wirklich <strong>alle Warnungen</strong> als gelesen markieren?</p>
                            <p class="text-muted small">Die Warnungen werden als gelesen markiert und verschwinden aus der aktiven Liste.</p>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>${this.warnings.length} Warnung(en)</strong> werden als gelesen markiert.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Abbrechen
                            </button>
                            <button type="button" class="btn btn-success" id="confirmAcknowledgeAllWarnings">
                                <i class="fas fa-check-double me-1"></i>
                                Alle als gelesen markieren
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Modal zum DOM hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Prüfen ob Bootstrap verfügbar ist, sonst Fallback zu confirm()
        let modal = null;
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            modal = new bootstrap.Modal(document.getElementById('acknowledgeAllWarningsModal'));
            modal.show();
        } else {
            // Fallback zu confirm() wenn Bootstrap nicht verfügbar
            if (confirm(`Möchtest du wirklich alle ${this.warnings.length} Warnungen als gelesen markieren?`)) {
                // Alle Warnungen bestätigen
                for (const warning of this.warnings) {
                    try {
                        await fetch(`/api/phase-warnings/acknowledge/${warning.id}`, {
                            method: 'POST'
                        });
                    } catch (error) {
                        console.error('Fehler beim Bestätigen der Warnung:', error);
                    }
                }
                
                this.showSuccess('Alle Warnungen als gelesen markiert');
                await this.loadWarnings();
            }
            // Modal aus DOM entfernen
            safeRemoveModal('acknowledgeAllWarningsModal');
            return;
        }
        
        // Event-Listener für Bestätigung
        document.getElementById('confirmAcknowledgeAllWarnings').addEventListener('click', async () => {
            if (modal) modal.hide();
            
            // Alle Warnungen bestätigen
            for (const warning of this.warnings) {
                try {
                    await fetch(`/api/phase-warnings/acknowledge/${warning.id}`, {
                        method: 'POST'
                    });
                } catch (error) {
                    console.error('Fehler beim Bestätigen der Warnung:', error);
                }
            }
            
            this.showSuccess('Alle Warnungen als gelesen markiert');
            await this.loadWarnings();
            
            // Modal aus DOM entfernen
            safeRemoveModal('acknowledgeAllWarningsModal');
        });
        
        // Modal aus DOM entfernen wenn geschlossen
        const modalElement = document.getElementById('acknowledgeAllWarningsModal');
        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', () => {
                safeRemoveModal('acknowledgeAllWarningsModal');
            });
        }
    }
    
    async deleteAll() {
        // Sichere Funktion zum Entfernen des Modals
        const safeRemoveModal = (modalId) => {
            const element = document.getElementById(modalId);
            if (element && element.parentNode) {
                element.remove();
            }
        };
        
        // Modal für Bestätigung anzeigen
        const modalHtml = `
            <div class="modal fade" id="deleteAllWarningsModal" tabindex="-1" aria-labelledby="deleteAllWarningsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteAllWarningsModalLabel">
                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                Alle Warnungen löschen
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Möchtest du wirklich <strong>alle Warnungen</strong> löschen?</p>
                            <p class="text-muted small">Diese Aktion kann nicht rückgängig gemacht werden.</p>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>${this.warnings.length} Warnung(en)</strong> werden gelöscht.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Abbrechen
                            </button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteAllWarnings">
                                <i class="fas fa-trash me-1"></i>
                                Alle löschen
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Modal zum DOM hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Prüfen ob Bootstrap verfügbar ist, sonst Fallback zu confirm()
        let modal = null;
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            modal = new bootstrap.Modal(document.getElementById('deleteAllWarningsModal'));
            modal.show();
        } else {
            // Fallback zu confirm() wenn Bootstrap nicht verfügbar
            if (confirm(`Möchtest du wirklich alle ${this.warnings.length} Warnungen löschen?`)) {
                // Alle Warnungen löschen
                for (const warning of this.warnings) {
                    try {
                        await fetch(`/api/phase-warnings/delete/${warning.id}`, {
                            method: 'DELETE'
                        });
                    } catch (error) {
                        console.error('Fehler beim Löschen der Warnung:', error);
                    }
                }
                
                this.showSuccess('Alle Warnungen gelöscht');
                await this.loadWarnings();
            }
            // Modal aus DOM entfernen
            safeRemoveModal('deleteAllWarningsModal');
            return;
        }
        
        // Event-Listener für Bestätigung
        document.getElementById('confirmDeleteAllWarnings').addEventListener('click', async () => {
            if (modal) modal.hide();
            
            // Alle Warnungen löschen
            for (const warning of this.warnings) {
                try {
                    await fetch(`/api/phase-warnings/delete/${warning.id}`, {
                        method: 'DELETE'
                    });
                } catch (error) {
                    console.error('Fehler beim Löschen der Warnung:', error);
                }
            }
            
            this.showSuccess('Alle Warnungen gelöscht');
            await this.loadWarnings();
            
            // Modal aus DOM entfernen
            safeRemoveModal('deleteAllWarningsModal');
        });
        
        // Modal aus DOM entfernen wenn geschlossen
        const modalElement = document.getElementById('deleteAllWarningsModal');
        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', () => {
                safeRemoveModal('deleteAllWarningsModal');
            });
        }
    }
    
    getAlertClass(level) {
        const alertClasses = {
            'info': 'info',
            'warning': 'warning',
            'danger': 'danger',
            'success': 'success'
        };
        return alertClasses[level] || 'info';
    }
    
    getWarningIcon(type) {
        const icons = {
            'phase_transition': 'fa-exchange-alt',
            'phase_start': 'fa-play',
            'trichome_check': 'fa-search',
            'vpd_low': 'fa-thermometer-empty',
            'vpd_high': 'fa-thermometer-full',
            'temp_low': 'fa-thermometer-empty',
            'temp_high': 'fa-thermometer-full',
            'phase_countdown': 'fa-clock'
        };
        return icons[type] || 'fa-exclamation-triangle';
    }
    
    getWarningTypeDisplay(type) {
        const types = {
            'phase_transition': 'Phasenwechsel',
            'phase_start': 'Phase gestartet',
            'trichome_check': 'Trichome prüfen',
            'vpd_low': 'VPD zu niedrig',
            'vpd_high': 'VPD zu hoch',
            'temp_low': 'Temperatur zu niedrig',
            'temp_high': 'Temperatur zu hoch',
            'phase_countdown': 'Countdown'
        };
        return types[type] || 'Warnung';
    }
    
    getPhaseDisplayName(phaseName) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            // Alternative Namen aus der Datenbank
            'Keimung': 'Keimung',
            'Frühe Wachstumsphase': 'Frühe Wachstumsphase',
            'Mittlere Wachstumsphase': 'Mittlere Wachstumsphase',
            'Späte Wachstumsphase': 'Späte Wachstumsphase',
            'Frühe Blüte': 'Frühe Blüte',
            'Mittlere Blüte': 'Mittlere Blüte',
            'Späte Blüte': 'Späte Blüte'
        };
        return phaseNames[phaseName] || phaseName;
    }
    
    formatDate(dateString) {
        if (!dateString) return '';
        
        // Parse das Datum und behandle Zeitzonen korrekt
        let date;
        if (dateString.includes('T') && dateString.includes('Z')) {
            // ISO String mit Z (UTC) - konvertiere zu lokaler Zeit
            date = new Date(dateString);
        } else if (dateString.includes('T')) {
            // ISO String ohne Z - behandle als lokale Zeit
            date = new Date(dateString + 'Z');
        } else {
            // Andere Formate
            date = new Date(dateString);
        }
        
        return date.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Europe/Berlin'
        });
    }
    
    showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const contentDiv = document.getElementById('phase-warnings-content');
        if (contentDiv) {
            contentDiv.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    }
    
    showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const contentDiv = document.getElementById('phase-warnings-content');
        if (contentDiv) {
            contentDiv.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }
}

// Globale Instanz für einfachen Zugriff
let phaseWarningsWidget = null;

// Initialisierungsfunktion
function initPhaseWarningsWidget(containerId, plantId) {
    phaseWarningsWidget = new PhaseWarningsWidget(containerId, plantId);
    return phaseWarningsWidget;
} 