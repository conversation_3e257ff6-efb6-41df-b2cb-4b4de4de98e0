/**
 * 📅 Smart Scheduling System
 * 
 * Intelligente Beleuchtungsplanung mit automatischen Zeitplänen
 * Berücksichtigt Pflanzenwachstum, Wetter und Benutzer-Präferenzen
 */

class SmartScheduler {
    constructor() {
        this.schedules = new Map();
        this.weatherData = new Map();
        this.userPreferences = new Map();
        this.learningData = new Map();
        this.activeSchedules = new Set();
        
        // Standard-Zeitpläne für verschiedene Phasen
        this.defaultSchedules = this.initializeDefaultSchedules();
        
        // Wetter-Integration
        this.weatherAPI = null;
        this.setupWeatherIntegration();
        
        console.log('📅 SmartScheduler: Initialisiert');
    }
    
    /**
     * Standard-Zeitpläne initialisieren
     */
    initializeDefaultSchedules() {
        return {
            // Vegetations-Phase Zeitpläne
            'vegetative_early': {
                name: 'Frühe Vegetation',
                description: 'Sanftes Wachstum für junge Pflanzen',
                schedule: {
                    start_time: '06:00',
                    end_time: '00:00',
                    total_hours: 18,
                    ppfd_ramp: {
                        start: 250,
                        peak: 350,
                        end: 300
                    },
                    color_temp: 5000,
                    intensity_curve: 'gentle'
                }
            },
            'vegetative_middle': {
                name: '<PERSON>ttlere Vegetation',
                description: 'Kräftiges Wachstum für etablierte Pflanzen',
                schedule: {
                    start_time: '06:00',
                    end_time: '00:00',
                    total_hours: 18,
                    ppfd_ramp: {
                        start: 400,
                        peak: 500,
                        end: 450
                    },
                    color_temp: 4500,
                    intensity_curve: 'moderate'
                }
            },
            'vegetative_late': {
                name: 'Späte Vegetation',
                description: 'Vorbereitung auf Blüte-Phase',
                schedule: {
                    start_time: '06:00',
                    end_time: '00:00',
                    total_hours: 18,
                    ppfd_ramp: {
                        start: 500,
                        peak: 600,
                        end: 550
                    },
                    color_temp: 4000,
                    intensity_curve: 'aggressive'
                }
            },
            
            // Blüte-Phase Zeitpläne
            'flowering_early': {
                name: 'Frühe Blüte',
                description: 'Blüteninduktion und erste Blütenbildung',
                schedule: {
                    start_time: '06:00',
                    end_time: '18:00',
                    total_hours: 12,
                    ppfd_ramp: {
                        start: 600,
                        peak: 700,
                        end: 650
                    },
                    color_temp: 3500,
                    intensity_curve: 'flowering'
                }
            },
            'flowering_middle': {
                name: 'Mittlere Blüte',
                description: 'Maximale Blütenbildung und Trichom-Entwicklung',
                schedule: {
                    start_time: '06:00',
                    end_time: '18:00',
                    total_hours: 12,
                    ppfd_ramp: {
                        start: 700,
                        peak: 800,
                        end: 750
                    },
                    color_temp: 3000,
                    intensity_curve: 'intense'
                }
            },
            'flowering_late': {
                name: 'Späte Blüte',
                description: 'Reifung und Trichom-Entwicklung',
                schedule: {
                    start_time: '06:00',
                    end_time: '18:00',
                    total_hours: 12,
                    ppfd_ramp: {
                        start: 600,
                        peak: 700,
                        end: 650
                    },
                    color_temp: 3000,
                    intensity_curve: 'mature'
                }
            },
            
            // Flush-Phase Zeitplan
            'flush': {
                name: 'Flush-Phase',
                description: 'Schonende Trichom-Entwicklung',
                schedule: {
                    start_time: '08:00',
                    end_time: '18:00',
                    total_hours: 10,
                    ppfd_ramp: {
                        start: 450,
                        peak: 600,
                        end: 500
                    },
                    color_temp: 2700,
                    intensity_curve: 'gentle'
                }
            }
        };
    }
    
    /**
     * Wetter-Integration einrichten
     */
    setupWeatherIntegration() {
        // Wetter-API Integration (OpenWeatherMap Beispiel)
        this.weatherAPI = {
            apiKey: localStorage.getItem('weather_api_key') || '',
            location: localStorage.getItem('weather_location') || 'Berlin,DE',
            updateInterval: 30 * 60 * 1000 // 30 Minuten
        };
        
        // Wetter-Daten laden
        this.loadWeatherData();
        
        // Regelmäßige Updates deaktiviert
        console.log('📅 SmartScheduler: Automatische Wetter-Updates deaktiviert');
        
        // Hinweis anzeigen
        this.showUpdateNotice('Wetter-Integration: Automatische Updates deaktiviert. Daten werden nur bei Bedarf aktualisiert.');
    }
    
    /**
     * Update-Hinweis anzeigen
     */
    showUpdateNotice(message) {
        console.log('📅 SmartScheduler:', message);
        // Hinweis wird in der Konsole angezeigt, da SmartScheduler kein eigenes UI hat
    }
    
    /**
     * Wetter-Daten laden
     */
    async loadWeatherData() {
        if (!this.weatherAPI.apiKey) {
            console.log('📅 SmartScheduler: Keine Wetter-API verfügbar');
            return;
        }
        
        try {
            const response = await fetch(
                `https://api.openweathermap.org/data/2.5/weather?q=${this.weatherAPI.location}&appid=${this.weatherAPI.apiKey}&units=metric`
            );
            
            if (response.ok) {
                const weatherData = await response.json();
                this.weatherData.set('current', {
                    temperature: weatherData.main.temp,
                    humidity: weatherData.main.humidity,
                    conditions: weatherData.weather[0].main,
                    timestamp: new Date().toISOString()
                });
                
                console.log('📅 SmartScheduler: Wetter-Daten aktualisiert');
            }
        } catch (error) {
            console.error('📅 SmartScheduler: Fehler beim Laden der Wetter-Daten:', error);
        }
    }
    
    /**
     * Intelligente Zeitpläne generieren
     */
    async generateSmartSchedule(plantId, plantData, lightingData, userPreferences = {}) {
        try {
            const baseSchedule = this.getBaseSchedule(plantData.phase);
            const weatherAdjustments = this.calculateWeatherAdjustments();
            const learningAdjustments = this.getLearningAdjustments(plantId);
            const userAdjustments = this.applyUserPreferences(baseSchedule, userPreferences);
            
            // Alle Anpassungen kombinieren
            const smartSchedule = this.combineAdjustments(
                baseSchedule,
                weatherAdjustments,
                learningAdjustments,
                userAdjustments
            );
            
            // Zeitplan validieren und optimieren
            const optimizedSchedule = this.optimizeSchedule(smartSchedule, plantData);
            
            // Zeitplan speichern
            this.schedules.set(plantId, {
                ...optimizedSchedule,
                plantId,
                generated_at: new Date().toISOString(),
                adjustments: {
                    weather: weatherAdjustments,
                    learning: learningAdjustments,
                    user: userAdjustments
                }
            });
            
            console.log('📅 SmartScheduler: Intelligenter Zeitplan generiert für Pflanze', plantId);
            
            return optimizedSchedule;
            
        } catch (error) {
            console.error('📅 SmartScheduler: Fehler beim Generieren des Zeitplans:', error);
            return this.getBaseSchedule(plantData.phase);
        }
    }
    
    /**
     * Basis-Zeitplan für Phase abrufen
     */
    getBaseSchedule(phase) {
        const defaultSchedule = this.defaultSchedules[phase];
        if (!defaultSchedule) {
            console.warn(`📅 SmartScheduler: Kein Standard-Zeitplan für Phase ${phase}`);
            return this.defaultSchedules['vegetative_middle'];
        }
        
        return JSON.parse(JSON.stringify(defaultSchedule));
    }
    
    /**
     * Wetter-basierte Anpassungen berechnen
     */
    calculateWeatherAdjustments() {
        const weather = this.weatherData.get('current');
        if (!weather) return {};
        
        const adjustments = {};
        
        // Temperatur-basierte Anpassungen
        if (weather.temperature > 30) {
            // Heißes Wetter - PPFD reduzieren, mehr Kühlung
            adjustments.ppfd_reduction = 0.15;
            adjustments.start_time_delay = 30; // 30 Minuten später starten
            adjustments.intensity_curve = 'gentle';
        } else if (weather.temperature < 15) {
            // Kaltes Wetter - PPFD erhöhen, mehr Wärme
            adjustments.ppfd_increase = 0.1;
            adjustments.start_time_advance = 30; // 30 Minuten früher starten
            adjustments.intensity_curve = 'aggressive';
        }
        
        // Wetterbedingungen-basierte Anpassungen
        if (weather.conditions === 'Rain' || weather.conditions === 'Clouds') {
            // Bewölkt/Regen - mehr Licht kompensieren
            adjustments.ppfd_increase = (adjustments.ppfd_increase || 0) + 0.2;
            adjustments.total_hours_increase = 1;
        } else if (weather.conditions === 'Clear') {
            // Klar - natürliches Licht nutzen
            adjustments.ppfd_reduction = (adjustments.ppfd_reduction || 0) + 0.1;
        }
        
        return adjustments;
    }
    
    /**
     * Machine Learning Anpassungen abrufen
     */
    getLearningAdjustments(plantId) {
        const learningData = this.learningData.get(plantId);
        if (!learningData || learningData.entries.length < 5) {
            return {};
        }
        
        const adjustments = {};
        const recentEntries = learningData.entries.slice(-10);
        
        // Erfolgreiche Anpassungen analysieren
        const successfulAdjustments = recentEntries.filter(entry => entry.success_rate > 0.7);
        
        if (successfulAdjustments.length > 0) {
            // Durchschnittliche erfolgreiche Anpassungen berechnen
            const avgPPFDAdjustment = successfulAdjustments.reduce((sum, entry) => 
                sum + (entry.ppfd_adjustment || 0), 0) / successfulAdjustments.length;
            
            const avgTimeAdjustment = successfulAdjustments.reduce((sum, entry) => 
                sum + (entry.time_adjustment || 0), 0) / successfulAdjustments.length;
            
            if (Math.abs(avgPPFDAdjustment) > 10) {
                adjustments.learned_ppfd_adjustment = avgPPFDAdjustment;
            }
            
            if (Math.abs(avgTimeAdjustment) > 15) {
                adjustments.learned_time_adjustment = avgTimeAdjustment;
            }
        }
        
        return adjustments;
    }
    
    /**
     * Benutzer-Präferenzen anwenden
     */
    applyUserPreferences(baseSchedule, userPreferences) {
        const adjustments = {};
        
        // Startzeit-Präferenz
        if (userPreferences.preferred_start_time) {
            adjustments.start_time = userPreferences.preferred_start_time;
        }
        
        // Beleuchtungsstunden-Präferenz
        if (userPreferences.preferred_hours) {
            adjustments.total_hours = userPreferences.preferred_hours;
        }
        
        // PPFD-Präferenz
        if (userPreferences.preferred_ppfd_intensity) {
            adjustments.ppfd_intensity = userPreferences.preferred_ppfd_intensity;
        }
        
        // Energiespar-Modus
        if (userPreferences.energy_saving_mode) {
            adjustments.ppfd_reduction = 0.2;
            adjustments.intensity_curve = 'gentle';
        }
        
        return adjustments;
    }
    
    /**
     * Alle Anpassungen kombinieren
     */
    combineAdjustments(baseSchedule, weatherAdjustments, learningAdjustments, userAdjustments) {
        const combined = JSON.parse(JSON.stringify(baseSchedule));
        
        // PPFD-Anpassungen kombinieren
        let ppfdAdjustment = 0;
        if (weatherAdjustments.ppfd_reduction) ppfdAdjustment -= weatherAdjustments.ppfd_reduction;
        if (weatherAdjustments.ppfd_increase) ppfdAdjustment += weatherAdjustments.ppfd_increase;
        if (learningAdjustments.learned_ppfd_adjustment) ppfdAdjustment += learningAdjustments.learned_ppfd_adjustment;
        
        // PPFD-Ramp anpassen
        if (ppfdAdjustment !== 0) {
            combined.schedule.ppfd_ramp.start = Math.max(0, combined.schedule.ppfd_ramp.start + ppfdAdjustment);
            combined.schedule.ppfd_ramp.peak = Math.max(0, combined.schedule.ppfd_ramp.peak + ppfdAdjustment);
            combined.schedule.ppfd_ramp.end = Math.max(0, combined.schedule.ppfd_ramp.end + ppfdAdjustment);
        }
        
        // Zeit-Anpassungen
        if (userAdjustments.start_time) {
            combined.schedule.start_time = userAdjustments.start_time;
        }
        
        if (userAdjustments.total_hours) {
            combined.schedule.total_hours = userAdjustments.total_hours;
            // Endzeit entsprechend anpassen
            const startTime = new Date(`2000-01-01 ${combined.schedule.start_time}`);
            const endTime = new Date(startTime.getTime() + userAdjustments.total_hours * 60 * 60 * 1000);
            combined.schedule.end_time = endTime.toTimeString().slice(0, 5);
        }
        
        // Intensitäts-Kurve anpassen
        if (weatherAdjustments.intensity_curve) {
            combined.schedule.intensity_curve = weatherAdjustments.intensity_curve;
        }
        
        return combined;
    }
    
    /**
     * Zeitplan optimieren
     */
    optimizeSchedule(schedule, plantData) {
        const optimized = JSON.parse(JSON.stringify(schedule));
        
        // Phasen-spezifische Optimierungen
        switch (plantData.phase) {
            case 'vegetative_early':
                // Sanfte Intensitäts-Kurve für junge Pflanzen
                optimized.schedule.intensity_curve = 'gentle';
                break;
                
            case 'flowering_middle':
                // Intensive Kurve für maximale Blütenbildung
                optimized.schedule.intensity_curve = 'intense';
                break;
                
            case 'flush':
                // Sehr sanfte Kurve für Trichom-Entwicklung
                optimized.schedule.intensity_curve = 'gentle';
                optimized.schedule.ppfd_ramp.peak = Math.min(optimized.schedule.ppfd_ramp.peak, 600);
                break;
        }
        
        // Strain-spezifische Optimierungen
        if (plantData.strain_type === 'autoflowering') {
            // Autoflowers brauchen keine Photoperiode-Änderung
            optimized.schedule.total_hours = 18;
            const startTime = new Date(`2000-01-01 ${optimized.schedule.start_time}`);
            const endTime = new Date(startTime.getTime() + 18 * 60 * 60 * 1000);
            optimized.schedule.end_time = endTime.toTimeString().slice(0, 5);
        }
        
        return optimized;
    }
    
    /**
     * Zeitplan aktivieren
     */
    activateSchedule(plantId) {
        const schedule = this.schedules.get(plantId);
        if (!schedule) {
            console.warn(`📅 SmartScheduler: Kein Zeitplan für Pflanze ${plantId} gefunden`);
            return false;
        }
        
        this.activeSchedules.add(plantId);
        
        // Zeitplan an LightingManager senden
        if (window.lightingManager) {
            window.lightingManager.setSchedule(plantId, schedule);
        }
        
        console.log(`📅 SmartScheduler: Zeitplan für Pflanze ${plantId} aktiviert`);
        return true;
    }
    
    /**
     * Zeitplan deaktivieren
     */
    deactivateSchedule(plantId) {
        this.activeSchedules.delete(plantId);
        
        // LightingManager informieren
        if (window.lightingManager) {
            window.lightingManager.removeSchedule(plantId);
        }
        
        console.log(`📅 SmartScheduler: Zeitplan für Pflanze ${plantId} deaktiviert`);
    }
    
    /**
     * Zeitplan-Erfolg speichern (Machine Learning)
     */
    saveScheduleSuccess(plantId, successData) {
        const learningData = this.learningData.get(plantId) || { entries: [] };
        
        learningData.entries.push({
            ...successData,
            timestamp: new Date().toISOString()
        });
        
        // Nur die letzten 50 Einträge behalten
        if (learningData.entries.length > 50) {
            learningData.entries.splice(0, learningData.entries.length - 50);
        }
        
        this.learningData.set(plantId, learningData);
        
        // In localStorage speichern
        localStorage.setItem(`schedule_learning_${plantId}`, JSON.stringify(learningData));
        
        console.log(`📅 SmartScheduler: Erfolgs-Daten für Pflanze ${plantId} gespeichert`);
    }
    
    /**
     * Benutzer-Präferenzen speichern
     */
    saveUserPreferences(plantId, preferences) {
        this.userPreferences.set(plantId, {
            ...preferences,
            timestamp: new Date().toISOString()
        });
        
        // In localStorage speichern
        localStorage.setItem(`user_preferences_${plantId}`, JSON.stringify(preferences));
    }
    
    /**
     * Benutzer-Präferenzen abrufen
     */
    getUserPreferences(plantId) {
        return this.userPreferences.get(plantId) || {};
    }
    
    /**
     * Aktive Zeitpläne abrufen
     */
    getActiveSchedules() {
        return Array.from(this.activeSchedules).map(plantId => ({
            plantId,
            schedule: this.schedules.get(plantId)
        }));
    }
    
    /**
     * Zeitplan-Status abrufen
     */
    getScheduleStatus(plantId) {
        const schedule = this.schedules.get(plantId);
        const isActive = this.activeSchedules.has(plantId);
        
        if (!schedule) {
            return { status: 'no_schedule', message: 'Kein Zeitplan verfügbar' };
        }
        
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 5);
        const isInSchedule = this.isTimeInSchedule(currentTime, schedule.schedule);
        
        return {
            status: isActive ? (isInSchedule ? 'active' : 'scheduled') : 'inactive',
            schedule: schedule,
            isActive: isActive,
            isInSchedule: isInSchedule,
            nextChange: this.getNextScheduleChange(schedule.schedule)
        };
    }
    
    /**
     * Prüfen ob aktuelle Zeit im Zeitplan liegt
     */
    isTimeInSchedule(currentTime, schedule) {
        const startTime = schedule.start_time;
        const endTime = schedule.end_time;
        
        // Einfache Zeitvergleichung (für 24h-Zeitpläne)
        if (startTime <= endTime) {
            return currentTime >= startTime && currentTime <= endTime;
        } else {
            // Über Mitternacht
            return currentTime >= startTime || currentTime <= endTime;
        }
    }
    
    /**
     * Nächste Zeitplan-Änderung berechnen
     */
    getNextScheduleChange(schedule) {
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 5);
        
        if (this.isTimeInSchedule(currentTime, schedule)) {
            // Aktuell aktiv - nächste Änderung ist Ende
            return {
                time: schedule.end_time,
                type: 'end',
                description: 'Beleuchtung ausschalten'
            };
        } else {
            // Aktuell inaktiv - nächste Änderung ist Start
            return {
                time: schedule.start_time,
                type: 'start',
                description: 'Beleuchtung einschalten'
            };
        }
    }
    
    /**
     * Debug-Informationen ausgeben
     */
    debug() {
        console.log('📅 SmartScheduler Debug Info:');
        console.log('Schedules:', this.schedules.size);
        console.log('Active Schedules:', this.activeSchedules.size);
        console.log('Weather Data:', this.weatherData.size);
        console.log('User Preferences:', this.userPreferences.size);
        console.log('Learning Data:', this.learningData.size);
    }
}

// Globale Instanz erstellen
window.smartScheduler = new SmartScheduler();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartScheduler;
} 