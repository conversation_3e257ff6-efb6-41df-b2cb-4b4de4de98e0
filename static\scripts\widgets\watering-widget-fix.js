/**
 * Watering Widget Fix
 * Verhindert das Zurücksetzen der Werte nach der Berechnung
 */

// Override der calculateWatering Methode
const originalCalculateWatering = WateringWidget.prototype.calculateWatering;

WateringWidget.prototype.calculateWatering = function() {
    // Aktuelle Werte speichern
    const currentWaterAmount = this.waterAmount;
    const currentPotSize = this.potSize;
    
    // Ursprüngliche Methode aufrufen
    const result = originalCalculateWatering.call(this);
    
    // Nach der Berechnung die Werte wiederherstellen
    setTimeout(() => {
        if (this.waterAmount !== currentWaterAmount) {
            this.waterAmount = currentWaterAmount;
            const waterAmountInput = document.getElementById('water-amount-input');
            if (waterAmountInput) {
                waterAmountInput.value = currentWaterAmount;
            }
        }
        
        if (this.potSize !== currentPotSize) {
            this.potSize = currentPotSize;
            const potSizeInput = document.getElementById('pot-size-input');
            if (potSizeInput) {
                potSizeInput.value = currentPotSize;
            }
        }
    }, 100);
    
    return result;
};

// Override der loadSavedSettings Methode
const originalLoadSavedSettings = WateringWidget.prototype.loadSavedSettings;

WateringWidget.prototype.loadSavedSettings = function() {
    // Nur laden, wenn keine Berechnung gerade stattgefunden hat
    if (!this.justCalculated) {
        return originalLoadSavedSettings.call(this);
    }
};

 