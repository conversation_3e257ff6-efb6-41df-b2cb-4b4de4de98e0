/**
 * Pot Size Autocomplete Module
 * Enhanced autocomplete functionality for pot size input
 */

class PotSizeAutocomplete {
    constructor() {
        this.init();
    }
    
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupAutocomplete();
        });
    }
    
    setupAutocomplete() {
        const potSizeInput = document.getElementById('plant-pot-size');
        if (!potSizeInput) return;
        
        // Add input event listener for real-time suggestions
        potSizeInput.addEventListener('input', (e) => {
            this.showSuggestions(e.target.value);
        });
        
        // Add focus event to show all suggestions
        potSizeInput.addEventListener('focus', (e) => {
            this.showSuggestions(e.target.value);
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.pot-size-container')) {
                this.hideSuggestions();
            }
        });
    }
    
    showSuggestions(inputValue) {
        const potSizeInput = document.getElementById('plant-pot-size');
        const suggestionsContainer = this.getOrCreateSuggestionsContainer();
        const container = potSizeInput.closest('.pot-size-container');
        
        // Get saved values from datalist
        const datalist = document.getElementById('pot-size-suggestions');
        const savedValues = Array.from(datalist.options).map(option => option.value);
        
        // Filter suggestions based on input
        const filteredSuggestions = savedValues.filter(value => 
            value.toLowerCase().includes(inputValue.toLowerCase())
        );
        
        // Show suggestions
        if (filteredSuggestions.length > 0 || inputValue.trim() !== '') {
            suggestionsContainer.innerHTML = '';
            
            // Add filtered saved values
            filteredSuggestions.forEach(suggestion => {
                const suggestionElement = document.createElement('div');
                suggestionElement.className = 'suggestion-item';
                suggestionElement.textContent = suggestion;
                suggestionElement.addEventListener('click', () => {
                    potSizeInput.value = suggestion;
                    this.hideSuggestions();
                });
                suggestionsContainer.appendChild(suggestionElement);
            });
            
            // Add current input as "new value" if it's not empty and not in saved values
            if (inputValue.trim() !== '' && !savedValues.includes(inputValue.trim())) {
                const newValueElement = document.createElement('div');
                newValueElement.className = 'suggestion-item new-value';
                newValueElement.innerHTML = `<i class="bi bi-plus-circle"></i> "${inputValue}" (neu)`;
                newValueElement.addEventListener('click', () => {
                    potSizeInput.value = inputValue.trim();
                    this.hideSuggestions();
                });
                suggestionsContainer.appendChild(newValueElement);
            }
            
            suggestionsContainer.style.display = 'block';
            if (container) {
                container.classList.add('active');
            }
        } else {
            this.hideSuggestions();
        }
    }
    
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('pot-size-suggestions-container');
        const potSizeInput = document.getElementById('plant-pot-size');
        const container = potSizeInput?.closest('.pot-size-container');
        
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
        
        if (container) {
            container.classList.remove('active');
        }
    }
    
    getOrCreateSuggestionsContainer() {
        let container = document.getElementById('pot-size-suggestions-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'pot-size-suggestions-container';
            container.className = 'suggestions-container';
            
            const potSizeInput = document.getElementById('plant-pot-size');
            const parentContainer = potSizeInput.closest('.mb-3');
            
            if (parentContainer) {
                parentContainer.classList.add('pot-size-container');
                parentContainer.appendChild(container);
            }
        }
        
        return container;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new PotSizeAutocomplete();
});

// Export for use in other modules
window.PotSizeAutocomplete = PotSizeAutocomplete; 