/* ===== NAVIGATION LAYOUT ===== */

.navbar {
  background: var(--background) !important;
  color: var(--text) !important;
  border: none !important;
  border-bottom: 1.5px solid var(--card-border) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: 700;
  color: var(--accent) !important;
  text-decoration: none;
}

.navbar-brand:hover {
  color: var(--accent-dark) !important;
  text-decoration: none;
}

.nav-link {
  font-weight: 500;
  color: var(--text) !important;
  transition: color 0.2s ease;
}

.nav-link.active,
.nav-link:focus,
.nav-link:hover {
  color: var(--accent) !important;
}

/* Theme Toggle */
.theme-toggle {
  background: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
  color: var(--text) !important;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.theme-toggle:hover,
.theme-toggle:focus {
  background-color: var(--accent) !important;
  color: white !important;
} 