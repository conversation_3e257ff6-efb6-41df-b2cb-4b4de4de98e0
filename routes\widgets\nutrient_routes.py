"""
Nährstoff-Diagnose API Routes
Erweiterte Analyse und Diagnose für EC/pH-Werte mit Strain- und Substrat-spezifischen Anpassungen
"""

from flask import Blueprint, request, jsonify
import json
import os
from datetime import datetime
import sqlite3

nutrient_routes = Blueprint('nutrient_routes', __name__)

def load_nutrient_guidelines():
    """Lädt die Nährstoff-Guidelines aus der JSON-Datei"""
    try:
        guidelines_path = os.path.join('static', 'data', 'nutrient-guidelines.json')
        with open(guidelines_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('nutrientGuidelines', {})
    except Exception as e:
        print(f"Fehler beim Laden der Nährstoff-Guidelines: {e}")
        return {}

def get_plant_data(plant_id):
    """Holt Pflanzen-Daten aus der Datenbank"""
    try:
        conn = sqlite3.connect('grow_diary_basic.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, plant_name, strain_type, substrate, created_at, note
            FROM plants 
            WHERE id = ?
        """, (plant_id,))
        
        plant = cursor.fetchone()
        if plant:
            return {
                'id': plant[0],
                'plant_name': plant[1],
                'strain_type': plant[2] or 'photoperiodic',
                'substrate': plant[3] or 'soil',
                'created_at': plant[4],
                'notes': plant[5]
            }
        return None
    except Exception as e:
        print(f"Fehler beim Laden der Pflanzen-Daten: {e}")
        return None
    finally:
        conn.close()

def get_nutrient_history(plant_id, limit=10):
    """Holt Nährstoff-Historie aus der Datenbank"""
    try:
        conn = sqlite3.connect('grow_diary_basic.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, entry_date, ec_input, ec_drain, ph_input, ph_drain, 
                   substrate, phase, notes
            FROM nutrient_entries 
            WHERE plant_id = ? 
            ORDER BY entry_date DESC 
            LIMIT ?
        """, (plant_id, limit))
        
        entries = cursor.fetchall()
        return [{
            'id': entry[0],
            'entry_date': entry[1],
            'ec_input': entry[2],
            'ec_drain': entry[3],
            'ph_input': entry[4],
            'ph_drain': entry[5],
            'substrate': entry[6],
            'phase': entry[7],
            'notes': entry[8]
        } for entry in entries]
    except Exception as e:
        print(f"Fehler beim Laden der Nährstoff-Historie: {e}")
        return []
    finally:
        conn.close()

def analyze_nutrient_values(input_ec, input_ph, drain_ec, drain_ph, phase, strain_type, substrate):
    """Führt eine vollständige Nährstoff-Analyse durch"""
    guidelines = load_nutrient_guidelines()
    
    # Phase-spezifische Guidelines finden
    phase_guideline = None
    for p in guidelines.get('phases', []):
        if p['phase'] == phase:
            phase_guideline = p
            break
    
    if not phase_guideline:
        return {
            'error': f'Keine Guidelines für Phase {phase} gefunden'
        }
    
    # Strain-spezifische Anpassungen
    # Autoflower benötigen weniger Nährstoffe
    strain_adjustment = {
        'ecReductionFactor': 0.8 if 'auto' in strain_type.lower() else 1.0
    }
    
    # EC/pH-Bereiche für aktuelles Substrat
    # Verwende ecTarget und phTarget aus der JSON-Struktur
    ec_target = phase_guideline.get('ecTarget', {})
    ph_target = phase_guideline.get('phTarget', {})
    
    # Strain-spezifische EC-Werte
    strain_key = 'autoflower' if 'auto' in strain_type.lower() else 'photoperiod'
    ec_value = ec_target.get(strain_key, 1.0)
    
    # Substrat-spezifische pH-Werte
    ph_value = ph_target.get(substrate, 6.2)
    
    # Bereiche definieren (mit Toleranz)
    ec_range = {'min': ec_value * 0.8, 'max': ec_value * 1.2}
    ph_range = {'min': ph_value - 0.3, 'max': ph_value + 0.3}
    
    # Strain-spezifische EC-Anpassung
    adjusted_ec_min = ec_range['min'] * strain_adjustment['ecReductionFactor']
    adjusted_ec_max = ec_range['max'] * strain_adjustment['ecReductionFactor']
    
    # Analyse durchführen
    analysis = {
        'ec_status': 'optimal',
        'ph_status': 'optimal',
        'alerts': [],
        'recommendations': [],
        'warnings': [],
        'targets': {
            'ec': {'min': adjusted_ec_min, 'max': adjusted_ec_max},
            'ph': ph_range
        }
    }
    
    # EC-Analyse
    if input_ec < adjusted_ec_min:
        analysis['ec_status'] = 'niedrig'
        analysis['alerts'].append('EC-Zuführung zu niedrig')
        analysis['recommendations'].append('EC-Zuführung erhöhen - langsam steigern')
    elif input_ec > adjusted_ec_max:
        analysis['ec_status'] = 'hoch'
        analysis['alerts'].append('EC-Zuführung zu hoch')
        analysis['recommendations'].append('EC-Zuführung reduzieren oder spülen')
    
    # pH-Analyse
    if input_ph < ph_range['min']:
        analysis['ph_status'] = 'niedrig'
        analysis['alerts'].append('pH-Zuführung zu niedrig')
        analysis['recommendations'].append('pH-Wert erhöhen - pH-Up verwenden')
    elif input_ph > ph_range['max']:
        analysis['ph_status'] = 'hoch'
        analysis['alerts'].append('pH-Zuführung zu hoch')
        analysis['recommendations'].append('pH-Wert senken - pH-Down verwenden')
    
    # Drain-Analyse (falls verfügbar)
    if drain_ec is not None:
        drain_ec_diff = drain_ec - input_ec
        if drain_ec_diff > 0.5:
            analysis['warnings'].append('Salzansammlung im Substrat - Drain-EC deutlich höher als Zuführung')
            analysis['recommendations'].append('Spülen mit pH-reguliertem Wasser bis Drain-EC < 0.6')
    
    if drain_ph is not None:
        drain_ph_diff = abs(drain_ph - input_ph)
        if drain_ph_diff > 0.5:
            analysis['warnings'].append('pH-Drift im Substrat - Drain-pH weicht stark von Zuführung ab')
            analysis['recommendations'].append('Substrat-pH stabilisieren - regelmäßiger pH-Check empfohlen')
    
    # Phase-spezifische Hinweise
    if phase_guideline.get('notes'):
        analysis['phase_notes'] = phase_guideline['notes']
    
    # Makro- und Mikronährstoffe
    if phase_guideline.get('macronutrients'):
        analysis['macronutrients'] = phase_guideline['macronutrients']
    if phase_guideline.get('micronutrients'):
        analysis['micronutrients'] = phase_guideline['micronutrients']
    
    return analysis

@nutrient_routes.route('/api/nutrients/diagnose', methods=['GET'])
def get_nutrient_diagnose():
    """GET: Lädt Nährstoff-Diagnose für eine Phase"""
    try:
        phase = request.args.get('phase', 'vegetative_middle')
        strain_type = request.args.get('strain_type', 'photoperiodic')
        substrate = request.args.get('substrate', 'soil')
        plant_id = request.args.get('plant_id')
        
        guidelines = load_nutrient_guidelines()
        
        # Phase-spezifische Guidelines finden
        phase_guideline = None
        for p in guidelines.get('phases', []):
            if p['phase'] == phase:
                phase_guideline = p
                break
        
        if not phase_guideline:
            return jsonify({'error': f'Keine Guidelines für Phase {phase} gefunden'}), 404
        
        # Strain-spezifische Anpassungen
        # Autoflower benötigen weniger Nährstoffe
        strain_adjustment = {
            'ecReductionFactor': 0.8 if 'auto' in strain_type.lower() else 1.0
        }
        
        # EC/pH-Bereiche für aktuelles Substrat
        # Verwende ecTarget und phTarget aus der JSON-Struktur
        ec_target = phase_guideline.get('ecTarget', {})
        ph_target = phase_guideline.get('phTarget', {})
        
        # Strain-spezifische EC-Werte
        strain_key = 'autoflower' if 'auto' in strain_type.lower() else 'photoperiod'
        ec_value = ec_target.get(strain_key, 1.0)
        
        # Substrat-spezifische pH-Werte
        ph_value = ph_target.get(substrate, 6.2)
        
        # Bereiche definieren (mit Toleranz)
        ec_range = {'min': ec_value * 0.8, 'max': ec_value * 1.2}
        ph_range = {'min': ph_value - 0.3, 'max': ph_value + 0.3}
        
        # Strain-spezifische EC-Anpassung
        adjusted_ec_min = ec_range['min'] * strain_adjustment['ecReductionFactor']
        adjusted_ec_max = ec_range['max'] * strain_adjustment['ecReductionFactor']
        
        # Pflanzen-Daten holen (falls plant_id vorhanden)
        plant_data = None
        if plant_id:
            plant_data = get_plant_data(plant_id)
        
        # Nährstoff-Historie holen (falls plant_id vorhanden)
        history = []
        if plant_id:
            history = get_nutrient_history(plant_id, 5)
        
        return jsonify({
            'phase': phase,
            'strain_type': strain_type,
            'substrate': substrate,
            'guidelines': phase_guideline,
            'strain_adjustment': strain_adjustment,
            'targets': {
                'ec': {'min': adjusted_ec_min, 'max': adjusted_ec_max},
                'ph': ph_range
            },
            'plant_data': plant_data,
            'history': history
        })
        
    except Exception as e:
        return jsonify({'error': f'Fehler bei der Nährstoff-Diagnose: {str(e)}'}), 500

@nutrient_routes.route('/api/nutrients/analyse', methods=['POST'])
def post_nutrient_analyse():
    """POST: Führt vollständige Nährstoff-Analyse durch"""
    try:
        data = request.get_json()
        
        # Pflichtfelder prüfen
        required_fields = ['input_ec', 'input_ph', 'phase', 'strain_type', 'substrate']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Pflichtfeld fehlt: {field}'}), 400
        
        # Werte extrahieren
        input_ec = float(data['input_ec'])
        input_ph = float(data['input_ph'])
        drain_ec = float(data.get('drain_ec', 0)) if data.get('drain_ec') else None
        drain_ph = float(data.get('drain_ph', 0)) if data.get('drain_ph') else None
        phase = data['phase']
        strain_type = data['strain_type']
        substrate = data['substrate']
        plant_id = data.get('plant_id')
        
        # Analyse durchführen
        analysis = analyze_nutrient_values(
            input_ec, input_ph, drain_ec, drain_ph, 
            phase, strain_type, substrate
        )
        
        # Eintrag in Datenbank speichern (falls plant_id vorhanden)
        if plant_id:
            try:
                conn = sqlite3.connect('grow_diary_basic.db')
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO nutrient_entries 
                    (plant_id, entry_date, ec_input, ec_drain, ph_input, ph_drain, 
                     substrate, phase, analysis_result, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    plant_id, datetime.now().isoformat(), input_ec, drain_ec,
                    input_ph, drain_ph, substrate, phase, 
                    json.dumps(analysis), data.get('notes', '')
                ))
                
                conn.commit()
                analysis['entry_id'] = cursor.lastrowid
                
            except Exception as e:
                print(f"Fehler beim Speichern des Nährstoff-Eintrags: {e}")
            finally:
                conn.close()
        
        return jsonify({
            'phase': phase,
            'strain': strain_type,
            'substrate': substrate,
            'input_ec': input_ec,
            'drain_ec': drain_ec,
            'input_ph': input_ph,
            'drain_ph': drain_ph,
            'analysis': analysis
        })
        
    except Exception as e:
        return jsonify({'error': f'Fehler bei der Nährstoff-Analyse: {str(e)}'}), 500

@nutrient_routes.route('/api/nutrients/history/<int:plant_id>', methods=['GET'])
def get_nutrient_history_api(plant_id):
    """GET: Lädt Nährstoff-Historie für eine Pflanze"""
    try:
        limit = request.args.get('limit', 20, type=int)
        history = get_nutrient_history(plant_id, limit)
        
        return jsonify({
            'plant_id': plant_id,
            'history': history,
            'count': len(history)
        })
        
    except Exception as e:
        return jsonify({'error': f'Fehler beim Laden der Historie: {str(e)}'}), 500 