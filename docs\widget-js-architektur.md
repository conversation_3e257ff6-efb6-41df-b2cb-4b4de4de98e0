# 🛠️ Widget JavaScript-Architektur

## 1. Widget-Manager
- Zentrale Klasse: `static/scripts/core/widget-manager.js`
- Verantwortlich für:
  - Registrierung und Initialisierung aller Widgets
  - Event-System für Kommunikation
  - Dynamisches Laden/Entladen

## 2. Widget-Registrierung
- Jedes Widget exportiert eine Klasse (z. B. `LightingWidget`)
- Der Widget-Manager sucht nach DOM-Elementen mit passender `data-widget`-ID und initialisiert das Widget
- Beispiel:
  ```js
  // lighting-widget.js
  export class LightingWidget { ... }
  // Im HTML: <div data-widget="lighting-widget"></div>
  ```

## 3. Event-System
- Widgets können Events dispatchen (`widget:updated`, `widget:action`, ...)
- Andere Widgets oder der Manager können darauf reagieren
- Beispiel:
  ```js
  this.dispatchEvent(new CustomEvent('widget:updated', { detail: { value: 42 } }));
  ```

## 4. Erweiterungen & Debugging
- Neue Widgets einfach als neue JS/CSS/HTML-Dateien anlegen
- Für Debugging: Logging im Widget-Manager aktivieren
- Fehler werden im Widget-Manager zentral geloggt

---
*Letzte Aktualisierung: {{ "now" | date("%d.%m.%Y %H:%M") }}* 