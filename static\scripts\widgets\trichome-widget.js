/**
 * Trichom-Analyse-Widget
 * Handles trichome analysis and harvest timing functionality
 */

class TrichomeWidget {
    constructor(containerId = 'trichome-monitor-box', options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.plantId = options.plantId || this.getPlantIdFromDOM();
        this.trichomeChart = null;
        this.guidelines = null;
        
        if (this.container) {
            this.init();
        } else {
            console.warn(`Trichome Widget: Container ${containerId} nicht gefunden`);
        }
    }

    init() {

        this.loadGuidelines();
        this.loadTrichomeStatus();
        this.setupEventListeners();
    }

    getPlantIdFromDOM() {
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            return plantIdElement.getAttribute('data-plant-id');
        }
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('plant_id');
    }

    setupEventListeners() {
        // Event-Listener entfernen falls bereits vorhanden
        if (this._clickListener) {
            this.container.removeEventListener('click', this._clickListener);
        }
        if (this._inputListener) {
            this.container.removeEventListener('input', this._inputListener);
        }
        if (this._globalClickListener) {
            document.removeEventListener('click', this._globalClickListener);
        }

        // Container-spezifische Events
        this._clickListener = (e) => {
            if (e.target.id === 'new-trichome-entry-btn') {
        
                this.showTrichomeEntryModal();
            }
            if (e.target.classList.contains('trichome-delete-entry-btn')) {
                const entryId = e.target.getAttribute('data-entry-id');
                if (entryId) {
                    this.deleteTrichomeEntry(entryId);
                }
            }
            if (e.target.id === 'trichome-guidelines-btn') {
        
                this.showGuidelinesModal();
            }
        };

        this._inputListener = (e) => {
            if (e.target.classList.contains('trichome-slider')) {
                this.updateSliderValues();
            }
        };

        // Globale Event-Delegation für Modal-Buttons
        this._globalClickListener = (e) => {
            if (e.target.id === 'save-trichome-entry') {
        
                this.saveTrichomeEntry();
            }
            if (e.target.id === 'use-current-bloom-day-btn') {
                const bloomDayInput = document.querySelector('input[name="bloom_day"]');
                const strainTypeContainer = document.getElementById('strainTypeContainer');
                if (strainTypeContainer && bloomDayInput) {
                    const flowerStartDate = strainTypeContainer.getAttribute('data-flower-start');
                    if (flowerStartDate && typeof this.calculateBloomDay === 'function') {
                        const bloomDay = this.calculateBloomDay(flowerStartDate);
                        if (!isNaN(parseInt(bloomDay))) {
                            bloomDayInput.value = parseInt(bloomDay);
                        }
                    }
                }
            }
        };

        // Event-Listener hinzufügen
        this.container.addEventListener('click', this._clickListener);
        this.container.addEventListener('input', this._inputListener);
        document.addEventListener('click', this._globalClickListener);
    }

    async loadGuidelines() {
        try {
            const response = await fetch('/api/trichomes/guidelines');
            if (response.ok) {
                const data = await response.json();
                this.guidelines = data.data;
            }
        } catch (error) {
            console.error('Fehler beim Laden der Guidelines:', error);
        }
    }

    async loadTrichomeStatus() {
        if (!this.plantId) return;

        // Widget-Titel
        const widgetTitle = `<h5 class="trichome-widget-title mb-3"><i class="fas fa-microscope me-2"></i>Trichom-Analyse</h5>`;

        this.container.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Lade Trichom-Analyse...</div>';

        try {
            const response = await fetch(`/api/trichomes/status/${this.plantId}`);
            const data = await response.json();

            if (!response.ok && data && data.error === 'Keine Trichom-Daten verfügbar') {
                this.container.innerHTML = `
                    ${widgetTitle}
                    <div class="alert alert-info mb-3">
                        <i class="fa fa-info-circle"></i>
                        Noch keine Trichom-Einträge vorhanden.
                    </div>
                    <div class="text-center">
                        <button id="new-trichome-entry-btn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Neue Erfassung
                        </button>
                    </div>
                `;
                return;
            }
            if (!response.ok) {
                throw new Error('Trichom-Status konnte nicht geladen werden');
            }

            this.container.innerHTML = widgetTitle;
            this.renderTrichomeWidget(data.data);
            await this.loadTrichomeHistory();

        } catch (error) {
            this.container.innerHTML = `
                ${widgetTitle}
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler beim Laden der Trichom-Analyse: ${error.message}
                </div>
            `;
        }
    }

    renderTrichomeWidget(data) {
        const currentStatus = data.current_trichome_status || null;
        const maturity = data.maturity_analysis || { score: 0, description: 'Keine Daten vorhanden' };
        const harvest = data.harvest_recommendation || { recommendation: 'Keine Empfehlung', urgency: 'none' };
        const strainInfo = data.strain_specific || 'Keine Sorteninfo';

        // Aktuellen Blütetag berechnen
        const strainTypeContainer = document.getElementById('strainTypeContainer');
        let currentBloomDay = '?';
        if (strainTypeContainer) {
            const flowerStartDate = strainTypeContainer.getAttribute('data-flower-start');
            if (flowerStartDate) {
                currentBloomDay = this.calculateBloomDay(flowerStartDate);
            }
        }

        const html = `
            <div class="trichome-widget-card">
                <div class="trichome-widget-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-microscope"></i>
                        Trichomen-Analyse
                        <span class="badge bg-info ms-2">BT+${currentBloomDay}</span>
                    </div>
                    <div>
                        <button id="trichome-guidelines-btn" class="btn btn-sm btn-outline-light me-2" title="Trichom-Guidelines">
                            <i class="fas fa-book-open"></i>
                        </button>
                        <button id="new-trichome-entry-btn" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Neue Erfassung
                        </button>
                    </div>
                </div>
                
                <div class="trichome-widget-body">
                    ${this.renderCurrentStatus(currentStatus, maturity)}
                    ${this.renderHarvestRecommendation(harvest)}
                    ${this.renderStrainInfo(strainInfo)}
                    ${this.renderMaturityIndicator(maturity)}
                </div>
            </div>
        `;

        this.container.innerHTML = html;
        this.initTooltips();
    }

    renderCurrentStatus(currentStatus, maturity) {
        if (!currentStatus) {
            return `
                <div class="trichome-section-title">
                    <i class="fas fa-info-circle me-2"></i>Aktueller Status
                </div>
                <div class="trichome-section-content">
                    <p class="text-muted">Keine Trichom-Daten verfügbar</p>
                </div>
            `;
        }

        // None-Werte abfangen und auf 0 setzen
        const clear = currentStatus.clear || 0;
        const milky = currentStatus.milky || 0;
        const amber = currentStatus.amber || 0;
        
        const total = clear + milky + amber;
        const clearPct = total > 0 ? Math.round((clear / total) * 100) : 0;
        const milkyPct = total > 0 ? Math.round((milky / total) * 100) : 0;
        const amberPct = total > 0 ? Math.round((amber / total) * 100) : 0;

        return `
            <div class="trichome-section-title">
                <i class="fas fa-chart-pie me-2"></i>Aktueller Status
            </div>
            <div class="trichome-section-content">
                <div class="trichome-percentages">
                    <div class="percentage-item clear">
                        <span class="label">Klar</span>
                        <span class="value">${clearPct}%</span>
                        <div class="progress">
                            <div class="progress-bar bg-secondary" style="width: ${clearPct}%"></div>
                        </div>
                    </div>
                    <div class="percentage-item milky">
                        <span class="label">Milchig</span>
                        <span class="value">${milkyPct}%</span>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: ${milkyPct}%"></div>
                        </div>
                    </div>
                    <div class="percentage-item amber">
                        <span class="label">Bernstein</span>
                        <span class="value">${amberPct}%</span>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: ${amberPct}%"></div>
                        </div>
                    </div>
                </div>
                <div class="maturity-info mt-3">
                    <strong>Reifegrad:</strong> ${maturity.score}/100<br>
                    <strong>Status:</strong> ${maturity.description}
                </div>
            </div>
        `;
    }

    renderHarvestRecommendation(harvest) {
        if (!harvest) return '';

        const urgencyColors = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'success',
            'none': 'secondary'
        };

        const urgencyColor = urgencyColors[harvest.urgency] || 'secondary';

        return `
            <div class="trichome-section-title">
                <i class="fas fa-calendar-check me-2"></i>Ernteempfehlung
            </div>
            <div class="trichome-section-content">
                <div class="alert alert-${urgencyColor}">
                    <strong>${harvest.recommendation}</strong>
                </div>
                <div class="harvest-details">
                    <strong>Dringlichkeit:</strong> <span class="badge bg-${urgencyColor}">${harvest.urgency}</span><br>
                    <strong>Erntefenster:</strong> ${harvest.harvest_window[0]}-${harvest.harvest_window[1]} Tage<br>
                    <strong>Strain-Typ:</strong> ${harvest.strain_type}<br>
                    <strong>Phase:</strong> ${harvest.phase}
                </div>
            </div>
        `;
    }

    renderStrainInfo(strainInfo) {
        if (!strainInfo) return '';

        return `
            <div class="trichome-section-title">
                <i class="fas fa-leaf me-2"></i>Strain-spezifische Hinweise
            </div>
            <div class="trichome-section-content">
                <div class="strain-info">
                    <strong>Typ:</strong> ${strainInfo.strain_type}<br>
                    <strong>Analyse-Start:</strong> ${strainInfo.analysis_start}<br>
                    <strong>Beobachtungsintervall:</strong> ${strainInfo.observation_interval} Tage<br>
                    <strong>Mikroskop:</strong> ${strainInfo.microscope_recommendation}
                </div>
                ${strainInfo.notes && strainInfo.notes.length > 0 ? `
                    <div class="strain-notes mt-2">
                        <strong>Hinweise:</strong>
                        <ul class="mb-0">
                            ${strainInfo.notes.map(note => `<li>${note}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    renderMaturityIndicator(maturity) {
        const score = maturity.score || 0;
        let color = 'secondary';
        let icon = 'question-circle';

        if (score >= 80) {
            color = 'success';
            icon = 'check-circle';
        } else if (score >= 60) {
            color = 'warning';
            icon = 'clock';
        } else if (score >= 40) {
            color = 'info';
            icon = 'seedling';
        } else {
            color = 'secondary';
            icon = 'leaf';
        }

        return `
            <div class="trichome-section-title">
                <i class="fas fa-chart-line me-2"></i>Reifegrad
            </div>
            <div class="trichome-section-content">
                <div class="maturity-indicator">
                    <div class="progress mb-2">
                        <div class="progress-bar bg-${color}" style="width: ${score}%"></div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Reifegrad: ${score}/100</small>
                        <i class="fas fa-${icon} text-${color}"></i>
                    </div>
                </div>
            </div>
        `;
    }

    async loadTrichomeHistory() {
        if (!this.plantId) return;

        try {
            const response = await fetch(`/api/trichomes/history/${this.plantId}?days=30`);
            if (!response.ok) return;

            const data = await response.json();
            this.renderTrichomeHistory(data.data);
        } catch (error) {
            console.error('Fehler beim Laden der Historie:', error);
        }
    }

    renderTrichomeHistory(data) {
        const historySection = document.createElement('div');
        historySection.className = 'trichome-history-section';
        historySection.innerHTML = `
            <div class="trichome-section-title">
                <i class="fas fa-chart-area me-2"></i>Trichom-Historie (letzte 30 Tage)
            </div>
            <div class="trichome-section-content">
                <canvas id="trichome-history-chart" height="120"></canvas>
                <div id="trichome-entries-list" class="mt-3"></div>
            </div>
        `;

        this.container.appendChild(historySection);
        this.renderTrichomeChart(data);
        this.renderTrichomeEntriesList(data.history);
    }

    renderTrichomeChart(data) {
        if (!window.Chart || !data.history || data.history.length === 0) return;

        const chartCanvas = document.getElementById('trichome-history-chart');
        if (!chartCanvas) return;

        // Altes Chart zerstören falls vorhanden
        if (this.trichomeChart) {
            this.trichomeChart.destroy();
        }

        const labels = data.history.map(e => e.date.split('T')[0]);
        const clearData = data.history.map(e => e.clear_percentage);
        const milkyData = data.history.map(e => e.milky_percentage);
        const amberData = data.history.map(e => e.amber_percentage);

        this.trichomeChart = new window.Chart(chartCanvas.getContext('2d'), {
            type: 'line',
            data: {
                labels: labels.reverse(),
                datasets: [
                    {
                        label: 'Klar (%)',
                        data: clearData.reverse(),
                        borderColor: '#6c757d',
                        backgroundColor: 'rgba(108, 117, 125, 0.1)',
                        tension: 0.3,
                        pointRadius: 2
                    },
                    {
                        label: 'Milchig (%)',
                        data: milkyData.reverse(),
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.3,
                        pointRadius: 2
                    },
                    {
                        label: 'Bernstein (%)',
                        data: amberData.reverse(),
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.3,
                        pointRadius: 2
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: true }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: { display: true, text: 'Prozent (%)' }
                    }
                }
            }
        });
    }

    renderTrichomeEntriesList(entries) {
        const entriesListContainer = document.getElementById('trichome-entries-list');
        if (!entriesListContainer) return;

        if (!entries || entries.length === 0) {
            entriesListContainer.innerHTML = '<p class="text-muted text-center">Keine Trichom-Einträge vorhanden</p>';
            return;
        }

        const entriesHtml = entries.map(entry => {
            const date = entry.date ? entry.date.split('T')[0] : 'Unbekannt';
            const time = entry.date ? entry.date.split('T')[1]?.split('.')[0] : '';
            
            return `
                <div class="trichome-entry-item d-flex justify-content-between align-items-center p-2 border-bottom">
                    <div class="trichome-entry-info">
                        <small class="text-muted">${date} ${time}</small><br>
                        <strong>Klar: ${entry.clear_percentage}% | Milchig: ${entry.milky_percentage}% | Bernstein: ${entry.amber_percentage}%</strong><br>
                        <small class="text-muted">Standort: ${entry.location} | BT+${entry.bloom_day}</small>
                    </div>
                    <button 
                        class="btn btn-sm btn-outline-danger trichome-delete-entry-btn" 
                        data-entry-id="${entry.id}"
                        title="Trichom-Eintrag löschen"
                    >
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
        }).join('');

        entriesListContainer.innerHTML = `
            <div class="trichome-entries-container" style="max-height:200px;overflow-y:auto;">
                ${entriesHtml}
            </div>
        `;
    }

    showTrichomeEntryModal() {
        // Blüte-Start-Datum aus dem DOM holen
        let flowerStartDate = null;
        const strainTypeContainer = document.getElementById('strainTypeContainer');
        if (strainTypeContainer) {
            flowerStartDate = strainTypeContainer.getAttribute('data-flower-start');
        }

        // Aktuellen Blütetag berechnen (wenn möglich)
        let currentBloomDay = null;
        if (typeof this.calculateBloomDay === 'function' && flowerStartDate) {
            const bloomDay = this.calculateBloomDay(flowerStartDate);
            if (!isNaN(parseInt(bloomDay))) {
                currentBloomDay = parseInt(bloomDay);
            }
        }

        const bloomDayButton = currentBloomDay !== null ? `
            <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="use-current-bloom-day-btn">
                Aktuellen Blütetag übernehmen (${currentBloomDay})
            </button>
        ` : '';

        const modalHtml = `
            <div class="modal fade" id="trichome-entry-modal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-microscope"></i> Trichomen-Erfassung
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="trichome-entry-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Standort</label>
                                            <select class="form-select" name="location" required>
                                                <option value="top_buds">Buds oben</option>
                                                <option value="middle_buds">Buds mitte</option>
                                                <option value="bottom_buds">Buds unten</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3 d-flex align-items-end">
                                            <div style="flex:1">
                                                <label class="form-label">Blütetag (optional)</label>
                                                <input type="number" class="form-control" name="bloom_day" min="0">
                                            </div>
                                            ${bloomDayButton}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Trichome-Verteilung</label>
                                    <div class="trichome-sliders">
                                        <div class="slider-group mb-3">
                                            <label>Klar (%) <span class="slider-value">30</span></label>
                                            <input type="range" class="form-range trichome-slider" name="clear_percentage" 
                                                   min="0" max="100" value="30" data-target="clear">
                                        </div>
                                        <div class="slider-group mb-3">
                                            <label>Milchig (%) <span class="slider-value">60</span></label>
                                            <input type="range" class="form-range trichome-slider" name="milky_percentage" 
                                                   min="0" max="100" value="60" data-target="milky">
                                        </div>
                                        <div class="slider-group mb-3">
                                            <label>Bernstein (%) <span class="slider-value">10</span></label>
                                            <input type="range" class="form-range trichome-slider" name="amber_percentage" 
                                                   min="0" max="100" value="10" data-target="amber">
                                        </div>
                                    </div>
                                    <div class="total-validation">
                                        <small class="text-muted">Gesamt: <span id="total-percentage">100%</span></small>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Bemerkungen</label>
                                    <textarea class="form-control" name="notes" rows="3" 
                                              placeholder="Beobachtungen, Besonderheiten..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Tags</label>
                                    <input type="text" class="form-control" name="tags" 
                                           placeholder="Tags durch Komma getrennt (z.B. fett, farbenfroh)">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
                            <button type="button" class="btn btn-primary" id="save-trichome-entry">
                                <i class="fas fa-save"></i> Speichern
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modal = new bootstrap.Modal(document.getElementById('trichome-entry-modal'));
        modal.show();

        // Event-Listener für Speichern-Button robust binden
        const saveBtn = document.getElementById('save-trichome-entry');
        if (saveBtn) {
            // Vorherige Listener entfernen (falls mehrfach geöffnet)
            saveBtn.onclick = null;
            saveBtn.removeEventListener('click', this.saveTrichomeEntry);
            
            // Neuen Event-Listener setzen
            const saveHandler = () => {
                this.saveTrichomeEntry();
            };
            
            saveBtn.addEventListener('click', saveHandler);
        } else {
            console.error('[Trichom-Widget] Speichern-Button nicht gefunden!');
        }

        // Event-Listener für Blütetag-Button
        if (currentBloomDay !== null) {
            const btn = document.getElementById('use-current-bloom-day-btn');
            if (btn) {
                btn.addEventListener('click', () => {
                    const bloomDayInput = document.querySelector('input[name="bloom_day"]');
                    if (bloomDayInput) {
                        bloomDayInput.value = currentBloomDay;
                    }
                });
            }
        }

        // Slider-Event-Listener für Live-Updates im Modal
        const modalSliders = document.querySelectorAll('#trichome-entry-modal .trichome-slider');
        modalSliders.forEach(slider => {
            slider.addEventListener('input', () => {
                this.updateSliderValues();
            });
        });

        // Modal nach dem Schließen entfernen und Backdrop fixen
        document.getElementById('trichome-entry-modal').addEventListener('hidden.bs.modal', () => {
            document.getElementById('trichome-entry-modal').remove();
            document.body.classList.remove('modal-open');
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(bd => bd.remove());
        });
    }

    updateSliderValues() {
        const sliders = document.querySelectorAll('.trichome-slider');
        let total = 0;

        sliders.forEach(slider => {
            const value = parseInt(slider.value);
            const valueSpan = slider.parentNode.querySelector('.slider-value');
            if (valueSpan) {
                valueSpan.textContent = value;
            }
            total += value;
        });

        const totalSpan = document.getElementById('total-percentage');
        if (totalSpan) {
            totalSpan.textContent = total + '%';
            totalSpan.className = total === 100 ? 'text-success' : 'text-danger';
        }
    }

    async saveTrichomeEntry() {
        // Form-Status prüfen
        const form = document.getElementById('trichome-entry-form');
        if (!form) {
            console.error('[Trichom-Widget] Form nicht gefunden!');
            this.showNotification('Formular nicht gefunden', 'error');
            return;
        }
        
        const formData = new FormData(form);
        
        // Slider-Werte direkt aus DOM auslesen (da FormData bei Range-Inputs problematisch sein kann)
        const clearSlider = document.querySelector('input[name="clear_percentage"]');
        const milkySlider = document.querySelector('input[name="milky_percentage"]');
        const amberSlider = document.querySelector('input[name="amber_percentage"]');
        
        // Location direkt aus DOM auslesen
        const locationSelect = document.querySelector('select[name="location"]');
        const location = locationSelect ? locationSelect.value : 'top_buds'; // Fallback
        
        const entryData = {
            plant_id: this.plantId,
            location: location,
            clear_percentage: clearSlider ? parseInt(clearSlider.value) : 0,
            milky_percentage: milkySlider ? parseInt(milkySlider.value) : 0,
            amber_percentage: amberSlider ? parseInt(amberSlider.value) : 0,
            notes: formData.get('notes') || '',
            tags: formData.get('tags') ? formData.get('tags').split(',').map(t => t.trim()) : []
        };

        // Blütetag direkt aus DOM auslesen (da FormData bei number-Inputs problematisch sein kann)
        const bloomDayInput = document.querySelector('input[name="bloom_day"]');
        if (bloomDayInput && bloomDayInput.value) {
            entryData.bloom_day = parseInt(bloomDayInput.value);
        }

        try {
            const response = await fetch('/api/trichomes/entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(entryData)
            });

            const result = await response.json();

            if (result.success) {
                // Modal schließen
                bootstrap.Modal.getInstance(document.getElementById('trichome-entry-modal')).hide();
                
                // Widget neu laden
                await this.loadTrichomeStatus();
                
                // Erfolgsmeldung
                this.showNotification('Trichom-Eintrag erfolgreich erstellt', 'success');
            } else {
                this.showNotification(`Fehler: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Fehler beim Speichern:', error);
            this.showNotification('Fehler beim Speichern des Eintrags', 'error');
        }
    }

    async deleteTrichomeEntry(entryId) {
        if (!confirm('Möchtest Du diesen Trichom-Eintrag wirklich löschen?')) {
            return;
        }

        try {
            const response = await fetch(`/api/trichomes/entry/${entryId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                await this.loadTrichomeStatus();
                this.showNotification('Trichom-Eintrag erfolgreich gelöscht', 'success');
            } else {
                this.showNotification(`Fehler: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Fehler beim Löschen:', error);
            this.showNotification('Fehler beim Löschen des Eintrags', 'error');
        }
    }

    showGuidelinesModal() {
        if (!this.guidelines) {
            this.showNotification('Guidelines konnten nicht geladen werden', 'error');
            return;
        }

        // Bestehendes Modal entfernen falls vorhanden
        const existingModal = document.getElementById('trichome-guidelines-modal');
        if (existingModal) {
            const existingBootstrapModal = bootstrap.Modal.getInstance(existingModal);
            if (existingBootstrapModal) {
                existingBootstrapModal.dispose();
            }
            existingModal.remove();
        }

        // Backdrop manuell entfernen falls vorhanden
        const existingBackdrop = document.querySelector('.modal-backdrop');
        if (existingBackdrop) {
            existingBackdrop.remove();
        }

        const modalHtml = `
            <div class="modal fade" id="trichome-guidelines-modal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-book-open"></i> Trichom-Guidelines
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                        </div>
                        <div class="modal-body">
                            ${this.renderGuidelinesContent()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modalElement = document.getElementById('trichome-guidelines-modal');
        const modal = new bootstrap.Modal(modalElement);
        
        // Event-Listener für sauberes Schließen
        modalElement.addEventListener('hidden.bs.modal', () => {
    
            modal.dispose();
            modalElement.remove();
            
            // Backdrop sicher entfernen
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            // Body-Klassen zurücksetzen
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });

        modal.show();
    }

    renderGuidelinesContent() {
        if (!this.guidelines) return '<p>Guidelines nicht verfügbar</p>';

        const reifestufen = this.guidelines.reifestufen || [];
        const faustregeln = this.guidelines.faustregeln || [];
        const mikroskopTipps = this.guidelines.mikroskopTipps || [];

        return `
            <div class="guidelines-content">
                <h6><i class="fas fa-list-check me-2"></i>Reifestufen</h6>
                <div class="row">
                    ${reifestufen.map(stufe => `
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <strong>${stufe.stufe}</strong>
                                </div>
                                <div class="card-body">
                                    <p class="small">${stufe.beschreibung}</p>
                                    <p class="small"><strong>Wirkung:</strong> ${stufe.wirkung}</p>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                
                <hr>
                
                <h6><i class="fas fa-lightbulb me-2"></i>Faustregeln</h6>
                <ul>
                    ${faustregeln.map(regel => `<li>${regel}</li>`).join('')}
                </ul>
                
                <hr>
                
                <h6><i class="fas fa-microscope me-2"></i>Mikroskop-Tipps</h6>
                <ul>
                    ${mikroskopTipps.map(tipp => `<li>${tipp}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    calculateBloomDay(dateString) {
        // Einfache Berechnung des Blütetags
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    initTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    destroy() {
        if (this.trichomeChart) {
            this.trichomeChart.destroy();
            this.trichomeChart = null;
        }
        
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Global verfügbar machen
window.TrichomeWidget = TrichomeWidget; 