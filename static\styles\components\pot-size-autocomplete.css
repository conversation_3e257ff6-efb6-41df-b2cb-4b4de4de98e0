/**
 * <PERSON>t Size Autocomplete Styles
 */

.pot-size-container {
    position: relative;
}

.pot-size-container::after {
    content: '▼'; /* Unicode Pfeil nach unten */
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
    z-index: 10;
    transition: transform 0.2s ease;
    font-size: 12px;
    font-weight: bold;
}

.pot-size-container.active::after {
    transform: translateY(-50%) rotate(180deg);
}

.pot-size-container input {
    padding-right: 35px; /* Platz für das Pfeil-Icon */
}

.suggestions-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.suggestion-item {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    border-bottom: 1px solid var(--border);
    transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background-color: var(--primary-light);
}

.suggestion-item.new-value {
    color: var(--primary);
    font-style: italic;
}

.suggestion-item.new-value i {
    margin-right: var(--spacing-xs);
}

/* Dark mode adjustments */
[data-theme="dark"] .suggestions-container {
    background: var(--surface-dark);
    border-color: var(--border-dark);
}

[data-theme="dark"] .suggestion-item:hover {
    background-color: var(--primary-dark);
} 