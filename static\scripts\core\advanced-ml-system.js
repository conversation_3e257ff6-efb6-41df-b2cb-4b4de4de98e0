/**
 * 🤖 Advanced Machine Learning System
 * 
 * Erweiterte ML-Features mit Deep Learning, Bilderkennung
 * und automatischer Anomalie-Erkennung
 */

class AdvancedMLSystem {
    constructor() {
        this.deepLearningModels = new Map();
        this.imageRecognitionModels = new Map();
        this.anomalyDetectors = new Map();
        this.patternRecognizers = new Map();
        this.optimizationEngines = new Map();
        this.trainingData = new Map();
        
        // ML-Konfiguration
        this.mlConfig = {
            modelUpdateInterval: 24 * 60 * 60 * 1000, // 24 Stunden
            trainingDataThreshold: 100, // Mindestanzahl Datenpunkte
            confidenceThreshold: 0.8,
            anomalyThreshold: 0.7,
            imageQualityThreshold: 0.6
        };
        
        // TensorFlow.js Integration (falls verfügbar)
        this.tf = null;
        this.setupTensorFlow();
        
        console.log('🤖 AdvancedMLSystem: Initialisiert');
    }
    
    /**
     * TensorFlow.js Setup
     */
    setupTensorFlow() {
        // Prüfen ob TensorFlow.js verfügbar ist
        if (typeof tf !== 'undefined') {
            this.tf = tf;
            console.log('🤖 AdvancedMLSystem: TensorFlow.js verfügbar');
        } else {
            console.log('🤖 AdvancedMLSystem: TensorFlow.js nicht verfügbar, verwende vereinfachte ML-Modelle');
        }
    }
    
    /**
     * Deep Learning Modell für Wachstumsprognose
     */
    async createDeepLearningModel(plantId, plantData) {
        try {
            if (!this.tf) {
                return this.createSimplifiedModel(plantId, plantData);
            }
            
            // LSTM-basiertes Modell für Zeitreihen-Prognose
            const model = this.tf.sequential({
                layers: [
                    this.tf.layers.lstm({
                        units: 50,
                        returnSequences: true,
                        inputShape: [7, 5] // 7 Tage, 5 Features
                    }),
                    this.tf.layers.dropout(0.2),
                    this.tf.layers.lstm({
                        units: 30,
                        returnSequences: false
                    }),
                    this.tf.layers.dropout(0.2),
                    this.tf.layers.dense({
                        units: 10,
                        activation: 'relu'
                    }),
                    this.tf.layers.dense({
                        units: 2, // Höhe und PPFD
                        activation: 'linear'
                    })
                ]
            });
            
            // Modell kompilieren
            model.compile({
                optimizer: this.tf.train.adam(0.001),
                loss: 'meanSquaredError',
                metrics: ['mae']
            });
            
            // Modell speichern
            this.deepLearningModels.set(plantId, {
                model: model,
                plantData: plantData,
                lastTrained: new Date().toISOString(),
                accuracy: 0.0
            });
            
            console.log('🤖 AdvancedMLSystem: Deep Learning Modell erstellt für Pflanze', plantId);
            
            return model;
            
        } catch (error) {
            console.error('🤖 AdvancedMLSystem: Fehler beim Erstellen des Deep Learning Modells:', error);
            return this.createSimplifiedModel(plantId, plantData);
        }
    }
    
    /**
     * Vereinfachtes Modell (Fallback)
     */
    createSimplifiedModel(plantId, plantData) {
        const model = {
            type: 'simplified_ml',
            plantId: plantId,
            features: ['height', 'ppfd', 'temperature', 'humidity', 'phase_day'],
            weights: this.initializeWeights(5),
            bias: 0.1,
            learningRate: 0.01
        };
        
        this.deepLearningModels.set(plantId, {
            model: model,
            plantData: plantData,
            lastTrained: new Date().toISOString(),
            accuracy: 0.0
        });
        
        return model;
    }
    
    /**
     * Gewichte initialisieren
     */
    initializeWeights(featureCount) {
        const weights = [];
        for (let i = 0; i < featureCount; i++) {
            weights.push(Math.random() * 2 - 1); // -1 bis 1
        }
        return weights;
    }
    
    /**
     * Modell trainieren
     */
    async trainModel(plantId, trainingData) {
        try {
            const modelData = this.deepLearningModels.get(plantId);
            if (!modelData) {
                console.warn('🤖 AdvancedMLSystem: Kein Modell für Pflanze', plantId);
                return false;
            }
            
            if (trainingData.length < this.mlConfig.trainingDataThreshold) {
                console.log('🤖 AdvancedMLSystem: Unzureichende Trainingsdaten für Pflanze', plantId);
                return false;
            }
            
            const model = modelData.model;
            
            if (this.tf && model.layers) {
                // TensorFlow.js Training
                await this.trainTensorFlowModel(model, trainingData);
            } else {
                // Vereinfachtes Training
                this.trainSimplifiedModel(model, trainingData);
            }
            
            // Modell-Aktualisierung
            modelData.lastTrained = new Date().toISOString();
            modelData.accuracy = this.calculateModelAccuracy(model, trainingData);
            
            console.log('🤖 AdvancedMLSystem: Modell trainiert für Pflanze', plantId, 'Genauigkeit:', modelData.accuracy);
            
            return true;
            
        } catch (error) {
            console.error('🤖 AdvancedMLSystem: Fehler beim Training:', error);
            return false;
        }
    }
    
    /**
     * TensorFlow.js Modell trainieren
     */
    async trainTensorFlowModel(model, trainingData) {
        // Daten vorbereiten
        const { inputs, outputs } = this.prepareTensorFlowData(trainingData);
        
        // Training
        await model.fit(inputs, outputs, {
            epochs: 50,
            batchSize: 32,
            validationSplit: 0.2,
            callbacks: {
                onEpochEnd: (epoch, logs) => {
                    if (epoch % 10 === 0) {
                        console.log(`🤖 Epoch ${epoch}: loss = ${logs.loss.toFixed(4)}, mae = ${logs.mae.toFixed(4)}`);
                    }
                }
            }
        });
    }
    
    /**
     * TensorFlow.js Daten vorbereiten
     */
    prepareTensorFlowData(trainingData) {
        const sequences = [];
        const targets = [];
        
        // 7-Tage Sequenzen erstellen
        for (let i = 6; i < trainingData.length; i++) {
            const sequence = trainingData.slice(i - 6, i + 1);
            const features = sequence.map(day => [
                day.height || 0,
                day.ppfd || 0,
                day.temperature || 25,
                day.humidity || 60,
                day.phase_day || 0
            ]);
            
            sequences.push(features);
            targets.push([
                trainingData[i + 1]?.height || 0,
                trainingData[i + 1]?.ppfd || 0
            ]);
        }
        
        return {
            inputs: this.tf.tensor3d(sequences),
            outputs: this.tf.tensor2d(targets)
        };
    }
    
    /**
     * Vereinfachtes Modell trainieren
     */
    trainSimplifiedModel(model, trainingData) {
        const learningRate = model.learningRate;
        
        for (let epoch = 0; epoch < 100; epoch++) {
            let totalError = 0;
            
            for (const dataPoint of trainingData) {
                // Vorhersage
                const prediction = this.predictSimplified(model, dataPoint);
                const actual = dataPoint.height || 0;
                
                // Fehler berechnen
                const error = actual - prediction;
                totalError += Math.abs(error);
                
                // Gewichte aktualisieren
                for (let i = 0; i < model.weights.length; i++) {
                    const feature = this.getFeatureValue(dataPoint, i);
                    model.weights[i] += learningRate * error * feature;
                }
                
                model.bias += learningRate * error;
            }
            
            // Früher Stopp bei geringem Fehler
            if (totalError / trainingData.length < 0.1) {
                break;
            }
        }
    }
    
    /**
     * Vereinfachte Vorhersage
     */
    predictSimplified(model, dataPoint) {
        let prediction = model.bias;
        
        for (let i = 0; i < model.weights.length; i++) {
            const feature = this.getFeatureValue(dataPoint, i);
            prediction += model.weights[i] * feature;
        }
        
        return Math.max(0, prediction);
    }
    
    /**
     * Feature-Wert extrahieren
     */
    getFeatureValue(dataPoint, featureIndex) {
        const features = [
            dataPoint.height || 0,
            dataPoint.ppfd || 0,
            dataPoint.temperature || 25,
            dataPoint.humidity || 60,
            dataPoint.phase_day || 0
        ];
        
        return features[featureIndex] || 0;
    }
    
    /**
     * Modell-Genauigkeit berechnen
     */
    calculateModelAccuracy(model, testData) {
        if (testData.length < 10) return 0.0;
        
        let totalError = 0;
        let predictions = 0;
        
        for (const dataPoint of testData.slice(-10)) {
            let prediction;
            
            if (this.tf && model.predict) {
                // TensorFlow.js Vorhersage
                const input = this.tf.tensor3d([[
                    [dataPoint.height || 0, dataPoint.ppfd || 0, dataPoint.temperature || 25, dataPoint.humidity || 60, dataPoint.phase_day || 0]
                ]]);
                prediction = model.predict(input).dataSync()[0];
            } else {
                // Vereinfachte Vorhersage
                prediction = this.predictSimplified(model, dataPoint);
            }
            
            const actual = dataPoint.height || 0;
            totalError += Math.abs(actual - prediction);
            predictions++;
        }
        
        const mae = totalError / predictions;
        const maxHeight = Math.max(...testData.map(d => d.height || 0));
        
        return Math.max(0, 1 - (mae / maxHeight));
    }
    
    /**
     * Bilderkennung für Pflanzenzustand
     */
    async analyzePlantImage(plantId, imageData) {
        try {
            // Vereinfachte Bildanalyse (ohne TensorFlow.js)
            const analysis = this.simplifiedImageAnalysis(imageData);
            
            // Bilderkennungs-Modell speichern
            this.imageRecognitionModels.set(plantId, {
                lastAnalysis: new Date().toISOString(),
                results: analysis,
                confidence: analysis.confidence
            });
            
            return analysis;
            
        } catch (error) {
            console.error('🤖 AdvancedMLSystem: Fehler bei Bildanalyse:', error);
            return null;
        }
    }
    
    /**
     * Vereinfachte Bildanalyse
     */
    simplifiedImageAnalysis(imageData) {
        // Simulierte Bildanalyse
        const analysis = {
            plantHealth: Math.random() * 0.3 + 0.7, // 70-100%
            stressIndicators: [],
            growthStage: this.detectGrowthStage(imageData),
            leafColor: this.analyzeLeafColor(imageData),
            trichomeDevelopment: this.analyzeTrichomes(imageData),
            confidence: Math.random() * 0.2 + 0.8 // 80-100%
        };
        
        // Stress-Indikatoren erkennen
        if (analysis.plantHealth < 0.8) {
            analysis.stressIndicators.push('Reduzierte Vitalität');
        }
        
        if (analysis.leafColor.yellowing > 0.3) {
            analysis.stressIndicators.push('Gelbverfärbung');
        }
        
        return analysis;
    }
    
    /**
     * Wachstumsstadium erkennen
     */
    detectGrowthStage(imageData) {
        // Simulierte Wachstumsstadium-Erkennung
        const stages = ['seedling', 'vegetative_early', 'vegetative_middle', 'vegetative_late', 'flowering_early', 'flowering_middle', 'flowering_late', 'flush'];
        const randomStage = stages[Math.floor(Math.random() * stages.length)];
        
        return {
            stage: randomStage,
            confidence: Math.random() * 0.3 + 0.7
        };
    }
    
    /**
     * Blattfarbe analysieren
     */
    analyzeLeafColor(imageData) {
        return {
            green: Math.random() * 0.3 + 0.7,
            yellowing: Math.random() * 0.2,
            browning: Math.random() * 0.1,
            confidence: Math.random() * 0.2 + 0.8
        };
    }
    
    /**
     * Trichome analysieren
     */
    analyzeTrichomes(imageData) {
        return {
            clear: Math.random() * 0.4,
            milky: Math.random() * 0.4,
            amber: Math.random() * 0.2,
            density: Math.random() * 0.3 + 0.7,
            confidence: Math.random() * 0.2 + 0.8
        };
    }
    
    /**
     * Anomalie-Erkennung
     */
    async detectAnomalies(plantId, sensorData) {
        try {
            const anomalies = [];
            
            // PPFD-Anomalien
            const ppfdAnomalies = this.detectPPFDAnomalies(sensorData.ppfd);
            anomalies.push(...ppfdAnomalies);
            
            // Temperatur-Anomalien
            const tempAnomalies = this.detectTemperatureAnomalies(sensorData.temperature);
            anomalies.push(...tempAnomalies);
            
            // Luftfeuchtigkeit-Anomalien
            const humidityAnomalies = this.detectHumidityAnomalies(sensorData.humidity);
            anomalies.push(...humidityAnomalies);
            
            // Wachstums-Anomalien
            const growthAnomalies = this.detectGrowthAnomalies(sensorData.growth);
            anomalies.push(...growthAnomalies);
            
            // Anomalie-Detektor speichern
            this.anomalyDetectors.set(plantId, {
                anomalies: anomalies,
                lastDetection: new Date().toISOString(),
                confidence: this.calculateAnomalyConfidence(anomalies)
            });
            
            return anomalies;
            
        } catch (error) {
            console.error('🤖 AdvancedMLSystem: Fehler bei Anomalie-Erkennung:', error);
            return [];
        }
    }
    
    /**
     * PPFD-Anomalien erkennen
     */
    detectPPFDAnomalies(ppfdData) {
        const anomalies = [];
        
        if (!ppfdData || !Array.isArray(ppfdData)) return anomalies;
        
        const recentPPFD = ppfdData.slice(-10);
        const avgPPFD = recentPPFD.reduce((sum, val) => sum + val, 0) / recentPPFD.length;
        
        // Plötzliche Änderungen erkennen
        for (let i = 1; i < recentPPFD.length; i++) {
            const change = Math.abs(recentPPFD[i] - recentPPFD[i-1]);
            const changePercent = change / avgPPFD;
            
            if (changePercent > 0.3) { // 30% Änderung
                anomalies.push({
                    type: 'ppfd_spike',
                    severity: 'medium',
                    description: `Plötzliche PPFD-Änderung: ${changePercent.toFixed(1)}%`,
                    timestamp: new Date().toISOString(),
                    confidence: 0.8
                });
            }
        }
        
        // Zu niedrige/hohe Werte
        if (avgPPFD < 200) {
            anomalies.push({
                type: 'ppfd_too_low',
                severity: 'high',
                description: `PPFD zu niedrig: ${avgPPFD.toFixed(0)} μmol/m²/s`,
                timestamp: new Date().toISOString(),
                confidence: 0.9
            });
        } else if (avgPPFD > 1000) {
            anomalies.push({
                type: 'ppfd_too_high',
                severity: 'high',
                description: `PPFD zu hoch: ${avgPPFD.toFixed(0)} μmol/m²/s`,
                timestamp: new Date().toISOString(),
                confidence: 0.9
            });
        }
        
        return anomalies;
    }
    
    /**
     * Temperatur-Anomalien erkennen
     */
    detectTemperatureAnomalies(temperatureData) {
        const anomalies = [];
        
        if (!temperatureData || !Array.isArray(temperatureData)) return anomalies;
        
        const recentTemp = temperatureData.slice(-10);
        const avgTemp = recentTemp.reduce((sum, val) => sum + val, 0) / recentTemp.length;
        
        // Temperatur-Bereich prüfen
        if (avgTemp < 18) {
            anomalies.push({
                type: 'temperature_too_low',
                severity: 'medium',
                description: `Temperatur zu niedrig: ${avgTemp.toFixed(1)}°C`,
                timestamp: new Date().toISOString(),
                confidence: 0.8
            });
        } else if (avgTemp > 32) {
            anomalies.push({
                type: 'temperature_too_high',
                severity: 'high',
                description: `Temperatur zu hoch: ${avgTemp.toFixed(1)}°C`,
                timestamp: new Date().toISOString(),
                confidence: 0.9
            });
        }
        
        return anomalies;
    }
    
    /**
     * Luftfeuchtigkeit-Anomalien erkennen
     */
    detectHumidityAnomalies(humidityData) {
        const anomalies = [];
        
        if (!humidityData || !Array.isArray(humidityData)) return anomalies;
        
        const recentHumidity = humidityData.slice(-10);
        const avgHumidity = recentHumidity.reduce((sum, val) => sum + val, 0) / recentHumidity.length;
        
        // Luftfeuchtigkeit-Bereich prüfen
        if (avgHumidity < 40) {
            anomalies.push({
                type: 'humidity_too_low',
                severity: 'medium',
                description: `Luftfeuchtigkeit zu niedrig: ${avgHumidity.toFixed(0)}%`,
                timestamp: new Date().toISOString(),
                confidence: 0.8
            });
        } else if (avgHumidity > 80) {
            anomalies.push({
                type: 'humidity_too_high',
                severity: 'high',
                description: `Luftfeuchtigkeit zu hoch: ${avgHumidity.toFixed(0)}%`,
                timestamp: new Date().toISOString(),
                confidence: 0.9
            });
        }
        
        return anomalies;
    }
    
    /**
     * Wachstums-Anomalien erkennen
     */
    detectGrowthAnomalies(growthData) {
        const anomalies = [];
        
        if (!growthData || !Array.isArray(growthData)) return anomalies;
        
        const recentGrowth = growthData.slice(-10);
        
        // Wachstumsrate berechnen
        const growthRates = [];
        for (let i = 1; i < recentGrowth.length; i++) {
            const rate = recentGrowth[i] - recentGrowth[i-1];
            growthRates.push(rate);
        }
        
        const avgGrowthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
        
        // Negatives Wachstum
        if (avgGrowthRate < 0) {
            anomalies.push({
                type: 'negative_growth',
                severity: 'high',
                description: 'Negatives Wachstum erkannt',
                timestamp: new Date().toISOString(),
                confidence: 0.9
            });
        }
        
        // Sehr langsames Wachstum
        if (avgGrowthRate < 0.5) {
            anomalies.push({
                type: 'slow_growth',
                severity: 'medium',
                description: 'Sehr langsames Wachstum',
                timestamp: new Date().toISOString(),
                confidence: 0.7
            });
        }
        
        return anomalies;
    }
    
    /**
     * Anomalie-Konfidenz berechnen
     */
    calculateAnomalyConfidence(anomalies) {
        if (anomalies.length === 0) return 1.0;
        
        const totalConfidence = anomalies.reduce((sum, anomaly) => sum + anomaly.confidence, 0);
        return totalConfidence / anomalies.length;
    }
    
    /**
     * Muster-Erkennung für Optimierung
     */
    async recognizePatterns(plantId, historicalData) {
        try {
            const patterns = [];
            
            // Wachstums-Muster
            const growthPatterns = this.analyzeGrowthPatterns(historicalData);
            patterns.push(...growthPatterns);
            
            // Beleuchtungs-Muster
            const lightingPatterns = this.analyzeLightingPatterns(historicalData);
            patterns.push(...lightingPatterns);
            
            // Umwelt-Muster
            const environmentalPatterns = this.analyzeEnvironmentalPatterns(historicalData);
            patterns.push(...environmentalPatterns);
            
            // Muster-Erkenner speichern
            this.patternRecognizers.set(plantId, {
                patterns: patterns,
                lastAnalysis: new Date().toISOString(),
                confidence: this.calculatePatternConfidence(patterns)
            });
            
            return patterns;
            
        } catch (error) {
            console.error('🤖 AdvancedMLSystem: Fehler bei Muster-Erkennung:', error);
            return [];
        }
    }
    
    /**
     * Wachstums-Muster analysieren
     */
    analyzeGrowthPatterns(historicalData) {
        const patterns = [];
        
        if (!historicalData || historicalData.length < 20) return patterns;
        
        // Zyklische Wachstums-Muster
        const growthCycles = this.detectGrowthCycles(historicalData);
        if (growthCycles.length > 0) {
            patterns.push({
                type: 'growth_cycles',
                description: `${growthCycles.length} Wachstums-Zyklen erkannt`,
                confidence: 0.8,
                data: growthCycles
            });
        }
        
        // Beschleunigungs-/Verlangsamungs-Muster
        const accelerationPatterns = this.detectAccelerationPatterns(historicalData);
        if (accelerationPatterns.length > 0) {
            patterns.push({
                type: 'acceleration_patterns',
                description: 'Beschleunigungs-Muster erkannt',
                confidence: 0.7,
                data: accelerationPatterns
            });
        }
        
        return patterns;
    }
    
    /**
     * Wachstums-Zyklen erkennen
     */
    detectGrowthCycles(historicalData) {
        const cycles = [];
        const growthData = historicalData.map(d => d.height || 0);
        
        // Einfache Zyklus-Erkennung basierend auf Peaks
        for (let i = 1; i < growthData.length - 1; i++) {
            if (growthData[i] > growthData[i-1] && growthData[i] > growthData[i+1]) {
                cycles.push({
                    peak: i,
                    value: growthData[i],
                    duration: this.calculateCycleDuration(growthData, i)
                });
            }
        }
        
        return cycles;
    }
    
    /**
     * Zyklus-Dauer berechnen
     */
    calculateCycleDuration(growthData, peakIndex) {
        let start = peakIndex;
        let end = peakIndex;
        
        // Rückwärts suchen
        while (start > 0 && growthData[start] >= growthData[start-1]) {
            start--;
        }
        
        // Vorwärts suchen
        while (end < growthData.length - 1 && growthData[end] >= growthData[end+1]) {
            end++;
        }
        
        return end - start + 1;
    }
    
    /**
     * Beschleunigungs-Muster erkennen
     */
    detectAccelerationPatterns(historicalData) {
        const patterns = [];
        const growthData = historicalData.map(d => d.height || 0);
        
        // Beschleunigung berechnen (zweite Ableitung)
        for (let i = 2; i < growthData.length; i++) {
            const acceleration = growthData[i] - 2 * growthData[i-1] + growthData[i-2];
            
            if (Math.abs(acceleration) > 0.5) {
                patterns.push({
                    day: i,
                    acceleration: acceleration,
                    type: acceleration > 0 ? 'acceleration' : 'deceleration'
                });
            }
        }
        
        return patterns;
    }
    
    /**
     * Beleuchtungs-Muster analysieren
     */
    analyzeLightingPatterns(historicalData) {
        const patterns = [];
        
        if (!historicalData || historicalData.length < 20) return patterns;
        
        // PPFD-Effizienz-Muster
        const efficiencyPatterns = this.detectEfficiencyPatterns(historicalData);
        if (efficiencyPatterns.length > 0) {
            patterns.push({
                type: 'efficiency_patterns',
                description: 'PPFD-Effizienz-Muster erkannt',
                confidence: 0.7,
                data: efficiencyPatterns
            });
        }
        
        return patterns;
    }
    
    /**
     * Effizienz-Muster erkennen
     */
    detectEfficiencyPatterns(historicalData) {
        const patterns = [];
        
        for (const dataPoint of historicalData) {
            if (dataPoint.ppfd && dataPoint.lamp_power) {
                const efficiency = dataPoint.ppfd / dataPoint.lamp_power;
                
                if (efficiency > 1.2) {
                    patterns.push({
                        day: dataPoint.day,
                        efficiency: efficiency,
                        type: 'high_efficiency'
                    });
                } else if (efficiency < 0.8) {
                    patterns.push({
                        day: dataPoint.day,
                        efficiency: efficiency,
                        type: 'low_efficiency'
                    });
                }
            }
        }
        
        return patterns;
    }
    
    /**
     * Umwelt-Muster analysieren
     */
    analyzeEnvironmentalPatterns(historicalData) {
        const patterns = [];
        
        if (!historicalData || historicalData.length < 20) return patterns;
        
        // Temperatur-Humidity-Korrelation
        const tempHumidityPatterns = this.detectTempHumidityPatterns(historicalData);
        if (tempHumidityPatterns.length > 0) {
            patterns.push({
                type: 'temp_humidity_patterns',
                description: 'Temperatur-Humidity-Korrelation erkannt',
                confidence: 0.6,
                data: tempHumidityPatterns
            });
        }
        
        return patterns;
    }
    
    /**
     * Temperatur-Humidity-Muster erkennen
     */
    detectTempHumidityPatterns(historicalData) {
        const patterns = [];
        
        for (let i = 1; i < historicalData.length; i++) {
            const current = historicalData[i];
            const previous = historicalData[i-1];
            
            if (current.temperature && current.humidity && previous.temperature && previous.humidity) {
                const tempChange = current.temperature - previous.temperature;
                const humidityChange = current.humidity - previous.humidity;
                
                // Negative Korrelation (höhere Temperatur = niedrigere Luftfeuchtigkeit)
                if (tempChange > 2 && humidityChange < -5) {
                    patterns.push({
                        day: i,
                        tempChange: tempChange,
                        humidityChange: humidityChange,
                        type: 'negative_correlation'
                    });
                }
            }
        }
        
        return patterns;
    }
    
    /**
     * Muster-Konfidenz berechnen
     */
    calculatePatternConfidence(patterns) {
        if (patterns.length === 0) return 0.0;
        
        const totalConfidence = patterns.reduce((sum, pattern) => sum + pattern.confidence, 0);
        return totalConfidence / patterns.length;
    }
    
    /**
     * Optimierungs-Engine
     */
    async generateOptimizationRecommendations(plantId, currentData, historicalData) {
        try {
            const recommendations = [];
            
            // ML-basierte Optimierungen
            const mlRecommendations = this.generateMLRecommendations(plantId, currentData, historicalData);
            recommendations.push(...mlRecommendations);
            
            // Muster-basierte Optimierungen
            const patternRecommendations = this.generatePatternRecommendations(plantId, historicalData);
            recommendations.push(...patternRecommendations);
            
            // Anomalie-basierte Optimierungen
            const anomalyRecommendations = this.generateAnomalyRecommendations(plantId);
            recommendations.push(...anomalyRecommendations);
            
            // Empfehlungen nach Priorität sortieren
            recommendations.sort((a, b) => b.priority - a.priority);
            
            // Optimierungs-Engine speichern
            this.optimizationEngines.set(plantId, {
                recommendations: recommendations,
                lastGenerated: new Date().toISOString(),
                confidence: this.calculateRecommendationConfidence(recommendations)
            });
            
            return recommendations;
            
        } catch (error) {
            console.error('🤖 AdvancedMLSystem: Fehler bei Optimierungs-Empfehlungen:', error);
            return [];
        }
    }
    
    /**
     * ML-basierte Empfehlungen generieren
     */
    generateMLRecommendations(plantId, currentData, historicalData) {
        const recommendations = [];
        
        const modelData = this.deepLearningModels.get(plantId);
        if (!modelData) return recommendations;
        
        // Modell-Genauigkeit basierte Empfehlungen
        if (modelData.accuracy < 0.7) {
            recommendations.push({
                type: 'ml_model_improvement',
                priority: 8,
                description: 'ML-Modell benötigt mehr Trainingsdaten',
                action: 'Sammle mehr Daten für bessere Vorhersagen',
                expectedImpact: 0.3,
                confidence: 0.8
            });
        }
        
        // Wachstums-Trend basierte Empfehlungen
        const growthTrend = this.analyzeGrowthTrend(historicalData);
        if (growthTrend.slope < 0.5) {
            recommendations.push({
                type: 'growth_optimization',
                priority: 9,
                description: 'Wachstumsrate kann optimiert werden',
                action: 'PPFD und Nährstoffe überprüfen',
                expectedImpact: 0.4,
                confidence: 0.7
            });
        }
        
        return recommendations;
    }
    
    /**
     * Wachstums-Trend analysieren
     */
    analyzeGrowthTrend(historicalData) {
        if (!historicalData || historicalData.length < 10) {
            return { slope: 0, confidence: 0 };
        }
        
        const growthData = historicalData.map(d => d.height || 0);
        const n = growthData.length;
        
        let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
        
        for (let i = 0; i < n; i++) {
            sumX += i;
            sumY += growthData[i];
            sumXY += i * growthData[i];
            sumX2 += i * i;
        }
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        const confidence = Math.min(1, n / 20); // Mehr Daten = höhere Konfidenz
        
        return { slope, confidence };
    }
    
    /**
     * Muster-basierte Empfehlungen generieren
     */
    generatePatternRecommendations(plantId, historicalData) {
        const recommendations = [];
        
        const patternData = this.patternRecognizers.get(plantId);
        if (!patternData) return recommendations;
        
        for (const pattern of patternData.patterns) {
            if (pattern.type === 'efficiency_patterns') {
                recommendations.push({
                    type: 'efficiency_optimization',
                    priority: 7,
                    description: 'PPFD-Effizienz kann verbessert werden',
                    action: 'Lampenabstand und Ausrichtung optimieren',
                    expectedImpact: 0.2,
                    confidence: pattern.confidence
                });
            }
        }
        
        return recommendations;
    }
    
    /**
     * Anomalie-basierte Empfehlungen generieren
     */
    generateAnomalyRecommendations(plantId) {
        const recommendations = [];
        
        const anomalyData = this.anomalyDetectors.get(plantId);
        if (!anomalyData) return recommendations;
        
        for (const anomaly of anomalyData.anomalies) {
            if (anomaly.type === 'ppfd_too_low') {
                recommendations.push({
                    type: 'ppfd_increase',
                    priority: 9,
                    description: 'PPFD erhöhen für besseres Wachstum',
                    action: 'Lampenleistung oder Abstand anpassen',
                    expectedImpact: 0.5,
                    confidence: anomaly.confidence
                });
            } else if (anomaly.type === 'temperature_too_high') {
                recommendations.push({
                    type: 'temperature_control',
                    priority: 8,
                    description: 'Temperatur senken für optimales Wachstum',
                    action: 'Lüftung oder Klimaanlage aktivieren',
                    expectedImpact: 0.3,
                    confidence: anomaly.confidence
                });
            }
        }
        
        return recommendations;
    }
    
    /**
     * Empfehlungs-Konfidenz berechnen
     */
    calculateRecommendationConfidence(recommendations) {
        if (recommendations.length === 0) return 0.0;
        
        const totalConfidence = recommendations.reduce((sum, rec) => sum + rec.confidence, 0);
        return totalConfidence / recommendations.length;
    }
    
    /**
     * Trainingsdaten hinzufügen
     */
    addTrainingData(plantId, dataPoint) {
        const trainingData = this.trainingData.get(plantId) || [];
        
        trainingData.push({
            ...dataPoint,
            timestamp: new Date().toISOString()
        });
        
        // Nur die letzten 1000 Datenpunkte behalten
        if (trainingData.length > 1000) {
            trainingData.splice(0, trainingData.length - 1000);
        }
        
        this.trainingData.set(plantId, trainingData);
        
        // Automatisches Training wenn genug Daten vorhanden
        if (trainingData.length >= this.mlConfig.trainingDataThreshold) {
            this.trainModel(plantId, trainingData);
        }
    }
    
    /**
     * Debug-Informationen ausgeben
     */
    debug() {
        console.log('🤖 AdvancedMLSystem Debug Info:');
        console.log('Deep Learning Models:', this.deepLearningModels.size);
        console.log('Image Recognition Models:', this.imageRecognitionModels.size);
        console.log('Anomaly Detectors:', this.anomalyDetectors.size);
        console.log('Pattern Recognizers:', this.patternRecognizers.size);
        console.log('Optimization Engines:', this.optimizationEngines.size);
        console.log('Training Data:', this.trainingData.size);
        console.log('TensorFlow.js:', this.tf ? 'Verfügbar' : 'Nicht verfügbar');
    }
}

// Globale Instanz erstellen
window.advancedMLSystem = new AdvancedMLSystem();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedMLSystem;
} 