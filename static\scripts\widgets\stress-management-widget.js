/**
 * Stress Management Widget
 * Modular JavaScript for stress management widget
 */

class StressManagementWidget {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.phaseNameElement = document.getElementById('stressPhaseName');
        // Nutze immer den englischen Key für die Phase
        this.currentPhase = (options.currentPhase && options.currentPhase.current_phase) ? options.currentPhase.current_phase : options.currentPhase || null;
        this.plantId = options.plantId || null;
        this.strainType = this.detectStrainType();
        
        if (this.currentPhase && this.plantId) {
            this.loadStressManagementData();
        } else {
            console.error('StressManagementWidget: currentPhase oder plantId fehlt!', this.currentPhase, this.plantId);
        }
    }
    
    detectStrainType() {
        // Strain-Type aus dem Template oder Widget holen
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            return strainTypeElement.getAttribute('data-plant-strain-type');
        }
        
        const strainTypeWidget = document.querySelector('#strain-type-widget');
        if (strainTypeWidget) {
            const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
            if (strainTypeValue) {
                const strainTypeText = strainTypeValue.textContent.trim();
                if (strainTypeText.toLowerCase().includes('auto')) {
                    return 'autoflowering';
                }
            }
        }
        
        return 'photoperiodic';
    }

    showError(message) {
        if (this.container) {
            this.container.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        }
    }

    /**
     * Load stress management data from API
     */
    async loadStressManagementData() {
        if (!this.currentPhase) {
            this.showError('Keine Phase-Informationen verfügbar');
            console.error('Stress-Widget: currentPhase fehlt!');
            return;
        }
        // Fallback: sub_stage oder direkt currentPhase als String
        let phaseKey = this.currentPhase.sub_stage || this.currentPhase;
        if (!phaseKey) {
            this.showError('Keine gültige Phase gefunden!');
            console.error('Stress-Widget: phaseKey fehlt! currentPhase:', this.currentPhase);
            return;
        }
        
        try {
            // Load both stress management and guidelines
            const [managementResponse, guidelinesResponse] = await Promise.all([
                fetch(`/api/stress/management/${phaseKey}?plant_id=${this.plantId}`),
                fetch(`/api/stress/guidelines/${phaseKey}?strain_type=${this.strainType}`)
            ]);
            
            if (!managementResponse.ok) {
                throw new Error(`HTTP error! status: ${managementResponse.status}`);
            }
            
            const managementData = await managementResponse.json();
            const guidelinesData = guidelinesResponse.ok ? await guidelinesResponse.json() : null;
            
            // Combine data
            const combinedData = {
                ...managementData,
                guidelines: guidelinesData ? guidelinesData.guidelines : null,
                guidelines_used: !!guidelinesData
            };
            
            this.renderStressManagementData(combinedData);
        } catch (error) {
            console.error('Error loading stress management data:', error);
            this.showError('Fehler beim Laden der Stress-Management-Daten');
        }
    }

    /**
     * Render stress management data
     */
    renderStressManagementData(data) {
        if (!this.container) return;
        if (data.error) {
            this.showError(data.error);
            return;
        }

        let html = `
            <div class="stress-widget-header">
                <h5>
                    <i class="fa-solid fa-shield-alt"></i>
                    Stress-Management
                    <span class="stress-phase-name">${this.getFriendlyPhaseName(data.phase || this.currentPhase)}</span>
                </h5>
                <button 
                    type="button" 
                    class="btn btn-sm btn-outline-light" 
                    data-bs-toggle="modal" 
                    data-bs-target="#stress-guidelines-modal"
                    title="Stress-Richtlinien & Faustregeln anzeigen"
                >
                    <i class="fas fa-book-open"></i>
                </button>
            </div>
            <div class="stress-widget-body">
        `;

        // Guidelines-basierte Anzeige
        if (data.guidelines_used && data.guidelines) {
            html += this.renderGuidelinesBasedContent(data);
        } else {
            html += this.renderLegacyContent(data);
        }

        // Guidelines-Vorschau
        html += this.renderGuidelinesPreview();

        html += `
            </div>
        `;

        this.container.innerHTML = html;
        
        // Guidelines-Modal erstellen
        this.createGuidelinesModal();
    }
    
    renderGuidelinesBasedContent(data) {
        const guidelines = data.guidelines;
        const isAutoflower = this.strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        return `
            <div class="stress-section-title stress-guidelines">
                <i class="fa-solid fa-check-circle me-2"></i>Sortentyp-spezifische Empfehlungen
                <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
            </div>
            <div class="stress-section-content stress-guidelines-content">
                <div class="stress-strain-specific">
                    <i class="fa fa-leaf me-2"></i>
                    <strong>Phase:</strong> ${guidelines.description}
                </div>
                <div><span class="stress-label">Stresstoleranz:</span> <b>${guidelines.toleranz}</b></div>
                <div><span class="stress-label">Erlaubte Eingriffe:</span> <b>${Array.isArray(guidelines.erlaubte_eingriffe) ? guidelines.erlaubte_eingriffe.join(', ') : 'Keine Daten vorhanden'}</b></div>
                
                <div class="stress-grenzwerte">
                    <strong>Kritische Grenzwerte:</strong>
                    <ul class="stress-grenzwerte-list">
                        ${Object.entries(guidelines.kritische_grenzwerte).map(([key, value]) => {
                            if (typeof value === 'object') {
                                if ('min' in value && 'max' in value) {
                                    return `<li><strong>${key.toUpperCase()}:</strong> ${value.min}-${value.max}</li>`;
                                } else if ('max' in value) {
                                    return `<li><strong>${key.toUpperCase()}:</strong> max ${value.max}</li>`;
                                } else if ('min' in value) {
                                    return `<li><strong>${key.toUpperCase()}:</strong> min ${value.min}</li>`;
                                }
                            }
                            return `<li><strong>${key.toUpperCase()}:</strong> ${value}</li>`;
                        }).join('')}
                    </ul>
                </div>
            </div>
            
            ${Array.isArray(guidelines.typische_stressarten) ? this.renderStressTypes(guidelines.typische_stressarten) : '<div>Keine Daten vorhanden</div>'}
            
            ${this.renderFaustregeln()}
        `;
    }
    
    renderLegacyContent(data) {
        let html = '';

        // Header/Phase
        if (data.phase_info && data.phase_info.name) {
            html += `
                <div class="stress-phase-info">
                    <h5><i class="fa-solid fa-leaf"></i> ${data.phase_info.name}</h5>
                </div>
            `;
        }

        // LST Card
        html += this.renderTrainingCard('LST', data.available_techniques && data.available_techniques.lst ? data.available_techniques.lst : []);
        // HST Card
        html += this.renderTrainingCard('HST', data.available_techniques && data.available_techniques.hst ? data.available_techniques.hst : []);

        // Warnungen
        if (data.warnings && data.warnings.length > 0) {
            html += `
            <div class="stress-section-title stress-warnings">
                <i class="fa-solid fa-exclamation-triangle"></i>
                <span>Warnungen</span>
            </div>
            <div class="stress-section-content stress-warnings-content">
                    ${data.warnings.map(w => `<div class="stress-warning"><i class="fa-solid fa-exclamation-triangle"></i> <strong>Warnung:</strong> ${w}</div>`).join('')}
                </div>
            `;
        }

        // Tipps & Empfehlungen
        if (data.recommendations && data.recommendations.length > 0) {
            html += `
            <div class="stress-section-title stress-tips">
                <i class="fa-solid fa-lightbulb"></i>
                <span>Tipps & Empfehlungen</span>
            </div>
            <div class="stress-section-content stress-tips-content">
                    ${data.recommendations.map(tip => `<div class="stress-tip"><i class="fa-solid fa-lightbulb"></i> <strong>Hinweise:</strong> ${tip}</div>`).join('')}
                </div>
            `;
        }

        // Training-Status
        if (data.training_status) {
            html += `<div class="stress-training-status"><i class="fa-solid fa-info-circle"></i> <span>${data.training_status}</span></div>`;
        }

        return html;
    }
    
    renderStressTypes(stressTypes) {
        if (!Array.isArray(stressTypes)) {
            return '<div>Keine Daten vorhanden</div>';
        }
        return `
            <div class="stress-section-title stress-types">
                <i class="fa-solid fa-exclamation-triangle me-2"></i>Typische Stressarten
            </div>
            <div class="stress-section-content stress-types-content">
                ${stressTypes.map(stress => `
                    <div class="stress-type-item">
                        <h6 class="stress-type-title">
                            <i class="fa-solid fa-exclamation-circle"></i>
                            ${stress.typ}
                        </h6>
                        <div class="stress-type-details">
                            <div class="stress-ursachen">
                                <strong>Ursachen:</strong>
                                <ul>
                                    ${(Array.isArray(stress.ursachen) ? stress.ursachen : []).map(u => `<li>${u}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="stress-symptome">
                                <strong>Symptome:</strong>
                                <ul>
                                    ${(Array.isArray(stress.symptome) ? stress.symptome : []).map(s => `<li>${s}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="stress-massnahmen">
                                <strong>Empfohlene Maßnahmen:</strong>
                                <ul>
                                    ${(Array.isArray(stress.empfohleneMassnahmen) ? stress.empfohleneMassnahmen : []).map(m => `<li>${m}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    renderGuidelinesPreview() {
        const isAutoflower = this.strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        return `
            <div class="stress-section-title stress-guidelines">
                <i class="fa-solid fa-shield-alt me-2"></i>Stress-Management Übersicht
                <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
            </div>
            <div class="stress-section-content stress-guidelines-content">
                <div class="stress-guidelines-preview">
                    <p><strong>Sortentyp:</strong> ${strainTypeDisplay}</p>
                    <p><strong>Stresstoleranz:</strong> ${isAutoflower ? 'Niedrig - vorsichtig behandeln' : 'Hoch - robuste Sorten'}</p>
                    <p><strong>Empfohlene Techniken:</strong> ${isAutoflower ? 'Nur sanfte LST' : 'Alle LST/HST Techniken'}</p>
                    <div class="stress-guidelines-more">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#stress-guidelines-modal"
                        >
                            <i class="fas fa-book-open"></i> Alle Richtlinien anzeigen
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    getFriendlyPhaseName(phase) {
        const phaseNames = {
            'vegetative_early': 'Frühe Vegetation',
            'vegetative_middle': 'Mittlere Vegetation',
            'vegetative_late': 'Späte Vegetation',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Spülung'
        };
        return phaseNames[phase] || phase;
    }
    
    renderFaustregeln() {
        // Guidelines global laden, falls noch nicht vorhanden
        if (!StressManagementWidget.stressGuidelines) return '';
        const faustregeln = StressManagementWidget.stressGuidelines.faustregeln || [];
        if (!faustregeln.length) return '';
        return `
            <div class="stress-section-title stress-faustregeln">
                <i class="fa-solid fa-lightbulb me-2"></i>Faustregeln
            </div>
            <div class="stress-section-content stress-faustregeln-content">
                <ul class="stress-faustregeln-list">
                    ${faustregeln.map(r => `<li>${r}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Guidelines global laden und cachen
    static stressGuidelines = null;
    static stressGuidelinesLoaded = false;
    static loadStressGuidelines = async function() {
        if (StressManagementWidget.stressGuidelinesLoaded) return StressManagementWidget.stressGuidelines;
        try {
            const response = await fetch('/static/data/stress-guidelines.json');
            if (response.ok) {
                const json = await response.json();
                StressManagementWidget.stressGuidelines = json.stressGuidelines;
                StressManagementWidget.stressGuidelinesLoaded = true;
                return StressManagementWidget.stressGuidelines;
            }
        } catch (e) {}
        return null;
    };

    // Modal erweitern: Stressarten und Quellen anzeigen
    createGuidelinesModal() {
        // Prüfen ob Modal bereits existiert
        if (document.getElementById('stress-guidelines-modal')) {
            return;
        }
        const guidelines = StressManagementWidget.stressGuidelines;
        const stressArten = guidelines && guidelines.stressArten ? guidelines.stressArten : [];
        const meta = guidelines && guidelines.meta ? guidelines.meta : {};
        const modalHtml = `
            <div class="modal fade" id="stress-guidelines-modal" tabindex="-1" aria-labelledby="stress-guidelines-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="stress-guidelines-modal-label">
                                <i class="fa-solid fa-shield-alt me-2"></i>Stress-Richtlinien & Faustregeln
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="stress-guidelines-content">
                                <h6><i class="fa-solid fa-lightbulb me-2"></i>Faustregeln</h6>
                                <ul class="stress-faustregeln-list">
                                    ${(guidelines && guidelines.faustregeln ? guidelines.faustregeln.map(r => `<li>${r}</li>`).join('') : '')}
                                </ul>
                                <hr class="my-4">
                                <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>Alle Stressarten</h6>
                                <ul class="stress-arten-list">
                                    ${stressArten.map(a => `<li>${a}</li>`).join('')}
                                </ul>
                                <hr class="my-4">
                                <div class="stress-meta">
                                    <h6><i class="fa-solid fa-link me-2"></i>Quellen</h6>
                                    <ul class="stress-meta-quellen-list">
                                        ${(meta.quelle || []).map(q => `<li>${q}</li>`).join('')}
                                    </ul>
                                    <p class="text-muted small mt-2">
                                        <i class="fa-solid fa-calendar me-1"></i>Stand: ${meta.erstelltAm || ''} | 
                                        <i class="fa-solid fa-user me-1"></i>Erstellt von: ${meta.erstelltVon || ''}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    renderTrainingCard(type, techniques) {
        // type: 'LST' oder 'HST'
        const isLST = type === 'LST';
        const colorClass = isLST ? 'stress-lst' : 'stress-hst';
        const badge = isLST ? 'LST' : 'HST';
        const icon = isLST ? 'fa-seedling' : 'fa-cut';
        const title = isLST ? 'Low Stress Training (LST)' : 'High Stress Training (HST)';
        const recommended = techniques.length > 0;
        const empText = recommended ? 'Ja' : 'Nein';
        // Häufigkeit und Beschreibung aus Techniken extrahieren
        let freqText = 'Nicht angegeben';
        let descText = 'Keine Beschreibung';
        if (recommended) {
            // Wenn mehrere Techniken, fasse zusammen
            const freqArr = techniques.map(t => t.frequency).filter(Boolean);
            const descArr = techniques.map(t => t.description).filter(Boolean);
            if (freqArr.length > 0) freqText = freqArr.join(', ');
            if (descArr.length > 0) descText = descArr.join(' | ');
        }
        return `
            <div class="stress-section-title ${colorClass}">
                <i class="fa-solid ${icon}"></i>
                <span>${title}</span>
            </div>
            <div class="stress-section-content ${colorClass}-content">
                <div class="mb-3"><span class="stress-label">Empfohlen:</span> <span class="stress-recommended">${empText}</span></div>
                <div class="mb-3"><span class="stress-label">Häufigkeit:</span> <span class="stress-frequency">${freqText}</span></div>
                <div class="mb-3"><span class="stress-label">Beschreibung:</span> <span class="stress-value">${descText}</span></div>
                <span class="stress-badge ${badge.toLowerCase()}">${badge}</span>
            </div>
        `;
    }
}

// Beim ersten Widget-Start Guidelines laden
(async () => {
    if (!StressManagementWidget.stressGuidelinesLoaded) {
        await StressManagementWidget.loadStressGuidelines();
    }
})();

window.StressManagementWidget = StressManagementWidget;