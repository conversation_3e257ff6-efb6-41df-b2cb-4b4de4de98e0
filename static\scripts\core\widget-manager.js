/**
 * Widget Manager Module
 * Zentrale Verwaltung aller Widgets mit Event-System und dynamischem Laden/Entladen
 */

class WidgetManager {
    constructor() {
        this.widgets = new Map(); // Speichert alle aktiven Widget-Instanzen
        this.eventListeners = new Map(); // Event-System zwischen Widgets
        this.widgetRegistry = new Map(); // Registry für verfügbare Widget-Klassen
        
        // Widget Manager initialisiert
        
        // Widget-Klassen werden später registriert, wenn sie verfügbar sind
        this.setupWidgetRegistration();
    }

    /**
     * Setup für Widget-Registrierung
     */
    setupWidgetRegistration() {
        // Warten bis DOM geladen ist, dann Widgets registrieren
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.registerWidgetClasses();
            });
        } else {
            // DOM ist bereits geladen, sofort registrieren
            this.registerWidgetClasses();
        }
    }

    /**
     * Registriert alle verfügbaren Widget-Klassen
     */
    registerWidgetClasses() {
        // Widget-Klassen werden global verfügbar gemacht
        if (window.VPDWidget) {
            this.widgetRegistry.set('vpd', window.VPDWidget);
        }
        if (window.WateringWidget) {
            this.widgetRegistry.set('watering', window.WateringWidget);
        }
        if (window.LightingWidget) {
            this.widgetRegistry.set('lighting', window.LightingWidget);
        }
        if (window.StrainTypeWidget) {
            this.widgetRegistry.set('strain-type', window.StrainTypeWidget);
        }
        if (window.StressManagementWidget) {
            this.widgetRegistry.set('stress-management', window.StressManagementWidget);
        }
        if (window.NutrientWidget) {
            this.widgetRegistry.set('nutrient', window.NutrientWidget);
        }
        if (window.TrichomeWidget) {
            this.widgetRegistry.set('trichome', window.TrichomeWidget);
        }
        if (window.FloweringWidget) {
            this.widgetRegistry.set('flowering', window.FloweringWidget);
        }
        if (window.VegetationManagementWidget) {
            this.widgetRegistry.set('vegetation', window.VegetationManagementWidget);
        }
        if (window.VegetationWidget) {
            this.widgetRegistry.set('vegetation', window.VegetationWidget);
        }
    }

    /**
     * Widget laden und initialisieren
     * @param {string} widgetType - Typ des Widgets (z.B. 'vpd', 'watering')
     * @param {string} containerId - ID des Container-Elements
     * @param {Object} options - Zusätzliche Optionen für das Widget
     * @returns {Object|null} Widget-Instanz oder null bei Fehler
     */
    loadWidget(widgetType, containerId, options = {}) {
        try {
    
            
            // Prüfen ob Widget-Klasse registriert ist
            const WidgetClass = this.widgetRegistry.get(widgetType);
            if (!WidgetClass) {
                console.error(`Widget-Klasse '${widgetType}' nicht gefunden`);
                return null;
            }

            // Prüfen ob Container existiert
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`Container '${containerId}' nicht gefunden`);
                return null;
            }

            // Widget-Instanz erstellen
            const widgetInstance = new WidgetClass(containerId, options);
            
            // Widget in Registry speichern
            const widgetKey = `${widgetType}-${containerId}`;
            this.widgets.set(widgetKey, widgetInstance);
            
            // Automatische Initialisierung für Widgets mit initialize-Methode
            if (typeof widgetInstance.initialize === 'function' && options.plantId) {
        
                widgetInstance.initialize(options.plantId);
            }
            
            // Widget geladen
            
            // Event auslösen
            this.emit('widget:loaded', { widgetType, containerId, instance: widgetInstance });
            
            return widgetInstance;
            
        } catch (error) {
            console.error(`Fehler beim Laden des Widgets '${widgetType}':`, error);
            return null;
        }
    }

    /**
     * Widget entladen und aufräumen
     * @param {string} widgetType - Typ des Widgets
     * @param {string} containerId - ID des Container-Elements
     * @returns {boolean} Erfolg des Entladens
     */
    unloadWidget(widgetType, containerId) {
        try {
            const widgetKey = `${widgetType}-${containerId}`;
            const widgetInstance = this.widgets.get(widgetKey);
            
            if (!widgetInstance) {
                console.warn(`Widget '${widgetKey}' nicht gefunden`);
                return false;
            }

            // Widget destroy-Methode aufrufen falls vorhanden
            if (typeof widgetInstance.destroy === 'function') {
                widgetInstance.destroy();
            }

            // Widget aus Registry entfernen
            this.widgets.delete(widgetKey);
            
            // Widget entladen
            
            // Event auslösen
            this.emit('widget:unloaded', { widgetType, containerId });
            
            return true;
            
        } catch (error) {
            console.error(`Fehler beim Entladen des Widgets '${widgetType}':`, error);
            return false;
        }
    }

    /**
     * Widget-Instanz abrufen
     * @param {string} widgetType - Typ des Widgets
     * @param {string} containerId - ID des Container-Elements
     * @returns {Object|null} Widget-Instanz oder null
     */
    getWidget(widgetType, containerId) {
        const widgetKey = `${widgetType}-${containerId}`;
        return this.widgets.get(widgetKey) || null;
    }

    /**
     * Alle Widgets eines bestimmten Typs abrufen
     * @param {string} widgetType - Typ des Widgets
     * @returns {Array} Array von Widget-Instanzen
     */
    getWidgetsByType(widgetType) {
        const widgets = [];
        for (const [key, instance] of this.widgets) {
            if (key.startsWith(`${widgetType}-`)) {
                widgets.push(instance);
            }
        }
        return widgets;
    }

    /**
     * Alle aktiven Widgets abrufen
     * @returns {Array} Array aller Widget-Instanzen
     */
    getAllWidgets() {
        return Array.from(this.widgets.values());
    }

    /**
     * Event-Listener registrieren
     * @param {string} event - Event-Name
     * @param {Function} callback - Callback-Funktion
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * Event-Listener entfernen
     * @param {string} event - Event-Name
     * @param {Function} callback - Callback-Funktion
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Event auslösen
     * @param {string} event - Event-Name
     * @param {Object} data - Event-Daten
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Fehler in Event-Listener für '${event}':`, error);
                }
            });
        }
    }

    /**
     * Widget-Status abrufen
     * @returns {Object} Status-Informationen
     */
    getStatus() {
        return {
            totalWidgets: this.widgets.size,
            registeredTypes: Array.from(this.widgetRegistry.keys()),
            activeWidgets: Array.from(this.widgets.keys()),
            eventListeners: Array.from(this.eventListeners.keys())
        };
    }

    /**
     * Alle Widgets entladen
     */
    unloadAllWidgets() {
        const widgetKeys = Array.from(this.widgets.keys());
        widgetKeys.forEach(key => {
            const [widgetType, containerId] = key.split('-', 2);
            this.unloadWidget(widgetType, containerId);
        });
    }

    /**
     * Widget-Klasse zur Registry hinzufügen
     * @param {string} widgetType - Typ des Widgets
     * @param {Class} WidgetClass - Widget-Klasse
     */
    registerWidgetClass(widgetType, WidgetClass) {
        this.widgetRegistry.set(widgetType, WidgetClass);
    }
}

// Globale Instanz erstellen
window.widgetManager = new WidgetManager();

// Export für Module
window.WidgetManager = WidgetManager;

// Widget Manager bereit 