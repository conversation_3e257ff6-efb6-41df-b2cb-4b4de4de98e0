"""
Trichom-Analyse Logic Module
Handles trichome analysis, maturity calculation, and harvest recommendations
"""

import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# Add parent directories to path for imports
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from database_basic import db
from phase_logic.core import PhaseLogic


class TrichomeLogic:
    """Logik für Trichom-Analyse und Erntezeitbestimmung"""
    
    @staticmethod
    def load_trichome_guidelines() -> Dict:
        """Lädt die Trichom-Guidelines aus der JSON-Datei."""
        try:
            guidelines_path = os.path.join('static', 'data', 'bluete-zeitmanagement-guidelines.json')
            with open(guidelines_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('trichomAnalyseGuidelines', {})
        except Exception as e:
            print(f"Fehler beim Laden der Trichom-Guidelines: {e}")
            return {}
    
    @staticmethod
    def calculate_maturity_level(clear: int, milky: int, amber: int) -> Dict:
        """Berechnet den Reifegrad basierend auf Trichom-Verteilung."""
        # None-Werte abfangen und auf 0 setzen
        clear = clear or 0
        milky = milky or 0
        amber = amber or 0
        
        total = clear + milky + amber
        if total == 0:
            return {
                'level': 'unknown',
                'score': 0,
                'description': 'Keine Daten verfügbar'
            }
        
        # Prozentuale Verteilung
        clear_pct = (clear / total) * 100
        milky_pct = (milky / total) * 100
        amber_pct = (amber / total) * 100
        
        # Reifegrad-Score (0-100)
        # Klar = unreif, Milchig = optimal, Bernstein = überreif
        maturity_score = (milky_pct * 0.8) + (amber_pct * 0.6) + (clear_pct * 0.2)
        
        # Reifegrad bestimmen
        if clear_pct > 70:
            level = 'early_flowering'
            description = 'Frühe Blüte - Trichome noch überwiegend klar'
        elif milky_pct > 60:
            level = 'late_flowering'
            description = 'Späte Blüte - Milchige Trichome dominieren'
        elif amber_pct > 30:
            level = 'overripe'
            description = 'Überreif - Viele bernsteinfarbene Trichome'
        elif milky_pct > 40 and amber_pct > 10:
            level = 'optimal_harvest'
            description = 'Optimaler Erntezeitpunkt'
        else:
            level = 'developing'
            description = 'Entwicklung - Trichome reifen noch'
        
        return {
            'level': level,
            'score': round(maturity_score, 1),
            'description': description,
            'percentages': {
                'clear': round(clear_pct, 1),
                'milky': round(milky_pct, 1),
                'amber': round(amber_pct, 1)
            }
        }
    
    @staticmethod
    def get_harvest_recommendation(plant_id: str, clear: int, milky: int, amber: int) -> Dict:
        """Erstellt eine Ernteempfehlung basierend auf Trichom-Daten."""
        # None-Werte abfangen und auf 0 setzen
        clear = clear or 0
        milky = milky or 0
        amber = amber or 0
        
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return {'error': 'Pflanze nicht gefunden'}
        
        strain_type = plant.get('strain_type', 'photoperiodic')
        guidelines = TrichomeLogic.load_trichome_guidelines()
        
        # Aktuelle Phase bestimmen
        start_date = plant.get('start_date')
        flowering_date = plant.get('flower_start_date')
        
        if start_date:
            current_phase = PhaseLogic().get_current_phase(start_date, flowering_date)
            phase_name = current_phase.get('sub_phase', 'unknown') if current_phase and 'error' not in current_phase else 'unknown'
        else:
            phase_name = 'unknown'
        
        # Strain-spezifische Ziele
        if strain_type == 'autoflowering':
            target_milky = 70
            target_amber = 20
            max_amber = 40
        else:
            target_milky = 75
            target_amber = 15
            max_amber = 30
        
        total = clear + milky + amber
        if total == 0:
            return {
                'recommendation': 'Keine Trichom-Daten verfügbar',
                'urgency': 'unknown',
                'harvest_window': [0, 0]
            }
        
        milky_pct = (milky / total) * 100
        amber_pct = (amber / total) * 100
        
        # Ernteempfehlung erstellen
        if amber_pct > max_amber:
            recommendation = f"Überreif! Ernte sofort - {amber_pct:.1f}% Bernstein"
            urgency = 'critical'
            harvest_window = [0, 1]
        elif milky_pct >= target_milky and amber_pct >= target_amber:
            recommendation = f"Optimaler Erntezeitpunkt - {milky_pct:.1f}% milchig, {amber_pct:.1f}% Bernstein"
            urgency = 'high'
            harvest_window = [1, 3]
        elif milky_pct >= 50:
            recommendation = f"Ernte in 3-7 Tagen - {milky_pct:.1f}% milchig, {amber_pct:.1f}% Bernstein"
            urgency = 'medium'
            harvest_window = [3, 7]
        elif milky_pct >= 30:
            recommendation = f"Ernte in 7-14 Tagen - {milky_pct:.1f}% milchig"
            urgency = 'low'
            harvest_window = [7, 14]
        else:
            recommendation = f"Zu früh für Ernte - {milky_pct:.1f}% milchig"
            urgency = 'none'
            harvest_window = [14, 21]
        
        return {
            'recommendation': recommendation,
            'urgency': urgency,
            'harvest_window': harvest_window,
            'target_milky': target_milky,
            'target_amber': target_amber,
            'current_milky': round(milky_pct, 1),
            'current_amber': round(amber_pct, 1),
            'strain_type': strain_type,
            'phase': phase_name
        }
    
    @staticmethod
    def analyze_trichome_trend(plant_id: str, days: int = 10) -> Dict:
        """Analysiert den Trichom-Trend über die letzten Tage."""
        entries = db.get_trichome_entries(plant_id, limit=days)
        if len(entries) < 2:
            return {
                'trend': 'insufficient_data',
                'direction': 'unknown',
                'speed': 'unknown',
                'prediction': None
            }
        
        # Daten sortieren (älteste zuerst)
        entries.sort(key=lambda x: x['date'])
        
        # Trend berechnen
        first_entry = entries[0]
        last_entry = entries[-1]
        
        # None-Werte abfangen und auf 0 setzen
        first_milky = first_entry.get('milky_percentage', 0) or 0
        last_milky = last_entry.get('milky_percentage', 0) or 0
        first_amber = first_entry.get('amber_percentage', 0) or 0
        last_amber = last_entry.get('amber_percentage', 0) or 0
        
        milky_change = last_milky - first_milky
        amber_change = last_amber - first_amber
        
        # Trendrichtung bestimmen
        if milky_change > 10 and amber_change > 5:
            direction = 'maturing_fast'
            speed = 'fast'
        elif milky_change > 5:
            direction = 'maturing'
            speed = 'normal'
        elif milky_change < -5:
            direction = 'degrading'
            speed = 'fast'
        else:
            direction = 'stable'
            speed = 'slow'
        
        # Vorhersage für optimalen Erntezeitpunkt
        prediction = None
        if direction == 'maturing' and last_milky < 70:
            days_to_optimal = int((70 - last_milky) / max(milky_change / len(entries), 1))
            prediction = {
                'optimal_harvest_day': days_to_optimal,
                'confidence': 'medium' if len(entries) >= 3 else 'low'
            }
        
        return {
            'trend': direction,
            'direction': direction,
            'speed': speed,
            'milky_change': milky_change,
            'amber_change': amber_change,
            'data_points': len(entries),
            'prediction': prediction
        }
    
    @staticmethod
    def get_strain_specific_recommendations(plant_id: str) -> Dict:
        """Holt strain-spezifische Empfehlungen."""
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return {}
        
        strain_type = plant.get('strain_type', 'photoperiodic')
        guidelines = TrichomeLogic.load_trichome_guidelines()
        
        strain_info = guidelines.get('strainTypen', {}).get(
            'autoflower' if strain_type == 'autoflowering' else 'photoperiod', {}
        )
        
        return {
            'strain_type': strain_type,
            'analysis_start': strain_info.get('empfohleneAnalyseStart', 'Unbekannt'),
            'observation_interval': strain_info.get('beobachtungsintervallTage', 3),
            'microscope_recommendation': strain_info.get('mikroskopEmpfehlung', '60x Vergrößerung'),
            'notes': strain_info.get('hinweise', [])
        }
    
    @staticmethod
    def validate_trichome_data(clear: int, milky: int, amber: int) -> Dict:
        """Validiert Trichom-Daten."""
        # None-Werte abfangen und auf 0 setzen
        clear = clear or 0
        milky = milky or 0
        amber = amber or 0
        
        total = clear + milky + amber
        
        if total == 0:
            return {
                'valid': False,
                'error': 'Mindestens ein Trichom-Typ muss angegeben werden'
            }
        
        if total > 100:
            return {
                'valid': False,
                'error': f'Gesamtsumme ({total}) darf 100 nicht überschreiten'
            }
        
        if clear < 0 or milky < 0 or amber < 0:
            return {
                'valid': False,
                'error': 'Negative Werte sind nicht erlaubt'
            }
        
        return {
            'valid': True,
            'total': total,
            'message': 'Daten sind gültig'
        }
    
    @staticmethod
    def create_trichome_entry(plant_id: str, entry_data: Dict) -> Dict:
        """Erstellt einen neuen Trichom-Eintrag mit Analyse."""
        # Validierung
        validation = TrichomeLogic.validate_trichome_data(
            entry_data.get('clear_percentage', 0),
            entry_data.get('milky_percentage', 0),
            entry_data.get('amber_percentage', 0)
        )
        
        if not validation['valid']:
            return {
                'success': False,
                'error': validation['error']
            }
        
        # Blütetag berechnen oder verwenden
        plant = db.get_plant_by_id(plant_id)
        
        # Wenn ein manueller bloom_day angegeben wurde, diesen verwenden
        if entry_data.get('bloom_day') is not None:
            entry_data['bloom_day'] = int(entry_data['bloom_day'])
        # Ansonsten automatisch berechnen
        elif plant and plant.get('flower_start_date'):
            try:
                flower_start = datetime.fromisoformat(plant['flower_start_date'])
                bloom_day = (datetime.now() - flower_start).days
                entry_data['bloom_day'] = max(0, bloom_day)
            except:
                entry_data['bloom_day'] = 0
        else:
            entry_data['bloom_day'] = 0
        
        # Eintrag speichern
        entry_id = db.add_trichome_entry(plant_id, entry_data)
        if not entry_id:
            return {
                'success': False,
                'error': 'Fehler beim Speichern des Eintrags'
            }
        
        # Analyse durchführen
        analysis = TrichomeLogic.perform_complete_analysis(plant_id)
        
        return {
            'success': True,
            'entry_id': entry_id,
            'analysis': analysis
        }
    
    @staticmethod
    def perform_complete_analysis(plant_id: str) -> Dict:
        """Führt eine vollständige Trichom-Analyse durch."""
        # Neuesten Eintrag holen
        entries = db.get_trichome_entries(plant_id, limit=1)
        if not entries:
            return {
                'error': 'Keine Trichom-Daten verfügbar'
            }
        
        latest_entry = entries[0]
        
        # Reifegrad berechnen
        maturity = TrichomeLogic.calculate_maturity_level(
            latest_entry.get('clear_percentage', 0) or 0,
            latest_entry.get('milky_percentage', 0) or 0,
            latest_entry.get('amber_percentage', 0) or 0
        )
        
        # Ernteempfehlung
        harvest = TrichomeLogic.get_harvest_recommendation(
            plant_id,
            latest_entry.get('clear_percentage', 0) or 0,
            latest_entry.get('milky_percentage', 0) or 0,
            latest_entry.get('amber_percentage', 0) or 0
        )
        
        # Trend-Analyse
        trend = TrichomeLogic.analyze_trichome_trend(plant_id)
        
        # Strain-spezifische Empfehlungen
        strain_info = TrichomeLogic.get_strain_specific_recommendations(plant_id)
        
        # Analyse speichern
        analysis_data = {
            'analysis_date': datetime.now().date().isoformat(),
            'maturity_level': maturity['level'],
            'harvest_recommendation': harvest['recommendation'],
            'harvest_window_start': harvest['harvest_window'][0],
            'harvest_window_end': harvest['harvest_window'][1],
            'urgency': harvest['urgency'],
            'flush_alignment': 'optimal',  # TODO: Mit Flush-Modul integrieren
            'strain_specific_data': strain_info,
            'trend_analysis': trend
        }
        
        db.save_trichome_analysis(plant_id, analysis_data)
        
        return {
            'current_trichome_status': {
                'clear': latest_entry.get('clear_percentage', 0) or 0,
                'milky': latest_entry.get('milky_percentage', 0) or 0,
                'amber': latest_entry.get('amber_percentage', 0) or 0
            },
            'maturity_analysis': maturity,
            'harvest_recommendation': harvest,
            'trend_analysis': trend,
            'strain_specific': strain_info,
            'last_entry_date': latest_entry['date']
        } 