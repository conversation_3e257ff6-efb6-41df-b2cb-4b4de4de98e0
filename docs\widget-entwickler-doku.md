# 🧩 Widget-Entwickler-Dokumentation

## 1. Architektur & Grundprinzipien
- Jedes Widget besteht aus:
  - **JavaScript-Modul**: `static/scripts/widgets/<widget-name>-widget.js`
  - **CSS-Modul**: `static/styles/widgets/<widget-name>-widget.css`
  - **HTML-Template**: `templates/widgets/<widget-name>-widget.html`
- Die Widget-Initialisierung erfolgt über den Widget-Manager (`static/scripts/core/widget-manager.js`).

## 2. Neues Widget anlegen
1. Lege die drei Dateien im jeweiligen Ordner an (siehe oben).
2. Registriere das Widget im Widget-Manager (meist automatisch, wenn die Datei eingebunden ist).
3. Binde das HTML-Template im gewünschten Partial oder Template ein:
   ```jinja
   {% include 'widgets/<widget-name>-widget.html' %}
   ```
4. Binde das CSS-Modul in `main.css` oder im jeweiligen Partial ein.

## 3. Kommunikation zwischen Widgets
- Widgets kommunizieren über ein zentrales Event-System im Widget-Manager.
- Beispiel: Ein Widget kann ein Event `widget:updated` dispatchen, andere Widgets können darauf reagieren.
- Für komplexe Interaktionen empfiehlt sich die Nutzung von Custom Events oder das Widget-API.

## 4. Best Practices
- Halte die Widget-Logik modular und unabhängig.
- Verwende BEM-Methodologie für CSS-Klassen.
- Nutze Utility-Klassen für wiederkehrende Styles.
- Schreibe möglichst keine Inline-Styles oder Inline-JS.
- Dokumentiere neue Events und API-Änderungen im Code und in der Doku.

## 5. Beispiel: Neues Widget "humidity-widget"
- `static/scripts/widgets/humidity-widget.js`
- `static/styles/widgets/humidity-widget.css`
- `templates/widgets/humidity-widget.html`
- Einbindung im Template:
  ```jinja
  {% include 'widgets/humidity-widget.html' %}
  ```
- Registrierung im Widget-Manager (meist automatisch)

---
*Letzte Aktualisierung: {{ "now" | date("%d.%m.%Y %H:%M") }}* 