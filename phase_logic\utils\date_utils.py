#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Date-Utilities für das Grow-Tagebuch
"""

from datetime import datetime, timedelta

def format_date_german(date_str):
    """
    Formatiert ein Datum im deutschen Format (DD.MM.YYYY)
    
    Args:
        date_str (str): Datum im Format 'YYYY-MM-DD'
    
    Returns:
        str: Datum im deutschen Format
    """
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        return date_obj.strftime('%d.%m.%Y')
    except ValueError:
        return date_str

def calculate_days_between(start_date, end_date):
    """
    Berechnet die Anzahl der Tage zwischen zwei Daten
    
    Args:
        start_date (str): Startdatum im Format 'YYYY-MM-DD'
        end_date (str): Enddatum im Format 'YYYY-MM-DD'
    
    Returns:
        int: <PERSON><PERSON><PERSON> der Tage
    """
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        return (end - start).days
    except ValueError:
        return 0

def get_week_number(date_str):
    """
    Berechnet die Woche seit einem Startdatum
    
    Args:
        date_str (str): Datum im Format 'YYYY-MM-DD'
    
    Returns:
        int: Woche seit Start
    """
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        # Woche 1 beginnt am 1. Januar
        return date_obj.isocalendar()[1]
    except ValueError:
        return 0

def add_days_to_date(date_str, days):
    """
    Addiert Tage zu einem Datum
    
    Args:
        date_str (str): Startdatum im Format 'YYYY-MM-DD'
        days (int): Anzahl der Tage zu addieren
    
    Returns:
        str: Neues Datum im Format 'YYYY-MM-DD'
    """
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        new_date = date_obj + timedelta(days=days)
        return new_date.strftime('%Y-%m-%d')
    except ValueError:
        return date_str

def get_date_range(start_date, end_date):
    """
    Generiert eine Liste aller Daten zwischen Start- und Enddatum
    
    Args:
        start_date (str): Startdatum im Format 'YYYY-MM-DD'
        end_date (str): Enddatum im Format 'YYYY-MM-DD'
    
    Returns:
        list: Liste der Daten
    """
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime('%Y-%m-%d'))
            current += timedelta(days=1)
        
        return dates
    except ValueError:
        return [] 