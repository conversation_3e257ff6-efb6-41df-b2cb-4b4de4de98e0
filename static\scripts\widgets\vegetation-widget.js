/**
 * 🌱 Vegetation-Management Widget
 * 
 * Kombiniert alle Wachstumsphase-Funktionen in einer einheitlichen Oberfläche:
 * - Substratdatenbank & Gießverhalten
 * - Bewässerungs-Analyse & Planung
 * - Klimamanagement (VPD/Temperatur/Luftfeuchte)
 * - Nährstoff-Guidelines & EC/pH-Tracking
 * - Stress-Management & Trainingszeitpunkt
 * - Trainingszeitpunkt- & Eingriffs-Widget
 */

class VegetationManagementWidget {
    constructor(containerId, options = {}) {
        this.widgetId = containerId || 'vegetationManagementWidget';
        this.plantId = options.plantId || null;
        this.guidelines = {};
        this.currentPhase = null;
        this.strainType = null;
        this.substrate = null;
        
        // Module-Instanzen
        this.substrateManager = null;
        this.wateringManager = null;
        this.climateManager = null;
        this.nutrientManager = null;
        this.stressManager = null;
        this.trainingManager = null;
        
        this.isInitialized = false;
        
        // Automatisch initialisieren wenn plantId vorhanden
        if (this.plantId) {
            this.initialize(this.plantId);
        } else {
            console.warn('🌱 Vegetation-Widget: Keine plantId im Constructor, manuelle Initialisierung erforderlich');
        }
    }

    /**
     * Widget initialisieren
     */
    async initialize(plantId) {
        try {
            this.plantId = plantId;
            this.loadWateringInputValuesFromStorage();
            
            // Pflanzendaten laden
            await this.loadPlantData();
            
            // Alle Guidelines laden
            await this.loadAllGuidelines();
            
            // Module initialisieren
            this.initializeModules();
            
            // UI erstellen
            this.createWidget();
            
            // Event-Listener einrichten
            this.setupEventListeners();
            
            // Erste Daten laden
            await this.loadCurrentData();
            
            this.isInitialized = true;
    
            
        } catch (error) {
            console.error('Fehler beim Initialisieren des Vegetation-Widgets:', error);
            this.showError('Widget konnte nicht initialisiert werden');
        }
    }

    /**
     * Hilfsfunktion für stille API-Aufrufe (404-Fehler werden nicht geloggt)
     */
    async silentFetch(url) {
        try {
            const response = await fetch(url);
            if (response.ok) {
                return await response.json();
            } else if (response.status === 404) {
                // 404-Fehler still behandeln - keine Messwerte vorhanden
                return { success: false, entries: [] };
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            if (error.message.includes('404')) {
                // 404-Fehler still behandeln
                return { success: false, entries: [] };
            }
            throw error;
        }
    }

    /**
     * Pflanzendaten laden
     */
    async loadPlantData() {
        try {
            // Pflanzendaten zuerst laden, um Strain-Type zu bestimmen
            const response = await fetch(`/api/plants/${this.plantId}`);
            if (!response.ok) {
                throw new Error('Pflanzendaten konnten nicht geladen werden');
            }

            const data = await response.json();
            const plant = data.plant || data;

            // Strain-Type extrahieren
            let strainType = plant.strain_type || plant.strainType || 'photoperiodic';
            if (typeof strainType === 'string') {
                strainType = strainType.toLowerCase();
            }

            // Phase und Tag aus Flowering-Status laden (für alle Strain-Typen)
            try {
                const floweringResponse = await fetch(`/flowering/status/${this.plantId}`);
                if (floweringResponse.ok) {
                    const floweringData = await floweringResponse.json();
                    if (floweringData.success && floweringData.flowering_status) {
                        this.currentDay = floweringData.flowering_status.current_day;
                        this.currentPhase = floweringData.flowering_status.phase;
                        console.log('🌱 Vegetation-Widget: Phase geladen:', this.currentPhase, 'Tag:', this.currentDay);
                    }
                }
            } catch (error) {
                console.warn('Flowering-Status nicht verfügbar, verwende Fallback-Berechnung');
            }

            // Fallback: Tag und Phase berechnen falls nicht verfügbar
            if (!this.currentDay) {
                this.currentDay = this.calculateCurrentDay(plant);
            }
            if (!this.currentPhase) {
                this.currentPhase = this.calculateCurrentPhase(this.currentDay, strainType);
            }

            // Substrat extrahieren
            let substrate = plant.substrate || 'coco';
            if (typeof substrate === 'string') {
                substrate = substrate.toLowerCase();
            }
            
            // Topfgröße extrahieren
            let potSize = plant.pot_size || plant.potSize || 11.0;
            if (typeof potSize === 'string') {
                potSize = parseFloat(potSize);
            }
            
            // Werte setzen (Phase und Tag bereits oben gesetzt)
            this.strainType = strainType;
            this.substrate = substrate;
            this.potSize = potSize;

            return {
                currentPhase: this.currentPhase,
                strainType: this.strainType,
                substrate: this.substrate,
                currentDay: this.currentDay,
                rawData: plant
            };

        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Pflanzendaten:', error);
            throw error;
        }
    }

    /**
     * Aktuellen Tag berechnen
     */
    calculateCurrentDay(plantData) {
        try {
            // Startdatum aus verschiedenen möglichen Feldern
            const startDate = plantData.start_date || plantData.created_at || new Date().toISOString();
            
            if (!startDate) {
                console.warn('Kein Startdatum gefunden, verwende aktuelles Datum');
                return 1;
            }
            
            const start = new Date(startDate);
            const today = new Date();
            
            // Differenz in Tagen berechnen
            const diffTime = Math.abs(today - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            return Math.max(1, diffDays); // Mindestens Tag 1
            
        } catch (error) {
            console.error('Fehler beim Berechnen des Tags:', error);
            return 1;
        }
    }

    /**
     * Aktuelle Phase basierend auf Tag und Strain-Type berechnen
     */
    calculateCurrentPhase(day, strainType) {
        if (strainType === 'autoflowering' || strainType === 'autoflower') {
            // Autoflower: Kürzere Phasen
            if (day <= 7) {
                return 'germination';
            } else if (day <= 21) {
                // Vegetative Phase (7-21 Tage)
                if (day <= 14) {
                    return 'vegetative_early';
                } else {
                    return 'vegetative_middle';
                }
            } else {
                // Blüte-Phase (21+ Tage)
                const floweringDay = day - 21;
                if (floweringDay <= 14) {
                    return 'flowering_early';
                } else if (floweringDay <= 35) {
                    return 'flowering_middle';
                } else {
                    return 'flowering_late';
                }
            }
        } else {
            // Photoperiodisch: Längere Phasen
            if (day <= 7) {
                return 'germination';
            } else if (day <= 42) {
                // Vegetative Phase (7-42 Tage)
                if (day <= 21) {
                    return 'vegetative_early';
                } else if (day <= 35) {
                    return 'vegetative_middle';
                } else {
                    return 'vegetative_late';
                }
            } else {
                // Blüte-Phase (42+ Tage)
                const floweringDay = day - 42;
                if (floweringDay <= 14) {
                    return 'flowering_early';
                } else if (floweringDay <= 35) {
                    return 'flowering_middle';
                } else {
                    return 'flowering_late';
                }
            }
        }
    }

    /**
     * Header mit Tag und Fortschritt aktualisieren
     */
    updateHeader() {
        // Tag anzeigen
        const phaseDayElement = document.getElementById('phaseDay');
        
        if (phaseDayElement && this.currentDay) {
            phaseDayElement.textContent = this.currentDay;
        }
        
        // Fortschritt im Kreis anzeigen
        const progressCircle = document.getElementById('phaseProgressCircle');
        const progressText = document.getElementById('phaseProgressText');
        
        if (progressCircle && progressText) {
            const progress = this.getPhaseProgress();
            const circumference = 2 * Math.PI * 25; // r=25
            const strokeDasharray = (progress / 100) * circumference;
            
            progressCircle.style.strokeDasharray = `${strokeDasharray} ${circumference}`;
            progressText.textContent = `${Math.round(progress)}%`;
        }

        // Beispiel: Passe die Anzeige im Header an
        const substrateName = document.getElementById('substrateName');
        const strainType = document.getElementById('strainType');
        const phaseName = document.getElementById('phaseName');
        const potSizeInput = document.getElementById('pot-size-input');
        if (substrateName) substrateName.textContent = this.substrate || '-';
        if (strainType) strainType.textContent = this.strainType || '-';
        if (phaseName) phaseName.textContent = this.getFriendlyPhaseName(this.currentPhase) || '-';
        if (potSizeInput) potSizeInput.value = this.potSize;
    }

    /**
     * Alle Guidelines parallel laden
     */
    async loadAllGuidelines() {
        try {
            const guidelineFiles = [
                'substrat-guidelines.json',
                'watering-guidelines.json',
                'klimamanagement-guidelines.json',
                'nutrient-guidelines.json',
                'stress-guidelines.json',
                'training-guidelines.json'
            ];

            const promises = guidelineFiles.map(file => 
                fetch(`/static/data/${file}`)
                    .then(response => response.json())
                    .catch(error => {
                        console.warn(`Guidelines ${file} konnten nicht geladen werden:`, error);
                        return null;
                    })
            );

            const results = await Promise.all(promises);
            
            // Guidelines zuweisen
            this.guidelines = {
                substrate: results[0]?.substratGuidelines || {},
                watering: results[1]?.wateringGuidelines || {},
                climate: results[2]?.klimaManagementGuidelines || {},
                nutrients: results[3]?.nutrientGuidelines || {},
                stress: results[4]?.stressManagementGuidelines || {},
                training: results[5]?.trainingGuidelines || {}
            };

    
            
        } catch (error) {
            console.error('Fehler beim Laden der Guidelines:', error);
            throw error;
        }
    }

    /**
     * Module initialisieren
     */
    initializeModules() {
        // Module-Instanzen erstellen (falls vorhanden)
        this.substrateManager = window.SubstrateManager || this.createSubstrateManager();
        this.wateringManager = window.WateringManager || this.createWateringManager();
        this.climateManager = window.ClimateManager || this.createClimateManager();
        this.nutrientManager = window.NutrientManager || this.createNutrientManager();
        this.stressManager = window.StressManager || this.createStressManager();
        this.trainingManager = window.TrainingManager || this.createTrainingManager();
    }

    /**
     * Widget-UI erstellen
     */
    createWidget() {
        const container = document.getElementById(this.widgetId);
        if (!container) {
            console.error(`Container '${this.widgetId}' für Vegetation-Widget nicht gefunden`);
            return;
        }

        // Nur Loading-Indicator einfügen, Tab-Struktur bleibt erhalten
        const existingContent = container.innerHTML;
        container.innerHTML = `
            <div id="vegetationLoading" class="text-center py-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Lädt...</span>
                </div>
            </div>
            ${existingContent}
        `;
        this.container = container;
    }

    /**
     * Event-Listener einrichten
     */
    setupEventListeners() {
        // Tab-Wechsel Events für eigene Tab-Buttons
        const tabButtons = document.querySelectorAll('.vegetation-widget-container .tab-btn');
        tabButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                event.preventDefault();
                
                // Alle Tab-Buttons deaktivieren
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // Klicked Button aktivieren
                button.classList.add('active');
                
                // Tab-ID aus data-tab Attribut holen
                const tabId = button.getAttribute('data-tab');
                
                // Alle Tab-Panes ausblenden
                const tabPanes = document.querySelectorAll('.vegetation-widget-container .tab-pane');
                tabPanes.forEach(pane => pane.classList.remove('active'));
                
                // Gewählte Tab-Pane anzeigen
                const targetPane = document.getElementById(tabId);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
                
                // Tab-spezifische Daten laden
                this.onTabChange(tabId);
            });
        });

        // ESC-Taste zum Schließen
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.closeWidget();
            }
        });

        // VPD-Event-Listener werden jetzt in setupVPDEventListeners() behandelt
        // nach dem Rendern der VPD-Optimierung

        // Bewässerungs-Details Button
        this.container.addEventListener('click', (event) => {
            if (event.target.closest('.watering-details-btn')) {
                this.onTabChange('watering');
            }
        });
    }

    /**
     * Tab-Wechsel Handler
     */
    onTabChange(tabId) {
        console.log('🌱 Vegetation-Widget: Tab-Wechsel zu:', tabId);
        
        // Aktuellen Tab aktualisieren
        this.currentTab = tabId;
        
        // Alle Tab-Buttons deaktivieren
        const tabButtons = this.container.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Alle Tab-Inhalte ausblenden
        const tabContents = this.container.querySelectorAll('.tab-pane');
        tabContents.forEach(content => {
            content.classList.remove('show', 'active');
            content.style.display = 'none';
        });
        
        // Gewählten Tab aktivieren
        const activeButton = this.container.querySelector(`[data-tab="${tabId}"]`);
        const activeContent = this.container.querySelector(`#${tabId}`);
        
        if (activeButton) {
            activeButton.classList.add('active');
        }
        
        if (activeContent) {
            activeContent.classList.add('show', 'active');
            activeContent.style.display = 'block';
        }
        
        // Tab-spezifische Daten laden
        switch (tabId) {
            case 'overview':
                this.loadOverviewData();
                break;
            case 'watering':
                this.loadWateringTab();
                break;
            case 'climate':
                this.loadClimateTab();
                break;
            case 'lighting':
                this.loadLightingData();
                break;
            case 'nutrients':
                // Verzögerung für Nährstoff-Tab, um sicherzustellen, dass der Tab-Wechsel abgeschlossen ist
                setTimeout(() => this.loadNutrientsData(), 100);
                break;
            case 'training':
                this.loadTrainingData();
                break;
            case 'stress':
                this.loadStressData();
                break;
        }
    }

    /**
     * Gießen-Tab laden (nur Bewässerungsplan)
     */
    async loadWateringTab() {
        try {
            await this.loadWateringData();
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden des Gießen-Tabs:', error);
        }
    }

    /**
     * Event-Listener für VPD-Inputs nach dem Rendern einrichten
     */
    setupVPDEventListeners() {
        // VPD-Berechnungs-Button
        const vpdCalcBtn = document.getElementById('vpd-calc-btn');
        if (vpdCalcBtn) {
            vpdCalcBtn.addEventListener('click', () => this.calculateVPD());
        }

        // Erweiterte Analyse Button
        const advancedAnalysisBtn = document.getElementById('vpd-advanced-analysis-btn');
        if (advancedAnalysisBtn) {
            advancedAnalysisBtn.addEventListener('click', () => this.performAdvancedVPDAnalysis());
        }



        // Input-Felder für Live-Updates
        const temperatureInput = document.getElementById('temperature-input');
        const humidityInput = document.getElementById('humidity-input');
        const leafTemperatureInput = document.getElementById('leaf-temperature-input');

        if (temperatureInput) {
            temperatureInput.addEventListener('input', () => {
                this.updateVPDInputs();
                this.saveVPDInputValues(
                    parseFloat(temperatureInput.value.replace(',', '.')),
                    parseFloat(humidityInput.value.replace(',', '.')),
                    leafTemperatureInput ? parseFloat(leafTemperatureInput.value.replace(',', '.')) : null
                );
            });
        }
        if (humidityInput) {
            humidityInput.addEventListener('input', () => {
                this.updateVPDInputs();
                this.saveVPDInputValues(
                    parseFloat(temperatureInput.value.replace(',', '.')),
                    parseFloat(humidityInput.value.replace(',', '.')),
                    leafTemperatureInput ? parseFloat(leafTemperatureInput.value.replace(',', '.')) : null
                );
            });
        }
        if (leafTemperatureInput) {
            leafTemperatureInput.addEventListener('input', () => {
                this.updateVPDInputs();
                this.saveVPDInputValues(
                    parseFloat(temperatureInput.value.replace(',', '.')),
                    parseFloat(humidityInput.value.replace(',', '.')),
                    leafTemperatureInput ? parseFloat(leafTemperatureInput.value.replace(',', '.')) : null
                );
            });
        }

        // Gespeicherte Werte in die Input-Felder eintragen
        this.populateVPDInputs();
    }

    /**
     * Klima-Event-Listener einrichten
     */
    setupClimateEventListeners() {
        // Klima-Daten aus VPD-Inputs befüllen Button
        const fillClimateBtn = document.getElementById('climate-fill-from-vpd-btn');
        if (fillClimateBtn) {
            fillClimateBtn.addEventListener('click', () => this.fillClimateFromVPD());
        }

        // Klima-Daten speichern Button
        const saveClimateBtn = document.getElementById('climate-save-data-btn');
        if (saveClimateBtn) {
            saveClimateBtn.addEventListener('click', () => this.saveClimateData());
        }
    }

    /**
     * VPD-Input-Werte zurücksetzen
     */
    resetVPDInputValues() {
        try {
            localStorage.removeItem(`vpd_inputs_${this.plantId}`);
            
            // Standardwerte eintragen
            const temperatureInput = document.getElementById('temperature-input');
            const humidityInput = document.getElementById('humidity-input');
            const leafTemperatureInput = document.getElementById('leaf-temperature-input');
            
            if (temperatureInput) temperatureInput.value = 25;
            if (humidityInput) humidityInput.value = 50;
            if (leafTemperatureInput) leafTemperatureInput.value = '';
            
            // VPD-Vorschau aktualisieren
            this.updateVPDInputs();
            
            console.log('VPD-Input-Werte zurückgesetzt');
        } catch (error) {
            console.error('Fehler beim Zurücksetzen der VPD-Input-Werte:', error);
        }
    }

    /**
     * Klima-Tab laden (VPD, Klima, Substrat, EC/pH)
     */
    async loadClimateTab() {
        try {
            // VPD-Optimierung laden
            const vpdOptimization = document.getElementById('vpdOptimization');
            if (vpdOptimization) {
                const vpd = await this.getVPDStatus();
                vpdOptimization.innerHTML = this.renderVPDOptimization(vpd);
                // Event-Listener nach dem Rendern einrichten
                this.setupVPDEventListeners();
                
                // VPD-Historie laden (nach dem Rendern der VPD-Optimierung)
                setTimeout(() => this.loadVPDHistory(), 100);
            }

            // Klima-Daten laden
            const climateData = document.getElementById('climateData');
            if (climateData) {
                const climate = await this.getClimateData();
                climateData.innerHTML = this.renderClimateData(climate);
            }

            // EC/pH-Status laden
            const ecphStatus = document.getElementById('ecphStatus');
            if (ecphStatus) {
                const ecph = await this.getECpHStatus();
                ecphStatus.innerHTML = this.renderECpHStatus(ecph);
            }

            // Substrat-Status laden
            const substrateStatus = document.getElementById('substrateStatus');
            if (substrateStatus) {
                const substrate = await this.getSubstrateStatus();
                substrateStatus.innerHTML = this.renderSubstrateStatus(substrate);
            }

            // VPD-Inputs mit aktuellen Klima-Daten befüllen
            await this.populateVPDInputsFromClimateData();
            
            // Klima-Event-Listener einrichten
            this.setupClimateEventListeners();
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden des Klima-Tabs:', error);
        }
    }

    // Passe die Tab-Initialisierung an, damit die neuen Tabs korrekt geladen werden
    async loadAllTabData() {
        try {
            // Nur die funktionalen Tabs laden (Gießen und Klima)
            await Promise.all([
                this.loadWateringTab(),
                this.loadClimateTab()
            ]);
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Tab-Daten:', error);
        }
    }

    /**
     * Aktuelle Daten laden
     */
    async loadCurrentData() {
        try {
            // Loading-Indikator anzeigen
            const loadingElement = document.getElementById('vegetationLoading');
            if (loadingElement) {
                loadingElement.style.display = 'block';
            }
            
            // Übersicht-Daten laden (Standard-Tab)
            await this.loadOverviewData();
            
            // Header mit Tag und Fortschritt aktualisieren (NACH dem Laden der Daten)
            this.updateHeader();
            
            // Alle Tab-Daten im Hintergrund laden
            this.loadAllTabData();
            
            // Loading-Indikator ausblenden
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            
        } catch (error) {
            console.error('Fehler beim Laden der aktuellen Daten:', error);
            this.showError('Daten konnten nicht geladen werden');
            
            // Loading-Indikator ausblenden bei Fehler
            const loadingElement = document.getElementById('vegetationLoading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        }
    }

    /**
     * Übersicht-Daten laden
     */
    async loadOverviewData() {
        const phaseProgress = document.getElementById('phaseProgress');
        const phasePercentage = document.getElementById('phasePercentage');
        const phaseName = document.getElementById('phaseName');
        const phaseProgressLabel = document.getElementById('phaseProgressLabel');
        const phaseDescription = document.getElementById('phaseDescription');
        const phaseRecommendations = document.getElementById('phaseRecommendations');
        const warningsList = document.getElementById('warningsList');
        const quickActions = document.getElementById('quickActions');

        if (!phaseProgress) {
            console.error('🌱 Vegetation-Widget: phaseProgress Element nicht gefunden');
            return;
        }

        try {
            // Phasen-Informationen
            const phaseInfo = this.getPhaseInfo(this.currentPhase);
            const progress = this.getPhaseProgress();
            
            // Phase-Percentage aktualisieren
            if (phasePercentage) {
                phasePercentage.textContent = `(${Math.round(progress)}%)`;
            }
            
            // Phase-Name aktualisieren
            if (phaseName) {
                phaseName.textContent = phaseInfo.name;
            }
            
            // Phase-Progress-Label aktualisieren
            if (phaseProgressLabel) {
                phaseProgressLabel.textContent = `${Math.round(progress)}%`;
            }
            
            // Phase-Description aktualisieren
            if (phaseDescription) {
                phaseDescription.textContent = phaseInfo.description;
            }
            
            // Phasen-Fortschritt (Progress-Bar)
            phaseProgress.innerHTML = `
                <div class="progress mb-3">
                    <div class="progress-bar" role="progressbar" style="width: ${progress}%"></div>
                </div>
                <p><strong>${phaseInfo.name}</strong></p>
                <p class="text-muted">${phaseInfo.description}</p>
            `;

            // Empfehlungen (async)
            if (phaseRecommendations) {
                const recommendations = await this.getCurrentRecommendations();
                
                // Bewässerungs-Hinweise hinzufügen
                let wateringNotes = [];
                try {
                    const wateringPlan = await this.getWateringPlan();
                    if (wateringPlan.notes && wateringPlan.notes.length > 0) {
                        wateringNotes = wateringPlan.notes.map(note => 
                            `<div class="alert alert-success"><i class="fa fa-tint"></i> <strong>Gießen:</strong> ${note}</div>`
                        );
                    }
                } catch (error) {
                    console.error('Fehler beim Laden der Bewässerungs-Hinweise:', error);
                }
                
                // Alle Empfehlungen kombinieren
                const allRecommendations = [
                    ...recommendations.map(rec => `<div class="alert alert-info">${rec}</div>`),
                    ...wateringNotes
                ];
                
                phaseRecommendations.innerHTML = allRecommendations.join('');
            }

            // Warnungen (async)
            if (warningsList) {

                const warnings = await this.getCurrentWarnings();
        
                warningsList.innerHTML = warnings.length > 0 ? 
                    warnings.map(warning => `<div class="alert alert-warning">${warning}</div>`).join('') :
                    '<div class="alert alert-success">Keine Warnungen</div>';
            }

            // Schnellaktionen
            if (quickActions) {
                const actions = this.getQuickActions();
                quickActions.innerHTML = actions.map(action => 
                    `<button class="btn btn-sm btn-outline-primary me-2 mb-2" onclick="${action.onClick}">${action.label}</button>`
                ).join('');
            }
            
            // Bewässerungs-Übersicht hinzufügen
            this.addWateringOverview();
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Übersichtsdaten:', error);
        }
    }

    /**
     * Bewässerungs-Übersicht zum Übersicht-Tab hinzufügen
     */
    async addWateringOverview() {
        try {
            // Zwinge Neuladen der Guidelines für die Übersicht
            await this.loadAllGuidelines();
            const wateringPlan = await this.getWateringPlan();
    
            const overviewGrid = document.querySelector('#overview .overview-grid');
            
            if (!overviewGrid) return;
            
            // Prüfen ob bereits eine Bewässerungs-Übersicht existiert
            let existingCard = document.getElementById('wateringOverviewCard');
            if (existingCard) {
                existingCard.remove();
            }
            
            // Bewässerungs-Übersicht erstellen
            const wateringCard = document.createElement('div');
            wateringCard.id = 'wateringOverviewCard';
            wateringCard.className = 'info-card watering-overview-card';
            
            const recommendedAmount = wateringPlan.recommendedAmount || 'Nicht verfügbar';
            const frequency = wateringPlan.frequency || 'Nicht verfügbar';
    
            
            wateringCard.innerHTML = `
                <h3>💧 Bewässerung</h3>
                <div class="watering-overview-content">
                    <div class="watering-overview-item">
                        <span class="watering-label">Empfohlene Menge:</span>
                        <span class="watering-value">${recommendedAmount}</span>
                    </div>
                    <div class="watering-overview-item">
                        <span class="watering-label">Häufigkeit:</span>
                        <span class="watering-value">${frequency}</span>
                    </div>
                    <div class="watering-overview-item">
                        <span class="watering-label">Strain-Typ:</span>
                        <span class="watering-value">${wateringPlan.strainType}</span>
                    </div>
                    <div class="watering-overview-item">
                        <span class="watering-label">Topfgröße:</span>
                        <span class="watering-value">${wateringPlan.potSize}</span>
                    </div>
                </div>
                <div class="watering-overview-actions">
                    <button class="btn btn-sm btn-outline-success watering-details-btn">
                        <i class="fa fa-tint"></i> Details anzeigen
                    </button>
                </div>
            `;
            
            // Nach den Schnellaktionen einfügen
            const quickActionsCard = document.getElementById('quickActionsCard');
            if (quickActionsCard && quickActionsCard.parentNode) {
                quickActionsCard.parentNode.insertBefore(wateringCard, quickActionsCard.nextSibling);
            } else {
                overviewGrid.appendChild(wateringCard);
            }
            
        } catch (error) {
            console.error('Fehler beim Hinzufügen der Bewässerungs-Übersicht:', error);
        }
    }

    /**
     * Bewässerungsplan laden
     */
    async loadWateringData() {
        try {
            // Guidelines laden falls noch nicht geschehen
            if (!this.guidelines.watering) {
                await this.loadAllGuidelines();
            }

            // Bewässerungsplan aus Guidelines berechnen
            const wateringPlan = await this.getWateringPlan();
            
            // Daten für Rendering vorbereiten
            const data = {
                phase: wateringPlan.phase,
                phaseName: wateringPlan.phaseName,
                strainType: wateringPlan.strainType,
                potSize: wateringPlan.potSize,
                recommendedAmount: wateringPlan.recommendedAmount,
                frequency: wateringPlan.frequency,
                notes: wateringPlan.notes,
                drainExpected: wateringPlan.drainExpected,
                wateringFactor: wateringPlan.wateringFactor,
                // Berechnete Werte für Rendering
                amount_ml_per_liter: this.calculateAmountPerLiter(wateringPlan.recommendedAmount, this.potSize),
                total_amount_ml: this.calculateTotalAmount(wateringPlan.recommendedAmount, this.potSize)
            };

            this.renderWateringPlan(data);
            
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden des Bewässerungsplans:', error);
            this.renderWateringError(error.message);
        }
    }

    /**
     * Gießmenge pro Liter berechnen
     */
    calculateAmountPerLiter(recommendedAmount, potSize) {
        if (recommendedAmount.includes('-')) {
            const [min, max] = recommendedAmount.split('-').map(s => parseFloat(s));
            const avgAmount = (min + max) / 2;
            return Math.round((avgAmount * 1000) / potSize); // ml pro Liter
        } else {
            const amount = parseFloat(recommendedAmount);
            return Math.round((amount * 1000) / potSize); // ml pro Liter
        }
    }

    /**
     * Gesamtmenge in ml berechnen
     */
    calculateTotalAmount(recommendedAmount, potSize) {
        if (recommendedAmount.includes('-')) {
            const [min, max] = recommendedAmount.split('-').map(s => parseFloat(s));
            const avgAmount = (min + max) / 2;
            return Math.round(avgAmount * 1000); // ml
        } else {
            const amount = parseFloat(recommendedAmount);
            return Math.round(amount * 1000); // ml
        }
    }

    /**
     * Bewässerungsplan rendern
     */
    renderWateringPlan(data) {
        
        // Verwende die korrekten Felder aus den Guidelines
        const recommendedAmount = data.recommendedAmount || 'Nicht verfügbar';
        const frequency = data.frequency || 'Nicht verfügbar';
        const notes = data.notes || [];
        const potSize = data.potSize ? parseFloat(data.potSize.replace(' L', '')) : this.potSize;
        
        // Berechne die Mengen basierend auf der empfohlenen Menge
        let amountPerPot = 0;
        let amountPerLiter = 0;
        
        if (recommendedAmount && recommendedAmount !== 'Nicht verfügbar') {
            if (recommendedAmount.includes('-')) {
                // Bereich (z.B. "1.5-2.5 L")
                const [min, max] = recommendedAmount.split('-').map(s => parseFloat(s.replace(' L', '')));
                const avgAmount = (min + max) / 2;
                amountPerPot = Math.round(avgAmount * 1000); // Durchschnitt in ml
                amountPerLiter = Math.round(amountPerPot / potSize);
            } else if (recommendedAmount.includes('L')) {
                // Einzelwert (z.B. "2.0 L")
                const amount = parseFloat(recommendedAmount.replace(' L', ''));
                amountPerPot = Math.round(amount * 1000); // In ml
                amountPerLiter = Math.round(amountPerPot / potSize);
            }
        }
        
        // Strain-Type ermitteln
        const strainType = this.getStrainType();
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        // Drainage-Ziele berechnen
        const drainTarget = Math.round(amountPerPot * 0.15); // 15% Drain-Ziel
        const drainMin = Math.round(amountPerPot * 0.10); // 10% Minimum
        const drainMax = Math.round(amountPerPot * 0.20); // 20% Maximum
        
        // Mengen-Vergleich
        let comparisonHtml = '';
        if (this.currentWaterAmount > 0) {
            const difference = this.currentWaterAmount - amountPerPot;
            const percentage = Math.round((this.currentWaterAmount / amountPerPot) * 100);
            
            let statusClass = 'text-success';
            let statusIcon = 'fa-check-circle';
            let statusText = 'Perfekt!';
            
            if (percentage < 80) {
                statusClass = 'text-warning';
                statusIcon = 'fa-exclamation-triangle';
                statusText = 'Zu wenig';
            } else if (percentage > 120) {
                statusClass = 'text-danger';
                statusIcon = 'fa-exclamation-circle';
                statusText = 'Zu viel';
            }
            
            comparisonHtml = `
                <div class="watering-comparison-card">
                    <div class="watering-header">
                        <h4><i class="fa-solid fa-balance-scale"></i> Mengen-Vergleich</h4>
                    </div>
                    <div class="watering-grid">
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-tint"></i>
                            </div>
                            <div class="watering-info">
                                <label>Deine Menge</label>
                                <span class="watering-value">${this.currentWaterAmount} ml</span>
                            </div>
                        </div>
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-check"></i>
                            </div>
                            <div class="watering-info">
                                <label>Empfohlen</label>
                                <span class="watering-value">${amountPerPot} ml</span>
                            </div>
                        </div>
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa ${statusIcon}"></i>
                            </div>
                            <div class="watering-info">
                                <label>Status</label>
                                <span class="watering-value ${statusClass}">${statusText} (${percentage}%)</span>
                            </div>
                        </div>
                        ${difference !== 0 ? `
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-arrow-${difference > 0 ? 'up' : 'down'}"></i>
                            </div>
                            <div class="watering-info">
                                <label>Differenz</label>
                                <span class="watering-value ${difference > 0 ? 'text-danger' : 'text-warning'}">${difference > 0 ? '+' : ''}${difference} ml</span>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // Topfgrößen-Beispiele
        let examplesHtml = '';
        if (this.wateringGuidelines && this.wateringGuidelines.topfGroessenBeispiel) {
            const currentPhaseExamples = this.wateringGuidelines.topfGroessenBeispiel.filter(ex => ex.phase === this.currentPhase);
            if (currentPhaseExamples.length > 0) {
                const strainTypeKey = isAutoflower ? 'autoflower' : 'photoperiod';
                examplesHtml = `
                    <div class="watering-examples-card">
                        <div class="watering-header">
                            <h4><i class="fa-solid fa-ruler"></i> Topfgrößen-Beispiele</h4>
                        </div>
                        <div class="watering-grid">
                            ${currentPhaseExamples.map(ex => {
                                const values = ex[strainTypeKey];
                                return `
                                    <div class="watering-item">
                                        <div class="watering-icon">
                                            <i class="fa fa-flask"></i>
                                        </div>
                                        <div class="watering-info">
                                            <label>${ex.potSizeLiters}L Topf</label>
                                            <span class="watering-value">${values.minMl}-${values.maxMl} ml</span>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
            }
        }

        // Pflanzen-Einstellungen
        const settingsHtml = `
            <div class="watering-settings-card">
                <div class="watering-header">
                    <h4><i class="fa-solid fa-cog"></i> Pflanzen-Einstellungen</h4>
                </div>
                <div class="watering-settings-grid">
                    <div class="watering-setting-item">
                        <label for="substrate-input">Substrat</label>
                        <select id="substrate-input" class="watering-input">
                            <option value="erde" ${this.substrate === 'erde' ? 'selected' : ''}>Erde</option>
                            <option value="coco" ${this.substrate === 'coco' ? 'selected' : ''}>Coco</option>
                            <option value="hydro" ${this.substrate === 'hydro' ? 'selected' : ''}>Hydro</option>
                        </select>
                    </div>
                    <div class="watering-setting-item">
                        <label for="pot-size-input">Topfgröße (L)</label>
                        <input type="number" min="1" max="50" step="0.5" 
                               id="pot-size-input" value="${this.potSize}" 
                               class="watering-input">
                    </div>
                    <div class="watering-setting-item">
                        <label for="water-amount-input">Gießwasser-Menge (ml)</label>
                        <input type="number" min="0" step="10" 
                               id="water-amount-input" placeholder="z.B. 500" 
                               value="${this.currentWaterAmount > 0 ? this.currentWaterAmount : ''}" 
                               class="watering-input">
                        <small class="watering-input-hint">
                            <i class="fa fa-info-circle"></i> Optional: Prüft ob die Menge ausreicht
                        </small>
                    </div>
                </div>
                <div class="watering-calc-section">
                    <button id="watering-calc-btn" class="btn btn-primary">
                        <i class="fa fa-calculator"></i> Bewässerung Berechnen
                    </button>
                </div>
            </div>
        `;

        // Guidelines-Vorschau
        const guidelinesHtml = `
            <div class="watering-guidelines-card">
                <div class="watering-header">
                    <h4><i class="fa-solid fa-lightbulb"></i> Wichtige Faustregeln</h4>
                </div>
                <div class="watering-guidelines-content">
                    <ul class="watering-guidelines-list">
                        <li><strong>Pro Gießvorgang:</strong> ca. 1/4 bis 1/3 des Topfvolumens</li>
                        <li><strong>Drainage:</strong> Immer auf ca. 10–20% Drain abzielen</li>
                        <li><strong>Trocknung:</strong> Zwischen Gießvorgängen vollständig trocknen lassen</li>
                        <li><strong>Gießweise:</strong> Besser selten und durchdringend als häufig und flach</li>
                        ${isAutoflower ? '<li><strong>Autoflower-spezifisch:</strong> Weniger Wasser pro Gießvorgang für optimales Wachstum</li>' : ''}
                    </ul>
                </div>
            </div>
        `;

        const html = `
            <div class="watering-main-section">
                ${settingsHtml}
                <!-- Empfohlene Bewässerung -->
                <div class="watering-recommendation-card">
                    <div class="recommendation-header">
                        <h4><i class="fa-solid fa-check-circle"></i> Empfohlene Bewässerung</h4>
                    </div>
                    <div class="watering-grid">
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-tint"></i>
                            </div>
                            <div class="watering-info">
                                <label>Empfohlene Menge</label>
                                <span class="watering-value highlight">${recommendedAmount}</span>
                            </div>
                        </div>
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-calculator"></i>
                            </div>
                            <div class="watering-info">
                                <label>Menge pro Liter</label>
                                <span class="watering-value">${amountPerLiter > 0 ? amountPerLiter + ' ml/L' : 'Berechne...'}</span>
                            </div>
                        </div>
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-clock"></i>
                            </div>
                            <div class="watering-info">
                                <label>Häufigkeit</label>
                                <span class="watering-value">${frequency}</span>
                            </div>
                        </div>
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-flask"></i>
                            </div>
                            <div class="watering-info">
                                <label>Menge pro Topf</label>
                                <span class="watering-value highlight">${amountPerPot > 0 ? amountPerPot + ' ml' : 'Berechne...'}</span>
                            </div>
                        </div>
                        ${data.watering_factor_min ? `
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-percentage"></i>
                            </div>
                            <div class="watering-info">
                                <label>Faktor</label>
                                <span class="watering-value">${(data.watering_factor_min * 100).toFixed(1)}-${(data.watering_factor_max * 100).toFixed(1)}% des Topfvolumens</span>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
                ${comparisonHtml}
                <div class="watering-drain-card">
                    <div class="watering-header">
                        <h4><i class="fa-solid fa-tint"></i> Drainage-Ziele</h4>
                    </div>
                    <div class="watering-grid">
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-bullseye"></i>
                            </div>
                            <div class="watering-info">
                                <label>Drain-Ziel</label>
                                <span class="watering-value highlight">${drainTarget} ml (15% der Gießmenge)</span>
                            </div>
                        </div>
                        <div class="watering-item">
                            <div class="watering-icon">
                                <i class="fa fa-arrows-alt-h"></i>
                            </div>
                            <div class="watering-info">
                                <label>Akzeptabler Bereich</label>
                                <span class="watering-value">${drainMin}-${drainMax} ml (10-20%)</span>
                            </div>
                        </div>
                    </div>
                    <div class="watering-drain-warnings">
                        <div class="watering-drain-warning">
                            <i class="fa fa-exclamation-triangle text-warning"></i>
                            <strong>Zu wenig Drain (&lt; 10%):</strong> Salzansammlung, Nährstoffblockaden
                        </div>
                        <div class="watering-drain-warning">
                            <i class="fa fa-exclamation-triangle text-danger"></i>
                            <strong>Zu viel Drain (&gt; 20%):</strong> Nährstoffverlust, ineffiziente Bewässerung
                        </div>
                    </div>
                </div>
                ${guidelinesHtml}
                ${notes.length > 0 ? `
                <div class="watering-notes-card">
                    <div class="notes-header">
                        <h4><i class="fa-solid fa-info-circle"></i> Wichtige Hinweise</h4>
                    </div>
                    <ul class="notes-list">
                        ${notes.map(note => `<li>${note}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
            </div>
        `;


        const container = document.getElementById('wateringPlan');

        
        if (container) {
            container.innerHTML = html;
            // Nach dem Rendern: Werte aus LocalStorage in Inputs setzen
            const settings = localStorage.getItem('vegetationWidgetWateringSettings');
            if (settings) {
                try {
                    const { substrate, potSize, waterAmount } = JSON.parse(settings);
                    const substrateInput = document.getElementById('substrate-input');
                    const potSizeInput = document.getElementById('pot-size-input');
                    const waterAmountInput = document.getElementById('water-amount-input');
                    if (substrateInput && substrate) substrateInput.value = substrate;
                    if (potSizeInput && potSize) potSizeInput.value = potSize;
                    if (waterAmountInput && waterAmount) waterAmountInput.value = waterAmount;
                } catch (e) {}
            }
            this.setupWateringEventListeners();
        } else {
            console.error('🌱 Vegetation-Widget: wateringPlan Container nicht gefunden!');
        }
    }

    /**
     * VPD-Optimierung rendern
     */
    renderVPDOptimization(vpd) {
        const getStatusClass = (status) => {
            switch (status) {
                case 'success': return 'modern-status-success';
                case 'warning': return 'modern-status-warning';
                case 'danger': return 'modern-status-danger';
                default: return 'modern-status-unknown';
            }
        };

        const getStatusIcon = (status) => {
            switch (status) {
                case 'success': return 'fa-check-circle';
                case 'warning': return 'fa-exclamation-triangle';
                case 'danger': return 'fa-exclamation-circle';
                default: return 'fa-question-circle';
            }
        };

        return `
            <div class="modern-card">
                <div class="modern-header">
                    <i class="fa-solid fa-droplet"></i>
                    VPD-Optimierung <span style="font-size:0.95em;opacity:0.7;">${this.getPhaseDisplayName(this.currentPhase)}</span>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-check-circle"></i> Empfohlene Werte
                </div>
                <div class="modern-section-content">
                    <div class="vpd-targets-grid">
                        <div class="vpd-target-item">
                            <span class="modern-label">VPD-Zielbereich:</span> 
                            <span class="modern-value">${vpd.targetRange}</span>
                        </div>
                        <div class="vpd-target-item">
                            <span class="modern-label">Optimal:</span> 
                            <span class="modern-value">${vpd.optimal}</span>
                        </div>
                        <div class="vpd-target-item">
                            <span class="modern-label">Temperatur:</span> 
                            <span class="modern-value">${vpd.temperatureRange}</span>
                        </div>
                        <div class="vpd-target-item">
                            <span class="modern-label">Luftfeuchte:</span> 
                            <span class="modern-value">${vpd.humidityRange}</span>
                        </div>
                    </div>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-thermometer-half"></i> VPD-Berechnung
                </div>
                <div class="modern-section-content">
                    <div class="vpd-input-grid mb-2">
                        <div class="vpd-input-group">
                            <label for="temperature-input" class="modern-label">Temperatur (°C)</label>
                            <input type="number" min="0" max="50" step="0.1" id="temperature-input" value="${this.loadVPDInputValues().temperature}" class="modern-input">
                        </div>
                        <div class="vpd-input-group">
                            <label for="humidity-input" class="modern-label">Luftfeuchte (%)</label>
                            <input type="number" min="0" max="100" step="0.1" id="humidity-input" value="${this.loadVPDInputValues().humidity}" class="modern-input">
                        </div>
                        <div class="vpd-input-group">
                            <label for="leaf-temperature-input" class="modern-label">Blatttemperatur (°C) <small class="text-muted">(optional)</small></label>
                            <input type="number" min="0" max="50" step="0.1" id="leaf-temperature-input" placeholder="z.B. 26.5" value="${this.loadVPDInputValues().leafTemperature || ''}" class="modern-input">
                        </div>
                    </div>
                    <div class="vpd-calc-section">
                        <button id="vpd-calc-btn" class="btn btn-sm btn-success">
                            <i class="fa fa-calculator"></i> VPD Berechnen
                        </button>
                        <button id="vpd-advanced-analysis-btn" class="btn btn-sm btn-outline-primary ms-2">
                            <i class="fa fa-chart-line"></i> Erweiterte Analyse
                        </button>
                        <span class="vpd-input-hint ms-2">
                            <i class="fa fa-info-circle"></i> (Thermometer/Hygrometer)
                        </span>
                        <small class="text-muted d-block mt-1">
                            <i class="fa fa-save"></i> Zuletzt verwendete Werte werden automatisch gespeichert
                        </small>
                    </div>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-chart-line"></i> Aktueller Status
                </div>
                <div class="modern-section-content vpd-status-content">
                    <div class="vpd-current-value mb-2">
                        <span class="modern-label">Aktueller VPD-Wert:</span> 
                        <span class="modern-value ${getStatusClass(vpd.status)}">
                            <i class="fa ${getStatusIcon(vpd.status)}"></i>
                            ${vpd.value} kPa
                        </span>
                    </div>
                    <div class="vpd-recommendation">
                        <span class="modern-label">Empfehlung:</span> 
                        <span class="modern-value">${vpd.recommendation}</span>
                    </div>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-history"></i> VPD-Historie
                </div>
                <div class="modern-section-content">
                    <div id="vpd-history-chart" style="height: 200px;">
                        <div class="text-center text-muted">
                            <i class="fa fa-chart-line fa-2x mb-2"></i>
                            <p>VPD-Verlauf wird geladen...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Klima-Daten rendern
     */
    renderClimateData(climate) {
        const getStatusClass = (status) => {
            switch (status) {
                case 'success': return 'modern-status-success';
                case 'warning': return 'modern-status-warning';
                case 'danger': return 'modern-status-danger';
                default: return 'modern-status-unknown';
            }
        };

        const getStatusIcon = (status) => {
            switch (status) {
                case 'success': return 'fa-check-circle';
                case 'warning': return 'fa-exclamation-triangle';
                case 'danger': return 'fa-exclamation-circle';
                default: return 'fa-question-circle';
            }
        };

        const getClimateRecommendation = (temp, humidity) => {
            // Prüfen ob Werte vorhanden sind
            if (!temp || !humidity || temp === 'N/A' || humidity === 'N/A') {
                return 'Keine aktuellen Messwerte verfügbar';
            }
            
            const tempNum = parseFloat(temp);
            const humNum = parseFloat(humidity);
            
            // Prüfen ob Werte gültige Zahlen sind
            if (isNaN(tempNum) || isNaN(humNum)) {
                return 'Ungültige Messwerte - bitte prüfen Sie die Eingaben';
            }
            
            // Detaillierte Klima-Empfehlung basierend auf aktuellen Guidelines
            let recommendations = [];
            
            // Temperatur-Analyse
            if (tempNum < 18) {
                recommendations.push('❄️ Temperatur zu niedrig (< 18°C) - Heizung erhöhen');
            } else if (tempNum < 22) {
                recommendations.push('🌡️ Temperatur leicht zu niedrig (18-22°C) - Heizung leicht erhöhen');
            } else if (tempNum >= 22 && tempNum <= 28) {
                recommendations.push('✅ Temperatur optimal (22-28°C)');
            } else if (tempNum > 28 && tempNum <= 32) {
                recommendations.push('🌡️ Temperatur leicht zu hoch (28-32°C) - Lüftung erhöhen');
            } else {
                recommendations.push('🔥 Temperatur kritisch hoch (> 32°C) - Sofortige Kühlung erforderlich');
            }
            
            // Luftfeuchte-Analyse
            if (humNum < 30) {
                recommendations.push('💨 Luftfeuchte kritisch niedrig (< 30%) - Verdunster aktivieren');
            } else if (humNum < 50) {
                recommendations.push('🌬️ Luftfeuchte zu niedrig (30-50%) - Luftbefeuchter aktivieren');
            } else if (humNum >= 50 && humNum <= 70) {
                recommendations.push('✅ Luftfeuchte optimal (50-70%)');
            } else if (humNum > 70 && humNum <= 80) {
                recommendations.push('💧 Luftfeuchte leicht zu hoch (70-80%) - Lüftung erhöhen');
            } else {
                recommendations.push('🌧️ Luftfeuchte kritisch hoch (> 80%) - Sofortige Entfeuchtung');
            }
            
            // VPD-Berechnung für zusätzliche Empfehlung
            const vpd = this.calculateVPDValue(tempNum, humNum);
            if (vpd < 0.5) {
                recommendations.push('⚠️ VPD zu niedrig - Schimmel-Risiko, Luftfeuchte reduzieren');
            } else if (vpd >= 0.8 && vpd <= 1.2) {
                recommendations.push('✅ VPD optimal (0.8-1.2 kPa)');
            } else if (vpd > 2.0) {
                recommendations.push('⚠️ VPD zu hoch - Stress-Risiko, Luftfeuchte erhöhen');
            }
            
            // Zusammenfassung erstellen
            const optimalCount = recommendations.filter(r => r.includes('✅')).length;
            const warningCount = recommendations.filter(r => r.includes('⚠️')).length;
            const criticalCount = recommendations.filter(r => r.includes('❄️') || r.includes('🔥') || r.includes('💨') || r.includes('🌧️')).length;
            
            if (criticalCount > 0) {
                return `🚨 Kritische Probleme: ${recommendations.filter(r => r.includes('❄️') || r.includes('🔥') || r.includes('💨') || r.includes('🌧️')).join(' | ')}`;
            } else if (warningCount > 0) {
                return `⚠️ Optimierungsbedarf: ${recommendations.filter(r => r.includes('⚠️')).join(' | ')}`;
            } else if (optimalCount >= 2) {
                return `✅ Klima-Bedingungen optimal (${tempNum}°C, ${humNum}%)`;
            } else {
                return recommendations.join(' | ');
            }
        };

        // Strain-spezifische Guidelines laden
        const strainType = this.strainType || 'photoperiodic';
        const strainKey = strainType === 'autoflowering' ? 'autoflower' : 'photoperiod';
        const climateGuidelines = this.guidelines.climate?.klimaManagementGuidelines?.phasen?.find(p => 
            p.phase === this.currentPhase || 
            (this.currentPhase.includes('vegetative') && p.phase === 'vegetative') ||
            (this.currentPhase.includes('flowering') && p.phase.includes('flowering'))
        );
        
        const strainGuidelines = climateGuidelines?.[strainKey];

        return `
            <div class="modern-card">
                <div class="modern-header">
                    <i class="fa-solid fa-cloud"></i> Klima-Daten
                    <span class="badge bg-info ms-2">${strainType === 'autoflowering' ? 'Autoflower' : 'Photoperiod'}</span>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-thermometer-half"></i> Aktuelle Messwerte
                </div>
                <div class="modern-section-content climate-data-container">
                    <div class="climate-section mb-3">
                        <div class="climate-value-row">
                            <span class="modern-label">Temperatur:</span> 
                            <span class="modern-value climate-temp-value ${getStatusClass(climate.temperature.status)}">
                                <i class="fa ${getStatusIcon(climate.temperature.status)}"></i>
                                ${climate.temperature.value}°C
                            </span>
                            <span class="badge climate-temp-status ${climate.temperature.status === 'success' ? 'bg-success' : climate.temperature.status === 'warning' ? 'bg-warning' : 'bg-danger'} ms-2">
                                ${climate.temperature.status === 'success' ? 'Optimal' : climate.temperature.status === 'warning' ? 'Akzeptabel' : 'Kritisch'}
                            </span>
                        </div>
                        <small class="text-muted">Ziel: ${climate.temperature.target}</small>
                    </div>
                    <div class="climate-section mb-3">
                        <div class="climate-value-row">
                            <span class="modern-label">Luftfeuchte:</span> 
                            <span class="modern-value climate-humidity-value ${getStatusClass(climate.humidity.status)}">
                                <i class="fa ${getStatusIcon(climate.humidity.status)}"></i>
                                ${climate.humidity.value}%
                            </span>
                            <span class="badge climate-humidity-status ${climate.humidity.status === 'success' ? 'bg-success' : climate.humidity.status === 'warning' ? 'bg-warning' : 'bg-danger'} ms-2">
                                ${climate.humidity.status === 'success' ? 'Optimal' : climate.humidity.status === 'warning' ? 'Akzeptabel' : 'Kritisch'}
                            </span>
                        </div>
                        <small class="text-muted">Ziel: ${climate.humidity.target}</small>
                    </div>
                    <div class="climate-section">
                        <div class="climate-value-row">
                            <span class="modern-label">VPD:</span> 
                            <span class="modern-value climate-vpd-value ${getStatusClass(climate.vpd.status)}">
                                <i class="fa ${getStatusIcon(climate.vpd.status)}"></i>
                                ${climate.vpd.value} kPa
                            </span>
                        </div>
                        <small class="text-muted">Ziel: ${climate.vpd.target}</small>
                    </div>
                </div>
                ${strainGuidelines ? `
                <div class="modern-section-title">
                    <i class="fa-solid fa-cog"></i> Strain-spezifische Ziele
                </div>
                <div class="modern-section-content">
                    <div class="strain-targets-grid">
                        <div class="strain-target-item">
                            <span class="modern-label">Tag-Temperatur:</span>
                            <span class="modern-value">${strainGuidelines.temperaturC?.tag?.[0] || 'N/A'} - ${strainGuidelines.temperaturC?.tag?.[1] || 'N/A'}°C</span>
                        </div>
                        <div class="strain-target-item">
                            <span class="modern-label">Nacht-Temperatur:</span>
                            <span class="modern-value">${strainGuidelines.temperaturC?.nacht?.[0] || 'N/A'} - ${strainGuidelines.temperaturC?.nacht?.[1] || 'N/A'}°C</span>
                        </div>
                        <div class="strain-target-item">
                            <span class="modern-label">Luftfeuchte:</span>
                            <span class="modern-value">${strainGuidelines.luftfeuchteProzent?.[0] || 'N/A'} - ${strainGuidelines.luftfeuchteProzent?.[1] || 'N/A'}%</span>
                        </div>
                        <div class="strain-target-item">
                            <span class="modern-label">VPD-Ziel:</span>
                            <span class="modern-value">${strainGuidelines.vpdZiel?.[0] || 'N/A'} - ${strainGuidelines.vpdZiel?.[1] || 'N/A'} kPa</span>
                        </div>
                        <div class="strain-target-item">
                            <span class="modern-label">CO₂:</span>
                            <span class="modern-value">${strainGuidelines.co2 || 'N/A'}</span>
                        </div>
                    </div>
                </div>
                ` : ''}
                <div class="modern-section-title">
                    <i class="fa-solid fa-lightbulb"></i> Empfehlung
                </div>
                <div class="modern-section-content">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong>Klima-Empfehlung:</strong> ${getClimateRecommendation(climate.temperature.value, climate.humidity.value)}
                    </div>
                    ${strainGuidelines?.empfehlungen ? `
                    <div class="strain-recommendations">
                        <h6 class="modern-label">Strain-spezifische Hinweise:</h6>
                        <ul class="recommendations-list">
                            ${strainGuidelines.empfehlungen.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                    ` : ''}
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-tools"></i> Klima-Aktionen
                </div>
                <div class="modern-section-content">
                    <div class="climate-actions mb-3">
                        <button id="climate-fill-from-vpd-btn" class="btn btn-sm btn-outline-info me-2">
                            <i class="fa fa-cloud-upload-alt"></i> Klima befüllen
                        </button>
                        <button id="climate-save-data-btn" class="btn btn-sm btn-outline-warning">
                            <i class="fa fa-database"></i> Klima-Daten speichern
                        </button>
                    </div>
                    <small class="text-info d-block">
                        <i class="fa fa-lightbulb"></i> "Klima befüllen" überträgt die VPD-Werte in die Klima-Daten
                    </small>
                    <small class="text-warning d-block mt-1">
                        <i class="fa fa-database"></i> "Klima-Daten speichern" speichert die aktuellen Werte in der Datenbank für die Historie
                    </small>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-history"></i> Klima-Historie
                </div>
                <div class="modern-section-content">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong>Klima-Daten-Historie:</strong> Verwenden Sie den "Klima-Daten speichern" Button, 
                        um aktuelle Messwerte in der Datenbank zu speichern. Eine detaillierte Klima-Historie mit Charts und Trends wird in Kürze implementiert.
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Substrat-Status rendern
     */
    renderSubstrateStatus(substrate) {
        return `
            <div class="modern-card">
                <div class="modern-header">
                    <i class="fa-solid fa-weight"></i> Substrat-Status
                </div>
                <div class="modern-section-content">
                    <div class="substrate-section mb-2">
                        <span class="modern-label">Substrat-Typ:</span> <span class="modern-value">${substrate.type}</span>
                    </div>
                    <div class="substrate-section mb-2">
                        <span class="modern-label">Topfgröße:</span> <span class="modern-value">${substrate.potSize}</span>
                    </div>
                    <div class="substrate-section">
                        <span class="modern-label">Feuchtigkeit:</span>
                        <div class="modern-progress mb-1">
                            <div class="modern-progress-bar" style="width: ${substrate.moisture.percentage}%"></div>
                        </div>
                        <small class="modern-value">${substrate.moisture.description}</small>
                    </div>
                    ${substrate.moisture.status === 'warning' ? `
                    <div class="alert alert-warning mt-2">
                        <i class="fa fa-exclamation-triangle"></i>
                        <strong>Hinweis:</strong> Die Feuchtigkeitsmessung ist noch nicht implementiert. 
                        Aktuell werden Dummy-Werte angezeigt. Eine echte Messung (z.B. mit Blumat Digital Pro oder ESP-Sensoren) wird in Zukunft integriert.
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * EC/pH-Status rendern
     */
    renderECpHStatus(ecph) {
        const getStatusClass = (status) => {
            switch (status) {
                case 'success': return 'modern-status-success';
                case 'warning': return 'modern-status-warning';
                case 'danger': return 'modern-status-danger';
                default: return 'modern-status-unknown';
            }
        };

        const getStatusIcon = (status) => {
            switch (status) {
                case 'success': return 'fa-check-circle';
                case 'warning': return 'fa-exclamation-triangle';
                case 'danger': return 'fa-exclamation-circle';
                default: return 'fa-question-circle';
            }
        };

        return `
            <div class="modern-card">
                <div class="modern-header">
                    <i class="fa-solid fa-flask"></i> Nährstoff-Status (EC/pH)
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-chart-line"></i> Aktuelle Werte
                </div>
                <div class="modern-section-content">
                    <div class="ecph-section mb-3">
                        <div class="ecph-value-row">
                            <span class="modern-label">EC-Wert:</span> 
                            <span class="modern-value ${getStatusClass(ecph.ec.status)}">
                                <i class="fa ${getStatusIcon(ecph.ec.status)}"></i>
                                ${ecph.ec.value} mS/cm
                            </span>
                        </div>
                        <small class="text-muted">Ziel: ${ecph.ec.target}</small>
                    </div>
                    <div class="ecph-section">
                        <div class="ecph-value-row">
                            <span class="modern-label">pH-Wert:</span> 
                            <span class="modern-value ${getStatusClass(ecph.ph.status)}">
                                <i class="fa ${getStatusIcon(ecph.ph.status)}"></i>
                                ${ecph.ph.value}
                            </span>
                        </div>
                        <small class="text-muted">Ziel: ${ecph.ph.target}</small>
                    </div>
                </div>
                <div class="modern-section-title">
                    <i class="fa-solid fa-info-circle"></i> Hinweise
                </div>
                <div class="modern-section-content">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        <strong>EC/pH-Monitoring:</strong> Regelmäßige Messungen helfen bei der optimalen Nährstoffversorgung.
                    </div>
                    <div class="ecph-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="document.querySelector('[data-tab=\"nutrients\"]').click()">
                            <i class="fa fa-flask"></i> Detaillierte Analyse
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Nährstoff-Daten laden
     */
    async loadNutrientsData() {
        try {
            // Spezifisch den Container im Vegetation-Widget suchen
            const nutrientsContent = this.container.querySelector('#nutrients');
            if (!nutrientsContent) {
                console.warn('🌱 Vegetation-Widget: nutrients Container nicht gefunden');
                return;
            }

            // Gespeicherte Werte aus LocalStorage laden
            const savedValues = this.loadNutrientInputValues();
            
            // API-Daten laden (falls verfügbar)
            let nutrientData;
            try {
                nutrientData = await this.getNutrientData();
            } catch (error) {
                console.warn('API-Daten nicht verfügbar, verwende Fallback-Daten');
                // Fallback-Daten mit gespeicherten Werten
                nutrientData = {
                    currentEC: savedValues.ec || 1.2,
                    currentPH: savedValues.ph || 6.0,
                    drainEC: savedValues.drainEC || null,
                    drainPH: savedValues.drainPH || null,
                    plantId: this.plantId,
                    targets: {
                        ec: { min: 1.0, max: 1.5 },
                        ph: { min: 6.0, max: 6.5 }
                    },
                    phase: this.currentPhase,
                    strain_type: this.getStrainType(),
                    substrate: savedValues.substrate || this.substrate || 'soil'
                };
            }

            // Nährstoff-Widget rendern
            this.renderNutrientWidget(nutrientData);
            
            // Event-Listener für Nährstoff-Tab einrichten
            this.setupNutrientEventListeners();
            
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Nährstoff-Daten:', error);
            const nutrientsContent = this.container.querySelector('#nutrients');
            if (nutrientsContent) {
                nutrientsContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fa fa-exclamation-triangle"></i>
                        Fehler beim Laden der Nährstoff-Daten: ${error.message}
                    </div>
                `;
            }
        }
    }

    /**
     * Training-Daten laden
     */
    async loadTrainingData() {
        try {
            // Platzhalter für Training-Daten
            const trainingContent = document.getElementById('trainingContent');
            if (trainingContent) {
                trainingContent.innerHTML = `
                    <div class="modern-card">
                        <div class="modern-header">
                            <i class="fa-solid fa-cut"></i> Training-Management
                        </div>
                        <div class="modern-section-content">
                            <p class="text-muted">Training-Funktionen werden in Kürze implementiert...</p>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Training-Daten:', error);
        }
    }

    /**
     * Indikatoren-Daten laden
     */
    async loadIndicatorsData() {
        try {
            // Platzhalter für Indikatoren-Daten
            const indicatorsContent = document.getElementById('indicatorsContent');
            if (indicatorsContent) {
                indicatorsContent.innerHTML = `
                    <div class="modern-card">
                        <div class="modern-header">
                            <i class="fa-solid fa-chart-line"></i> Wachstums-Indikatoren
                        </div>
                        <div class="modern-section-content">
                            <p class="text-muted">Indikator-Funktionen werden in Kürze implementiert...</p>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Indikatoren-Daten:', error);
        }
    }

    /**
     * Beleuchtungs-Daten laden
     */
    async loadLightingData() {
        try {
            // Aktuelle Phase für Vegetations-Beleuchtung bestimmen
            const currentPhase = this.currentPhase || 'vegetative_early';
            const strainType = this.strainType || 'photoperiodic';
            
            // Beleuchtungs-API aufrufen
            const response = await fetch(`/api/lighting/plan/${currentPhase}?strain_type=${strainType}&plant_id=${this.plantId}`);
            
            if (!response.ok) {
                throw new Error('Beleuchtungsdaten konnten nicht geladen werden');
            }
            
            const lightingData = await response.json();
            
            // Beleuchtungs-Übersicht rendern
            this.renderVegetationLightingOverview(lightingData);
            
            // Beleuchtungs-Einstellungen rendern
            this.renderVegetationLightingSettings(lightingData);
            
            // Beleuchtungs-Guidelines rendern
            this.renderVegetationLightingGuidelines(lightingData);
            
            // Status aktualisieren
            this.updateVegetationLightingStatus(lightingData);
            
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Beleuchtungsdaten:', error);
            this.showError('Beleuchtungsdaten konnten nicht geladen werden: ' + error.message);
        }
    }

    /**
     * Stress-Management-Daten laden
     */
    async loadStressData() {
        try {
            // Platzhalter für Stress-Management-Daten
            const stressContent = document.getElementById('stressContent');
            if (stressContent) {
                stressContent.innerHTML = `
                    <div class="modern-card">
                        <div class="modern-header">
                            <i class="fa-solid fa-exclamation-triangle"></i> Stress-Management
                        </div>
                        <div class="modern-section-content">
                            <p class="text-muted">Stress-Management-Funktionen werden in Kürze implementiert...</p>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Stress-Management-Daten:', error);
        }
    }

    // ===== HELPER-METHODEN =====

    /**
     * Phasen-Informationen abrufen
     */
    getPhaseInfo(phase) {
        const phaseNames = {
            'germination': { name: 'Keimung', description: 'Empfindliche Phase - minimale Eingriffe' },
            'vegetative_early': { name: 'Frühe Vegetation', description: 'Wurzel- und Blattentwicklung' },
            'vegetative_middle': { name: 'Mittlere Vegetation', description: 'Strukturelles Wachstum' },
            'vegetative_late': { name: 'Späte Vegetation', description: 'Vorbereitung auf Blüte' },
            'flowering_early': { name: 'Frühe Blüte', description: 'Stretchphase und Blüteninduktion' },
            'flowering_middle': { name: 'Mittlere Blüte', description: 'Volle Blüte - höchste Aufnahme' },
            'flowering_late': { name: 'Späte Blüte', description: 'Reifephase - Nährstoffreduktion' },
            'flush': { name: 'Spülung', description: 'Letzte Phase - Schonung' }
        };
        
        return phaseNames[phase] || { name: 'Unbekannte Phase', description: 'Phase nicht erkannt' };
    }

    /**
     * Phasen-Fortschritt berechnen
     */
    getPhaseProgress() {
        // Für Autoflowers: Fortschritt basierend auf Blüte-Tag berechnen
        if (this.strainType === 'autoflowering' || this.strainType === 'autoflower') {
            if (!this.currentDay) return 0;
            
            // Autoflower: Typische Gesamtdauer 70-90 Tage
            const totalDays = 80; // Durchschnitt
            const progress = Math.min(100, (this.currentDay / totalDays) * 100);
            return Math.round(progress);
        }
        
        // Für Photoperiodische: Fortschritt basierend auf Phase-Position
        const phaseOrder = [
            'germination', 'vegetative_early', 'vegetative_middle', 'vegetative_late',
            'flowering_early', 'flowering_middle', 'flowering_late', 'flush'
        ];
        
        const currentIndex = phaseOrder.indexOf(this.currentPhase);
        return currentIndex >= 0 ? Math.round(((currentIndex + 1) / phaseOrder.length) * 100) : 0;
    }

    /**
     * Aktuelle Empfehlungen abrufen
     */
    async getCurrentRecommendations() {
        const recommendations = [];
        
        try {
            // Bewässerungs-Empfehlung aus Guidelines
            const wateringGuidelines = this.guidelines.watering?.phases?.find(p => p.phase === this.currentPhase);
            if (wateringGuidelines) {
                const strainData = wateringGuidelines[this.strainType];
                if (strainData) {
                    recommendations.push(`Gießen: ${strainData.wateringFrequency} - ${strainData.wateringVolume}`);
                }
            }
            
            // Nährstoff-Empfehlung aus Guidelines
            const nutrientGuidelines = this.guidelines.nutrients?.phases?.find(p => p.phase === this.currentPhase);
            if (nutrientGuidelines) {
                const ecTarget = nutrientGuidelines.ecTarget?.[this.strainType];
                if (ecTarget) {
                    recommendations.push(`EC-Ziel: ${ecTarget} mS/cm`);
                }
            }
            
            // VPD-Empfehlung aus Guidelines
            const climateGuidelines = this.guidelines.climate?.phasen?.find(p => p.phase === this.currentPhase);
            if (climateGuidelines) {
                const vpdTarget = climateGuidelines[this.strainType]?.vpdTarget;
                if (vpdTarget) {
                    recommendations.push(`VPD-Ziel: ${vpdTarget} kPa`);
                }
            }
            
        } catch (error) {
            console.error('Fehler beim Laden der Empfehlungen:', error);
            recommendations.push('Empfehlungen konnten nicht geladen werden');
        }
        
        return recommendations;
    }

    /**
     * Aktuelle Warnungen abrufen
     */
    async getCurrentWarnings() {
        const warnings = [];
        
        try {
            // Phase-spezifische Warnungen
            if (this.currentPhase === 'flowering_late') {
                warnings.push('Späte Blüte - Flush vorbereiten (ab Tag 61)');
                warnings.push('Ernte-Fenster: Tag 68-82');
            }
            
            if (this.currentPhase === 'flowering_middle') {
                warnings.push('Mittlere Blüte - Nährstoff-Bedarf hoch');
            }
            
            // Strain-spezifische Warnungen
            if (this.strainType.toLowerCase().includes('auto')) {
                if (this.currentPhase === 'vegetative_early') {
                    warnings.push('Autoflower in früher Vegi - vorsichtig mit Training');
                }
                warnings.push('Autoflower - nicht zu viel Stress geben');
            }
            
            // Bewässerungs-Warnungen basierend auf Guidelines
            try {
                const wateringPlan = await this.getWateringPlan();
                if (wateringPlan.notes && wateringPlan.notes.length > 0) {
                    wateringPlan.notes.forEach(note => {
                        if (note.toLowerCase().includes('warnung') || note.toLowerCase().includes('achtung')) {
                            warnings.push(`Bewässerung: ${note}`);
                        }
                    });
                }
            } catch (error) {
                console.error('Fehler beim Laden der Bewässerungs-Warnungen:', error);
            }
            
            // Versuche, aktuelle Messwerte zu laden für Warnungen
            try {
                const data = await this.silentFetch(`/api/plants/${this.plantId}/entries?entry_type=measurement&limit=1`);
                if (data.success && data.entries.length > 0) {
                    const latestMeasurement = data.entries[0];
                    
                    // VPD-Warnungen
                    if (latestMeasurement.vpd) {
                        if (latestMeasurement.vpd > 2.0) {
                            warnings.push('VPD ist zu hoch - Stress-Risiko');
                        } else if (latestMeasurement.vpd < 0.5) {
                            warnings.push('VPD ist zu niedrig - Schimmel-Risiko');
                        }
                    }
                    
                    // EC-Warnungen
                    if (latestMeasurement.ec_water) {
                        if (latestMeasurement.ec_water > 2.5) {
                            warnings.push('EC-Wert ist zu hoch - Nährstoff-Burn-Risiko');
                        } else if (latestMeasurement.ec_water < 0.5) {
                            warnings.push('EC-Wert ist zu niedrig - Nährstoff-Mangel-Risiko');
                        }
                    }
                    
                    // pH-Warnungen
                    if (latestMeasurement.ph_water) {
                        if (latestMeasurement.ph_water > 7.0) {
                            warnings.push('pH-Wert ist zu hoch - Nährstoff-Aufnahme gestört');
                        } else if (latestMeasurement.ph_water < 5.5) {
                            warnings.push('pH-Wert ist zu niedrig - Wurzel-Schäden möglich');
                        }
                    }
                } else {
                    // Keine Messwerte vorhanden - allgemeine Warnung
                    warnings.push('Keine aktuellen Messwerte - regelmäßig VPD, EC und pH kontrollieren');
                }
            } catch (error) {
                // Keine Messwerte vorhanden - das ist normal
                warnings.push('Keine aktuellen Messwerte - regelmäßig VPD, EC und pH kontrollieren');
            }
            
        } catch (error) {
            console.error('Fehler beim Laden der Warnungen:', error);
            warnings.push('Warnungen konnten nicht geladen werden');
        }
        
        return warnings;
    }

    /**
     * Schnellaktionen abrufen
     */
    getQuickActions() {
        return [
            { label: 'Gießen', onClick: 'this.recordWatering()' },
            { label: 'EC messen', onClick: 'this.recordEC()' },
            { label: 'Training', onClick: 'this.recordTraining()' },
            { label: 'Notiz', onClick: 'this.addNote()' }
        ];
    }

    /**
     * Bewässerungsplan abrufen
     */
    async getWateringPlan() {
        try {
            // Guidelines immer neu laden, um sicherzustellen, dass die richtige Phase verwendet wird
            await this.loadAllGuidelines();
    

            // Pflanzendaten für Topfgröße laden
            const plantResponse = await fetch(`/api/plants/${this.plantId}`);
            const plantData = await plantResponse.json();
            const plant = plantData.plant || plantData;
            
            // Topfgröße extrahieren (kann "25 L" oder 25 sein)
            let potSize = 11; // Standard 11L
            if (plant.pot_size) {
                if (typeof plant.pot_size === 'string') {
                    const match = plant.pot_size.match(/(\d+)/);
                    if (match) {
                        potSize = parseInt(match[1]);
                    }
                } else {
                    potSize = parseInt(plant.pot_size);
                }
            }

            // Guidelines für aktuelle Phase und Strain-Typ finden
            const wateringGuidelines = this.guidelines.watering;
            if (!wateringGuidelines) {
                throw new Error('Bewässerungs-Guidelines nicht verfügbar');
            }

    

            
            const phaseGuideline = wateringGuidelines.phases.find(p => p.phase === this.currentPhase);
            if (!phaseGuideline) {
                console.error('🌱 Vegetation-Widget: Keine Guidelines für Phase:', this.currentPhase);
                throw new Error(`Keine Guidelines für Phase: ${this.currentPhase}`);
            }


            const strainType = this.strainType.toLowerCase().includes('auto') ? 'autoflower' : 'photoperiod';
            const strainGuideline = phaseGuideline[strainType];
            if (!strainGuideline) {
                throw new Error(`Keine Guidelines für Strain-Typ: ${strainType}`);
            }

            // Gießmenge berechnen
            let recommendedAmount = strainGuideline.wateringVolume;
            if (recommendedAmount.includes('%')) {
                // Prozentuale Berechnung basierend auf Topfgröße
                const percentageMatch = recommendedAmount.match(/(\d+(?:-\d+)?)/);
                if (percentageMatch) {
                    const percentageStr = percentageMatch[1];
                    if (percentageStr.includes('-')) {
                        const [min, max] = percentageStr.split('-').map(p => parseFloat(p));
                        const minAmount = (potSize * min / 100).toFixed(1);
                        const maxAmount = (potSize * max / 100).toFixed(1);
                        recommendedAmount = `${minAmount}-${maxAmount} L`;
                    } else {
                        const percentage = parseFloat(percentageStr);
                        const amount = (potSize * percentage / 100).toFixed(1);
                        recommendedAmount = `${amount} L`;
                    }
                }
            } else if (recommendedAmount.includes('ml')) {
                // Milliliter-Werte direkt verwenden
                recommendedAmount = recommendedAmount;
            } else if (recommendedAmount.includes('L')) {
                // Liter-Werte direkt verwenden
                recommendedAmount = recommendedAmount;
            }

            // Drain-Erwartung
            const drainExpected = strainGuideline.drainExpected;
            const drainTarget = drainExpected ? '10-20%' : 'Kein Drain';

            // Nächster Gießtermin schätzen
            const frequency = strainGuideline.wateringFrequency;
            let nextWatering = 'Morgen';
            if (frequency.includes('täglich')) {
                nextWatering = 'Heute Abend';
            } else if (frequency.includes('alle 2')) {
                nextWatering = 'Übermorgen';
            } else if (frequency.includes('alle 3')) {
                nextWatering = 'In 3 Tagen';
            } else if (frequency.includes('alle 4')) {
                nextWatering = 'In 4 Tagen';
            }

            return {
                phase: this.currentPhase,
                phaseName: this.getPhaseDisplayName(this.currentPhase),
                strainType: strainType === 'autoflower' ? 'Autoflower' : 'Photoperiodisch',
                potSize: `${potSize} L`,
                nextWatering: nextWatering,
                recommendedAmount: recommendedAmount,
                drainTarget: drainTarget,
                frequency: frequency,
                notes: strainGuideline.notes || [],
                drainExpected: drainExpected,
                wateringFactor: strainGuideline.wateringVolume
            };
        } catch (error) {
            console.error('Fehler beim Laden des Bewässerungsplans:', error);
            return {
                phase: this.currentPhase,
                phaseName: this.getPhaseDisplayName(this.currentPhase),
                strainType: 'Unbekannt',
                potSize: 'Unbekannt',
                nextWatering: 'Nicht verfügbar',
                recommendedAmount: 'Nicht verfügbar',
                drainTarget: 'Nicht verfügbar',
                frequency: 'Nicht verfügbar',
                notes: ['Bewässerungsplan konnte nicht geladen werden'],
                drainExpected: false,
                wateringFactor: 'Nicht verfügbar'
            };
        }
    }

    /**
     * VPD-Status abrufen
     */
    async getVPDStatus() {
        try {
            const response = await fetch(`/api/vpd/optimization/${this.currentPhase}?plant_id=${this.plantId}&strain_type=${this.strainType}&save_entry=false`);
            if (!response.ok) throw new Error('VPD-Daten konnten nicht geladen werden');
            
            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Fehler beim Laden der VPD-Daten');
            
            return {
                value: data.current_vpd?.toFixed(2) || 'N/A',
                status: data.vpd_status || 'unknown',
                targetRange: data.target_range || '0.8-1.2 kPa',
                recommendation: data.recommendation || 'VPD-Daten nicht verfügbar',
                optimal: data.optimal || '1.0 kPa',
                temperatureRange: data.temperature_range || '20-28°C',
                humidityRange: data.humidity_range || '40-70%'
            };
        } catch (error) {
            console.error('Fehler beim Laden der VPD-Daten:', error);
            return {
                value: 'N/A',
                status: 'unknown',
                targetRange: '0.8-1.2 kPa',
                recommendation: 'VPD-Daten konnten nicht geladen werden',
                optimal: '1.0 kPa',
                temperatureRange: '20-28°C',
                humidityRange: '40-70%'
            };
        }
    }

    /**
     * Klima-Daten abrufen
     */
    async getClimateData() {
        try {
            // Versuche aktuelle Messwerte zu laden
            const data = await this.silentFetch(`/api/plants/${this.plantId}/entries?entry_type=measurement&limit=1`);
            
            if (data.success && data.entries.length > 0) {
                const measurement = data.entries[0];
                return {
                    temperature: {
                        value: measurement.temp_c || 'N/A',
                        status: this.getClimateStatus(measurement.temp_c, 20, 28),
                        target: '20-28°C'
                    },
                    humidity: {
                        value: measurement.humidity_percent || 'N/A',
                        status: this.getClimateStatus(measurement.humidity_percent, 40, 70),
                        target: '40-70%'
                    },
                    vpd: {
                        value: measurement.vpd || 'N/A',
                        status: this.getVPDStatusValue(measurement.vpd),
                        target: '0.8-1.2 kPa'
                    }
                };
            } else {
                return {
                    temperature: { value: 'N/A', status: 'unknown', target: '20-28°C' },
                    humidity: { value: 'N/A', status: 'unknown', target: '40-70%' },
                    vpd: { value: 'N/A', status: 'unknown', target: '0.8-1.2 kPa' }
                };
            }
        } catch (error) {
            console.error('Fehler beim Laden der Klima-Daten:', error);
            return {
                temperature: { value: 'N/A', status: 'unknown', target: '20-28°C' },
                humidity: { value: 'N/A', status: 'unknown', target: '40-70%' },
                vpd: { value: 'N/A', status: 'unknown', target: '0.8-1.2 kPa' }
            };
        }
    }

    /**
     * Substrat-Status abrufen
     */
    async getSubstrateStatus() {
        try {
            // Pflanzendaten laden für Substrat-Info
            const response = await fetch(`/api/plants/${this.plantId}`);
            if (!response.ok) throw new Error('Pflanzendaten konnten nicht geladen werden');
            
            const plantData = await response.json();
            const plant = plantData.plant || plantData;
            
            // Substrat-Info aus Guidelines laden
            const substrateGuidelines = this.guidelines.substrate?.substrateTypes || [];
            const substrateInfo = substrateGuidelines.find(s => 
                s.name.toLowerCase().includes(plant.substrate?.toLowerCase() || '')
            );
            
            return {
                type: substrateInfo?.name || plant.substrate || 'Unbekannt',
                potSize: plant.pot_size || 'Unbekannt',
                moisture: {
                    percentage: 60, // Dummy-Wert - echte Messung noch nicht implementiert
                    status: 'warning',
                    description: 'Dummy-Wert (60%) - echte Feuchtigkeitsmessung wird implementiert'
                }
            };
        } catch (error) {
            console.error('Fehler beim Laden des Substrat-Status:', error);
            return {
                type: 'Unbekannt',
                potSize: 'Unbekannt',
                moisture: {
                    percentage: 0,
                    status: 'unknown',
                    description: 'Keine Daten verfügbar'
                }
            };
        }
    }

    /**
     * Klima-Status bestimmen
     */
    getClimateStatus(value, min, max) {
        if (!value || value === 'N/A') return 'unknown';
        const numValue = parseFloat(value);
        if (numValue >= min && numValue <= max) return 'success';
        if (numValue < min) return 'warning';
        return 'danger';
    }

    /**
     * VPD-Status bestimmen (für einzelne Werte)
     */
    getVPDStatusValue(vpd) {
        if (!vpd || vpd === 'N/A') return 'unknown';
        const numVPD = parseFloat(vpd);
        if (numVPD >= 0.8 && numVPD <= 1.2) return 'success';
        if (numVPD < 0.5 || numVPD > 2.0) return 'danger';
        return 'warning';
    }

    /**
     * VPD berechnen und Klima-Daten automatisch befüllen
     */
    async calculateVPD() {
        try {
            const temperatureInput = document.getElementById('temperature-input');
            const humidityInput = document.getElementById('humidity-input');
            const leafTemperatureInput = document.getElementById('leaf-temperature-input');
            
            if (!temperatureInput || !humidityInput) {
                console.error('VPD-Input-Felder nicht gefunden');
                return;
            }
            
            const temperature = parseFloat(temperatureInput.value);
            const humidity = parseFloat(humidityInput.value.replace(',', '.'));
            const leafTemperature = leafTemperatureInput ? parseFloat(leafTemperatureInput.value) : null;
            
            if (isNaN(temperature) || isNaN(humidity)) {
                alert('Bitte geben Sie gültige Temperatur- und Luftfeuchte-Werte ein.');
                return;
            }
            
            // VPD-Berechnung
            const vpd = this.calculateVPDValue(temperature, humidity, leafTemperature);
            
            // VPD-Status aktualisieren
            const vpdStatusElement = document.querySelector('.vpd-status-content');
            if (vpdStatusElement) {
                const status = this.getVPDStatusValue(vpd);
                const statusColor = status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'danger';
                
                vpdStatusElement.innerHTML = `
                    <div class="vpd-current-value mb-2">
                        <span class="modern-label">Aktueller VPD-Wert:</span> 
                        <span class="modern-value modern-status-${statusColor}">
                            <i class="fa ${status === 'success' ? 'fa-check-circle' : status === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle'}"></i>
                            ${vpd.toFixed(2)} kPa
                        </span>
                    </div>
                    <div class="vpd-recommendation">
                        <span class="modern-label">Empfehlung:</span> 
                        <span class="modern-value">${this.getVPDRecommendation(vpd)}</span>
                    </div>
                `;
            }
            
            // Klima-Daten automatisch befüllen
            this.updateClimateDataFromVPD(temperature, humidity, leafTemperature);
            
            // VPD-Optimierung mit Speichern aufrufen
            try {
                const response = await fetch(`/api/vpd/optimization/${this.currentPhase}?plant_id=${this.plantId}&strain_type=${this.strainType}&temperature=${temperature}&humidity=${humidity}&leaf_temperature=${leafTemperature || ''}&save_entry=true`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.entry_saved) {
                        console.log('VPD-Eintrag automatisch gespeichert: ID', data.entry_id, 'für Pflanze', this.plantId);
                        
                        // VPD-Historie neu laden
                        setTimeout(() => this.loadVPDHistory(), 500);
                    }
                }
            } catch (error) {
                console.error('Fehler beim Speichern des VPD-Eintrags:', error);
            }
            
        } catch (error) {
            console.error('Fehler bei der VPD-Berechnung:', error);
            alert('Fehler bei der VPD-Berechnung. Bitte versuchen Sie es erneut.');
        }
    }

    /**
     * Erweiterte VPD-Analyse durchführen
     */
    async performAdvancedVPDAnalysis() {
        try {
            const temperatureInput = document.getElementById('temperature-input');
            const humidityInput = document.getElementById('humidity-input');
            const leafTemperatureInput = document.getElementById('leaf-temperature-input');
            
            if (!temperatureInput || !humidityInput) {
                alert('Bitte geben Sie zuerst Temperatur- und Luftfeuchte-Werte ein.');
                return;
            }
            
            const temperature = parseFloat(temperatureInput.value);
            const humidity = parseFloat(humidityInput.value.replace(',', '.'));
            const leafTemperature = leafTemperatureInput ? parseFloat(leafTemperatureInput.value) : null;
            
            if (isNaN(temperature) || isNaN(humidity)) {
                alert('Bitte geben Sie gültige Temperatur- und Luftfeuchte-Werte ein.');
                return;
            }
            
            // Loading-State in inline-Div anzeigen
            this.showAdvancedAnalysisInline('Lade erweiterte VPD-Analyse...');
            
            const response = await fetch('/api/vpd/analysis/comprehensive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    temperature: temperature,
                    humidity: humidity,
                    leaf_temperature: leafTemperature,
                    phase: this.currentPhase,
                    strain_type: this.strainType,
                    plant_id: this.plantId
                })
            });
            
            if (!response.ok) {
                throw new Error('Erweiterte VPD-Analyse konnte nicht geladen werden');
            }
            
            const data = await response.json();
            this.renderAdvancedAnalysisInline(data.analysis);
            
        } catch (error) {
            console.error('Fehler bei der erweiterten VPD-Analyse:', error);
            this.showAdvancedAnalysisInline(`Fehler: ${error.message}`);
        }
    }

    /**
     * Erweiterte Analyse inline anzeigen
     */
    showAdvancedAnalysisInline(content) {
        // Prüfen ob inline-Div bereits existiert
        let analysisDiv = document.getElementById('vpd-advanced-analysis-inline');
        if (analysisDiv) {
            analysisDiv.remove();
        }

        const analysisHtml = `
            <div id="vpd-advanced-analysis-inline" class="advanced-analysis-inline">
                <div class="advanced-analysis-header">
                    <h6><i class="fas fa-chart-line me-2"></i>Erweiterte VPD-Analyse</h6>
                    <button type="button" class="btn-close btn-close-sm" onclick="this.closest('.advanced-analysis-inline').remove()" aria-label="Close"></button>
                </div>
                <div class="advanced-analysis-content">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">${content}</p>
                    </div>
                </div>
            </div>
        `;

        // Inline-Div nach dem VPD-Berechnungs-Bereich einfügen
        const vpdCalcSection = document.querySelector('.vpd-calc-section');
        if (vpdCalcSection) {
            vpdCalcSection.insertAdjacentHTML('afterend', analysisHtml);
        } else {
            // Fallback: In VPD-Optimierung Container einfügen
            const vpdOptimization = document.getElementById('vpdOptimization');
            if (vpdOptimization) {
                vpdOptimization.insertAdjacentHTML('beforeend', analysisHtml);
            }
        }
    }

    /**
     * Erweiterte Analyse inline rendern
     */
    renderAdvancedAnalysisInline(analysis) {
        const content = document.querySelector('.advanced-analysis-content');
        if (!content) return;

        // Sicherheitsprüfung für analysis-Objekt
        if (!analysis || typeof analysis !== 'object') {
            content.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i>
                    Keine gültigen Analysedaten verfügbar
                </div>
            `;
            return;
        }

        // Sicherheitsprüfung für erforderliche Felder
        const current = analysis.current || {};
        const targets = analysis.targets || {};

        content.innerHTML = `
            <div class="advanced-analysis">
                <div class="row">
                    <div class="col-md-6">
                        <div class="analysis-card">
                            <h6><i class="fa fa-chart-line"></i> Aktuelle Werte</h6>
                            <div class="analysis-values">
                                <div class="analysis-value">
                                    <span class="label">VPD:</span>
                                    <span class="value ${this.getVPDStatusClass(current.vpd)}">${current.vpd ? current.vpd.toFixed(2) : 'N/A'} kPa</span>
                                </div>
                                <div class="analysis-value">
                                    <span class="label">Temperatur:</span>
                                    <span class="value">${current.temperature || 'N/A'}°C</span>
                                </div>
                                <div class="analysis-value">
                                    <span class="label">Luftfeuchte:</span>
                                    <span class="value">${current.humidity || 'N/A'}%</span>
                                </div>
                                ${current.leaf_temperature ? `
                                <div class="analysis-value">
                                    <span class="label">Blatttemperatur:</span>
                                    <span class="value">${current.leaf_temperature}°C</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="analysis-card">
                            <h6><i class="fa fa-target"></i> Zielbereiche</h6>
                            <div class="analysis-values">
                                <div class="analysis-value">
                                    <span class="label">VPD-Ziel:</span>
                                    <span class="value">${targets.vpd ? `${targets.vpd.min ? targets.vpd.min.toFixed(2) : 'N/A'} - ${targets.vpd.max ? targets.vpd.max.toFixed(2) : 'N/A'}` : 'N/A'} kPa</span>
                                </div>
                                <div class="analysis-value">
                                    <span class="label">Temperatur:</span>
                                    <span class="value">${targets.temperature ? `${targets.temperature.min || 'N/A'} - ${targets.temperature.max || 'N/A'}` : 'N/A'}°C</span>
                                </div>
                                <div class="analysis-value">
                                    <span class="label">Luftfeuchte:</span>
                                    <span class="value">${targets.humidity ? `${targets.humidity.min || 'N/A'} - ${targets.humidity.max || 'N/A'}` : 'N/A'}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                ${analysis.ai_recommendations && Array.isArray(analysis.ai_recommendations) ? `
                <div class="analysis-card mt-3">
                    <h6><i class="fa fa-robot"></i> KI-Empfehlungen</h6>
                    <div class="ai-recommendations">
                        ${analysis.ai_recommendations.map(rec => `
                            <div class="ai-recommendation">
                                <i class="fa fa-lightbulb"></i>
                                <span>${rec}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
                
                ${analysis.problem_analysis && Array.isArray(analysis.problem_analysis) ? `
                <div class="analysis-card mt-3">
                    <h6><i class="fa fa-exclamation-triangle"></i> Problemanalyse</h6>
                    <div class="problem-analysis">
                        ${analysis.problem_analysis.map(problem => `
                            <div class="problem-item">
                                <i class="fa fa-warning"></i>
                                <span>${problem}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Klima-Daten automatisch aus VPD-Inputs befüllen
     */
    updateClimateDataFromVPD(temperature, humidity, leafTemperature) {
        try {
            // Klima-Container finden
            const climateContainer = document.querySelector('.climate-data-container');
            if (!climateContainer) {
                return;
            }
            
            // Temperatur-Werte aktualisieren
            const tempElements = climateContainer.querySelectorAll('.climate-temp-value');
            tempElements.forEach(element => {
                element.textContent = `${temperature.toFixed(1)}°C`;
                element.classList.remove('text-success', 'text-warning', 'text-danger');
                element.classList.add('text-primary');
            });
            
            // Luftfeuchte-Werte aktualisieren
            const humidityElements = climateContainer.querySelectorAll('.climate-humidity-value');
            humidityElements.forEach(element => {
                element.textContent = `${humidity.toFixed(0)}%`;
                element.classList.remove('text-success', 'text-warning', 'text-danger');
                element.classList.add('text-primary');
            });
            
            // Blatttemperatur aktualisieren (falls vorhanden)
            if (leafTemperature !== null && !isNaN(leafTemperature)) {
                const leafTempElements = climateContainer.querySelectorAll('.climate-leaf-temp-value');
                leafTempElements.forEach(element => {
                    element.textContent = `${leafTemperature.toFixed(1)}°C`;
                    element.classList.remove('text-success', 'text-warning', 'text-danger');
                    element.classList.add('text-primary');
                });
            }
            
            // Status-Badges aktualisieren
            this.updateClimateStatusBadges(temperature, humidity);
            
            // Klima-Empfehlung aktualisieren
            this.updateClimateRecommendation(temperature, humidity);
            
            // Erfolgsmeldung anzeigen
            this.showClimateUpdateSuccess();
            

            
        } catch (error) {
            console.error('Fehler beim Befüllen der Klima-Daten:', error);
        }
    }
    
    /**
     * Klima-Status-Badges aktualisieren
     */
    updateClimateStatusBadges(temperature, humidity) {
        try {
            const climateContainer = document.querySelector('.climate-data-container');
            if (!climateContainer) return;
            
            // Temperatur-Status
            const tempStatus = this.getClimateStatus(temperature, 22, 28);
            const tempBadges = climateContainer.querySelectorAll('.climate-temp-status');
            tempBadges.forEach(badge => {
                badge.className = `badge climate-temp-status ${tempStatus === 'success' ? 'bg-success' : tempStatus === 'warning' ? 'bg-warning' : 'bg-danger'}`;
                badge.textContent = tempStatus === 'success' ? 'Optimal' : tempStatus === 'warning' ? 'Akzeptabel' : 'Kritisch';
            });
            
            // Luftfeuchte-Status
            const humidityStatus = this.getClimateStatus(humidity, 50, 70);
            const humidityBadges = climateContainer.querySelectorAll('.climate-humidity-status');
            humidityBadges.forEach(badge => {
                badge.className = `badge climate-humidity-status ${humidityStatus === 'success' ? 'bg-success' : humidityStatus === 'warning' ? 'bg-warning' : 'bg-danger'}`;
                badge.textContent = humidityStatus === 'success' ? 'Optimal' : humidityStatus === 'warning' ? 'Akzeptabel' : 'Kritisch';
            });
            
        } catch (error) {
            console.error('Fehler beim Aktualisieren der Klima-Status-Badges:', error);
        }
    }
    
    /**
     * Klima-Empfehlung basierend auf aktuellen Werten aktualisieren
     */
    updateClimateRecommendation(temperature, humidity) {
        try {
            // Klima-Empfehlung-Container finden
            const recommendationContainer = document.querySelector('.alert-info');
            if (!recommendationContainer) {
                console.log('Klima-Empfehlung-Container nicht gefunden');
                return;
            }
            
            // Neue Empfehlung generieren
            const newRecommendation = this.generateClimateRecommendation(temperature, humidity);
            
            // Empfehlung aktualisieren
            const recommendationText = recommendationContainer.querySelector('strong');
            if (recommendationText) {
                recommendationText.nextSibling.textContent = ` ${newRecommendation}`;
            }
            
            // Alert-Farbe basierend auf Empfehlung anpassen
            recommendationContainer.className = 'alert';
            if (newRecommendation.includes('🚨') || newRecommendation.includes('Kritische Probleme')) {
                recommendationContainer.classList.add('alert-danger');
            } else if (newRecommendation.includes('⚠️') || newRecommendation.includes('Optimierungsbedarf')) {
                recommendationContainer.classList.add('alert-warning');
            } else if (newRecommendation.includes('✅') || newRecommendation.includes('optimal')) {
                recommendationContainer.classList.add('alert-success');
            } else {
                recommendationContainer.classList.add('alert-info');
            }
            

            
        } catch (error) {
            console.error('Fehler beim Aktualisieren der Klima-Empfehlung:', error);
        }
    }

    /**
     * Erfolgsmeldung für Klima-Update anzeigen
     */
    showClimateUpdateSuccess() {
        // Bestehende Erfolgsmeldung entfernen
        const existingAlert = document.querySelector('.climate-update-alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // Neue Erfolgsmeldung erstellen
        const alertHtml = `
            <div class="climate-update-alert alert alert-success alert-dismissible fade show" role="alert">
                <i class="fa fa-check-circle me-2"></i>
                <strong>Klima-Daten automatisch aktualisiert!</strong> 
                Die Werte wurden aus der VPD-Berechnung übernommen.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Meldung in den Klima-Container einfügen
        const climateContainer = document.querySelector('.climate-data-container');
        if (climateContainer) {
            climateContainer.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Meldung nach 5 Sekunden automatisch ausblenden
            setTimeout(() => {
                const alert = document.querySelector('.climate-update-alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }

    /**
     * Klima-Daten in der Datenbank speichern
     */
    async saveClimateData() {
        try {
            const temperatureInput = document.getElementById('temperature-input');
            const humidityInput = document.getElementById('humidity-input');
            const leafTemperatureInput = document.getElementById('leaf-temperature-input');
            
            if (!temperatureInput || !humidityInput) {
                alert('Bitte geben Sie zuerst Temperatur- und Luftfeuchte-Werte ein.');
                return;
            }
            
            const temperature = parseFloat(temperatureInput.value.replace(',', '.'));
            const humidity = parseFloat(humidityInput.value.replace(',', '.'));
            const leafTemperature = leafTemperatureInput ? parseFloat(leafTemperatureInput.value.replace(',', '.')) : null;
            
            if (isNaN(temperature) || isNaN(humidity)) {
                alert('Bitte geben Sie gültige Temperatur- und Luftfeuchte-Werte ein.');
                return;
            }
            
            // VPD berechnen
            const vpd = this.calculateVPDValue(temperature, humidity, leafTemperature);
            
            // Klima-Daten in der Datenbank speichern (über VPD-API)
            const response = await fetch('/api/vpd/analyse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    plant_id: this.plantId,
                    temperature: temperature,
                    humidity: humidity,
                    leaf_temperature: leafTemperature,
                    phase: this.currentPhase,
                    strain_type: this.strainType,
                    save_entry: true,
                    notes: `Klima-Messung: ${temperature}°C, ${humidity}%, VPD: ${vpd.toFixed(2)} kPa`
                })
            });
            
            if (!response.ok) {
                throw new Error('Fehler beim Speichern der Klima-Daten');
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Erfolgsmeldung anzeigen
                this.showClimateSaveSuccess(temperature, humidity, vpd);
                
                // Klima-Empfehlung mit den gespeicherten Werten aktualisieren
                this.updateClimateRecommendation(temperature, humidity);
                
                // VPD-Historie neu laden
                setTimeout(() => this.loadVPDHistory(), 500);
                
                console.log('Klima-Daten erfolgreich gespeichert:', data.entry_id);
            } else {
                throw new Error(data.message || 'Unbekannter Fehler beim Speichern');
            }
            
        } catch (error) {
            console.error('Fehler beim Speichern der Klima-Daten:', error);
            alert(`Fehler beim Speichern der Klima-Daten: ${error.message}`);
        }
    }

    /**
     * Erfolgsmeldung für Klima-Daten-Speicherung anzeigen
     */
    showClimateSaveSuccess(temperature, humidity, vpd) {
        // Bestehende Erfolgsmeldung entfernen
        const existingAlert = document.querySelector('.climate-save-alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // Neue Erfolgsmeldung erstellen
        const alertHtml = `
            <div class="climate-save-alert alert alert-success alert-dismissible fade show" role="alert">
                <i class="fa fa-database me-2"></i>
                <strong>Klima-Daten erfolgreich gespeichert!</strong> 
                Temperatur: ${temperature}°C, Luftfeuchte: ${humidity}%, VPD: ${vpd.toFixed(2)} kPa
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Meldung in den VPD-Container einfügen
        const vpdContainer = document.querySelector('.vpd-calc-section');
        if (vpdContainer) {
            vpdContainer.insertAdjacentHTML('afterend', alertHtml);
            
            // Meldung nach 5 Sekunden automatisch ausblenden
            setTimeout(() => {
                const alert = document.querySelector('.climate-save-alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }

    /**
     * Klima-Daten manuell aus VPD-Inputs befüllen
     */
    fillClimateFromVPD() {
        try {
            const temperatureInput = document.getElementById('temperature-input');
            const humidityInput = document.getElementById('humidity-input');
            const leafTemperatureInput = document.getElementById('leaf-temperature-input');
            
            if (!temperatureInput || !humidityInput) {
                alert('Bitte geben Sie zuerst Temperatur- und Luftfeuchte-Werte in der VPD-Berechnung ein.');
                return;
            }
            
            const temperature = parseFloat(temperatureInput.value.replace(',', '.'));
            const humidity = parseFloat(humidityInput.value.replace(',', '.'));
            const leafTemperature = leafTemperatureInput ? parseFloat(leafTemperatureInput.value.replace(',', '.')) : null;
            
            if (isNaN(temperature) || isNaN(humidity)) {
                alert('Bitte geben Sie gültige Temperatur- und Luftfeuchte-Werte ein.');
                return;
            }
            
            // VPD berechnen
            const vpd = this.calculateVPDValue(temperature, humidity, leafTemperature);
            // Klima-Daten befüllen (inkl. kPa)
            this.updateClimateDataFromVPD(temperature, humidity, leafTemperature, vpd);
            
        } catch (error) {
            console.error('Fehler beim Befüllen der Klima-Daten:', error);
            alert('Fehler beim Befüllen der Klima-Daten. Bitte versuchen Sie es erneut.');
        }
    }

    /**
     * VPD-Status-Klasse bestimmen
     */
    getVPDStatusClass(vpd) {
        if (vpd >= 0.8 && vpd <= 1.2) return 'text-success';
        if (vpd < 0.5 || vpd > 2.0) return 'text-danger';
        return 'text-warning';
    }

    /**
     * VPD-Wert berechnen
     */
    calculateVPDValue(temperature, humidity, leafTemperature) {
        // Temperatur für VPD-Berechnung (Blatttemperatur falls vorhanden, sonst Lufttemperatur)
        const tempForVPD = leafTemperature || temperature;
        
        // Sättigungsdampfdruck berechnen (Magnus-Formel)
        const saturationVaporPressure = 0.61094 * Math.exp((17.625 * tempForVPD) / (tempForVPD + 243.04));
        
        // Aktueller Dampfdruck
        const actualVaporPressure = (humidity / 100) * saturationVaporPressure;
        
        // VPD = Sättigungsdampfdruck - Aktueller Dampfdruck
        const vpd = saturationVaporPressure - actualVaporPressure;
        
        return vpd;
    }

    /**
     * Klima-Empfehlung generieren (separate Methode für Updates)
     */
    generateClimateRecommendation(temp, humidity) {
        // Prüfen ob Werte vorhanden sind
        if (!temp || !humidity || temp === 'N/A' || humidity === 'N/A') {
            return 'Keine aktuellen Messwerte verfügbar';
        }
        
        const tempNum = parseFloat(temp);
        const humNum = parseFloat(humidity);
        
        // Prüfen ob Werte gültige Zahlen sind
        if (isNaN(tempNum) || isNaN(humNum)) {
            return 'Ungültige Messwerte - bitte prüfen Sie die Eingaben';
        }
        
        // Detaillierte Klima-Empfehlung basierend auf aktuellen Guidelines
        let recommendations = [];
        
        // Temperatur-Analyse
        if (tempNum < 18) {
            recommendations.push('❄️ Temperatur zu niedrig (< 18°C) - Heizung erhöhen');
        } else if (tempNum < 22) {
            recommendations.push('🌡️ Temperatur leicht zu niedrig (18-22°C) - Heizung leicht erhöhen');
        } else if (tempNum >= 22 && tempNum <= 28) {
            recommendations.push('✅ Temperatur optimal (22-28°C)');
        } else if (tempNum > 28 && tempNum <= 32) {
            recommendations.push('🌡️ Temperatur leicht zu hoch (28-32°C) - Lüftung erhöhen');
        } else {
            recommendations.push('🔥 Temperatur kritisch hoch (> 32°C) - Sofortige Kühlung erforderlich');
        }
        
        // Luftfeuchte-Analyse
        if (humNum < 30) {
            recommendations.push('💨 Luftfeuchte kritisch niedrig (< 30%) - Verdunster aktivieren');
        } else if (humNum < 50) {
            recommendations.push('🌬️ Luftfeuchte zu niedrig (30-50%) - Luftbefeuchter aktivieren');
        } else if (humNum >= 50 && humNum <= 70) {
            recommendations.push('✅ Luftfeuchte optimal (50-70%)');
        } else if (humNum > 70 && humNum <= 80) {
            recommendations.push('💧 Luftfeuchte leicht zu hoch (70-80%) - Lüftung erhöhen');
        } else {
            recommendations.push('🌧️ Luftfeuchte kritisch hoch (> 80%) - Sofortige Entfeuchtung');
        }
        
        // VPD-Berechnung für zusätzliche Empfehlung
        const vpd = this.calculateVPDValue(tempNum, humNum);
        if (vpd < 0.5) {
            recommendations.push('⚠️ VPD zu niedrig - Schimmel-Risiko, Luftfeuchte reduzieren');
        } else if (vpd >= 0.8 && vpd <= 1.2) {
            recommendations.push('✅ VPD optimal (0.8-1.2 kPa)');
        } else if (vpd > 2.0) {
            recommendations.push('⚠️ VPD zu hoch - Stress-Risiko, Luftfeuchte erhöhen');
        }
        
        // Zusammenfassung erstellen
        const optimalCount = recommendations.filter(r => r.includes('✅')).length;
        const warningCount = recommendations.filter(r => r.includes('⚠️')).length;
        const criticalCount = recommendations.filter(r => r.includes('❄️') || r.includes('🔥') || r.includes('💨') || r.includes('🌧️')).length;
        
        if (criticalCount > 0) {
            return `🚨 Kritische Probleme: ${recommendations.filter(r => r.includes('❄️') || r.includes('🔥') || r.includes('💨') || r.includes('🌧️')).join(' | ')}`;
        } else if (warningCount > 0) {
            return `⚠️ Optimierungsbedarf: ${recommendations.filter(r => r.includes('⚠️')).join(' | ')}`;
        } else if (optimalCount >= 2) {
            return `✅ Klima-Bedingungen optimal (${tempNum}°C, ${humNum}%)`;
        } else {
            return recommendations.join(' | ');
        }
    }

    /**
     * VPD-Empfehlung generieren
     */
    getVPDRecommendation(vpd) {
        if (vpd >= 0.8 && vpd <= 1.2) {
            return 'Optimal - Pflanze ist in idealen Bedingungen';
        } else if (vpd < 0.5) {
            return 'Zu niedrig - Schimmel-Risiko, Luftfeuchte reduzieren';
        } else if (vpd > 2.0) {
            return 'Zu hoch - Stress-Risiko, Luftfeuchte erhöhen';
        } else if (vpd < 0.8) {
            return 'Unter optimal - Luftfeuchte leicht reduzieren';
        } else {
            return 'Über optimal - Luftfeuchte leicht erhöhen';
        }
    }

    /**
     * VPD-Eintrag speichern
     */
    async saveVPDEntry(temperature, humidity, vpd, leafTemperature) {
        try {
            const entry = {
                plant_id: this.plantId,
                entry_type: 'measurement',
                entry_date: new Date().toISOString().split('T')[0],
                title: 'VPD-Messung',
                content: `Temperatur: ${temperature}°C, Luftfeuchte: ${humidity}%, VPD: ${vpd.toFixed(2)} kPa`,
                temp_c: temperature,
                humidity_percent: humidity,
                vpd: vpd,
                leaf_temperature: leafTemperature
            };
            
            const response = await fetch(`/api/entries`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(entry)
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('VPD-Eintrag automatisch gespeichert: ID', result.entry_id, 'für Pflanze', this.plantId);
                
                // Zuletzt verwendete Werte speichern
                this.saveVPDInputValues(temperature, humidity, leafTemperature);
            } else {
                console.error('Fehler beim Speichern des VPD-Eintrags:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('Fehler beim Speichern des VPD-Eintrags:', error);
        }
    }

    /**
     * VPD-Inputs aktualisieren
     */
    updateVPDInputs() {
        const temperatureInput = document.getElementById('temperature-input');
        const humidityInput = document.getElementById('humidity-input');
        const leafTemperatureInput = document.getElementById('leaf-temperature-input');
        
        if (temperatureInput && humidityInput) {
            const temperature = parseFloat(temperatureInput.value);
            const humidity = parseFloat(humidityInput.value.replace(',', '.'));
            const leafTemperature = leafTemperatureInput ? parseFloat(leafTemperatureInput.value) : null;
            
            if (!isNaN(temperature) && !isNaN(humidity)) {
                const vpd = this.calculateVPDValue(temperature, humidity, leafTemperature);
                const status = this.getVPDStatusValue(vpd);
                
                // Vorschau der VPD-Berechnung anzeigen
                const calcBtn = document.getElementById('vpd-calc-btn');
                if (calcBtn) {
                    calcBtn.innerHTML = `<i class="fa fa-calculator"></i> VPD: ${vpd.toFixed(2)} kPa`;
                    calcBtn.className = `btn btn-sm btn-${status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'danger'}`;
                }
            }
        }
    }

    /**
     * VPD-Input-Werte speichern
     */
    saveVPDInputValues(temperature, humidity, leafTemperature) {
        try {
            const vpdValues = {
                temperature: temperature,
                humidity: humidity,
                leafTemperature: leafTemperature,
                timestamp: new Date().toISOString()
            };
            console.log('saveVPDInputValues: plantId =', this.plantId, vpdValues);
            localStorage.setItem(`vpd_inputs_${this.plantId}`, JSON.stringify(vpdValues));
        } catch (error) {
            console.error('Fehler beim Speichern der VPD-Input-Werte:', error);
        }
    }

    /**
     * VPD-Input-Werte laden
     */
    loadVPDInputValues() {
        try {
            const stored = localStorage.getItem(`vpd_inputs_${this.plantId}`);
            if (stored) {
                const vpdValues = JSON.parse(stored);
        
                // Prüfen ob die Werte nicht zu alt sind (max. 7 Tage)
                const storedDate = new Date(vpdValues.timestamp);
                const now = new Date();
                const daysDiff = (now - storedDate) / (1000 * 60 * 60 * 24);
                if (daysDiff <= 7) {
                    return {
                        temperature: vpdValues.temperature,
                        humidity: vpdValues.humidity,
                        leafTemperature: vpdValues.leafTemperature
                    };
                } else {
                    // Alte Werte löschen
                    localStorage.removeItem(`vpd_inputs_${this.plantId}`);
                }
            }
        } catch (error) {
            console.error('Fehler beim Laden der VPD-Input-Werte:', error);
        }
        // Standardwerte zurückgeben
        return {
            temperature: 25,
            humidity: 50,
            leafTemperature: null
        };
    }

    /**
     * VPD-Inputs mit aktuellen Klima-Daten aus der Datenbank befüllen
     */
    async populateVPDInputsFromClimateData() {
        try {
            // Aktuelle Klima-Daten aus der Datenbank laden
            const climate = await this.getClimateData();
            
            const temperatureInput = document.getElementById('temperature-input');
            const humidityInput = document.getElementById('humidity-input');
            const leafTemperatureInput = document.getElementById('leaf-temperature-input');
            
            // Temperatur befüllen (falls verfügbar)
            if (temperatureInput && climate.temperature.value !== 'N/A') {
                temperatureInput.value = climate.temperature.value;
                console.log('Temperatur-Input befüllt mit:', climate.temperature.value);
            }
            
            // Luftfeuchte befüllen (falls verfügbar)
            if (humidityInput && climate.humidity.value !== 'N/A') {
                humidityInput.value = climate.humidity.value;
                console.log('Luftfeuchte-Input befüllt mit:', climate.humidity.value);
            }
            
            // Blatttemperatur befüllen (falls verfügbar - normalerweise nicht in der DB)
            if (leafTemperatureInput) {
                // Blatttemperatur ist normalerweise nicht in der DB gespeichert
                // Daher lassen wir das Feld leer oder verwenden einen Standardwert
                leafTemperatureInput.value = '';
            }
            
            // VPD-Vorschau aktualisieren
            this.updateVPDInputs();
            
            // Werte im LocalStorage speichern
            if (climate.temperature.value !== 'N/A' && climate.humidity.value !== 'N/A') {
                this.saveVPDInputValues(
                    parseFloat(climate.temperature.value),
                    parseFloat(climate.humidity.value),
                    null // Blatttemperatur
                );
                console.log('Klima-Daten in LocalStorage gespeichert');
            }
            
        } catch (error) {
            console.error('Fehler beim Befüllen der VPD-Inputs mit Klima-Daten:', error);
            // Fallback: Gespeicherte Werte verwenden
            this.populateVPDInputs();
        }
    }

    /**
     * VPD-Input-Werte in die Felder eintragen
     */
    populateVPDInputs(retryCount = 0) {
        if (!this.plantId) {
            if (retryCount < 10) {
                console.warn('populateVPDInputs: plantId noch nicht gesetzt, versuche erneut...', retryCount);
                setTimeout(() => this.populateVPDInputs(retryCount + 1), 100);
            } else {
                console.error('populateVPDInputs: plantId nach 10 Versuchen immer noch nicht gesetzt!');
            }
            return;
        }

        const vpdValues = this.loadVPDInputValues();
        
        const temperatureInput = document.getElementById('temperature-input');
        const humidityInput = document.getElementById('humidity-input');
        const leafTemperatureInput = document.getElementById('leaf-temperature-input');
        
        if (temperatureInput) {
            temperatureInput.value = vpdValues.temperature;
        }
        if (humidityInput) {
            humidityInput.value = vpdValues.humidity;
        }
        if (leafTemperatureInput && vpdValues.leafTemperature) {
            leafTemperatureInput.value = vpdValues.leafTemperature;
        }
        
        // VPD berechnen
        const vpd = this.calculateVPDValue(vpdValues.temperature, vpdValues.humidity, vpdValues.leafTemperature);
        // Klima-Daten automatisch befüllen (inkl. kPa)
        this.updateClimateDataFromVPD(vpdValues.temperature, vpdValues.humidity, vpdValues.leafTemperature, vpd);
        
        // VPD-Vorschau aktualisieren
        this.updateVPDInputs();
    }

    /**
     * EC/pH-Status abrufen
     */
    async getECpHStatus() {
        try {
            const response = await fetch(`/api/nutrients/diagnose?plant_id=${this.plantId}&phase=${this.currentPhase}&strain_type=${this.strainType}&substrate=${this.substrate}`);
            if (!response.ok) throw new Error('EC/pH-Daten konnten nicht geladen werden');
            
            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Fehler beim Laden der EC/pH-Daten');
            
            return {
                ec: { 
                    value: data.current_ec?.toFixed(2) || 'N/A', 
                    status: data.ec_status || 'unknown', 
                    target: data.ec_target || '0.9-1.1 mS/cm' 
                },
                ph: { 
                    value: data.current_ph?.toFixed(2) || 'N/A', 
                    status: data.ph_status || 'unknown', 
                    target: data.ph_target || '6.2-6.6' 
                }
            };
        } catch (error) {
            // Keine Messwerte vorhanden - das ist normal
            return {
                ec: { value: 'Keine Daten', status: 'unknown', target: '0.9-1.1 mS/cm' },
                ph: { value: 'Keine Daten', status: 'unknown', target: '6.2-6.6' }
            };
        }
    }

    /**
     * Dünger-Empfehlungen abrufen
     */
    async getFertilizerRecommendations() {
        try {
            const response = await fetch(`/api/nutrients/diagnose?plant_id=${this.plantId}&phase=${this.currentPhase}&strain_type=${this.strainType}&substrate=${this.substrate}`);
            if (!response.ok) throw new Error('Dünger-Empfehlungen konnten nicht geladen werden');
            
            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Fehler beim Laden der Dünger-Empfehlungen');
            
            return data.recommendations || [];
        } catch (error) {
            // Keine Dünger-Empfehlungen verfügbar - das ist normal
            return [];
        }
    }

    /**
     * Trainingszeitpunkte abrufen
     */
    async getTrainingTiming() {
        try {
            const trainingGuidelines = this.guidelines.training?.phases?.find(p => p.phase === this.currentPhase);
            const strainData = trainingGuidelines?.[this.strainType];
            
            if (!strainData) {
                return {
                    allowedMethods: ['Keine Daten verfügbar'],
                    forbiddenMethods: [],
                    optimalTiming: 'Nicht verfügbar'
                };
            }
            
            return {
                allowedMethods: strainData.empfohlenesTraining || [],
                forbiddenMethods: strainData.verboten || [],
                optimalTiming: strainData.optimalerZeitpunkt || 'Jetzt möglich'
            };
        } catch (error) {
            console.error('Fehler beim Laden der Training-Daten:', error);
            return {
                allowedMethods: ['Fehler beim Laden'],
                forbiddenMethods: [],
                optimalTiming: 'Nicht verfügbar'
            };
        }
    }

    /**
     * Wachstumsanalyse abrufen
     */
    async getGrowthAnalysis() {
        try {
            // Versuche, aktuelle Messwerte zu laden
            const data = await this.silentFetch(`/api/plants/${this.plantId}/entries?entry_type=measurement&limit=5`);
            if (!data.success) {
                // Keine Messwerte vorhanden - das ist normal
                return {
                    growthRate: { value: 'Keine Daten', status: 'unknown' },
                    trend: 'Keine Messwerte verfügbar'
                };
            }
            
            const measurements = data.entries || [];
            
            if (measurements.length === 0) {
                return {
                    growthRate: { value: 'Keine Daten', status: 'unknown' },
                    trend: 'Keine Messwerte verfügbar'
                };
            }
            
            // Einfache Trend-Analyse basierend auf Höhen-Messungen
            const heightMeasurements = measurements.filter(m => m.height_cm).map(m => m.height_cm);
            
            if (heightMeasurements.length >= 2) {
                const growthRate = heightMeasurements[heightMeasurements.length - 1] - heightMeasurements[0];
                const avgGrowth = growthRate / heightMeasurements.length;
                
                let status = 'success';
                let trend = 'Positiv - konstantes Wachstum';
                
                if (avgGrowth < 0.5) {
                    status = 'warning';
                    trend = 'Langsames Wachstum - prüfen Sie die Bedingungen';
                } else if (avgGrowth > 2) {
                    status = 'success';
                    trend = 'Sehr gutes Wachstum';
                }
                
                return {
                    growthRate: { value: `${avgGrowth.toFixed(1)} cm/Tag`, status },
                    trend
                };
            }
            
            return {
                growthRate: { value: 'Gut', status: 'success' },
                trend: 'Positiv - konstantes Wachstum'
            };
            
        } catch (error) {
            // Keine Messwerte vorhanden - das ist normal
            return {
                growthRate: { value: 'Keine Daten', status: 'unknown' },
                trend: 'Keine Messwerte verfügbar'
            };
        }
    }

    /**
     * Stress-Status abrufen
     */
    async getStressStatus() {
        try {
            // Versuche, aktuelle Messwerte zu laden
            const data = await this.silentFetch(`/api/plants/${this.plantId}/entries?entry_type=measurement&limit=3`);
            if (!data.success) {
                // Keine Messwerte vorhanden - das ist normal
                return {
                    level: 'unknown',
                    percentage: 0,
                    description: 'Keine Messwerte verfügbar'
                };
            }
            
            const measurements = data.entries || [];
            
            if (measurements.length === 0) {
                return {
                    level: 'unknown',
                    percentage: 0,
                    description: 'Keine Messwerte verfügbar'
                };
            }
            
            // Einfache Stress-Analyse basierend auf VPD und EC-Werten
            const latestMeasurement = measurements[0];
            let stressLevel = 'low';
            let stressPercentage = 25;
            let description = 'Niedriger Stress-Level - Pflanze ist gesund';
            
            if (latestMeasurement.vpd) {
                if (latestMeasurement.vpd > 1.5) {
                    stressLevel = 'medium';
                    stressPercentage = 50;
                    description = 'Mittlerer Stress-Level - VPD könnte optimiert werden';
                } else if (latestMeasurement.vpd > 2.0) {
                    stressLevel = 'high';
                    stressPercentage = 75;
                    description = 'Hoher Stress-Level - VPD ist zu hoch';
                }
            }
            
            if (latestMeasurement.ec_water) {
                if (latestMeasurement.ec_water > 2.5) {
                    stressLevel = 'high';
                    stressPercentage = 80;
                    description = 'Hoher Stress-Level - EC-Wert ist zu hoch';
                }
            }
            
            return {
                level: stressLevel,
                percentage: stressPercentage,
                description
            };
            
        } catch (error) {
            // Keine Messwerte vorhanden - das ist normal
            return {
                level: 'unknown',
                percentage: 0,
                description: 'Keine Messwerte verfügbar'
            };
        }
    }

    /**
     * Phase-Display-Name abrufen
     */
    getPhaseDisplayName(phase) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Vegetation',
            'vegetative_middle': 'Mittlere Vegetation',
            'vegetative_late': 'Späte Vegetation',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phase] || phase.replace('_', ' ');
    }

    // ===== MODULE-ERSTELLUNG =====

    createSubstrateManager() {
        return {
            getSubstrateInfo: (substrateId) => {
                const substrates = this.guidelines.substrate?.substrateTypes || [];
                return substrates.find(s => s.name.toLowerCase().includes(substrateId.toLowerCase()));
            }
        };
    }

    createWateringManager() {
        return {
            getWateringRecommendation: (phase, strainType) => {
                const guidelines = this.guidelines.watering?.phases?.find(p => p.phase === phase);
                return guidelines?.[strainType];
            }
        };
    }

    createClimateManager() {
        return {
            getVPDRecommendation: (phase, strainType) => {
                const guidelines = this.guidelines.climate?.phasen?.find(p => p.phase === phase);
                return guidelines?.[strainType];
            }
        };
    }

    createNutrientManager() {
        return {
            getNutrientRecommendation: (phase, strainType) => {
                const guidelines = this.guidelines.nutrients?.phases?.find(p => p.phase === phase);
                return guidelines;
            }
        };
    }

    createStressManager() {
        return {
            getStressRecommendation: (phase, strainType) => {
                const guidelines = this.guidelines.stress?.phases?.find(p => p.phase === phase);
                return guidelines?.[strainType];
            }
        };
    }

    createTrainingManager() {
        return {
            getTrainingRecommendation: (phase, strainType) => {
                const guidelines = this.guidelines.training?.phases?.find(p => p.phase === phase);
                return guidelines?.[strainType];
            }
        };
    }

    // ===== UTILITY-METHODEN =====

    showError(message) {
        console.error(message);
        // Hier könnte eine Toast-Nachricht oder Alert angezeigt werden
    }

    closeWidget() {
        const container = document.getElementById(this.widgetId);
        if (container) {
            container.innerHTML = '';
        }
    }

    // ===== ÖFFENTLICHE METHODEN =====

    /**
     * Widget aktualisieren
     */
    async update() {
        if (!this.isInitialized) return;
        
        await this.loadPlantData();
        await this.loadCurrentData();
    }

    /**
     * Phase ändern
     */
    async setPhase(phase) {
        this.currentPhase = phase;
        await this.update();
    }

    /**
     * Strain-Type ändern
     */
    async setStrainType(strainType) {
        this.strainType = strainType;
        await this.update();
    }

    /**
     * Event-Listener für Bewässerungsplan einrichten
     */
    setupWateringEventListeners() {
        // Event-Delegation für dynamisch erstellte Elemente
        const container = document.getElementById('wateringPlan');
        if (!container) return;

        container.addEventListener('click', (e) => {
            if (e.target.id === 'watering-calc-btn') {
                this.calculateWatering();
            }
        });

        // Input-Event-Listener für Live-Updates
        container.addEventListener('input', (e) => {
            if (
                e.target.id === 'substrate-input' ||
                e.target.id === 'pot-size-input' ||
                e.target.id === 'water-amount-input'
            ) {
                this.updateWateringInputValues();
                // Nur die Berechnung neu laden, nicht den Header aktualisieren
                this.loadWateringData();
            }
        });
    }

    /**
     * Bewässerungsplan berechnen
     */
    async calculateWatering() {
        this.updateWateringInputValues();
        await this.loadWateringData();
    }

    /**
     * Input-Werte für Bewässerung aktualisieren und im LocalStorage speichern
     */
    updateWateringInputValues() {
        const substrateInput = document.getElementById('substrate-input');
        const potSizeInput = document.getElementById('pot-size-input');
        const waterAmountInput = document.getElementById('water-amount-input');
        
        if (substrateInput) this.substrate = substrateInput.value;
        if (potSizeInput) this.potSize = parseFloat(potSizeInput.value) || 11;
        if (waterAmountInput) this.currentWaterAmount = parseFloat(waterAmountInput.value) || 0;

        // Speichere die Werte im LocalStorage
        const wateringSettings = {
            substrate: this.substrate,
            potSize: this.potSize,
            waterAmount: this.currentWaterAmount
        };
        localStorage.setItem('vegetationWidgetWateringSettings', JSON.stringify(wateringSettings));
    }

    /**
     * Lade gespeicherte Werte aus dem LocalStorage und setze sie in die Inputs
     */
    loadWateringInputValuesFromStorage() {
        const settings = localStorage.getItem('vegetationWidgetWateringSettings');
        if (settings) {
            try {
                const { substrate, potSize, waterAmount } = JSON.parse(settings);
                if (substrate) this.substrate = substrate;
                if (potSize) this.potSize = potSize;
                if (waterAmount) this.currentWaterAmount = waterAmount;
            } catch (e) {
                // Ignoriere Fehler
            }
        }
    }

    /**
     * Bewässerungsfehler rendern
     */
    renderWateringError(message) {
        const container = document.getElementById('wateringPlan');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler beim Laden des Bewässerungsplans: ${message}
                </div>
            `;
        }
    }

    /**
     * Strain-Type ermitteln
     */
    getStrainType() {
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        return strainType;
    }

    /**
     * Bewässerungs-Guidelines laden
     */
    async loadWateringGuidelines() {
        try {
            const response = await fetch('/static/data/watering-guidelines.json');
            if (!response.ok) {
                throw new Error('Guidelines konnten nicht geladen werden');
            }
            const data = await response.json();
            this.wateringGuidelines = data.wateringGuidelines;
        } catch (error) {
            console.warn('Bewässerungs-Guidelines konnten nicht geladen werden:', error);
            this.wateringGuidelines = null;
        }
    }

    /**
     * Benutzerfreundliche Phase-Namen
     */
    getFriendlyPhaseName(phaseKey) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phaseKey] || phaseKey.replace('_', ' ');
    }

    /**
     * VPD-Historie laden und anzeigen
     */
    async loadVPDHistory() {
        try {
            const historyContainer = document.getElementById('vpd-history-chart');
            if (!historyContainer) return;

            // Loading-Zustand anzeigen
            historyContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fa fa-spinner fa-spin fa-2x mb-2"></i>
                    <p>VPD-Verlauf wird geladen...</p>
                </div>
            `;

            // VPD-Einträge aus der vpd_entries Tabelle laden
            const response = await fetch(`/api/vpd/history/${this.plantId}?limit=20`);
            if (!response.ok) {
                throw new Error('VPD-Historie konnte nicht geladen werden');
            }

            const data = await response.json();
            const entries = data.history || [];

            if (entries.length === 0) {
                historyContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fa fa-chart-line fa-2x mb-2"></i>
                        <p>Keine VPD-Messungen vorhanden</p>
                        <small>Führen Sie Ihre erste VPD-Berechnung durch</small>
                    </div>
                `;
                return;
            }

            // Daten für Chart aufbereiten
            const vpdEntries = entries.map(entry => ({
                date: entry.entry_date,
                vpd: parseFloat(entry.vpd_value),
                temperature: entry.temperature,
                humidity: entry.humidity,
                leafTemperature: entry.leaf_temperature,
                phase: entry.phase,
                strainType: entry.strain_type
            }));

            // Chart erstellen
            this.renderVPDHistoryChart(vpdEntries, historyContainer);

        } catch (error) {
            console.error('Fehler beim Laden der VPD-Historie:', error);
            const historyContainer = document.getElementById('vpd-history-chart');
            if (historyContainer) {
                historyContainer.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fa fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>Fehler beim Laden der VPD-Historie</p>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }
    }

    /**
     * VPD-Historie-Chart rendern
     */
    renderVPDHistoryChart(vpdEntries, container) {
        // Daten für das Chart vorbereiten
        const chartData = vpdEntries
            .sort((a, b) => new Date(a.date) - new Date(b.date))
            .slice(-10); // Letzte 10 Einträge

        if (chartData.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fa fa-chart-line fa-2x mb-2"></i>
                    <p>Keine VPD-Daten für Chart</p>
                </div>
            `;
            return;
        }

        // Chart-HTML erstellen
        const chartHtml = `
            <div class="vpd-history-summary mb-3">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="vpd-stat">
                            <div class="vpd-stat-value">${chartData.length}</div>
                            <div class="vpd-stat-label">Messungen</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="vpd-stat">
                            <div class="vpd-stat-value">${(chartData.reduce((sum, entry) => sum + entry.vpd, 0) / chartData.length).toFixed(2)}</div>
                            <div class="vpd-stat-label">Ø VPD (kPa)</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="vpd-stat">
                            <div class="vpd-stat-value">${new Date(chartData[chartData.length - 1].date).toLocaleDateString('de-DE')}</div>
                            <div class="vpd-stat-label">Letzte Messung</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="vpd-history-chart-container">
                <canvas id="vpd-chart-canvas"></canvas>
            </div>
            <div class="vpd-history-table mt-3">
                <h6 class="modern-label mb-2">Letzte VPD-Messungen:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Datum</th>
                                <th>VPD (kPa)</th>
                                <th>Temp (°C)</th>
                                <th>LF (%)</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${chartData.slice(-5).reverse().map(entry => {
                                const status = this.getVPDStatusValue(entry.vpd);
                                const statusClass = status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'danger';
                                const statusIcon = status === 'success' ? 'fa-check-circle' : status === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle';
                                return `
                                    <tr>
                                        <td>${new Date(entry.date).toLocaleDateString('de-DE')}</td>
                                        <td><strong>${entry.vpd.toFixed(2)}</strong></td>
                                        <td>${entry.temperature}</td>
                                        <td>${entry.humidity}</td>
                                        <td><span class="badge bg-${statusClass}"><i class="fa ${statusIcon}"></i></span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        container.innerHTML = chartHtml;

        // Chart mit Chart.js erstellen (falls verfügbar)
        this.createVPDChart(chartData);
    }

    /**
     * VPD-Chart mit Chart.js erstellen
     */
    createVPDChart(chartData) {
        const canvas = document.getElementById('vpd-chart-canvas');
        if (!canvas || typeof Chart === 'undefined') {
            return; // Chart.js nicht verfügbar
        }

        const ctx = canvas.getContext('2d');
        
        // Chart-Daten vorbereiten
        const labels = chartData.map(entry => new Date(entry.date).toLocaleDateString('de-DE'));
        const vpdValues = chartData.map(entry => entry.vpd);
        
        // Zielbereiche definieren
        const optimalMin = 0.8;
        const optimalMax = 1.2;
        const warningMin = 0.5;
        const warningMax = 2.0;

        this._historyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'VPD (kPa)',
                    data: vpdValues,
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: 0
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `VPD: ${context.parsed.y.toFixed(2)} kPa`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: Math.max(0, Math.min(...vpdValues) - 0.2),
                        max: Math.max(...vpdValues) + 0.2,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(1) + ' kPa';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                annotation: {
                    annotations: {
                        optimalZone: {
                            type: 'box',
                            yMin: optimalMin,
                            yMax: optimalMax,
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            borderColor: 'rgba(76, 175, 80, 0.3)',
                            borderWidth: 1
                        },
                        warningZone: {
                            type: 'box',
                            yMin: warningMin,
                            yMax: warningMax,
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            borderColor: 'rgba(255, 193, 7, 0.3)',
                            borderWidth: 1
                        }
                    }
                }
            }
        });
        // Nach dem Rendern Resize erzwingen
        if (this._historyChart) {
            this._historyChart.resize();
        }
    }

    // updateClimateDataFromVPD erweitert um VPD-Wert
    updateClimateDataFromVPD(temperature, humidity, leafTemperature, vpdValue) {
        try {
            const climateContainer = document.querySelector('.climate-data-container');
            if (!climateContainer) {
                return;
            }
            // Temperatur
            const tempElements = climateContainer.querySelectorAll('.climate-temp-value');
            tempElements.forEach(element => {
                element.textContent = `${temperature.toFixed(1)}°C`;
                element.classList.remove('text-success', 'text-warning', 'text-danger');
                element.classList.add('text-primary');
            });
            // Luftfeuchte
            const humidityElements = climateContainer.querySelectorAll('.climate-humidity-value');
            humidityElements.forEach(element => {
                element.textContent = `${humidity.toFixed(0)}%`;
                element.classList.remove('text-success', 'text-warning', 'text-danger');
                element.classList.add('text-primary');
            });
            // Blatttemperatur
            if (leafTemperature !== null && !isNaN(leafTemperature)) {
                const leafTempElements = climateContainer.querySelectorAll('.climate-leaf-temp-value');
                leafTempElements.forEach(element => {
                    element.textContent = `${leafTemperature.toFixed(1)}°C`;
                    element.classList.remove('text-success', 'text-warning', 'text-danger');
                    element.classList.add('text-primary');
                });
            }
            // VPD-Wert (kPa)
            if (typeof vpdValue === 'number' && !isNaN(vpdValue)) {
                const vpdElements = climateContainer.querySelectorAll('.climate-vpd-value');
                vpdElements.forEach(element => {
                    element.textContent = `${vpdValue.toFixed(2)} kPa`;
                    element.classList.remove('text-success', 'text-warning', 'text-danger');
                    element.classList.add('text-primary');
                });
            }
            // Status-Badges aktualisieren
            this.updateClimateStatusBadges(temperature, humidity);
            // Erfolgsmeldung anzeigen
            this.showClimateUpdateSuccess();

        } catch (error) {
            console.error('Fehler beim Befüllen der Klima-Daten:', error);
        }
    }

    // ===== NÄHRSTOFF-FUNKTIONEN =====

    /**
     * Nährstoff-Daten laden
     */
    async getNutrientData() {
        try {
            // Plant ID aus DOM oder URL holen
            let plantId = this.plantId;
            if (!plantId) {
                const plantIdElement = document.querySelector('[data-plant-id]');
                if (plantIdElement) {
                    plantId = plantIdElement.getAttribute('data-plant-id');
                }
            }

            // Strain-Type ermitteln
            const strainType = this.getStrainType();
            
            // Gespeicherte Werte aus LocalStorage laden
            const storedValues = this.loadNutrientInputValues();
            const selectedSubstrate = storedValues.substrate || this.substrate || 'soil';
            
            // API-Aufruf für Nährstoff-Diagnose mit korrektem Substrat
            const url = `/api/nutrients/diagnose?phase=${encodeURIComponent(this.currentPhase)}&strain_type=${encodeURIComponent(strainType)}&substrate=${encodeURIComponent(selectedSubstrate)}${plantId ? `&plant_id=${plantId}` : ''}`;
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Nährstoff-Diagnose konnte nicht geladen werden (HTTP ${response.status})`);
            }
            
            const data = await response.json();
            
            // Aktuelle Werte aus LocalStorage laden
            const currentEC = storedValues.ec || 1.2;
            const currentPH = storedValues.ph || 6.0;
            const drainEC = storedValues.drainEC || null;
            const drainPH = storedValues.drainPH || null;
            
            // Daten erweitern
            data.currentEC = currentEC;
            data.currentPH = currentPH;
            data.drainEC = drainEC;
            data.drainPH = drainPH;
            data.plantId = plantId;
            data.substrate = selectedSubstrate; // Gespeichertes Substrat verwenden
            
            // Diagnose durchführen falls Werte vorhanden
            if (currentEC && currentPH && data.targets && data.targets.ec && data.targets.ph) {
                data.diagnosis = this.performNutrientDiagnosis(
                    currentEC, 
                    currentPH, 
                    data.targets.ec.min, 
                    data.targets.ec.max, 
                    data.targets.ph
                );
            }

            return data;
            
        } catch (error) {
            console.error('🌱 Vegetation-Widget: Fehler beim Laden der Nährstoff-API-Daten:', error);
            throw error;
        }
    }

    /**
     * Nährstoff-Widget rendern
     */
    renderNutrientWidget(data) {
        // Spezifisch den Container im Vegetation-Widget suchen
        const nutrientsContent = this.container.querySelector('#nutrients');
        if (!nutrientsContent) {
            console.warn('🌱 Vegetation-Widget: nutrients Container nicht gefunden in renderNutrientWidget');
            return;
        }

        // Vollständiges Nährstoff-Widget im modernen Stil
        const html = `
            <div class="nutrient-widget-container">
                <div class="modern-card">
                    <div class="modern-header">
                        <div>
                            <i class="fa-solid fa-flask"></i> Nährstoff-Management
                        </div>
                        <div class="modern-header-actions">
                            <button class="btn btn-sm btn-outline-primary" id="nutrient-guidelines-btn">
                                <i class="fa fa-book"></i> Guidelines
                            </button>
                            <button class="btn btn-sm btn-outline-info" id="nutrient-history-btn">
                                <i class="fa fa-history"></i> Historie
                            </button>
                        </div>
                    </div>
                    <div class="modern-section-content">
                        ${this.renderNutrientInputs(data)}
                        ${this.renderNutrientAnalysis(data)}
                        ${this.renderNutrientRecommendations(data)}
                    </div>
                </div>
            </div>
        `;
        
        // HTML setzen
        nutrientsContent.innerHTML = html;
        
        // Bootstrap-Tab-Problem beheben: display: none entfernen
        nutrientsContent.style.display = 'block';
        
        // Event-Listener einrichten
        this.setupNutrientEventListeners();
        
    }

    /**
     * Nährstoff-Eingaben rendern (basierend auf ursprünglichem Nutrient-Widget)
     */
    renderNutrientInputs(data) {
        // Gespeicherte Werte laden
        const savedValues = this.loadNutrientInputValues();
        
        // Substrat-Wert bestimmen: Priorität: savedValues > data.substrate > this.substrate > 'soil' als Fallback
        const selectedSubstrate = savedValues.substrate || data.substrate || this.substrate || 'soil';
        
        // Debug-Ausgabe
        console.log('🌱 Nährstoff-Inputs - Gespeicherte Werte:', savedValues);
        console.log('🌱 Nährstoff-Inputs - Data Substrat:', data.substrate);
        console.log('🌱 Nährstoff-Inputs - This Substrat:', this.substrate);
        console.log('🌱 Nährstoff-Inputs - Ausgewähltes Substrat:', selectedSubstrate);
        
        return `
            <div class="nutrient-inputs-section mb-4">
                <h6 class="modern-label mb-3">
                    <i class="fa fa-edit"></i> Aktuelle Werte
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <label for="ec-input" class="form-label">EC (mS/cm)</label>
                        <input type="number" class="form-control" id="ec-input" 
                               value="${savedValues.ec || data.currentEC || ''}" step="0.1" min="0" max="5">
                    </div>
                    <div class="col-md-3">
                        <label for="ph-input" class="form-label">pH</label>
                        <input type="number" class="form-control" id="ph-input" 
                               value="${savedValues.ph || data.currentPH || ''}" step="0.1" min="4" max="9">
                    </div>
                    <div class="col-md-3">
                        <label for="drain-ec-input" class="form-label">Drain EC (optional)</label>
                        <input type="number" class="form-control" id="drain-ec-input" 
                               value="${savedValues.drainEC || data.drainEC || ''}" step="0.1" min="0" max="5" placeholder="Optional">
                    </div>
                    <div class="col-md-3">
                        <label for="drain-ph-input" class="form-label">Drain pH (optional)</label>
                        <input type="number" class="form-control" id="drain-ph-input" 
                               value="${savedValues.drainPH || data.drainPH || ''}" step="0.1" min="4" max="9" placeholder="Optional">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label for="substrate-input" class="form-label">Substrat</label>
                        <select class="form-select" id="substrate-input">
                            <option value="soil" ${selectedSubstrate === 'soil' ? 'selected' : ''}>Erde</option>
                            <option value="coco" ${selectedSubstrate === 'coco' ? 'selected' : ''}>Coco</option>
                            <option value="hydro" ${selectedSubstrate === 'hydro' ? 'selected' : ''}>Hydro</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end gap-2">
                        <button class="btn btn-primary" id="nutrient-analyze-btn">
                            <i class="fa fa-calculator"></i> Analyse durchführen
                        </button>
                        <button class="btn btn-outline-warning btn-sm" id="nutrient-reset-btn" title="LocalStorage zurücksetzen">
                            <i class="fa fa-refresh"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Nährstoff-Analyse rendern (basierend auf ursprünglichem Nutrient-Widget)
     */
    renderNutrientAnalysis(data) {
        if (!data.diagnosis) {
            return `
                <div class="nutrient-analysis-section mb-4">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        Geben Sie EC- und pH-Werte ein und führen Sie eine Analyse durch.
                    </div>
                </div>
            `;
        }

        const getStatusClass = (status) => {
            switch (status) {
                case 'optimal': return 'success';
                case 'niedrig': return 'warning';
                case 'hoch': return 'danger';
                default: return 'secondary';
            }
        };

        const getStatusIcon = (status) => {
            switch (status) {
                case 'optimal': return 'fa-check-circle';
                case 'niedrig': return 'fa-arrow-down';
                case 'hoch': return 'fa-arrow-up';
                default: return 'fa-question-circle';
            }
        };

        return `
            <div class="nutrient-analysis-section mb-4">
                <h6 class="modern-label mb-3">
                    <i class="fa fa-chart-line"></i> Analyse-Ergebnisse
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">EC-Werte</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Aktuell: <strong>${data.currentEC} mS/cm</strong></span>
                                    <span class="badge bg-${getStatusClass(data.diagnosis.ec_status)}">
                                        <i class="fa ${getStatusIcon(data.diagnosis.ec_status)}"></i>
                                        ${data.diagnosis.ec_status}
                                    </span>
                                </div>
                                <small class="text-muted">
                                    Ziel: ${data.diagnosis.targets?.ec?.min?.toFixed(2) || 'N/A'} - ${data.diagnosis.targets?.ec?.max?.toFixed(2) || 'N/A'} mS/cm
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">pH-Werte</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Aktuell: <strong>${data.currentPH}</strong></span>
                                    <span class="badge bg-${getStatusClass(data.diagnosis.ph_status)}">
                                        <i class="fa ${getStatusIcon(data.diagnosis.ph_status)}"></i>
                                        ${data.diagnosis.ph_status}
                                    </span>
                                </div>
                                <small class="text-muted">
                                    Ziel: ${data.diagnosis.targets?.ph?.min?.toFixed(1) || 'N/A'} - ${data.diagnosis.targets?.ph?.max?.toFixed(1) || 'N/A'}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Nährstoff-Empfehlungen rendern
     */
    renderNutrientRecommendations(data) {
        if (!data.diagnosis) {
            return `
                <div class="nutrient-recommendations-section">
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        Keine Empfehlungen verfügbar. Führen Sie zuerst eine Analyse durch.
                    </div>
                </div>
            `;
        }

        const alerts = data.diagnosis.alerts || [];
        const recommendations = data.diagnosis.recommendations || [];
        const warnings = data.diagnosis.warnings || [];
        const phaseNotes = data.diagnosis.phase_notes || [];

        return `
            <div class="nutrient-recommendations-section">
                <h6 class="modern-label mb-3">
                    <i class="fa fa-lightbulb"></i> Empfehlungen & Hinweise
                </h6>
                
                ${alerts.length > 0 ? `
                    <div class="alert alert-danger mb-3">
                        <h6><i class="fa fa-exclamation-triangle"></i> Alerts</h6>
                        <ul class="mb-0">
                            ${alerts.map(alert => `<li>${alert}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${recommendations.length > 0 ? `
                    <div class="alert alert-info mb-3">
                        <h6><i class="fa fa-lightbulb"></i> Empfehlungen</h6>
                        <ul class="mb-0">
                            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${warnings.length > 0 ? `
                    <div class="alert alert-warning mb-3">
                        <h6><i class="fa fa-exclamation-circle"></i> Warnungen</h6>
                        <ul class="mb-0">
                            ${warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${phaseNotes.length > 0 ? `
                    <div class="alert alert-secondary">
                        <h6><i class="fa fa-info-circle"></i> Phase-spezifische Hinweise</h6>
                        <ul class="mb-0">
                            ${phaseNotes.map(note => `<li>${note}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Nährstoff-Diagnose durchführen
     */
    performNutrientDiagnosis(ec, ph, ecMin, ecMax, phRange) {
        const diagnosis = {
            ec_status: 'optimal',
            ph_status: 'optimal',
            alerts: [],
            recommendations: [],
            warnings: []
        };

        // EC-Analyse
        if (ec < ecMin) {
            diagnosis.ec_status = 'niedrig';
            diagnosis.alerts.push('EC-Zuführung zu niedrig');
            diagnosis.recommendations.push('Dünger-Konzentration erhöhen');
        } else if (ec > ecMax) {
            diagnosis.ec_status = 'hoch';
            diagnosis.alerts.push('EC-Zuführung zu hoch');
            diagnosis.recommendations.push('Dünger-Konzentration reduzieren oder spülen');
        }

        // pH-Analyse
        const phMin = phRange.min || 6.0;
        const phMax = phRange.max || 6.5;
        
        if (ph < phMin) {
            diagnosis.ph_status = 'niedrig';
            diagnosis.alerts.push('pH-Zuführung zu niedrig');
            diagnosis.recommendations.push('pH-Up verwenden');
        } else if (ph > phMax) {
            diagnosis.ph_status = 'hoch';
            diagnosis.alerts.push('pH-Zuführung zu hoch');
            diagnosis.recommendations.push('pH-Down verwenden');
        }

        return diagnosis;
    }

    /**
     * Nährstoff-Event-Listener einrichten
     */
    setupNutrientEventListeners() {
        // Analyse-Button
        const analyzeBtn = document.getElementById('nutrient-analyze-btn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => this.performNutrientAnalysis());
        }

        // Reset-Button
        const resetBtn = document.getElementById('nutrient-reset-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetNutrientLocalStorage());
        }

        // Guidelines-Button
        const guidelinesBtn = document.getElementById('nutrient-guidelines-btn');
        if (guidelinesBtn) {
            guidelinesBtn.addEventListener('click', () => this.showNutrientGuidelines());
        }

        // Historie-Button
        const historyBtn = document.getElementById('nutrient-history-btn');
        if (historyBtn) {
            historyBtn.addEventListener('click', () => this.loadNutrientHistory());
        }

        // Input-Event-Listener für Live-Updates
        const inputs = ['ec-input', 'ph-input', 'drain-ec-input', 'drain-ph-input'];
        inputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', () => this.updateNutrientInputValues());
            }
        });
        
        // Select-Event-Listener für Substrat
        const substrateSelect = document.getElementById('substrate-input');
        console.log('🌱 Suche nach Substrat-Select Element...');
        console.log('🌱 Gefundenes Element:', substrateSelect);
        
        if (substrateSelect) {
            console.log('🌱 Substrat-Select gefunden, Event-Listener hinzugefügt');
            console.log('🌱 Aktueller Wert:', substrateSelect.value);
            console.log('🌱 Verfügbare Optionen:', Array.from(substrateSelect.options).map(opt => opt.value));
            console.log('🌱 CSS-Klassen:', substrateSelect.className);
            
            // Direkte Event-Listener hinzufügen
            substrateSelect.addEventListener('change', (event) => {
                console.log('🌱 SUBSTRAT CHANGE EVENT! Neuer Wert:', event.target.value);
                this.updateNutrientInputValues();
            });
            
            // Auch auf die Optionen hören
            const options = substrateSelect.querySelectorAll('option');
            options.forEach(option => {
                option.addEventListener('click', (event) => {
                    console.log('🌱 Option geklickt:', event.target.value);
                });
            });
            
            // Polling-Methode deaktiviert
            console.log('🌱 Polling deaktiviert - Automatische Updates ausgeschaltet');
            
            // Hinweis anzeigen
            this.showUpdateNotice('Substrat-Änderungen: Automatische Erkennung deaktiviert. Bitte manuell aktualisieren.');
        } else {
            console.warn('🌱 Substrat-Select nicht gefunden!');
            // Alle Select-Elemente auflisten
            const allSelects = document.querySelectorAll('select');
            console.log('🌱 Alle Select-Elemente auf der Seite:', allSelects);
        }
    }

    /**
     * Nährstoff-Analyse durchführen
     */
    async performNutrientAnalysis() {
        try {
            // Aktuell ausgewähltes Substrat holen
            const substrateInput = document.getElementById('substrate-input');
            const selectedSubstrate = substrateInput ? substrateInput.value : 'soil';
            
            console.log('🌱 performNutrientAnalysis - Ausgewähltes Substrat:', selectedSubstrate);
            
            const analysisData = {
                input_ec: parseFloat(document.getElementById('ec-input').value) || 0,
                input_ph: parseFloat(document.getElementById('ph-input').value) || 0,
                drain_ec: parseFloat(document.getElementById('drain-ec-input').value) || null,
                drain_ph: parseFloat(document.getElementById('drain-ph-input').value) || null,
                phase: this.currentPhase,
                strain_type: this.getStrainType(),
                substrate: selectedSubstrate, // Aktuell ausgewähltes Substrat verwenden
                plant_id: this.plantId
            };

            // Validierung
            if (!analysisData.input_ec || !analysisData.input_ph) {
                alert('Bitte geben Sie gültige EC- und pH-Werte ein.');
                return;
            }

            // API-Aufruf
            const response = await fetch('/api/nutrients/analyse', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                throw new Error('Analyse konnte nicht durchgeführt werden');
            }

            const data = await response.json();
            
            // Werte speichern (mit korrektem Substrat)
            const valuesToSave = {
                ec: analysisData.input_ec,
                ph: analysisData.input_ph,
                drainEC: analysisData.drain_ec,
                drainPH: analysisData.drain_ph,
                substrate: selectedSubstrate // Korrektes Substrat speichern
            };
            this.saveNutrientInputValues(valuesToSave);
            
            // Eingabefelder leeren
            this.clearNutrientInputs();
            
            // Daten für das Rendering vorbereiten
            const renderData = {
                currentEC: data.input_ec,
                currentPH: data.input_ph,
                drainEC: data.drain_ec,
                drainPH: data.drain_ph,
                diagnosis: data.analysis,
                targets: data.analysis.targets,
                phase_notes: data.analysis.phase_notes,
                alerts: data.analysis.alerts,
                recommendations: data.analysis.recommendations,
                warnings: data.analysis.warnings
            };
            
            // Widget neu rendern
            this.renderNutrientWidget(renderData);
            
            // Event-Listener neu einrichten
            this.setupNutrientEventListeners();
            
            console.log('Nährstoff-Analyse erfolgreich durchgeführt:', data);
            
        } catch (error) {
            console.error('Fehler bei der Nährstoff-Analyse:', error);
            alert(`Fehler bei der Analyse: ${error.message}`);
        }
    }

    /**
     * Nährstoff-Input-Werte aktualisieren
     */
    updateNutrientInputValues() {
        const substrateInput = document.getElementById('substrate-input');
        const substrateValue = substrateInput?.value || 'soil';
        
        const values = {
            ec: parseFloat(document.getElementById('ec-input')?.value) || 0,
            ph: parseFloat(document.getElementById('ph-input')?.value) || 0,
            drainEC: parseFloat(document.getElementById('drain-ec-input')?.value) || null,
            drainPH: parseFloat(document.getElementById('drain-ph-input')?.value) || null,
            substrate: substrateValue
        };
        
        console.log('🌱 updateNutrientInputValues - Substrat Input:', substrateInput);
        console.log('🌱 updateNutrientInputValues - Substrat Value:', substrateValue);
        console.log('🌱 updateNutrientInputValues - Alle Werte:', values);
        
        this.saveNutrientInputValues(values);
    }

    /**
     * Nährstoff-Input-Werte speichern
     */
    saveNutrientInputValues(values) {
        try {
            // Werte in das richtige Format konvertieren
            const saveData = {
                ec: values.input_ec || values.ec || 0,
                ph: values.input_ph || values.ph || 0,
                drainEC: values.drain_ec || values.drainEC || null,
                drainPH: values.drain_ph || values.drainPH || null,
                substrate: values.substrate || 'soil'
            };
            
            console.log('🌱 saveNutrientInputValues - Eingangswerte:', values);
            console.log('🌱 saveNutrientInputValues - Zu speichernde Daten:', saveData);
            
            localStorage.setItem('nutrient_input_values', JSON.stringify(saveData));
            
            console.log('🌱 saveNutrientInputValues - LocalStorage gespeichert');
        } catch (error) {
            console.error('Fehler beim Speichern der Nährstoff-Werte:', error);
        }
    }

    /**
     * Nährstoff-Input-Werte laden
     */
    loadNutrientInputValues() {
        try {
            const stored = localStorage.getItem('nutrient_input_values');
            if (stored) {
                const parsed = JSON.parse(stored);
                // Debug: Zeige was geladen wird
                console.log('🌱 LocalStorage geladen:', parsed);
                return parsed;
            } else {
                console.log('🌱 Keine gespeicherten Werte gefunden, verwende Standard');
                return { ec: 1.2, ph: 6.0, drainEC: null, drainPH: null, substrate: 'soil' };
            }
        } catch (error) {
            console.error('Fehler beim Laden der Nährstoff-Werte:', error);
            return { ec: 1.2, ph: 6.0, drainEC: null, drainPH: null, substrate: 'soil' };
        }
    }

    /**
     * Nährstoff-Eingabefelder leeren
     */
    clearNutrientInputs() {
        const inputs = ['ec-input', 'ph-input', 'drain-ec-input', 'drain-ph-input'];
        inputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = '';
            }
        });
    }

    /**
     * Nährstoff-LocalStorage zurücksetzen (für Debugging)
     */
    resetNutrientLocalStorage() {
        try {
            localStorage.removeItem('nutrient_input_values');
            console.log('🌱 Nährstoff-LocalStorage zurückgesetzt');
            
            // Standardwerte setzen
            const defaultValues = { ec: 1.2, ph: 6.0, drainEC: null, drainPH: null, substrate: 'soil' };
            localStorage.setItem('nutrient_input_values', JSON.stringify(defaultValues));
            console.log('🌱 Standardwerte gesetzt:', defaultValues);
            
            // DOM-Element direkt aktualisieren
            const substrateSelect = document.getElementById('substrate-input');
            if (substrateSelect) {
                substrateSelect.value = 'soil';
                console.log('🌱 DOM-Element auf "soil" gesetzt');
            }
            
            // Widget neu laden
            this.loadNutrientsData();
            
        } catch (error) {
            console.error('Fehler beim Zurücksetzen des LocalStorage:', error);
        }
    }

    /**
     * Nährstoff-Guidelines inline anzeigen
     */
    async showNutrientGuidelines() {
        try {
            const container = this.container.querySelector('#nutrients');
            if (!container) return;

            // Guidelines-Container erstellen oder finden
            let guidelinesContainer = container.querySelector('#nutrient-guidelines-container');
            if (!guidelinesContainer) {
                guidelinesContainer = document.createElement('div');
                guidelinesContainer.id = 'nutrient-guidelines-container';
                container.appendChild(guidelinesContainer);
            }

            // Loading anzeigen
            guidelinesContainer.innerHTML = `
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fa fa-book"></i> Nährstoff-Guidelines für ${this.getPhaseDisplayName(this.currentPhase)}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin"></i> Guidelines werden geladen...
                        </div>
                    </div>
                </div>
            `;

            // API-Aufruf für Guidelines
            const response = await fetch(`/api/nutrients/diagnose?phase=${encodeURIComponent(this.currentPhase)}&strain_type=${encodeURIComponent(this.getStrainType())}&substrate=${encodeURIComponent(document.getElementById('substrate-input')?.value || 'soil')}`);
            
            if (!response.ok) {
                throw new Error('Guidelines konnten nicht geladen werden');
            }

            const data = await response.json();
            
            // Guidelines rendern
            this.renderNutrientGuidelinesInline(data, guidelinesContainer);
            
        } catch (error) {
            console.error('Fehler beim Laden der Guidelines:', error);
            const container = this.container.querySelector('#nutrients');
            if (container) {
                container.innerHTML += `
                    <div class="alert alert-danger mt-3">
                        <i class="fa fa-exclamation-triangle"></i>
                        Fehler beim Laden der Guidelines: ${error.message}
                    </div>
                `;
            }
        }
    }

    /**
     * Nährstoff-Guidelines inline rendern
     */
    renderNutrientGuidelinesInline(data, container) {
        const guidelines = data.guidelines;
        const targets = data.targets;
        
        container.innerHTML = `
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fa fa-book"></i> Nährstoff-Guidelines für ${this.getPhaseDisplayName(this.currentPhase)}
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="this.closest('#nutrient-guidelines-container').remove()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fa fa-target"></i> Zielwerte
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tbody>
                                        <tr>
                                            <td><strong>EC-Bereich:</strong></td>
                                            <td>${targets.ec.min.toFixed(2)} - ${targets.ec.max.toFixed(2)} mS/cm</td>
                                        </tr>
                                        <tr>
                                            <td><strong>pH-Bereich:</strong></td>
                                            <td>${targets.ph.min.toFixed(1)} - ${targets.ph.max.toFixed(1)}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>NPK-Ratio:</strong></td>
                                            <td>${guidelines.npkRatio || 'N/A'}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fa fa-flask"></i> Empfohlene Nährstoffe
                            </h6>
                            <ul class="list-unstyled">
                                ${guidelines.nutrients && guidelines.nutrients.length > 0 
                                    ? guidelines.nutrients.map(nutrient => `<li><i class="fa fa-check text-success"></i> ${nutrient}</li>`).join('')
                                    : '<li><i class="fa fa-info text-muted"></i> Keine spezifischen Nährstoffe empfohlen</li>'
                                }
                            </ul>
                        </div>
                    </div>
                    
                    ${guidelines.notes && guidelines.notes.length > 0 ? `
                        <div class="mt-3">
                            <h6 class="text-info">
                                <i class="fa fa-info-circle"></i> Wichtige Hinweise
                            </h6>
                            <ul class="list-unstyled">
                                ${guidelines.notes.map(note => `<li><i class="fa fa-arrow-right text-info"></i> ${note}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    
                    <div class="mt-3">
                        <h6 class="text-warning">
                            <i class="fa fa-exclamation-triangle"></i> Grenzwerte
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>EC:</strong> ${targets.ec.min.toFixed(2)} - ${targets.ec.max.toFixed(2)} mS/cm
                            </div>
                            <div class="col-md-6">
                                <strong>pH:</strong> ${targets.ph.min.toFixed(1)} - ${targets.ph.max.toFixed(1)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Nährstoff-Historie laden
     */
    async loadNutrientHistory() {
        try {
            if (!this.plantId) {
                alert('Keine Pflanzen-ID verfügbar für Historie.');
                return;
            }

            const response = await fetch(`/api/nutrients/history/${this.plantId}`);
            if (!response.ok) {
                throw new Error('Historie konnte nicht geladen werden');
            }

            const history = await response.json();
            
            // Historie-Modal anzeigen
            this.showNutrientHistoryModal(history);
            
        } catch (error) {
            console.error('Fehler beim Laden der Nährstoff-Historie:', error);
            alert(`Fehler beim Laden der Historie: ${error.message}`);
        }
    }

    /**
     * Nährstoff-Historie-Modal anzeigen
     */
    showNutrientHistoryModal(history) {
        const modalHtml = `
            <div class="modal fade" id="nutrientHistoryModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fa fa-history"></i> Nährstoff-Historie
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${history.length > 0 ? this.renderNutrientHistoryTable(history) : '<p class="text-muted">Keine Historie-Daten verfügbar.</p>'}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Modal zum DOM hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Modal öffnen
        const modal = new bootstrap.Modal(document.getElementById('nutrientHistoryModal'));
        modal.show();
        
        // Modal nach Schließen entfernen
        document.getElementById('nutrientHistoryModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    /**
     * Nährstoff-Historie-Tabelle rendern
     */
    renderNutrientHistoryTable(history) {
        return `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Datum</th>
                            <th>EC Input</th>
                            <th>EC Drain</th>
                            <th>pH Input</th>
                            <th>pH Drain</th>
                            <th>Substrat</th>
                            <th>Phase</th>
                            <th>Notizen</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${history.map(entry => `
                            <tr>
                                <td>${new Date(entry.entry_date).toLocaleDateString('de-DE')}</td>
                                <td>${entry.ec_input || '-'}</td>
                                <td>${entry.ec_drain || '-'}</td>
                                <td>${entry.ph_input || '-'}</td>
                                <td>${entry.ph_drain || '-'}</td>
                                <td>${entry.substrate || '-'}</td>
                                <td>${entry.phase || '-'}</td>
                                <td>${entry.notes || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Vegetations-Beleuchtungs-Übersicht rendern
     */
    renderVegetationLightingOverview(data) {
        const container = document.getElementById('vegetationLightingOverview');
        if (!container) return;
        
        const rec = data.recommendations;
        const current = data.current;
        
        // PPFD-Status berechnen
        let ppfdStatus = 'optimal';
        let ppfdMessage = 'Perfekte Lichtbedingungen für Vegetation!';
        
        if (current.ppfd_calculated) {
            const ppfdRange = rec.ppfd_range;
            const rangeMatch = ppfdRange.match(/(\d+)-(\d+)/);
            if (rangeMatch) {
                const minPPFD = parseInt(rangeMatch[1]);
                const maxPPFD = parseInt(rangeMatch[2]);
                
                if (current.ppfd_calculated < minPPFD) {
                    ppfdStatus = 'low';
                    ppfdMessage = 'PPFD zu niedrig - Wachstum verlangsamt';
                } else if (current.ppfd_calculated > maxPPFD) {
                    ppfdStatus = 'high';
                    ppfdMessage = 'PPFD zu hoch - Stress-Risiko';
                }
            }
        }
        
        container.innerHTML = `
            <div class="lighting-overview-grid">
                <div class="lighting-card ppfd-card ${ppfdStatus}">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-lightbulb"></i>
                        <span>PPFD-Status</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${current.ppfd_calculated || '--'} μmol/m²/s</div>
                        <div class="lighting-range">Ziel: ${rec.ppfd_range}</div>
                        <div class="lighting-message">${ppfdMessage}</div>
                    </div>
                </div>
                
                <div class="lighting-card dli-card">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-chart-line"></i>
                        <span>DLI (Daily Light Integral)</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${current.dli_calculated || '--'} mol/m²/Tag</div>
                        <div class="lighting-range">Ziel: ${rec.dli_range}</div>
                    </div>
                </div>
                
                <div class="lighting-card photoperiod-card">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-clock"></i>
                        <span>Photoperiode</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${current.light_hours}h</div>
                        <div class="lighting-range">Empfohlen: ${rec.light_hours}h</div>
                    </div>
                </div>
                
                <div class="lighting-card distance-card">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-arrows-alt-v"></i>
                        <span>Lampenabstand</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${current.lamp_distance_cm}cm</div>
                        <div class="lighting-range">Empfohlen: ${rec.lamp_distance_cm}cm</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Vegetations-Beleuchtungs-Einstellungen rendern
     */
    renderVegetationLightingSettings(data) {
        const container = document.getElementById('vegetationLightingSettings');
        if (!container) return;
        
        const current = data.current;
        
        container.innerHTML = `
            <div class="lighting-settings-section">
                <h4><i class="fa-solid fa-cog me-2"></i>Vegetations-Beleuchtungs-Einstellungen</h4>
                
                <div class="lighting-settings-grid">
                    <div class="lighting-setting-group">
                        <label for="vegetation-lamp-power">Lampenleistung (W)</label>
                        <input type="number" id="vegetation-lamp-power" value="${current.lamp_power_w}" min="1" step="1">
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="vegetation-lamp-distance">Lampenabstand (cm)</label>
                        <input type="number" id="vegetation-lamp-distance" value="${current.lamp_distance_cm}" min="5" step="1">
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="vegetation-light-hours">Beleuchtungsstunden</label>
                        <input type="number" id="vegetation-light-hours" value="${current.light_hours}" min="1" max="24" step="1">
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="vegetation-color-temp">Farbtemperatur (K)</label>
                        <input type="number" id="vegetation-color-temp" value="${current.color_temperature_k || 4000}" min="2000" max="10000" step="100">
                    </div>
                </div>
                
                <div class="lighting-actions">
                    <button type="button" class="btn btn-primary" id="updateVegetationLightingBtn">
                        <i class="fa-solid fa-save"></i> Einstellungen speichern
                    </button>
                    <button type="button" class="btn btn-outline-info" id="vegetationLightingGuidelinesBtn">
                        <i class="fa-solid fa-book"></i> Richtlinien anzeigen
                    </button>
                </div>
            </div>
        `;
        
        // Event-Listener für Vegetations-Beleuchtungs-Einstellungen
        this.setupVegetationLightingEventListeners();
    }
    
    /**
     * Vegetations-Beleuchtungs-Guidelines rendern
     */
    renderVegetationLightingGuidelines(data) {
        const container = document.getElementById('vegetationLightingGuidelines');
        if (!container) return;
        
        const rec = data.recommendations;
        
        container.innerHTML = `
            <div class="lighting-guidelines-section">
                <h4><i class="fa-solid fa-book me-2"></i>Vegetations-Beleuchtungsrichtlinien</h4>
                
                <div class="lighting-guidelines-content">
                    <div class="guideline-card">
                        <h5>🌱 Vegetationsphase-spezifische Empfehlungen</h5>
                        <div class="guideline-item">
                            <strong>PPFD-Zielbereich:</strong> ${rec.ppfd_range}
                        </div>
                        <div class="guideline-item">
                            <strong>DLI-Zielbereich:</strong> ${rec.dli_range}
                        </div>
                        <div class="guideline-item">
                            <strong>Photoperiode:</strong> ${rec.light_hours}h (${data.strain_type === 'autoflowering' ? 'Autoflower' : 'Photoperiod'})
                        </div>
                        <div class="guideline-item">
                            <strong>Lampenabstand:</strong> ${rec.lamp_distance_cm}cm
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <h5>💡 Vegetations-Optimierungstipps</h5>
                        <ul class="guideline-tips">
                            <li>Höhere PPFD-Werte fördern kompaktes Wachstum</li>
                            <li>Farbtemperatur 4000-6500K für optimales vegetatives Wachstum</li>
                            <li>Lampenabstand regelmäßig anpassen bei Pflanzenwachstum</li>
                            <li>Blau-Licht-Anteil fördert kompakte Internodien</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Vegetations-Beleuchtungs-Status aktualisieren
     */
    updateVegetationLightingStatus(data) {
        const statusIndicator = document.getElementById('vegetationLightingStatusIndicator');
        const statusText = document.getElementById('vegetationLightingStatusText');
        
        if (!statusIndicator || !statusText) return;
        
        // Status basierend auf PPFD-Bewertung
        const current = data.current;
        let status = 'optimal';
        let message = 'Vegetations-Beleuchtung optimal eingestellt';
        
        if (current.ppfd_calculated) {
            const rec = data.recommendations;
            const rangeMatch = rec.ppfd_range.match(/(\d+)-(\d+)/);
            if (rangeMatch) {
                const minPPFD = parseInt(rangeMatch[1]);
                const maxPPFD = parseInt(rangeMatch[2]);
                
                if (current.ppfd_calculated < minPPFD) {
                    status = 'warning';
                    message = 'PPFD zu niedrig - Wachstum verlangsamt';
                } else if (current.ppfd_calculated > maxPPFD) {
                    status = 'error';
                    message = 'PPFD zu hoch - Stress-Risiko';
                }
            }
        }
        
        statusIndicator.className = `status-indicator ${status}`;
        statusText.textContent = message;
    }
    
    /**
     * Event-Listener für Vegetations-Beleuchtungs-Einstellungen
     */
    setupVegetationLightingEventListeners() {
        const updateBtn = document.getElementById('updateVegetationLightingBtn');
        const guidelinesBtn = document.getElementById('vegetationLightingGuidelinesBtn');
        
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.updateVegetationLightingSettings();
            });
        }
        
        if (guidelinesBtn) {
            guidelinesBtn.addEventListener('click', () => {
                this.showVegetationLightingGuidelinesModal();
            });
        }
    }
    
    /**
     * Vegetations-Beleuchtungs-Einstellungen aktualisieren
     */
    async updateVegetationLightingSettings() {
        try {
            const lampPower = parseInt(document.getElementById('vegetation-lamp-power').value);
            const lampDistance = parseInt(document.getElementById('vegetation-lamp-distance').value);
            const lightHours = parseInt(document.getElementById('vegetation-light-hours').value);
            const colorTemp = parseInt(document.getElementById('vegetation-color-temp').value);
            
            // Hier würde die API-Aktualisierung erfolgen
            // Für jetzt nur Reload der Daten
            await this.loadLightingData();
            
            this.showSuccess('Vegetations-Beleuchtungs-Einstellungen aktualisiert');
            
        } catch (error) {
            console.error('Fehler beim Aktualisieren der Vegetations-Beleuchtungs-Einstellungen:', error);
            this.showError('Fehler beim Aktualisieren der Einstellungen');
        }
    }
    
    /**
     * Vegetations-Beleuchtungs-Guidelines Modal anzeigen
     */
    showVegetationLightingGuidelinesModal() {
        // Hier würde das Guidelines-Modal geöffnet werden
        // Für jetzt nur eine einfache Alert-Nachricht
        alert('Vegetations-Beleuchtungs-Richtlinien werden in Kürze verfügbar sein.');
    }
    
    /**
     * Update-Hinweis anzeigen
     */
    showUpdateNotice(message) {
        const noticeElement = document.createElement('div');
        noticeElement.className = 'alert alert-info alert-sm mt-2';
        noticeElement.innerHTML = `
            <i class="fa-solid fa-info-circle me-2"></i>
            <small>${message}</small>
        `;
        
        // Hinweis in den Widget-Container einfügen
        if (this.container) {
            this.container.appendChild(noticeElement);
        }
    }
}

// Globale Instanz verfügbar machen
window.VegetationManagementWidget = VegetationManagementWidget;