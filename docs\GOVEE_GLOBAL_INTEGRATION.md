# Globale Govee Sensor Integration

## Übersicht

Die Govee-Integration ist bereits **global implementiert** und kann von allen Widgets und Komponenten im Grow-Tagebuch genutzt werden. Es gibt zwei Möglichkeiten, die Integration zu nutzen:

1. **Direkte API-Aufrufe** - Für einfache Anwendungsfälle
2. **Globaler Sensor Manager** - Für erweiterte Funktionalität mit automatischen Updates

## 🚀 Schnellstart

### 1. Direkte API-Nutzung

```javascript
// Status prüfen
const response = await fetch('/api/sensor/govee/status');
const status = await response.json();

// Aktuelle Messwerte abrufen
const reading = await fetch('/api/sensor/govee/reading/DEVICE_ID?model=MODEL');
const data = await reading.json();
```

### 2. Globaler Sensor Manager

```javascript
// Widget als Subscriber registrieren
window.goveeSensorManager.subscribe('my-widget-id', (reading) => {
    console.log('Neue Govee-Daten:', reading);
    // Widget mit neuen Daten aktualisieren
});

// Setup-Modal anzeigen
window.goveeSensorManager.showSetupModal();
```

## 📋 Verfügbare API-Endpunkte

### Backend-APIs

| Endpunkt | Methode | Beschreibung |
|----------|---------|--------------|
| `/api/sensor/govee/setup` | POST | API-Key einrichten |
| `/api/sensor/govee/status` | GET | Verbindungsstatus |
| `/api/sensor/govee/devices` | GET | Alle Geräte auflisten |
| `/api/sensor/govee/reading/<device_id>` | GET | Aktuelle Messwerte |
| `/api/sensor/govee/test` | POST | Verbindung testen |

### Frontend Manager

| Methode | Beschreibung |
|---------|--------------|
| `goveeSensorManager.subscribe(widgetId, callback)` | Widget für Updates registrieren |
| `goveeSensorManager.unsubscribe(widgetId)` | Widget von Updates entfernen |
| `goveeSensorManager.showSetupModal()` | Globales Setup-Modal anzeigen |
| `goveeSensorManager.startAutoUpdate(deviceId, model)` | Automatische Updates starten |
| `goveeSensorManager.getStatus()` | Status-Informationen abrufen |

## 🔧 Widget-Integration

### Beispiel: Einfaches Widget

```javascript
class MyGoveeWidget {
    constructor(containerId) {
        this.containerId = containerId;
        this.widgetId = `my-widget-${Date.now()}`;
        this.init();
    }

    async init() {
        // Als Subscriber registrieren
        window.goveeSensorManager.subscribe(this.widgetId, (reading) => {
            this.updateWidget(reading);
        });

        // Status prüfen
        const status = await window.goveeSensorManager.checkConnectionStatus();
        this.renderStatus(status);
    }

    updateWidget(reading) {
        // Widget mit neuen Daten aktualisieren
        console.log('Temperatur:', reading.temperature);
        console.log('Luftfeuchte:', reading.humidity);
        console.log('Batterie:', reading.battery);
    }

    destroy() {
        // Cleanup beim Zerstören
        window.goveeSensorManager.unsubscribe(this.widgetId);
    }
}
```

### Beispiel: Erweiterte Integration

```javascript
class AdvancedGoveeWidget {
    constructor(containerId) {
        this.containerId = containerId;
        this.widgetId = `advanced-widget-${Date.now()}`;
        this.init();
    }

    async init() {
        this.renderWidget();
        await this.setupGoveeIntegration();
    }

    renderWidget() {
        this.container.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h6>Mein Govee Widget</h6>
                    <button id="govee-setup-btn" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-cog"></i> Setup
                    </button>
                </div>
                <div class="card-body">
                    <div id="govee-status">Status wird geprüft...</div>
                    <div id="govee-data" style="display: none;">
                        <div class="row">
                            <div class="col-4">
                                <h4 id="temp-display">--</h4>
                                <small>Temperatur</small>
                            </div>
                            <div class="col-4">
                                <h4 id="humidity-display">--</h4>
                                <small>Luftfeuchte</small>
                            </div>
                            <div class="col-4">
                                <h4 id="battery-display">--</h4>
                                <small>Batterie</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Event-Listener
        document.getElementById('govee-setup-btn').addEventListener('click', () => {
            window.goveeSensorManager.showSetupModal();
        });
    }

    async setupGoveeIntegration() {
        // Als Subscriber registrieren
        window.goveeSensorManager.subscribe(this.widgetId, (reading) => {
            this.updateDisplay(reading);
        });

        // Status prüfen
        const status = await window.goveeSensorManager.checkConnectionStatus();
        this.updateStatus(status);

        // Wenn bereits verbunden, aktuelle Daten abrufen
        if (status && status.connected && window.goveeSensorManager.currentDevice) {
            const device = window.goveeSensorManager.currentDevice;
            const reading = await window.goveeSensorManager.getCurrentReading(device.id, device.model);
            if (reading) {
                this.updateDisplay(reading);
            }
        }
    }

    updateStatus(status) {
        const statusElement = document.getElementById('govee-status');
        const dataElement = document.getElementById('govee-data');

        if (status && status.connected) {
            statusElement.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> Govee verbunden
                </div>
            `;
            dataElement.style.display = 'block';
        } else {
            statusElement.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Govee nicht verbunden
                </div>
            `;
            dataElement.style.display = 'none';
        }
    }

    updateDisplay(reading) {
        document.getElementById('temp-display').textContent = `${reading.temperature}°C`;
        document.getElementById('humidity-display').textContent = `${reading.humidity}%`;
        document.getElementById('battery-display').textContent = `${reading.battery}%`;
        
        // Status auf verbunden setzen
        this.updateStatus({ connected: true });
    }

    destroy() {
        window.goveeSensorManager.unsubscribe(this.widgetId);
    }
}
```

## 📊 Datenstruktur

### Govee Reading Object

```javascript
{
    temperature: 24.5,        // Temperatur in °C
    humidity: 65,            // Luftfeuchte in %
    battery: 85,             // Batterie in %
    timestamp: "2025-01-15T10:30:00",  // ISO Timestamp
    device_id: "H5179_XXXXX", // Geräte-ID
    model: "H5179"           // Geräte-Modell
}
```

### Status Object

```javascript
{
    connected: true,          // Verbindungsstatus
    api_key_set: true,       // API-Key gesetzt
    devices_count: 2,        // Anzahl verfügbarer Geräte
    last_test: "2025-01-15T10:30:00"  // Letzter Test
}
```

## 🔄 Automatische Updates

Der globale Sensor Manager unterstützt automatische Updates:

```javascript
// Automatische Updates starten (alle 30 Sekunden)
window.goveeSensorManager.startAutoUpdate('DEVICE_ID', 'MODEL', 30000);

// Automatische Updates stoppen
window.goveeSensorManager.stopAutoUpdate();
```

## 🛠️ Best Practices

### 1. Widget-Lifecycle

```javascript
class MyWidget {
    constructor(containerId) {
        this.widgetId = `widget-${Date.now()}`;
        this.init();
    }

    init() {
        // Als Subscriber registrieren
        window.goveeSensorManager.subscribe(this.widgetId, this.handleUpdate.bind(this));
    }

    handleUpdate(reading) {
        // Daten verarbeiten
        this.updateUI(reading);
    }

    destroy() {
        // Wichtig: Cleanup beim Zerstören
        window.goveeSensorManager.unsubscribe(this.widgetId);
    }
}
```

### 2. Fehlerbehandlung

```javascript
async setupGoveeIntegration() {
    try {
        if (!window.goveeSensorManager) {
            throw new Error('Govee Sensor Manager nicht verfügbar');
        }

        const status = await window.goveeSensorManager.checkConnectionStatus();
        if (!status || !status.connected) {
            throw new Error('Govee nicht verbunden');
        }

        // Integration erfolgreich
        this.enableGoveeFeatures();
    } catch (error) {
        console.error('Govee Integration fehlgeschlagen:', error);
        this.showError('Govee Integration nicht verfügbar');
    }
}
```

### 3. Performance-Optimierung

```javascript
// Nur bei Bedarf registrieren
if (this.needsGoveeData) {
    window.goveeSensorManager.subscribe(this.widgetId, this.handleUpdate.bind(this));
}

// Updates nur bei sichtbarem Widget
handleUpdate(reading) {
    if (this.isVisible()) {
        this.updateUI(reading);
    }
}
```

## 🎯 Anwendungsfälle

### VPD-Widget (bereits implementiert)
- Automatische Temperatur- und Luftfeuchte-Updates
- VPD-Berechnung mit Live-Daten
- Status-Anzeige

### Bewässerungs-Widget
- Automatische Luftfeuchte-Überwachung
- Bewässerungsempfehlungen basierend auf aktuellen Werten

### Klima-Widget
- Temperatur- und Luftfeuchte-Monitoring
- Trend-Analyse
- Alarm-Funktionen

### Dashboard-Widget
- Übersicht aller Sensor-Daten
- Status-Monitoring
- Schnellzugriff auf Setup

## 🔧 Konfiguration

### API-Key Setup

1. Gehe zu [developer.govee.com](https://developer.govee.com)
2. Erstelle ein kostenloses Konto
3. Erstelle eine neue App
4. Kopiere den API-Key
5. Verwende den globalen Setup-Dialog

### Geräte-Auswahl

- Nur Hygrometer werden automatisch erkannt
- Ein Gerät wird global für alle Widgets verwendet
- Automatische Updates können aktiviert/deaktiviert werden

## 📝 Hinweise

- Die Integration ist bereits **global verfügbar**
- Alle Widgets teilen sich die gleiche Govee-Verbindung
- Automatische Updates werden zentral verwaltet
- Cleanup ist wichtig beim Zerstören von Widgets
- Fehlerbehandlung sollte implementiert werden

## 🚀 Nächste Schritte

1. **Luftbefeuchter-Integration** - Steuerung von Govee-Luftbefeuchtern
2. **WLAN-Steckdosen** - Steuerung von Govee-Steckdosen
3. **Echtzeit-Alarme** - Benachrichtigungen bei Grenzwert-Überschreitungen
4. **Daten-Logging** - Automatisches Speichern von Sensor-Daten
5. **Multi-Device-Support** - Mehrere Geräte gleichzeitig 