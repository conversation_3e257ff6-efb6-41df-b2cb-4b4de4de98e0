# 🌺 Blüte-Management Widget

## Übersicht
Das Blüte-Management-Widget unterstützt Grower bei der Überwachung, Dokumentation und Steuerung der Blütephase. Es integriert Trichom-Analyse, Marker-Events, Flush-<PERSON><PERSON>, Prognose und Guideline-Logik in einer modularen Oberfläche.

---

## Tabs & Funktionen

### 1. Überblick
- Zeigt aktuelle Blütephase, Fortschritt (%), Blütetag
- Zeitstrahl aller Phasen (Preflower, Stretch, Hauptblüte, Reife, Flush)
- Empfehlungen & Guidelines je Phase

### 2. Marker
- Marker für Entwicklungsereignisse, Trichome, Pistillen, Flush, Stress
- Filter nach Kategorie & Wichtigkeit (Echtzeit)
- Marker-Management (Hinzufügen, Bearbeiten, Löschen)
- Marker werden in Zeitlinie und Auswertungen genutzt

### 3. Trichome
- Trichom-Status: Letz<PERSON> Beobachtung, <PERSON><PERSON><PERSON><PERSON>, Zielwerte
- Trichom-Guidelines (aus JSON): Zielwerte, Beobachtungsfrequenz, Fehlerquellen
- Trigger-Logik: Flush-Empfehlung, Ernte-Empfehlung
- Empfehlungen je Reifegrad

### 4. Flush-Trigger
- Flush-Status: Überwachung, empfohlen, aktiv
- Flush-Guidelines (aus JSON): Startbedingungen, Methoden, Fehlerquellen, Faustregeln
- Manueller Trigger möglich (mit Grund)
- Status-Details: Start, Dauer, Ziel-Erntetag

### 5. Prognose
- Berechnet verbleibende Tage bis Flush/Ernte
- Zeigt optimale Zeitpunkte & Warnstufen
- Prognose basiert auf Blütetagen, Markern, Trichomen

---

## Guideline-Logik
- Guidelines werden aus JSON-Dateien geladen (`bluete-management-guidelines.json`, `bluete-zeitmanagement-guidelines.json`)
- Trennung nach Tab/Modul (z.B. Flush-Guidelines nur im Flush-Tab)
- Dynamische Anzeige je nach Pflanzen-Typ (autoflower/photoperiod)

---

## Trigger & Empfehlungen
- Flush-Trigger: Automatisch (Trichome, Blütetage, Pistillen) & manuell
- Trichom-Trigger: Schwellenwerte für Flush/Ernte
- Empfehlungen werden je nach Status und Phase angezeigt

---

## API-Endpoints (Backend)
- `/flowering/status/<plant_id>`: Blüte-Status & Fortschritt
- `/flowering/markers/<plant_id>`: Marker-Events
- `/flowering/trichome-status/<plant_id>`: Trichom-Status
- `/flowering/trichome-guidelines/<plant_id>`: Trichome-Guidelines
- `/flowering/flush-trigger/<plant_id>`: Flush-Status & Guidelines
- `/flowering/prediction/<plant_id>`: Blüte-Prognose

---

## Besonderheiten
- **Filterbare Marker** (Kategorie, Wichtigkeit)
- **Dark Mode** voll unterstützt
- **Datenquellen**: Persistente DB, JSON-Guidelines
- **Modularer JS/CSS Aufbau** (keine Inline-Styles)
- **Barrierefreiheit**: Fokus- und Tastatursteuerung

---

## ToDo/Erweiterungen
- Automatische Benachrichtigungen (Flush/Ernte)
- Heatmap/Visualisierung für Marker
- Mehrsprachigkeit (DE/EN)
- Mobile-Optimierung

---

*Stand: 13.07.2025* 