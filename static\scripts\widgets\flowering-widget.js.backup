/**
 * Flowering Widget - Erweitert um Marker-Verwaltung und Flush-Trigger
 * Hauptfunktionalität für das Blüte-Zeitmanagement
 */

class FloweringWidget {
    constructor() {
        this.currentPlantId = this.getPlantIdFromDOM();
        this.floweringData = null;
        this.trichomeData = null; // Trichom-Daten initialisieren
        this.markers = [];
        this.currentTab = 'overview'; // Standard-Tab ist Übersicht
        this.trichomeGuidelinesLoaded = false;
        
        // Widget-Container finden und speichern
        this.element = document.querySelector('.flowering-widget-container');
        if (!this.element) {
            console.error('🌺 FloweringWidget: Widget-Container nicht gefunden');
            return;
        }
        
        this.init();
    }

    getPlantIdFromDOM() {
        // Versuche Plant-ID aus verschiedenen Quellen zu bekommen
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            return plantIdElement.getAttribute('data-plant-id');
        }
        
        // Fallback: Suche nach Plant-ID in der URL oder anderen Elementen
        const urlParams = new URLSearchParams(window.location.search);
        const plantId = urlParams.get('plant_id');
        if (plantId) {
            return plantId;
        }
        
        // Fallback: Verwende die erste verfügbare Plant-ID aus der Seite
        const strainTypeContainer = document.getElementById('strainTypeContainer');
        if (strainTypeContainer) {
            return strainTypeContainer.getAttribute('data-plant-id');
        }
        
        // Letzter Fallback
        return 'GG25L';
    }

    // Hilfsmethode für DOM-Abfragen innerhalb des Widgets
    getElement(selector) {
        if (this.element) {
            return this.element.querySelector(selector);
        }
        return document.querySelector(selector);
    }

    // Hilfsmethode für getElementById innerhalb des Widgets
    getElementById(id) {
        if (this.element) {
            return this.element.querySelector(`#${id}`);
        }
        return document.getElementById(id);
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.setupProgressCircle();
    }

    setupEventListeners() {
        // Tab-Navigation (nur innerhalb des Widgets)
        this.element.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                // console.log('🌺 FloweringWidget: Tab-Button geklickt:', e.target.dataset.tab);
                await this.switchTab(e.target.dataset.tab);
            });
        });

        // Trichom-Tab Event-Listener
        const trichomeTab = this.getElementById('trichomeTab');
        if (trichomeTab) {
            trichomeTab.addEventListener('click', async () => {
                await this.switchTab('trichome');
            });
        }

        // Trichom-Tab spezifische Event-Listener
        // Der addObservationBtn wird jetzt in setupObservationForm() behandelt

        // Modal Event-Listener
        this.setupModalEventListeners();

        // Marker Event-Listener
        this.setupMarkerEventListeners();

        // Flush-Trigger Event-Listener
        this.setupFlushTriggerEventListeners();

        // Event-Listener für den "Neuer Eintrag" Button
        this.setupObservationForm();
        
        // Event-Listener für das Timeline-Event-Formular
        this.setupTimelineEventForm();
        
        // Event-Listener für das Marker-Formular
        this.setupMarkerForm();
        
        // Guidelines-Tabs Event-Listener
        this.setupGuidelinesTabListeners();
        
        // Marker-Filter Event-Listener
        this.setupMarkerFilterListeners();
    }
    
    setupGuidelinesTabListeners() {
        const guidelineTabs = document.querySelectorAll('.guideline-tab');
        guidelineTabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchGuidelineTab(tab.dataset.tab));
        });
    }
    
    setupMarkerFilterListeners() {
        // Kategorie-Filter
        const categoryFilter = this.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filterMarkers();
            });
        }

        // Wichtigkeit-Filter
        const importanceFilter = this.getElementById('importanceFilter');
        if (importanceFilter) {
            importanceFilter.addEventListener('change', () => {
                this.filterMarkers();
            });
        }
    }
    
    switchGuidelineTab(tabName) {
        // Finde den Container, in dem der geklickte Tab ist
        const clickedTab = event.target;
        const guidelinesContainer = clickedTab.closest('.flush-guidelines, .trichome-guidelines');
        
        if (!guidelinesContainer) return;
        
        // Alle Tabs und Panels in diesem Container deaktivieren
        guidelinesContainer.querySelectorAll('.guideline-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        guidelinesContainer.querySelectorAll('.guideline-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Gewählten Tab aktivieren
        clickedTab.classList.add('active');
        
        // Entsprechendes Panel aktivieren
        const panelId = `${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Content`;
        const activePanel = guidelinesContainer.querySelector(`#flush${panelId}, #trichome${panelId}`);
        
        if (activePanel) activePanel.classList.add('active');
    }

    setupModalEventListeners() {
        // Trichom-Beobachtungs-Modal
        const trichomeModal = document.getElementById('trichomeObservationModal');
        const trichomeModalSave = document.getElementById('trichomeObservationModalSave');

        // Bootstrap 5 Modals schließen sich automatisch mit data-bs-dismiss="modal"
        // Keine separaten Event-Listener für Close/Cancel nötig
        if (trichomeModalSave) {
            trichomeModalSave.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveTrichomeObservation();
            });
        }

        // Trichom-Empfehlungs-Modal
        const recommendationModal = document.getElementById('trichomeRecommendationModal');
        // Bootstrap 5 Modals schließen sich automatisch mit data-bs-dismiss="modal"

        // Lösch-Bestätigungs-Modal
        const confirmDeleteModal = document.getElementById('confirmDeleteModal');
        // Bootstrap 5 Modals schließen sich automatisch mit data-bs-dismiss="modal"
        
        // Event-Listener für Lösch-Bestätigung
        const confirmDeleteBtn = document.querySelector('.btn-confirm-delete');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', async () => {
                const modal = bootstrap.Modal.getInstance(confirmDeleteModal);
                if (modal) {
                    modal.hide();
                }
                
                // Löschvorgang durchführen
                const index = confirmDeleteBtn.getAttribute('data-delete-index');
                if (index !== null) {
                    await this.performDelete(parseInt(index));
                }
            });
        }

        // ESC-Taste zum Schließen
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) {
                        modal.hide();
                    }
                }
            }
        });
    }

    setupMarkerEventListeners() {
        // Add Marker Button
        const addMarkerBtn = this.getElementById('addMarkerBtn');
        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.openMarkerModal();
            });
        }

        // Edit Marker Button
        const editMarkerBtn = this.getElementById('editMarkerBtn');
        if (editMarkerBtn) {
            editMarkerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.editMarker(e.target.getAttribute('data-marker-id'));
            });
        }

        // Delete Marker Button
        const deleteMarkerBtn = this.getElementById('deleteMarkerBtn');
        if (deleteMarkerBtn) {
            deleteMarkerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.deleteMarker(e.target.getAttribute('data-marker-id'));
            });
        }
    }

    setupFlushTriggerEventListeners() {
        // Manual Trigger Button
        const manualTriggerBtn = this.getElementById('manualTriggerBtn');
        if (manualTriggerBtn) {
            manualTriggerBtn.addEventListener('click', () => {
                this.triggerFlushManual();
            });
        }

        // Manual Flush Button
        const manualFlushBtn = this.getElementById('manualFlushBtn');
        if (manualFlushBtn) {
            manualFlushBtn.addEventListener('click', () => {
                this.triggerManualFlush();
            });
        }


    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadFloweringStatus(),
                this.loadTrichomeData(),
                this.loadMarkers(),
                this.loadFlushTriggerStatus(),
                this.loadPrediction()
            ]);
            
            // Direkte Trigger-Indicator-Aktualisierung nach dem Laden
            this.updateTriggerIndicator();
        } catch (error) {
            console.error('Fehler beim Laden der Daten:', error);
            this.showError('Fehler beim Laden der Daten');
        }
    }

    async loadFloweringStatus() {
        try {
            const response = await fetch(`/flowering/status/${this.currentPlantId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            this.floweringData = data;
            this.updateOverview(data);
            this.updateTimeline();
            this.updateProgressCircle(data.flowering_status.bloom_progress_percentage || data.flowering_status.progress_percentage);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden:', error);
            // Zeige Fehlermeldung im Widget
            const phasePercentageEl = document.getElementById('phasePercentage');
            if (phasePercentageEl) {
                phasePercentageEl.textContent = `(Fehler: ${error.message})`;
            }
        }
    }

    async loadMarkers() {
        const response = await fetch(`/flowering/markers/${this.currentPlantId}`);
        const data = await response.json();
        
        this.markers = data.markers;
        this.updateMarkersList();
        this.updateTimeline(); // Timeline auch aktualisieren
        this.updateTriggerConditions();
    }

    async loadFlushTriggerStatus() {
        const response = await fetch(`/flowering/flush-trigger/${this.currentPlantId}`);
        const data = await response.json();
        
        this.updateFlushTriggerStatus(data);
    }

    async loadPrediction() {
        const response = await fetch(`/flowering/prediction/${this.currentPlantId}`);
        const data = await response.json();
        
        this.updatePrediction(data);
    }

    async loadTrichomeData() {
        // console.log('🌺 FloweringWidget: Lade Trichom-Daten...');
        try {
            await Promise.all([
                this.loadTrichomeStatus(),
                this.loadTrichomeTrigger(),
                this.loadTrichomeRecommendation(),
                this.loadTrichomeProgress(),
                this.loadTrichomeGuidelines()
            ]);
            // console.log('🌺 FloweringWidget: Trichom-Daten erfolgreich geladen');
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden der Trichom-Daten:', error);
        }
    }

    async loadTrichomeStatus() {
        try {
            const response = await fetch(`/flowering/trichome-status/${this.currentPlantId}`);
            if (response.ok) {
                const data = await response.json();
                
                // Speichere die Daten direkt
                this.trichomeData = data;
                
                // Aktualisiere UI nur wenn Daten vorhanden sind
                if (data && data.has_data) {
                this.updateTrichomeStatus(data);
                this.updateTrichomeBadge(data);
                this.updateTrichomeSegments(data);
                this.updateFlushAlert(data);
                    this.updateFlushProgress(data.flush_progress || 0);
                
                // Beobachtungslog aktualisieren
                    if (data.observations) {
                    this.updateObservationList(data.observations);
                } else {
                    this.updateObservationList([]);
                    }
                } else {
                    this.updateTrichomeNoData();
                }
            } else {
                console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Status');
                this.updateTrichomeNoData();
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Status:', error);
            this.updateTrichomeNoData();
        }
    }

    async loadTrichomeTrigger() {
        try {
            const response = await fetch(`/flowering/trichome-trigger/${this.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeTriggerData = data; // Daten speichern
            this.updateTrichomeTrigger(data);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Triggers:', error);
        }
    }

    async loadTrichomeRecommendation() {
        try {
            const response = await fetch(`/flowering/trichome-recommendation/${this.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeRecommendationData = data; // Daten speichern
            this.updateTrichomeRecommendation(data);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden der Trichom-Empfehlung:', error);
        }
    }

    async loadTrichomeProgress() {
        try {
            const response = await fetch(`/flowering/trichome-progress/${this.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeProgressData = data; // Daten speichern
            this.updateTrichomeProgress(data);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Fortschritts:', error);
        }
    }

    async loadTrichomeGuidelines() {
        try {
            const response = await fetch(`/flowering/trichome-guidelines/${this.currentPlantId}`);
            const data = await response.json();
            
            if (data && data.trichome_guidelines) {
                this.loadTrichomeGuidelinesForTab(data.trichome_guidelines, data.strain_type);
            } else {
                console.error('🌺 FloweringWidget: Keine Trichome-Guidelines in der Antwort gefunden');
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden der Trichome-Guidelines:', error);
        }
    }

    async switchTab(tabName) {
        // console.log('🌺 FloweringWidget: switchTab aufgerufen für:', tabName);
        
        // Finde das Flowering-Widget-Container
        const widgetContainer = this.element || document.querySelector('.flowering-widget-container');
        if (!widgetContainer) {
            console.error('🌺 FloweringWidget: Widget-Container nicht gefunden');
            return;
        }
        
        // Aktiven Tab aktualisieren (nur innerhalb des Widgets)
        widgetContainer.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeTabBtn = widgetContainer.querySelector(`[data-tab="${tabName}"]`);
        if (activeTabBtn) {
            activeTabBtn.classList.add('active');
            // console.log('🌺 FloweringWidget: Tab-Button aktiviert:', tabName);
        }

        // Tab-Inhalte wechseln (nur innerhalb des Widgets)
        widgetContainer.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
            pane.style.display = 'none'; // Explizit verstecken
            // console.log('🌺 FloweringWidget: Tab-Pane deaktiviert:', pane.id);
        });
        
        // Suche nach der Tab-Pane mit der entsprechenden ID (nur innerhalb des Widgets)
        const activeTabPane = widgetContainer.querySelector(`#${tabName}`);
        // console.log('🌺 FloweringWidget: Suche nach Tab-Pane mit ID:', tabName);
        // console.log('🌺 FloweringWidget: Gefundene Tab-Pane:', activeTabPane);
        
        if (activeTabPane) {
            activeTabPane.classList.add('active');
            // console.log('🌺 FloweringWidget: Tab-Pane aktiviert:', tabName);
            // console.log('🌺 FloweringWidget: Tab-Pane Display:', activeTabPane.style.display);
            // console.log('🌺 FloweringWidget: Tab-Pane Classes:', activeTabPane.className);
            
            // Force reflow für besseres Rendering
            activeTabPane.offsetHeight;
            
            // Zusätzliche Sicherheit: Display-Style explizit setzen
            activeTabPane.style.display = 'block';
            // console.log('🌺 FloweringWidget: Display-Style explizit auf block gesetzt');
        } else {
            console.error('🌺 FloweringWidget: Tab-Pane nicht gefunden für:', tabName);
            // Debug: Alle verfügbaren Tab-Panes auflisten
            const allTabPanes = widgetContainer.querySelectorAll('.tab-pane');
            // console.log('🌺 FloweringWidget: Verfügbare Tab-Panes:', Array.from(allTabPanes).map(pane => pane.id));
        }

        this.currentTab = tabName;

        // Tab-spezifische Aktionen
        if (tabName === 'markers') {
            this.updateMarkersList();
        } else if (tabName === 'timeline') {
            this.updateTimeline();
        } else if (tabName === 'flush-trigger') {
            this.updateTriggerConditions();
        } else if (tabName === 'trichome') {
            // Trichom-Tab: UI mit gespeicherten Daten aktualisieren
            
            // Trichome-Guidelines laden, falls noch nicht geladen
            if (!this.trichomeGuidelinesLoaded) {
                await this.loadTrichomeGuidelines();
                this.trichomeGuidelinesLoaded = true;
            }
            
            // Verwende die direkt gespeicherten Trichom-Daten
            if (this.trichomeData && this.trichomeData.has_data) {
                this.updateTrichomeStatus(this.trichomeData);
            }
            
            if (this.trichomeTriggerData) {
                this.updateTrichomeTrigger(this.trichomeTriggerData);
            }
            if (this.trichomeRecommendationData) {
                this.updateTrichomeRecommendation(this.trichomeRecommendationData);
            }
            if (this.trichomeProgressData) {
                this.updateTrichomeProgress(this.trichomeProgressData);
            }
        }
    }

        updateOverview(data) {
        const status = data.flowering_status;
        const strain = data.strain_profile;

        // Grunddaten
        const strainNameEl = document.getElementById('strainName');
        if (strainNameEl) strainNameEl.textContent = strain.strain || strain.name || 'Sorte nicht angegeben';
        
        const currentDayEl = document.getElementById('currentDay');
        if (currentDayEl) currentDayEl.textContent = status.current_day;
        
        const currentPhaseEl = document.getElementById('currentPhase');
        if (currentPhaseEl) currentPhaseEl.textContent = this.getPhaseName(status.phase);
        
        // Phasen-Beschreibung aktualisieren
        const phaseDescriptionEl = document.getElementById('phaseDescription');
        if (phaseDescriptionEl) {
            const phaseName = this.getPhaseName(status.phase);
            const currentDay = status.current_day;
            const totalDays = strain.flowering_duration_days ? strain.flowering_duration_days[1] : 75;
            const daysRemaining = totalDays - currentDay;
            
            phaseDescriptionEl.textContent = `${phaseName} - Tag ${currentDay} von ${totalDays} (${daysRemaining} Tage verbleibend)`;
        }
        
        // Beide Fortschrittswerte verwenden - konsistent aus Backend
        const growProgress = status.grow_progress_percentage || 0;
        const bloomProgress = status.bloom_progress_percentage || status.progress_percentage || 0;
        
        // Header mit beiden Werten aktualisieren
        const phasePercentageEl = document.getElementById('phasePercentage');
        if (phasePercentageEl) {
            phasePercentageEl.textContent = `(Grow: ${growProgress.toFixed(1)}% | Blüte: ${bloomProgress.toFixed(1)}%)`;
            phasePercentageEl.removeAttribute('title'); // Tooltip entfernen
        }
        
        // Tooltip von der gesamten Phase-Card entfernen
        const phaseCard = document.querySelector('.phase-card');
        if (phaseCard) {
            phaseCard.removeAttribute('title');
            
            // Tooltip von allen Elementen in der Phase-Card entfernen
            const phaseCardElements = phaseCard.querySelectorAll('*');
            phaseCardElements.forEach(element => {
                element.removeAttribute('title');
            });
        }
        
        // Fortschrittsbalken und Label mit Blüte-Fortschritt
        const phaseProgressTextEl = document.getElementById('phaseProgressText');
        if (phaseProgressTextEl) {
            phaseProgressTextEl.textContent = `${bloomProgress.toFixed(1)}%`;
            phaseProgressTextEl.removeAttribute('title'); // Tooltip entfernen
        }
        
        const phaseProgressEl = document.getElementById('phaseProgress');
        if (phaseProgressEl) phaseProgressEl.style.width = `${bloomProgress}%`;
        
        // Progress Circle im Header aktualisieren
        const progressTextEl = document.getElementById('progressText');
        if (progressTextEl) {
            progressTextEl.textContent = `${bloomProgress.toFixed(1)}%`;
            progressTextEl.removeAttribute('title'); // Tooltip entfernen
        }
        
        // Flush-Status
        const flushStatus = data.flush_status;
        const flushCard = document.getElementById('flushCard');
        const flushStatusEl = document.getElementById('flushStatus');
        const flushCountdown = document.getElementById('flushCountdown');
        const flushReason = document.getElementById('flushReason');

        if (flushStatus.triggered) {
            if (flushCard) flushCard.classList.add('triggered');
            if (flushStatusEl) {
                flushStatusEl.textContent = 'Flush empfohlen';
                flushStatusEl.style.color = '#ef4444';
            }
            if (flushCountdown) {
                flushCountdown.innerHTML = `
                    <span class="days-remaining">${flushStatus.flush_day_today}</span>
                    <span class="countdown-label">Flush-Tag</span>
                `;
            }
        } else {
            if (flushCard) flushCard.classList.remove('triggered');
            if (flushStatusEl) {
                flushStatusEl.textContent = 'Noch nicht empfohlen';
                flushStatusEl.style.color = '#f59e0b';
            }
            if (flushCountdown) {
                const daysUntilFlush = flushStatus.start_recommended - status.current_day;
                flushCountdown.innerHTML = `
                    <span class="days-remaining">${daysUntilFlush}</span>
                    <span class="countdown-label">Tage bis Flush</span>
                `;
            }
        }

        if (flushReason) flushReason.textContent = flushStatus.status_message;

        // Ernte-Prognose
        const recommendation = data.recommendation;
        const harvestEarlyEl = document.getElementById('harvestEarly');
        if (harvestEarlyEl) harvestEarlyEl.textContent = `Tag ${recommendation.harvest_early[0]}-${recommendation.harvest_early[1]}`;
        
        const harvestOptimalEl = document.getElementById('harvestOptimal');
        if (harvestOptimalEl) harvestOptimalEl.textContent = `Tag ${recommendation.harvest_optimal[0]}-${recommendation.harvest_optimal[1]}`;
        
        const harvestLateEl = document.getElementById('harvestLate');
        if (harvestLateEl) harvestLateEl.textContent = `Tag ${recommendation.harvest_late[0]}-${recommendation.harvest_late[1]}`;

        // Meilensteine
        this.updateMilestones(data);
    }

    updateMilestones(data) {
        const milestonesList = document.getElementById('milestonesList');
        if (!milestonesList) return;
        
        const currentDay = data.flowering_status.current_day;
        const milestones = [];

        // Flush-Meilenstein
        if (data.flush_status.start_recommended > currentDay) {
            const daysUntilFlush = data.flush_status.start_recommended - currentDay;
            milestones.push({
                icon: '🚿',
                title: 'Flush-Start',
                details: `Tag ${data.flush_status.start_recommended}`,
                countdown: `${daysUntilFlush} Tage verbleibend`
            });
        }

        // Ernte-Meilenstein
        const optimalHarvest = data.recommendation.harvest_optimal[0];
        if (optimalHarvest > currentDay) {
            const daysUntilHarvest = optimalHarvest - currentDay;
            milestones.push({
                icon: '✂️',
                title: 'Optimaler Erntezeitpunkt',
                details: `Tag ${optimalHarvest}`,
                countdown: `${daysUntilHarvest} Tage verbleibend`
            });
        }

        // Marker-basierte Meilensteine
        const recentMarkers = this.markers
            .filter(m => m.bloom_day > currentDay - 7 && m.bloom_day <= currentDay)
            .slice(0, 2);

        recentMarkers.forEach(marker => {
            milestones.push({
                icon: this.getMarkerIcon(marker.event_type),
                title: marker.event_name,
                details: `Tag ${marker.bloom_day}`,
                countdown: 'Kürzlich hinzugefügt'
            });
        });

        // Meilensteine rendern
        milestonesList.innerHTML = milestones.map(milestone => `
            <div class="milestone-item">
                <div class="milestone-icon">${milestone.icon}</div>
                <div class="milestone-content">
                    <div class="milestone-title">${milestone.title}</div>
                    <div class="milestone-details">${milestone.details}</div>
                </div>
                <div class="milestone-countdown">${milestone.countdown}</div>
            </div>
        `).join('');
    }

    updateTimeline() {
        const timelineContent = document.getElementById('timelineContent');
        if (!timelineContent) return;
        
        // Timeline-Events aus den Markern erstellen
        const events = this.markers.map(marker => ({
            day: marker.bloom_day,
            date: marker.date,
            event: marker.event_name,
            category: marker.category,
            description: marker.notes
        }));

        // Timeline rendern
        timelineContent.innerHTML = events.map(event => `
            <div class="timeline-event ${event.category}">
                <div class="event-marker ${this.getEventMarkerClass(event)}"></div>
                <div class="event-content">
                    <div class="event-header">
                        <span class="event-day">BT${event.day}</span>
                        <span class="event-date">${this.formatDate(event.date)}</span>
                    </div>
                    <div class="event-title">${event.event}</div>
                    <div class="event-description">${event.description}</div>
                </div>
            </div>
        `).join('');
    }

    updateMarkersList() {
        const markersList = document.getElementById('markersList');
        if (!markersList) return;
        
        markersList.innerHTML = this.markers.map(marker => `
            <div class="marker-item" data-marker-id="${marker.id}" data-category="${marker.category}" data-importance="${marker.importance}">
                <div class="marker-header">
                    <div class="marker-info">
                        <div class="marker-title">${marker.event_name}</div>
                        <div class="marker-meta">
                            <span class="marker-category">${this.getCategoryName(marker.category)}</span>
                            <span class="marker-importance ${marker.importance}">${marker.importance}</span>
                            <span>Tag ${marker.bloom_day}</span>
                            <span>${this.formatDate(marker.date)}</span>
                        </div>
                    </div>
                    <div class="marker-actions">
                        <button class="marker-btn edit" data-marker-id="${marker.id}">
                            ✏️ Bearbeiten
                        </button>
                        <button class="marker-btn delete" data-marker-id="${marker.id}">
                            🗑️ Löschen
                        </button>
                    </div>
                </div>
                ${marker.notes ? `<div class="marker-notes">${marker.notes}</div>` : ''}
                
                <!-- Inline Bearbeitungs-Formular -->
                <div class="marker-edit-form" id="editMarkerForm${marker.id}" style="display: none;">
                    <div class="form-header">
                        <h5>✏️ Marker bearbeiten</h5>
                        <button class="btn-close" data-marker-id="${marker.id}">×</button>
                    </div>
                    <form class="marker-edit-form-content" data-marker-id="${marker.id}">
                        <div class="form-row">
                            <div class="form-group">
                                <label>Event-Typ:</label>
                                <input type="text" value="${marker.event_type}" readonly>
                            </div>
                            <div class="form-group">
                                <label>Blüte-Tag:</label>
                                <input type="number" value="${marker.bloom_day}" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Titel:</label>
                                <input type="text" name="event_name" value="${marker.event_name}" required>
                            </div>
                            <div class="form-group">
                                <label>Kategorie:</label>
                                <select name="category" required>
                                    <option value="growth" ${marker.category === 'growth' ? 'selected' : ''}>Wachstum</option>
                                    <option value="maturity" ${marker.category === 'maturity' ? 'selected' : ''}>Reifung</option>
                                    <option value="flush" ${marker.category === 'flush' ? 'selected' : ''}>Flush</option>
                                    <option value="stress" ${marker.category === 'stress' ? 'selected' : ''}>Stress</option>
                                    <option value="harvest" ${marker.category === 'harvest' ? 'selected' : ''}>Ernte</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Wichtigkeit:</label>
                                <select name="importance" required>
                                    <option value="high" ${marker.importance === 'high' ? 'selected' : ''}>Hoch</option>
                                    <option value="medium" ${marker.importance === 'medium' ? 'selected' : ''}>Mittel</option>
                                    <option value="low" ${marker.importance === 'low' ? 'selected' : ''}>Niedrig</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Beschreibung:</label>
                            <textarea name="notes" rows="3">${marker.notes || ''}</textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" data-marker-id="${marker.id}">Abbrechen</button>
                            <button type="submit" class="btn btn-primary">Speichern</button>
                        </div>
                    </form>
                </div>
                
                <!-- Inline Lösch-Bestätigung -->
                <div class="marker-delete-form" id="deleteMarkerForm${marker.id}" style="display: none;">
                    <div class="form-header">
                        <h5>🗑️ Marker löschen</h5>
                        <button class="btn-close" data-marker-id="${marker.id}">×</button>
                    </div>
                    <div class="delete-confirmation">
                        <p>Möchtest du den Marker "<strong>${marker.event_name}</strong>" wirklich löschen?</p>
                        <p class="delete-warning">Diese Aktion kann nicht rückgängig gemacht werden.</p>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" data-marker-id="${marker.id}">Abbrechen</button>
                            <button type="button" class="btn btn-danger" data-marker-id="${marker.id}">Löschen</button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        // Event-Listener für die neuen Buttons hinzufügen
        this.setupMarkerActionListeners();
    }

    updateTriggerConditions() {
        const currentDay = this.floweringData?.flowering_status?.current_day || 0;
        const floweringRange = this.floweringData?.strain_profile?.flowering_duration_days || [60, 75];
        
        // Blütefortschritt
        const bloomProgress = (currentDay / floweringRange[1]) * 100;
        const bloomProgressBar = document.getElementById('bloomProgressBar');
        const bloomProgressText = document.getElementById('bloomProgressText');
        const bloomProgressStatus = document.getElementById('bloomProgressStatus');
        
        if (bloomProgressBar) bloomProgressBar.style.width = `${bloomProgress}%`;
        if (bloomProgressText) bloomProgressText.textContent = `Tag ${currentDay}/${floweringRange[1]} (${bloomProgress.toFixed(1)}%)`;
        
        const bloomThreshold = 0.85 * floweringRange[1];
        if (bloomProgressStatus) {
            if (currentDay >= bloomThreshold) {
                bloomProgressStatus.textContent = '✅';
                bloomProgressStatus.className = 'condition-status met';
            } else {
                bloomProgressStatus.textContent = '❌';
                bloomProgressStatus.className = 'condition-status not-met';
            }
        }

        // Trichome-Status
        const trichomeMarkers = this.markers.filter(m => 
            m.event_type === 'trichome_milky' || m.event_type === 'trichome_amber'
        );
        
        const milkyCount = trichomeMarkers.filter(m => m.event_type === 'trichome_milky').length;
        const amberCount = trichomeMarkers.filter(m => m.event_type === 'trichome_amber').length;
        
        const milkyCountEl = document.getElementById('milkyCount');
        if (milkyCountEl) milkyCountEl.textContent = `${milkyCount} Marker`;
        
        const amberCountEl = document.getElementById('amberCount');
        if (amberCountEl) amberCountEl.textContent = `${amberCount} Marker`;
        
        const trichomeStatus = document.getElementById('trichomeStatus');
        if (trichomeStatus) {
            if (milkyCount >= 1 && currentDay >= 45) {
                trichomeStatus.textContent = '✅';
                trichomeStatus.className = 'condition-status met';
            } else {
                trichomeStatus.textContent = '❌';
                trichomeStatus.className = 'condition-status not-met';
            }
        }

        // Pistillen-Status
        const pistilMarkers = this.markers.filter(m => m.event_type === 'pistils_retracted');
        const pistilCountEl = document.getElementById('pistilCount');
        if (pistilCountEl) pistilCountEl.textContent = `${pistilMarkers.length} Marker`;
        
        const pistilStatus = document.getElementById('pistilStatus');
        if (pistilStatus) {
            if (pistilMarkers.length > 0) {
                pistilStatus.textContent = '✅';
                pistilStatus.className = 'condition-status met';
            } else {
                pistilStatus.textContent = '❌';
                pistilStatus.className = 'condition-status not-met';
            }
        }
    }

    updateFlushTriggerStatus(data) {
        const flushStatus = data.flush_status;
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        // Konsistente Logik mit updateTriggerIndicator
        if (flushStatus.triggered) {
            if (statusIndicator) statusIndicator.classList.add('triggered');
            if (statusText) statusText.textContent = 'Flush aktiv';
        } else if (data.flowering_status && data.flowering_status.current_day >= flushStatus.start_recommended) {
            if (statusIndicator) statusIndicator.classList.add('triggered');
            if (statusText) statusText.textContent = 'Flush empfohlen';
        } else {
            if (statusIndicator) statusIndicator.classList.remove('triggered');
            if (statusText) statusText.textContent = 'Überwachung aktiv';
        }

        // Trigger-Indicator direkt aktualisieren
        this.updateTriggerIndicator(data);

        // Flush-Status-Details
        const flushStatusDetailEl = document.getElementById('flushStatusDetail');
        if (flushStatusDetailEl) {
            flushStatusDetailEl.textContent = flushStatus.triggered ? 'Ausgelöst' : 'Nicht ausgelöst';
        }
        
        const flushStartDayEl = document.getElementById('flushStartDay');
        if (flushStartDayEl) flushStartDayEl.textContent = `Tag ${flushStatus.start_recommended}`;
        
        const flushDurationEl = document.getElementById('flushDuration');
        if (flushDurationEl) flushDurationEl.textContent = `${flushStatus.flush_duration_days} Tage`;
        
        const flushTargetDayEl = document.getElementById('flushTargetDay');
        if (flushTargetDayEl) flushTargetDayEl.textContent = `Tag ${flushStatus.flush_target_day}`;
        
        // Manual Flush Button Status aktualisieren
        const manualFlushBtn = document.getElementById('manualFlushBtn');
        if (manualFlushBtn) {
            if (flushStatus.triggered) {
                manualFlushBtn.disabled = true;
                manualFlushBtn.textContent = 'Flush bereits aktiv';
            } else {
                // Button aktivieren wenn Trichom-Daten vorhanden sind
                const hasTrichomeData = data.trichome_status && data.trichome_status.current_values;
                manualFlushBtn.disabled = !hasTrichomeData;
                manualFlushBtn.textContent = hasTrichomeData ? 'Flush manuell starten' : 'Keine Trichom-Daten';
            }
        }

        // Trigger-Indicator Status aktualisieren
        const triggerIndicator = this.getElementById('triggerIndicator');
        const triggerDot = this.getElementById('triggerDot');
        const triggerText = this.getElementById('triggerText');
        const triggerMessage = this.getElementById('triggerMessage');
        
        if (triggerIndicator && triggerDot && triggerText && triggerMessage) {
            console.log('🌺 FloweringWidget: Trigger-Status:', {
                triggered: flushStatus.triggered,
                current_day: data.flowering_status?.current_day,
                start_recommended: flushStatus.start_recommended,
                flush_target_day: flushStatus.flush_target_day
            });
            
            if (flushStatus.triggered) {
                // Flush ist aktiv - ROT
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = 'Flush aktiv';
                triggerMessage.textContent = `Flush läuft seit ${flushStatus.flush_day_today} Tagen`;
                triggerMessage.className = 'trigger-message high';
                console.log('🌺 FloweringWidget: Trigger-Status = ROT (Flush aktiv)');
            } else if (data.flowering_status && data.flowering_status.current_day >= flushStatus.start_recommended) {
                // Flush wird empfohlen - ORANGE (aktueller Tag >= empfohlener Start-Tag)
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = 'Flush empfohlen';
                triggerMessage.textContent = `Flush kann gestartet werden (Tag ${flushStatus.start_recommended})`;
                triggerMessage.className = 'trigger-message medium';
                console.log('🌺 FloweringWidget: Trigger-Status = ORANGE (Flush empfohlen)');
            } else {
                // Flush noch nicht empfohlen - GRÜN
                triggerDot.className = 'trigger-dot inactive';
                triggerText.textContent = 'Überwachung aktiv';
                triggerMessage.textContent = `70% milchige, 0% bernstein Trichome`;
                triggerMessage.className = 'trigger-message low';
                console.log('🌺 FloweringWidget: Trigger-Status = GRÜN (Überwachung aktiv)');
            }
        }

        // Guidelines in separate Container aufteilen
        console.log('🌺 FloweringWidget: updateFlushTriggerStatus - data:', data);
        console.log('🌺 FloweringWidget: flush_guidelines vorhanden:', data && data.flush_guidelines);
        
        // Prüfe ob Guidelines-Daten vorhanden sind und lade sie für beide Tabs
        if (data && data.flush_guidelines) {
            const guidelines = data.flush_guidelines;
            const strainType = data.strain_type; // Verwende strain_type aus den API-Daten
            console.log('🌺 FloweringWidget: Guidelines-Daten:', guidelines);
            console.log('🌺 FloweringWidget: Strain-Typ aus API:', strainType);
            
            // Lade Guidelines für Flush-Trigger-Tab
            this.loadGuidelinesForTab(guidelines, 'flush', strainType);
            
            // Lade Guidelines für Trichome-Tab
            this.loadGuidelinesForTab(guidelines, 'trichome', strainType);
            
            // Richte Guidelines-Tab-Event-Listener ein
            this.setupGuidelinesTabListeners();
        }
    }
    
    loadGuidelinesForTab(guidelines, tabType, strainType) {
        const prefix = tabType === 'flush' ? 'flush' : 'trichome';
        
        // Startbedingungen - nur für den relevanten Strain-Typ
        const startbedingungenContent = this.getElementById(`${prefix}StartbedingungenContent`);
        console.log(`🌺 FloweringWidget: ${prefix}StartbedingungenContent gefunden:`, startbedingungenContent);
        if (startbedingungenContent && guidelines.startbedingungen) {
            let startbedingungenHtml = '';
            console.log(`🌺 FloweringWidget: Verwende Strain-Typ für ${tabType}-Tab:`, strainType);
            
            console.log('🌺 FloweringWidget: Strain-Typ:', strainType);
            console.log('🌺 FloweringWidget: Verfügbare Startbedingungen:', Object.keys(guidelines.startbedingungen));
            
            // Zeige nur die Guidelines für den relevanten Strain-Typ
            if (guidelines.startbedingungen[strainType]) {
                const strainGuidelines = guidelines.startbedingungen[strainType];
                const strainName = strainType === 'autoflower' ? 'Autoflower' : 'Photoperiod';
                
                startbedingungenHtml += '<div class="strain-type-section">';
                startbedingungenHtml += `<h6>🎯 ${strainName}-Startbedingungen:</h6>`;
                startbedingungenHtml += '<ul>';
                strainGuidelines.indikatoren.forEach(indikator => {
                    startbedingungenHtml += `<li>${indikator}</li>`;
                });
                startbedingungenHtml += '</ul>';
                startbedingungenHtml += `<p><strong>Zeitpunkt:</strong> ${strainGuidelines.zeitpunktTageVorErnte} Tage vor Ernte</p>`;
                startbedingungenHtml += '</div>';
                
                console.log(`🌺 FloweringWidget: ${strainName}-Guidelines geladen mit ${strainGuidelines.indikatoren.length} Indikatoren`);
            } else {
                startbedingungenHtml += '<div class="no-guidelines">';
                startbedingungenHtml += `<p>Keine spezifischen Guidelines für ${strainType} verfügbar.</p>`;
                startbedingungenHtml += '</div>';
            }
            
            startbedingungenContent.innerHTML = startbedingungenHtml;
        }
        
        // Methoden
        const methodenContent = this.getElementById(`${prefix}MethodenContent`);
        console.log(`🌺 FloweringWidget: ${prefix}MethodenContent gefunden:`, methodenContent);
        if (methodenContent && guidelines.methoden) {
            let methodenHtml = '';
            guidelines.methoden.forEach((methode, index) => {
                methodenHtml += '<div class="method-item">';
                methodenHtml += `<h6>${methode.typ}</h6>`;
                methodenHtml += `<p>${methode.beschreibung}</p>`;
                
                if (methode.vorteile || methode.nachteile) {
                    methodenHtml += '<div class="method-pros-cons">';
                    if (methode.vorteile) {
                        methodenHtml += '<div class="pros">';
                        methodenHtml += '<strong>✅ Vorteile:</strong>';
                        methodenHtml += '<ul>';
                        methode.vorteile.forEach(vorteil => {
                            methodenHtml += `<li>${vorteil}</li>`;
                        });
                        methodenHtml += '</ul>';
                        methodenHtml += '</div>';
                    }
                    if (methode.nachteile) {
                        methodenHtml += '<div class="cons">';
                        methodenHtml += '<strong>❌ Nachteile:</strong>';
                        methodenHtml += '<ul>';
                        methode.nachteile.forEach(nachteil => {
                            methodenHtml += `<li>${nachteil}</li>`;
                        });
                        methodenHtml += '</ul>';
                        methodenHtml += '</div>';
                    }
                    methodenHtml += '</div>';
                }
                methodenHtml += '</div>';
            });
            methodenContent.innerHTML = methodenHtml;
        }
        
        // Fehlerquellen
        const fehlerquellenContent = this.getElementById(`${prefix}FehlerquellenContent`);
        console.log(`🌺 FloweringWidget: ${prefix}FehlerquellenContent gefunden:`, fehlerquellenContent);
        if (fehlerquellenContent && guidelines.fehlerquellen) {
            let fehlerquellenHtml = '<ul class="error-list">';
            guidelines.fehlerquellen.forEach(fehler => {
                fehlerquellenHtml += `<li>${fehler}</li>`;
            });
            fehlerquellenHtml += '</ul>';
            fehlerquellenContent.innerHTML = fehlerquellenHtml;
        }
        
        // Faustregeln
        const faustregelnContent = this.getElementById(`${prefix}FaustregelnContent`);
        console.log(`🌺 FloweringWidget: ${prefix}FaustregelnContent gefunden:`, faustregelnContent);
        if (faustregelnContent && guidelines.faustregeln) {
            let faustregelnHtml = '<ul class="rule-list">';
            guidelines.faustregeln.forEach(regel => {
                faustregelnHtml += `<li>${regel}</li>`;
            });
            faustregelnHtml += '</ul>';
            faustregelnContent.innerHTML = faustregelnHtml;
        }
    }

    loadTrichomeGuidelinesForTab(guidelines, strainType) {
        console.log('🌺 FloweringWidget: Lade Trichome-Guidelines für Tab:', strainType);
        console.log('🌺 FloweringWidget: Guidelines-Daten:', guidelines);
        
        // Debug: Alle verfügbaren Felder anzeigen
        console.log('🌺 FloweringWidget: Verfügbare Guidelines-Felder:', Object.keys(guidelines));
        
        // Event-Marker
        const startbedingungenContent = this.getElementById('trichomeStartbedingungenContent');
        if (startbedingungenContent && guidelines.eventMarker) {
            let startbedingungenHtml = '<div class="event-markers-section">';
            startbedingungenHtml += '<h6>🎯 Trichome-Event-Marker:</h6>';
            startbedingungenHtml += '<ul>';
            guidelines.eventMarker.forEach(marker => {
                // Zeige alle Event-Marker, nicht nur trichome-spezifische
                startbedingungenHtml += `<li><strong>${marker.beschreibung}</strong>`;
                startbedingungenHtml += `<br><small>Trigger: ${marker.trigger.join(', ')}</small>`;
                startbedingungenHtml += `<br><small>Bedeutung: ${marker.bedeutung}</small></li>`;
            });
            startbedingungenHtml += '</ul></div>';
            startbedingungenContent.innerHTML = startbedingungenHtml;
        }
        
        // Harvest-Empfehlungen
        const methodenContent = this.getElementById('trichomeMethodenContent');
        if (methodenContent && guidelines.harvestEmpfehlungen) {
            let methodenHtml = '<div class="harvest-recommendations-section">';
            methodenHtml += '<h6>🌾 Ernte-Empfehlungen:</h6>';
            
            Object.entries(guidelines.harvestEmpfehlungen).forEach(([type, data]) => {
                if (type !== 'meta') {
                    methodenHtml += `<div class="harvest-type">`;
                    methodenHtml += `<h7>${type.charAt(0).toUpperCase() + type.slice(1)}:</h7>`;
                    methodenHtml += `<p><strong>Wirkung:</strong> ${data.wirkung}</p>`;
                    if (data.trichome) {
                        methodenHtml += `<p><strong>Trichome:</strong> ${data.trichome.milchig} milchig, ${data.trichome.bernstein} bernstein</p>`;
                    }
                    if (data.tageNachBluete) {
                        methodenHtml += `<p><strong>Zeitraum:</strong> ${data.tageNachBluete.join('-')} Tage nach Blüte</p>`;
                    }
                    methodenHtml += '</div>';
                }
            });
            methodenHtml += '</div>';
            methodenContent.innerHTML = methodenHtml;
        }
        
        // Strain-Abhängigkeit
        const fehlerquellenContent = this.getElementById('trichomeFehlerquellenContent');
        if (fehlerquellenContent && guidelines.strainAbhaengigkeit) {
            let fehlerquellenHtml = '<div class="strain-dependency-section">';
            fehlerquellenHtml += '<h6>🌱 Strain-Abhängigkeit:</h6>';
            fehlerquellenHtml += '<ul>';
            
            if (guidelines.strainAbhaengigkeit.flushVorErnteTage) {
                Object.entries(guidelines.strainAbhaengigkeit.flushVorErnteTage).forEach(([type, days]) => {
                    fehlerquellenHtml += `<li><strong>${type}:</strong> ${days} Tage Flush vor Ernte</li>`;
                });
            }
            
            if (guidelines.strainAbhaengigkeit.beispiele) {
                fehlerquellenHtml += '</ul><h6>🌱 Beispiele:</h6><ul>';
                Object.entries(guidelines.strainAbhaengigkeit.beispiele).forEach(([strain, data]) => {
                    fehlerquellenHtml += `<li><strong>${strain}:</strong> ${data.gesamtBlüteTage.join('-')} Tage Blüte, Flush ab Tag ${data.flushEmpfehlungAb}</li>`;
                });
            }
            
            fehlerquellenHtml += '</ul>';
            fehlerquellenHtml += '</div>';
            fehlerquellenContent.innerHTML = fehlerquellenHtml;
        }
        
        // Faustregeln
        const faustregelnContent = this.getElementById('trichomeFaustregelnContent');
        if (faustregelnContent && guidelines.faustregeln) {
            let faustregelnHtml = '<ul class="rule-list">';
            guidelines.faustregeln.forEach(regel => {
                faustregelnHtml += `<li>${regel}</li>`;
            });
            faustregelnHtml += '</ul>';
            faustregelnContent.innerHTML = faustregelnHtml;
        }
    }

    updateTriggerIndicator(data = null) {
        // Verwende gespeicherte Daten oder übergebene Daten
        const flushData = data || this.floweringData;
        if (!flushData) {
            console.log('🌺 FloweringWidget: Keine Daten für Trigger-Indicator verfügbar');
            return;
        }

        const flushStatus = flushData.flush_status;
        const currentDay = flushData.flowering_status?.current_day;

        console.log('🌺 FloweringWidget: updateTriggerIndicator:', {
            triggered: flushStatus.triggered,
            current_day: currentDay,
            start_recommended: flushStatus.start_recommended
        });

        const triggerDot = this.getElementById('triggerDot');
        const triggerText = this.getElementById('triggerText');
        const triggerMessage = this.getElementById('triggerMessage');

        if (triggerDot && triggerText && triggerMessage) {
            if (flushStatus.triggered) {
                // Flush ist aktiv - ROT
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = '🚿 Flush aktiv';
                triggerMessage.textContent = `Flush läuft seit ${flushStatus.flush_day_today} Tagen`;
                triggerMessage.className = 'trigger-message high';
                console.log('🌺 FloweringWidget: Trigger-Status = ROT (Flush aktiv)');
            } else if (currentDay >= flushStatus.start_recommended) {
                // Flush wird empfohlen - ORANGE
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = '🚿 Flush empfohlen';
                triggerMessage.textContent = `Flush kann gestartet werden (Tag ${flushStatus.start_recommended})`;
                triggerMessage.className = 'trigger-message medium';
                console.log('🌺 FloweringWidget: Trigger-Status = ORANGE (Flush empfohlen)');
            } else {
                // Flush noch nicht empfohlen - GRÜN
                triggerDot.className = 'trigger-dot inactive';
                triggerText.textContent = '🚿 Flush-Trigger';
                triggerMessage.textContent = `Überwachung aktiv`;
                triggerMessage.className = 'trigger-message low';
                console.log('🌺 FloweringWidget: Trigger-Status = GRÜN (Überwachung aktiv)');
            }
        }
    }

    updatePrediction(data) {
        const predictions = data.predictions;
        
        // Flush-Start
        const flushStart = predictions.flush_start;
        const flushStartDateEl = document.getElementById('flushStartDate');
        if (flushStartDateEl) flushStartDateEl.textContent = this.formatDate(flushStart.date);
        
        const flushStartCountdownEl = document.getElementById('flushStartCountdown');
        if (flushStartCountdownEl) {
            flushStartCountdownEl.textContent = `${flushStart.days_remaining} Tage verbleibend`;
        }

        // Ernte optimal
        const harvestOptimal = predictions.harvest_optimal;
        const harvestOptimalDateEl = document.getElementById('harvestOptimalDate');
        if (harvestOptimalDateEl) harvestOptimalDateEl.textContent = this.formatDate(harvestOptimal.date);
        
        const harvestOptimalCountdownEl = document.getElementById('harvestOptimalCountdown');
        if (harvestOptimalCountdownEl) {
            harvestOptimalCountdownEl.textContent = `${harvestOptimal.days_remaining} Tage verbleibend`;
        }

        // Empfehlungen
        const recommendationsList = document.getElementById('recommendationsList');
        if (recommendationsList) {
            recommendationsList.innerHTML = data.recommendations.immediate.map(rec => `
                <div class="recommendation-item">
                    <div class="recommendation-icon">💡</div>
                    <div class="recommendation-text">${rec}</div>
                </div>
            `).join('');
        }

        // Risikofaktoren
        this.updateRiskFactors(data.risk_factors);
    }

    updateRiskFactors(riskFactors) {
        // Hohe Risiken
        const highRisks = document.getElementById('highRisks');
        if (highRisks) {
            const highRisksList = highRisks.querySelector('.risk-list');
            if (highRisksList) {
                highRisksList.innerHTML = riskFactors.high.map(risk => 
                    `<li>${risk}</li>`
                ).join('');
            }
        }

        // Mittlere Risiken
        const mediumRisks = document.getElementById('mediumRisks');
        if (mediumRisks) {
            const mediumRisksList = mediumRisks.querySelector('.risk-list');
            if (mediumRisksList) {
                mediumRisksList.innerHTML = riskFactors.medium.map(risk => 
                    `<li>${risk}</li>`
                ).join('');
            }
        }

        // Niedrige Risiken
        const lowRisks = document.getElementById('lowRisks');
        if (lowRisks) {
            const lowRisksList = lowRisks.querySelector('.risk-list');
            if (lowRisksList) {
                lowRisksList.innerHTML = riskFactors.low.map(risk => 
                    `<li>${risk}</li>`
                ).join('');
            }
        }
    }

    // Modal-Funktionen
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // Focus auf erstes Input-Feld setzen
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }

    closeModal(modal) {
        if (typeof modal === 'string') {
            modal = document.getElementById(modal);
        }
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        }
    }

    // Marker-Methoden
    openMarkerModal(markerId = null) {
        const modal = document.getElementById('markerModal');
        const title = document.getElementById('markerModalTitle');
        
        if (markerId) {
            title.textContent = 'Marker bearbeiten';
            this.fillMarkerForm(markerId);
        } else {
            title.textContent = 'Neuer Marker';
            this.clearMarkerForm();
        }
        
        this.openModal('markerModal');
    }

    fillMarkerForm(markerId) {
        // Marker-Daten laden und Formular füllen
    }

    clearMarkerForm() {
        const form = document.getElementById('markerForm');
        if (form) {
            form.reset();
        }
    }

    async saveMarker() {
        // Marker-Speicherlogik implementieren
    }

    async editMarker(markerId) {
        this.openMarkerModal(markerId);
    }

    async deleteMarker(markerId) {
        // Marker-Löschlogik implementieren
    }

    filterMarkers() {
        const categoryFilter = this.getElementById('categoryFilter');
        const importanceFilter = this.getElementById('importanceFilter');
        const markersList = this.getElementById('markersList');
        
        if (!categoryFilter || !importanceFilter || !markersList) {
            console.error('🌺 FloweringWidget: Filter-Elemente nicht gefunden');
            return;
        }
        
        const selectedCategory = categoryFilter.value;
        const selectedImportance = importanceFilter.value;
        
        // Alle Marker-Elemente durchgehen
        const markerItems = markersList.querySelectorAll('.marker-item');
        
        markerItems.forEach(markerItem => {
            const category = markerItem.dataset.category;
            const importance = markerItem.dataset.importance;
            
            let showMarker = true;
            
            // Kategorie-Filter
            if (selectedCategory && category !== selectedCategory) {
                showMarker = false;
            }
            
            // Wichtigkeit-Filter
            if (selectedImportance && importance !== selectedImportance) {
                showMarker = false;
            }
            
            // Marker anzeigen/verstecken
            if (showMarker) {
                markerItem.style.display = 'block';
                markerItem.classList.remove('filtered-out');
            } else {
                markerItem.style.display = 'none';
                markerItem.classList.add('filtered-out');
            }
        });
        
        // Anzahl der sichtbaren Marker anzeigen
        const visibleMarkers = markersList.querySelectorAll('.marker-item:not(.filtered-out)').length;
        const totalMarkers = markerItems.length;
        
        // Optional: "Keine Marker" Nachricht anzeigen
        const noMarkersMessage = markersList.querySelector('.no-markers-message');
        if (visibleMarkers === 0 && totalMarkers > 0) {
            if (!noMarkersMessage) {
                const message = document.createElement('div');
                message.className = 'no-markers-message';
                message.innerHTML = `
                    <div class="empty-state">
                        <span class="empty-icon">🔍</span>
                        <span class="empty-text">Keine Marker mit den gewählten Filtern gefunden</span>
                    </div>
                `;
                markersList.appendChild(message);
            }
        } else if (noMarkersMessage) {
            noMarkersMessage.remove();
        }
    }

    // Flush-Trigger-Methoden
    async triggerFlushManual() {
        // Manueller Flush-Trigger-Logik implementieren
    }

    triggerManualFlush() {
        // Manueller Flush-Logik implementieren
    }

    // Hilfsfunktionen
    setupProgressCircle() {
        const circle = document.getElementById('progressCircle');
        if (!circle) return;
        
        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        
        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = circumference;
    }

    updateProgressCircle(percentage) {
        const circle = document.getElementById('progressCircle');
        const progressText = document.getElementById('progressText');
        
        if (!circle || !progressText) return;
        
        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        
        const offset = circumference - (percentage / 100) * circumference;
        circle.style.strokeDashoffset = offset;
        
        progressText.textContent = `${percentage.toFixed(1)}%`;
        progressText.removeAttribute('title'); // Tooltip entfernen
    }

    getPhaseName(phaseKey) {
        const phaseNames = {
            'preflower': 'Vorblüte',
            'stretch': 'Stretch-Phase',
            'flowering_middle': 'Hauptblüte',
            'flowering_late': 'Spätblüte',
            'flush': 'Flush-Phase'
        };
        return phaseNames[phaseKey] || phaseKey;
    }

    getCategoryName(category) {
        const categoryNames = {
            'growth': 'Wachstum',
            'maturity': 'Reifung',
            'flush': 'Flush',
            'stress': 'Stress'
        };
        return categoryNames[category] || category;
    }

    getMarkerIcon(eventType) {
        const icons = {
            'stretch_completed': '📈',
            'trichome_milky': '🔬',
            'trichome_amber': '🔬',
            'pistils_retracted': '🌸',
            'flush_started': '🚿',
            'flowering_delay': '⚠️'
        };
        return icons[eventType] || '📍';
    }

    getEventMarkerClass(event) {
        const currentDay = this.floweringData?.flowering_status?.current_day || 0;
        
        if (event.day === currentDay) return 'current';
        if (event.day < currentDay) return 'past';
        return 'future';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE');
    }

    showSuccess(message) {
        // Einfache Erfolgsmeldung
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1001;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    showError(message) {
        // Einfache Fehlermeldung anzeigen
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    // ===== TRICHOM UPDATE FUNCTIONS =====

    updateTrichomeStatus(data) {
        // Prüfe ob die Daten die erwartete Struktur haben
        if (!data || !data.has_data) {
            this.updateTrichomeNoData();
            return;
        }

        // Status-Badge mit erweiterten Daten
        this.updateTrichomeBadge(data);

        // Letzte Aktualisierung
        const lastUpdate = this.getElementById('lastTrichomeUpdate');
        if (lastUpdate && data.latest_entry) {
            const date = new Date(data.latest_entry.date).toLocaleDateString('de-DE');
            lastUpdate.textContent = `Letzte Analyse: ${date}`;
        }

        // Verbesserte Trichom-Balken mit neuen Daten
        this.updateTrichomeSegments(data);

        // Flush-Alert mit erweiterten Daten
        this.updateFlushAlert(data);

        // Reifegrad mit erweiterten Daten
        const maturityLevel = this.getElementById('maturityLevelValue');
        if (maturityLevel) {
            maturityLevel.textContent = this.getMaturityLevelText(data.maturity_level);
            maturityLevel.className = `level-value ${data.maturity_level}`;
        }

        // Flush-Fortschritt mit neuen Daten
        this.updateFlushProgress(data.flush_progress);
        
        // Ernte-Fortschritt (neu)
        const harvestProgress = this.getElementById('harvestProgress');
        if (harvestProgress && data.harvest_progress !== undefined) {
            harvestProgress.style.width = `${data.harvest_progress}%`;
            harvestProgress.setAttribute('title', `${data.harvest_progress}% Ernte-Fortschritt`);
        }
        
        // Trend-Anzeige (neu)
        const trendElement = this.getElementById('trichomeTrend');
        if (trendElement && data.trend) {
            trendElement.textContent = data.trend;
            trendElement.className = `trend-indicator ${data.trend.replace(' ', '-')}`;
        }
        
        // Urgency-Anzeige (neu)
        const urgencyElement = this.getElementById('trichomeUrgency');
        if (urgencyElement && data.urgency) {
            urgencyElement.textContent = data.urgency;
            urgencyElement.className = `urgency-indicator ${data.urgency}`;
        }
        
        // Beobachtungslog aktualisieren
        console.log('🌺 FloweringWidget: Aktualisiere Beobachtungslog beim Laden...');
        if (data.observations) {
            this.updateObservationList(data.observations);
        } else {
            console.log('🌺 FloweringWidget: Keine Beobachtungen in Status-Daten gefunden');
        }
        

    }

    updateTrichomeBadge(data) {
        const badge = this.getElementById('trichomeStatusBadge');
        const badgeIcon = this.getElementById('badgeIcon');
        const badgeText = this.getElementById('badgeText');

        if (badge && badgeIcon && badgeText) {
            // Badge-Klasse setzen
            badge.className = `trichome-status-badge ${data.maturity_level}`;
            
            // Icon basierend auf Reifegrad
            const icons = {
                'early': '🌱',
                'developing': '🌿',
                'flush_approaching': '🚰',
                'flush_ready': '🚰',
                'harvest_approaching': '✂️',
                'harvest_ready': '✂️'
            };
            badgeIcon.textContent = icons[data.maturity_level] || '🔬';
            
            // Text setzen
            badgeText.textContent = data.recommendation;
        }
    }

    updateTrichomeSegments(data) {
        // Verwende current_values aus den Daten
        const currentValues = data.current_values || {};
        
        // Klare Trichome
        const clearSegment = this.getElementById('clearSegmentBar');
        const clearValue = this.getElementById('clearValueBar');
        if (clearSegment && clearValue) {
            const clearPercentage = currentValues.clear_percentage || 0;
            clearSegment.style.width = `${clearPercentage}%`;
            clearValue.textContent = `${clearPercentage}%`;
        }

        // Milchige Trichome
        const milkySegment = this.getElementById('milkySegmentBar');
        const milkyValue = this.getElementById('milkyValueBar');
        if (milkySegment && milkyValue) {
            const milkyPercentage = currentValues.milky_percentage || 0;
            milkySegment.style.width = `${milkyPercentage}%`;
            milkyValue.textContent = `${milkyPercentage}%`;
        }

        // Bernstein Trichome
        const amberSegment = this.getElementById('amberSegmentBar');
        const amberValue = this.getElementById('amberValueBar');
        if (amberSegment && amberValue) {
            const amberPercentage = currentValues.amber_percentage || 0;
            amberSegment.style.width = `${amberPercentage}%`;
            amberValue.textContent = `${amberPercentage}%`;
        }
    }

    updateFlushAlert(data) {
        const flushAlert = this.getElementById('flushAlert');
        if (!flushAlert) return;

        // Erweiterte Flush-Alert-Logik basierend auf neuen Daten
        if (data.flush_progress >= 80) {
            flushAlert.classList.add('active', 'high');
            flushAlert.textContent = 'Flush-Fortschritt hoch - Flush starten!';
        } else if (data.flush_progress >= 60) {
            flushAlert.classList.add('active', 'medium');
            flushAlert.textContent = 'Flush-Fortschritt mittel - Überwachung erhöhen';
        } else if (data.flush_progress >= 40) {
            flushAlert.classList.add('active', 'low');
            flushAlert.textContent = 'Flush-Fortschritt niedrig - Weiter beobachten';
        } else {
            flushAlert.classList.remove('active', 'high', 'medium', 'low');
            flushAlert.textContent = '';
        }
    }

    updateFlushProgress(progress) {
        const flushProgressEl = this.getElementById('flushProgress');
        if (!flushProgressEl) return;

        flushProgressEl.style.width = `${progress}%`;
        flushProgressEl.setAttribute('title', `${progress}% Flush-Fortschritt`);
    }

    getMaturityLevelText(level) {
        const levels = {
            'early': 'Frühreif',
            'developing': 'Entwicklungsphase',
            'flush_approaching': 'Flush nähert sich',
            'flush_ready': 'Flush-Bereit',
            'harvest_approaching': 'Ernte nähert sich',
            'harvest_ready': 'Erntebereit'
        };
        return levels[level] || level;
    }

    getTrendClass(trendText) {
        if (trendText.includes('↑') || trendText.includes('zunehmend') || trendText.includes('increasing')) {
            return 'increasing';
        } else if (trendText.includes('↓') || trendText.includes('abnehmend') || trendText.includes('decreasing')) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }

    updateTrichomeRecommendation(data) {
        
        const recommendationMessage = this.getElementById('recommendationMessage');
        const actionText = this.getElementById('actionText');
        const urgencyText = this.getElementById('urgencyText');
        const nextCheckText = this.getElementById('nextCheckText');
        const tipsList = this.getElementById('tipsList');
        const trendAnalysis = this.getElementById('trendAnalysis');
        
        // Empfehlung aus dem recommendation-Objekt extrahieren
        if (data && data.recommendation) {
            const recommendation = data.recommendation;
            
            if (recommendationMessage) {
                recommendationMessage.textContent = recommendation.message || 'Empfehlung wird geladen...';
            }
            
            if (actionText) {
                actionText.textContent = recommendation.action || '--';
            }
            
            if (urgencyText) {
                urgencyText.textContent = recommendation.urgency || 'Niedrig';
            }
            
            if (nextCheckText) {
                nextCheckText.textContent = recommendation.next_check || '--';
            }
            
            if (tipsList && recommendation.tips) {
                tipsList.innerHTML = '';
                recommendation.tips.forEach(tip => {
                    const li = document.createElement('li');
                    li.textContent = tip;
                    tipsList.appendChild(li);
                });
            }
            
            if (trendAnalysis) {
                trendAnalysis.textContent = recommendation.trend_analysis || 'Keine Trend-Daten';
            }
        } else {
            // Fallback für alte Datenstruktur
            if (recommendationMessage && data) {
                recommendationMessage.textContent = data.message || data.recommendation || 'Empfehlung wird geladen...';
            }
            
            if (actionText && data) {
                actionText.textContent = data.action || '--';
            }
            
            if (urgencyText && data) {
                urgencyText.textContent = data.urgency || 'Niedrig';
            }
        }
    }

    updateTrichomeTrigger(data) {
        const triggerMessage = this.getElementById('triggerMessage');
        const triggerText = this.getElementById('triggerText');
        const triggerType = this.getElementById('triggerType');
        const triggerAction = this.getElementById('triggerAction');
        const manualFlushBtn = this.getElementById('manualFlushBtn');
        
        if (data && data.trigger_status) {
            const trigger = data.trigger_status;
            
            if (triggerMessage) {
                triggerMessage.textContent = trigger.message || 'Trigger-Status wird geladen...';
            }
            
            if (triggerText) {
                triggerText.textContent = trigger.triggered ? 'Trigger aktiv' : 'Überwachung aktiv';
            }
            
            if (triggerType) {
                triggerType.textContent = trigger.trigger_type || 'none';
                triggerType.className = `trigger-type ${trigger.trigger_type || 'none'}`;
            }
            
            if (triggerAction) {
                triggerAction.textContent = trigger.action || 'monitor';
                triggerAction.className = `trigger-action ${trigger.action || 'monitor'}`;
            }
            
            if (manualFlushBtn) {
                manualFlushBtn.disabled = !trigger.triggered;
                manualFlushBtn.className = trigger.triggered ? 'btn btn-warning' : 'btn btn-secondary disabled';
            }
        }
    }

    updateTrichomeNoData() {
        const noDataEl = this.getElementById('trichomeNoData');
        if (!noDataEl) return;

        noDataEl.classList.add('active');
    }

    updateTrichomeProgress(data) {
        
        // Beobachtungsanzahl
        const entriesCount = this.getElementById('entriesCount');
        if (entriesCount && data.observations_count !== undefined) {
            entriesCount.textContent = `${data.observations_count} Beobachtungen`;
        }
        
        // Fortschritts-Balken
        const developmentProgress = this.getElementById('developmentProgress');
        const maturityProgress = this.getElementById('maturityProgress');
        const flushProgress = this.getElementById('flushProgress');
        const overallProgress = this.getElementById('overallProgress');
        
        if (data.progress) {
            if (developmentProgress) {
                developmentProgress.style.width = `${data.progress.development_progress}%`;
                developmentProgress.setAttribute('title', `Entwicklung: ${data.progress.development_progress}%`);
            }
            
            if (maturityProgress) {
                maturityProgress.style.width = `${data.progress.maturity_progress}%`;
                maturityProgress.setAttribute('title', `Reife: ${data.progress.maturity_progress}%`);
            }
            
            if (flushProgress) {
                flushProgress.style.width = `${data.progress.flush_progress}%`;
                flushProgress.setAttribute('title', `Flush: ${data.progress.flush_progress}%`);
            }
            
            if (overallProgress) {
                overallProgress.style.width = `${data.progress.overall_progress}%`;
                overallProgress.setAttribute('title', `Gesamt: ${data.progress.overall_progress}%`);
            }
        }
        
        // Neue Spalten-Daten
        const clearTrendValue = this.getElementById('clearTrendValue');
        const milkyTrendValue = this.getElementById('milkyTrendValue');
        const amberTrendValue = this.getElementById('amberTrendValue');
        const clearTrend = this.getElementById('clearTrend');
        const milkyTrend = this.getElementById('milkyTrend');
        const amberTrend = this.getElementById('amberTrend');
        
        // Aktuelle Werte in Spalten anzeigen
        if (data.current_values) {
            if (clearTrendValue) {
                clearTrendValue.textContent = `${data.current_values.clear_percentage || 0}%`;
            }
            if (milkyTrendValue) {
                milkyTrendValue.textContent = `${data.current_values.milky_percentage || 0}%`;
            }
            if (amberTrendValue) {
                amberTrendValue.textContent = `${data.current_values.amber_percentage || 0}%`;
            }
        }
        
        // Trend-Pfeile
        if (data.trend) {
            if (clearTrend) {
                const clearTrendText = data.trend.clear_trend || '→';
                clearTrend.textContent = clearTrendText;
                clearTrend.className = `trend-arrow ${this.getTrendClass(clearTrendText)}`;
            }
            
            if (milkyTrend) {
                const milkyTrendText = data.trend.milky_trend || '→';
                milkyTrend.textContent = milkyTrendText;
                milkyTrend.className = `trend-arrow ${this.getTrendClass(milkyTrendText)}`;
            }
            
            if (amberTrend) {
                const amberTrendText = data.trend.amber_trend || '→';
                amberTrend.textContent = amberTrendText;
                amberTrend.className = `trend-arrow ${this.getTrendClass(amberTrendText)}`;
            }
        }
        
        // Phasen-Fortschritt
        if (data.phases) {
            Object.keys(data.phases).forEach(phaseKey => {
                const phase = data.phases[phaseKey];
                const phaseElement = this.getElementById(`${phaseKey}Progress`);
                if (phaseElement) {
                    phaseElement.style.width = `${phase.current_progress}%`;
                    phaseElement.setAttribute('title', `${phase.name}: ${phase.current_progress}%`);
                    phaseElement.className = `phase-progress ${phase.completed ? 'completed' : 'in-progress'}`;
                }
            });
        }
        
        // Aktuelle Werte
        const currentMilky = this.getElementById('currentMilky');
        const currentAmber = this.getElementById('currentAmber');
        const currentClear = this.getElementById('currentClear');
        
        if (data.current_values) {
            if (currentMilky) {
                currentMilky.textContent = `${data.current_values.milky_percentage}%`;
            }
            if (currentAmber) {
                currentAmber.textContent = `${data.current_values.amber_percentage}%`;
            }
            if (currentClear) {
                currentClear.textContent = `${data.current_values.clear_percentage}%`;
            }
        }
        
        // Durchschnittswerte
        const avgMilky = this.getElementById('avgMilky');
        const avgAmber = this.getElementById('avgAmber');
        const avgClear = this.getElementById('avgClear');
        
        if (data.averages) {
            if (avgMilky) {
                avgMilky.textContent = `${data.averages.milky_percentage}%`;
            }
            if (avgAmber) {
                avgAmber.textContent = `${data.averages.amber_percentage}%`;
            }
            if (avgClear) {
                avgClear.textContent = `${data.averages.clear_percentage}%`;
            }
        }
        // Status-Text im Header aktualisieren
        const statusText = this.getElementById('trichomeStatusText');
        if (statusText && data.current_values) {
            const klar = data.current_values.clear_percentage || 0;
            const milchig = data.current_values.milky_percentage || 0;
            const bernstein = data.current_values.amber_percentage || 0;
            statusText.textContent = `Klar: ${klar}%, Milchig: ${milchig}%, Bernstein: ${bernstein}%`;
        } else if (statusText) {
            statusText.textContent = "Keine Trichom-Daten vorhanden";
        }
        // Flush-Fortschritt aktualisieren
        const flushProgressText = this.getElementById('flushProgressText');
        const flushProgressCircle = this.getElementById('flushProgressCircle');
        if (flushProgressText && data.progress && data.progress.flush_progress !== undefined) {
            const percent = Math.round(data.progress.flush_progress);
            flushProgressText.textContent = `${percent}%`;
            // SVG-Kreis animieren
            if (flushProgressCircle) {
                const radius = 15;
                const circumference = 2 * Math.PI * radius;
                const offset = circumference - (percent / 100) * circumference;
                flushProgressCircle.setAttribute('stroke-dasharray', circumference);
                flushProgressCircle.setAttribute('stroke-dashoffset', offset);
            }
        }
        // ... bestehender Code ...
        console.log('🌺 Übergabe an updateTrichomeSegments:', data.current_values);
        this.updateTrichomeSegments(data);
    }

    // Neue Methode zum Rendern des Beobachtungslogs
    updateObservationList(observations) {
        
        const listEl = this.getElementById('observationList');
        if (!listEl) return;
        listEl.innerHTML = '';
        if (!observations || observations.length === 0) {
            listEl.innerHTML = '<div class="observation-placeholder"><span class="placeholder-text">Keine Beobachtungen verfügbar</span></div>';
            return;
        }
        observations.sort((a, b) => new Date(b.date) - new Date(a.date));
        observations.forEach((obs, index) => {
            const item = document.createElement('div');
            item.className = 'observation-item';
            item.setAttribute('data-observation-index', index);
            const date = new Date(obs.date);
            const formattedDate = date.toLocaleDateString('de-DE');
            let timeDisplay = '';
            if (obs.created_at) {
                const createdDate = new Date(obs.created_at);
                if (!isNaN(createdDate.getTime())) {
                    timeDisplay = createdDate.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' });
                }
            }
            item.innerHTML = `
                <div class="observation-header">
                    <span class="observation-date">${formattedDate}${timeDisplay ? ` ${timeDisplay}` : ''}</span>
                    <span class="observation-day">Blüte-Tag ${obs.bloom_day}</span>
                </div>
                <div class="observation-values">
                    <span class="value clear">Klar: ${obs.clear_percentage}%</span>
                    <span class="value milky">Milchig: ${obs.milky_percentage}%</span>
                    <span class="value amber">Bernstein: ${obs.amber_percentage}%</span>
                </div>
                ${obs.location ? `<div class="observation-location">Ort: ${obs.location}</div>` : ''}
                ${obs.notes ? `<div class="observation-notes">Notizen: ${obs.notes}</div>` : ''}
                <div class="observation-actions">
                    <button class="btn-edit" data-index="${index}">Bearbeiten</button>
                    <button class="btn-delete" data-index="${index}">Löschen</button>
                </div>
            `;
            const editBtn = item.querySelector('.btn-edit');
            const deleteBtn = item.querySelector('.btn-delete');
            editBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.editObservation(index);
            });
            deleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.deleteObservation(index);
            });
            listEl.appendChild(item);
        });
        // Nach dem Rendern: Hervorhebung setzen
        if (this.currentEditIndex !== null) {
            const item = document.querySelector(`.observation-item[data-observation-index="${this.currentEditIndex}"]`);
            if (item) item.classList.add('is-editing');
        }
        if (this.currentDeleteIndex !== null) {
            const item = document.querySelector(`.observation-item[data-observation-index="${this.currentDeleteIndex}"]`);
            if (item) item.classList.add('is-deleting');
        }
    }

    // Hilfsmethoden für das zentrale Formular-Rendering
    showEditForm(index) {
        // Alle anderen Formulare schließen
        this.hideAllForms();
        
        this.currentEditIndex = index;
        this.currentDeleteIndex = null;
        this.clearObservationIndicators();
        const container = document.getElementById('observationActionFormContainer');
        if (!container) return;
        
        // Markiere den aktuellen Eintrag
        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-editing');
        }
        
        // Container leeren (ohne hideActionForm zu verwenden)
        container.innerHTML = '';
        const currentObservations = this.getCurrentObservations();
        if (!currentObservations || !currentObservations[index]) return;
        const obs = currentObservations[index];
        // Formular-HTML erzeugen
        container.innerHTML = `
            <div class="edit-form">
                <div class="form-card compact">
                    <h5>✏️ Beobachtung bearbeiten</h5>
                    <form id="editObservationForm">
                        <div class="form-row compact">
                            <div class="form-group">
                                <label>Datum:</label>
                                <input type="date" name="date" value="${obs.date}" required>
                            </div>
                            <div class="form-group">
                                <label>Blüte-Tag:</label>
                                <input type="number" name="bloom_day" min="1" max="100" value="${obs.bloom_day}" required>
                            </div>
                        </div>
                        
                        <!-- Location als Radio-Buttons -->
                        <div class="form-group">
                            <label>Ort:</label>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Top Buds" ${obs.location === 'Top Buds' ? 'checked' : ''} required>
                                    <span class="radio-label">Top Buds</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Side Buds" ${obs.location === 'Side Buds' ? 'checked' : ''}>
                                    <span class="radio-label">Side Buds</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Lower Buds" ${obs.location === 'Lower Buds' ? 'checked' : ''}>
                                    <span class="radio-label">Lower Buds</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Sugar Leaves" ${obs.location === 'Sugar Leaves' ? 'checked' : ''}>
                                    <span class="radio-label">Sugar Leaves</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Fan Leaves" ${obs.location === 'Fan Leaves' ? 'checked' : ''}>
                                    <span class="radio-label">Fan Leaves</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Mixed" ${obs.location === 'Mixed' ? 'checked' : ''}>
                                    <span class="radio-label">Mixed</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Trichom-Werte als Schieberegler -->
                        <div class="form-group">
                            <label>Trichom-Verteilung:</label>
                            <div class="slider-group">
                                <div class="slider-item">
                                    <label for="editClearPercentage">Klar: <span id="editClearPercentageDisplay">${obs.clear_percentage}</span>%</label>
                                    <input type="range" id="editClearPercentage" name="clear_percentage" min="0" max="100" value="${obs.clear_percentage}" class="trichome-slider clear-slider">
                                </div>
                                <div class="slider-item">
                                    <label for="editMilkyPercentage">Milchig: <span id="editMilkyPercentageDisplay">${obs.milky_percentage}</span>%</label>
                                    <input type="range" id="editMilkyPercentage" name="milky_percentage" min="0" max="100" value="${obs.milky_percentage}" class="trichome-slider milky-slider">
                                </div>
                                <div class="slider-item">
                                    <label for="editAmberPercentage">Bernstein: <span id="editAmberPercentageDisplay">${obs.amber_percentage}</span>%</label>
                                    <input type="range" id="editAmberPercentage" name="amber_percentage" min="0" max="100" value="${obs.amber_percentage}" class="trichome-slider amber-slider">
                                </div>
                            </div>
                            
                            <!-- Live-Summenanzeige -->
                            <div class="percentage-summary">
                                <div class="sum-display" id="editSumDisplay">
                                    <span class="sum-label">Summe:</span>
                                    <span class="sum-value" id="editSumValue">${obs.clear_percentage + obs.milky_percentage + obs.amber_percentage}%</span>
                                </div>
                                <div class="sum-warning" id="editSumWarning" style="display: none;">
                                    <span class="warning-icon">⚠️</span>
                                    <span class="warning-text">Summe über 100%!</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Notizen:</label>
                            <textarea name="notes" rows="2">${obs.notes || ''}</textarea>
                        </div>
                        <div class="form-actions compact">
                            <button type="submit" class="btn btn-primary btn-sm">Aktualisieren</button>
                            <button type="button" class="btn btn-secondary btn-sm btn-cancel">Abbrechen</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        // Event-Listener für das Formular
        const form = document.getElementById('editObservationForm');
        if (form) {
            form.onsubmit = (e) => {
                e.preventDefault();
                this.submitEditObservation(index, form);
            };
        }
        const cancelBtn = container.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideActionForm();
        }
        
        // Schieberegler Event-Listener für das Bearbeiten-Formular
        this.setupEditSliderListeners();
    }

    showDeleteForm(index) {
        // Alle anderen Formulare schließen
        this.hideAllForms();
        
        this.currentDeleteIndex = index;
        this.currentEditIndex = null;
        this.clearObservationIndicators();
        const container = document.getElementById('observationActionFormContainer');
        if (!container) return;
        
        // Markiere den aktuellen Eintrag
        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-deleting');
        }
        
        // Container leeren (ohne hideActionForm zu verwenden)
        container.innerHTML = '';
        const currentObservations = this.getCurrentObservations();
        if (!currentObservations || !currentObservations[index]) return;
        const obs = currentObservations[index];
        const date = new Date(obs.date);
        const formattedDate = date.toLocaleDateString('de-DE');
        container.innerHTML = `
            <div class="delete-form">
                <div class="form-card compact delete-confirmation">
                    <h5>🗑️ Beobachtung löschen</h5>
                    <div class="confirm-text">
                        Möchtest du die Beobachtung vom ${formattedDate} (Blüte-Tag ${obs.bloom_day}) wirklich löschen?
                    </div>
                    <div class="form-actions compact">
                        <button type="button" class="btn btn-danger btn-sm btn-delete-confirm">Löschen</button>
                        <button type="button" class="btn btn-secondary btn-sm btn-cancel">Abbrechen</button>
                    </div>
                </div>
            </div>
        `;
        const deleteBtn = container.querySelector('.btn-delete-confirm');
        if (deleteBtn) {
            deleteBtn.onclick = () => this.performDelete(index);
        }
        const cancelBtn = container.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideActionForm();
        }
    }

    hideActionForm() {
        // Verwende die zentrale Methode
        this.hideAllForms();
    }

    clearObservationIndicators() {
        document.querySelectorAll('.observation-item.is-editing').forEach(el => el.classList.remove('is-editing'));
        document.querySelectorAll('.observation-item.is-deleting').forEach(el => el.classList.remove('is-deleting'));
    }

    // editObservation und deleteObservation anpassen:
    editObservation(index) {
        this.showEditForm(index);
    }
    
    deleteObservation(index) {
        this.showDeleteForm(index);
    }
    
    // Fehlende performDelete-Funktion hinzufügen
    async performDelete(index) {
        
        // Hole die aktuelle Beobachtung, um die ID zu bekommen
        const observations = this.getCurrentObservations();
        if (!observations || !observations[index]) {
            this.showError('Beobachtung nicht gefunden');
            return;
        }
        
        const observation = observations[index];
        const observationId = observation.id;
        
        try {
            const response = await fetch(`/flowering/trichome-observation/${this.currentPlantId}?id=${observationId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showSuccess('Beobachtung erfolgreich gelöscht');
                
                // Formular verstecken
                this.hideActionForm();
                
                // Alle Trichom-Daten vollständig neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
                
                // Zusätzlich: UI sofort aktualisieren, falls keine Beobachtungen mehr vorhanden sind
                const currentObservations = this.getCurrentObservations();
                if (!currentObservations || currentObservations.length === 0) {
                    this.updateObservationList([]);
                }
            } else {
                const errorData = await response.json();
                
                // Spezielle Behandlung für "nicht gefunden" Fehler
                if (errorData.error && errorData.error.includes('nicht gefunden')) {
                    this.showSuccess('Beobachtung wurde bereits gelöscht');
                    this.hideActionForm();
                    
                    // UI sofort aktualisieren
                    await Promise.all([
                        this.loadTrichomeStatus(),
                        this.loadTrichomeTrigger(),
                        this.loadTrichomeRecommendation(),
                        this.loadTrichomeProgress()
                    ]);
                } else {
                    this.showError(`Fehler beim Löschen: ${errorData.error || 'Unbekannter Fehler'}`);
                }
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Netzwerkfehler beim Löschen:', error);
            this.showError('Netzwerkfehler beim Löschen der Beobachtung');
        }
    }

    // Methode zum Bearbeiten einer Beobachtung
    async submitEditObservation(index, form) {
        // Formulardaten sammeln
        const formData = new FormData(form);
        
        const observationData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            location: formData.get('location'),
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };
        
        // Flexiblere Validierung für Trichom-Schätzungen
        const total = observationData.clear_percentage + observationData.milky_percentage + observationData.amber_percentage;
        if (total < 90 || total > 110) {
            alert(`Die Summe der Prozentwerte sollte zwischen 90-110% liegen (aktuell: ${total}%). Trichom-Analysen sind oft Schätzungen!`);
            return;
        }
        
        try {
            // Hole die aktuelle Beobachtung, um die ID zu bekommen
            const observations = this.getCurrentObservations();
            if (!observations || !observations[index]) {
                this.showError('Beobachtung nicht gefunden');
                return;
            }
            
            const observation = observations[index];
            const observationId = observation.id;
            
            // ID zur Beobachtung hinzufügen
            observationData.id = observationId;
            
            // Backend-Aufruf zum Bearbeiten
            const response = await fetch(`/flowering/trichome-observation/${this.currentPlantId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(observationData)
            });
            
            if (response.ok) {
                this.showSuccess('Beobachtung erfolgreich aktualisiert');
                
                // Alle Formulare verstecken (zentrale Methode)
                this.hideAllForms();
                
                // Alle Trichom-Daten vollständig neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
            } else {
                this.showError('Fehler beim Bearbeiten der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Netzwerkfehler beim Bearbeiten:', error);
            this.showError('Netzwerkfehler beim Bearbeiten der Beobachtung');
        }
    }

    // Hilfsmethode zum Abrufen der aktuellen Beobachtungen
    getCurrentObservations() {
        // Versuche die Beobachtungen aus verschiedenen möglichen Strukturen zu holen
        if (this.floweringData && this.floweringData.trichome_status && this.floweringData.trichome_status.observations) {
            return this.floweringData.trichome_status.observations;
        }
        
        // Alternative: Direkt aus floweringData.observations
        if (this.floweringData && this.floweringData.observations) {
            return this.floweringData.observations;
        }
        
        // Neue Alternative: Aus den geladenen Trichom-Daten
        if (this.trichomeData && this.trichomeData.observations) {
            return this.trichomeData.observations;
        }
        
        // Fallback: Aus der globalen Variable (falls vorhanden)
        if (typeof SAVED_TRICHOME_OBSERVATIONS !== 'undefined' && SAVED_TRICHOME_OBSERVATIONS[this.currentPlantId]) {
            return SAVED_TRICHOME_OBSERVATIONS[this.currentPlantId];
        }
        
        return [];
    }

    // Event-Listener für den "Neuer Eintrag" Button
    setupObservationForm() {
        const addBtn = document.getElementById('addObservationBtn');
        const formContainer = document.getElementById('observationFormContainer');
        const cancelBtn = document.getElementById('cancelObservationBtn');
        const form = document.getElementById('trichomeObservationForm');
        
        if (addBtn) {
            addBtn.addEventListener('click', () => {
                // Formular anzeigen und zurücksetzen
                this.showObservationForm();
            });
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideObservationForm();
            });
        }
        
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitObservation();
            });
        }
        
        // Schieberegler Event-Listener für Live-Updates
        this.setupSliderListeners();
    }
    
    setupSliderListeners() {
        const clearSlider = document.getElementById('clearPercentage');
        const milkySlider = document.getElementById('milkyPercentage');
        const amberSlider = document.getElementById('amberPercentage');
        
        const clearDisplay = document.getElementById('clearPercentageDisplay');
        const milkyDisplay = document.getElementById('milkyPercentageDisplay');
        const amberDisplay = document.getElementById('amberPercentageDisplay');
        
        const sumValue = document.getElementById('sumValue');
        const sumWarning = document.getElementById('sumWarning');
        
        const updateSum = () => {
            const clear = parseInt(clearSlider.value) || 0;
            const milky = parseInt(milkySlider.value) || 0;
            const amber = parseInt(amberSlider.value) || 0;
            
            const total = clear + milky + amber;
            
            // Display-Werte aktualisieren
            if (clearDisplay) clearDisplay.textContent = clear;
            if (milkyDisplay) milkyDisplay.textContent = milky;
            if (amberDisplay) amberDisplay.textContent = amber;
            
            // Summe anzeigen
            if (sumValue) {
                sumValue.textContent = total + '%';
                
                // Farbe basierend auf Summe
                if (total > 100) {
                    sumValue.style.color = '#dc2626'; // Rot
                } else if (total === 100) {
                    sumValue.style.color = '#059669'; // Grün
                } else {
                    sumValue.style.color = '#6b7280'; // Grau
                }
            }
            
            // Warnung anzeigen/verstecken
            if (sumWarning) {
                if (total > 100) {
                    sumWarning.style.display = 'flex';
                } else {
                    sumWarning.style.display = 'none';
                }
            }
        };
        
        // Event-Listener für alle Schieberegler
        [clearSlider, milkySlider, amberSlider].forEach(slider => {
            if (slider) {
                slider.addEventListener('input', updateSum);
            }
        });
        
        // Initiale Summe berechnen
        updateSum();
    }
    
    setupEditSliderListeners() {
        const clearSlider = document.getElementById('editClearPercentage');
        const milkySlider = document.getElementById('editMilkyPercentage');
        const amberSlider = document.getElementById('editAmberPercentage');
        
        const clearDisplay = document.getElementById('editClearPercentageDisplay');
        const milkyDisplay = document.getElementById('editMilkyPercentageDisplay');
        const amberDisplay = document.getElementById('editAmberPercentageDisplay');
        
        const sumValue = document.getElementById('editSumValue');
        const sumWarning = document.getElementById('editSumWarning');
        
        const updateSum = () => {
            const clear = parseInt(clearSlider.value) || 0;
            const milky = parseInt(milkySlider.value) || 0;
            const amber = parseInt(amberSlider.value) || 0;
            
            const total = clear + milky + amber;
            
            // Display-Werte aktualisieren
            if (clearDisplay) clearDisplay.textContent = clear;
            if (milkyDisplay) milkyDisplay.textContent = milky;
            if (amberDisplay) amberDisplay.textContent = amber;
            
            // Summe anzeigen
            if (sumValue) {
                sumValue.textContent = total + '%';
                
                // Farbe basierend auf Summe
                if (total > 100) {
                    sumValue.style.color = '#dc2626'; // Rot
                } else if (total === 100) {
                    sumValue.style.color = '#059669'; // Grün
                } else {
                    sumValue.style.color = '#6b7280'; // Grau
                }
            }
            
            // Warnung anzeigen/verstecken
            if (sumWarning) {
                if (total > 100) {
                    sumWarning.style.display = 'flex';
                } else {
                    sumWarning.style.display = 'none';
                }
            }
        };
        
        // Event-Listener für alle Schieberegler
        [clearSlider, milkySlider, amberSlider].forEach(slider => {
            if (slider) {
                slider.addEventListener('input', updateSum);
            }
        });
        
        // Initiale Summe berechnen
        updateSum();
    }

    // Formular zurücksetzen und mit aktuellen Daten füllen
    resetObservationForm() {
        console.log('🌺 FloweringWidget: Setze Formular zurück');
        
        const form = document.getElementById('trichomeObservationForm');
        if (form) {
            form.reset();
        }
        
        // Aktuelles Datum setzen
        const dateInput = document.getElementById('observationDate');
        if (dateInput) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
        
        // Aktuellen Blüte-Tag setzen
        const bloomDayInput = document.getElementById('observationBloomDay');
        if (bloomDayInput && this.floweringData) {
            bloomDayInput.value = this.floweringData.flowering_status.current_day || 41;
        }
        
        // Prozentwerte auf 0 setzen (Schieberegler)
        const clearInput = document.getElementById('clearPercentage');
        const milkyInput = document.getElementById('milkyPercentage');
        const amberInput = document.getElementById('amberPercentage');
        
        if (clearInput) clearInput.value = 0;
        if (milkyInput) milkyInput.value = 0;
        if (amberInput) amberInput.value = 0;
        
        // Display-Werte zurücksetzen
        const clearDisplay = document.getElementById('clearPercentageDisplay');
        const milkyDisplay = document.getElementById('milkyPercentageDisplay');
        const amberDisplay = document.getElementById('amberPercentageDisplay');
        
        if (clearDisplay) clearDisplay.textContent = '0';
        if (milkyDisplay) milkyDisplay.textContent = '0';
        if (amberDisplay) amberDisplay.textContent = '0';
        
        // Summe zurücksetzen
        const sumValue = document.getElementById('sumValue');
        const sumWarning = document.getElementById('sumWarning');
        
        if (sumValue) {
            sumValue.textContent = '0%';
            sumValue.style.color = '#6b7280';
        }
        if (sumWarning) {
            sumWarning.style.display = 'none';
        }
        
        // Notizen leeren
        const notesInput = document.getElementById('observationNotes');
        if (notesInput) notesInput.value = '';
    }

    // Beobachtung absenden
    async submitObservation() {
        console.log('🌺 FloweringWidget: Sende Beobachtung...');
        
        // Formulardaten sammeln
        const formData = new FormData(document.getElementById('trichomeObservationForm'));
        
        const observationData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            location: formData.get('location'), // Aus dem Select-Feld lesen
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };
        
        console.log('🌺 FloweringWidget: Beobachtungsdaten:', observationData);
        
        try {
            const response = await fetch(`/flowering/trichome-observation/${this.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(observationData)
            });
            
            if (response.ok) {
                console.log('🌺 FloweringWidget: Beobachtung erfolgreich gespeichert');
                
                // Formular zurücksetzen und verstecken
                this.hideObservationForm();
                
                // Alle Trichom-Daten vollständig neu laden
                console.log('🌺 FloweringWidget: Lade alle Trichom-Daten neu nach Speichern...');
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
                
                // Erfolgsmeldung
                this.showSuccess('Beobachtung erfolgreich gespeichert!');
                
            } else {
                console.error('🌺 FloweringWidget: Fehler beim Speichern der Beobachtung');
                const errorData = await response.json();
                this.showError('Fehler beim Speichern: ' + (errorData.error || 'Unbekannter Fehler'));
            }
            
        } catch (error) {
            console.error('🌺 FloweringWidget: Netzwerkfehler beim Speichern:', error);
            this.showError('Netzwerkfehler beim Speichern der Beobachtung');
        }
    }

    // Zentrale Methode zum Schließen aller Formulare
    hideAllForms() {
        console.log('🌺 FloweringWidget: Schließe alle Formulare');
        
        // Neues Eintrag-Formular schließen
        const newFormContainer = document.getElementById('observationFormContainer');
        if (newFormContainer) {
            newFormContainer.style.display = 'none';
        }
        
        // Bearbeiten/Löschen-Formular schließen
        const actionFormContainer = document.getElementById('observationActionFormContainer');
        if (actionFormContainer) {
            actionFormContainer.innerHTML = '';
        }
        
        // Alle Hervorhebungen entfernen
        this.clearObservationIndicators();
        
        // Indizes zurücksetzen
        this.currentEditIndex = null;
        this.currentDeleteIndex = null;
    }

    // Formular anzeigen und zurücksetzen
    showObservationForm() {
        console.log('🌺 FloweringWidget: Zeige Trichom-Beobachtungsformular');
        
        // Alle anderen Formulare schließen
        this.hideAllForms();
        
        const formContainer = document.getElementById('observationFormContainer');
        if (formContainer) {
            formContainer.style.display = 'block';
        }
        
        // Formular zurücksetzen
        this.resetObservationForm();
    }

    // Formular verstecken
    hideObservationForm() {
        console.log('🌺 FloweringWidget: Verstecke Trichom-Beobachtungsformular');
        
        const formContainer = document.getElementById('observationFormContainer');
        if (formContainer) {
            formContainer.style.display = 'none';
        }
        
        // Formular zurücksetzen
        this.resetObservationForm();
    }

    // Hilfsmethoden für Bearbeiten-Formulare
    hideAllEditForms() {
        const editForms = document.querySelectorAll('.edit-form');
        editForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    hideEditForm(index) {
        const observationItem = document.querySelector(`[data-observation-index="${index}"]`);
        if (observationItem) {
            const editForm = observationItem.querySelector('.edit-form');
            if (editForm) {
                editForm.style.display = 'none';
            }
        }
    }

    // Hilfsmethoden für Löschen-Formulare
    hideAllDeleteForms() {
        const deleteForms = document.querySelectorAll('.delete-form');
        deleteForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    hideDeleteForm(index) {
        const observationItem = document.querySelector(`[data-observation-index="${index}"]`);
        if (observationItem) {
            const deleteForm = observationItem.querySelector('.delete-form');
            if (deleteForm) {
                deleteForm.style.display = 'none';
            }
        }
    }

    // Timeline Event-Formular Funktionen
    setupTimelineEventForm() {
        const addEventBtn = this.getElementById('addEventBtn');
        const closeEventForm = this.getElementById('closeEventForm');
        const cancelEventForm = this.getElementById('cancelEventForm');
        const eventForm = this.getElementById('eventForm');
        const eventFormContainer = this.getElementById('eventFormContainer');

        if (addEventBtn) {
            addEventBtn.addEventListener('click', () => {
                this.showTimelineEventForm();
            });
        }

        if (closeEventForm) {
            closeEventForm.addEventListener('click', () => {
                this.hideTimelineEventForm();
            });
        }

        if (cancelEventForm) {
            cancelEventForm.addEventListener('click', () => {
                this.hideTimelineEventForm();
            });
        }

        if (eventForm) {
            eventForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveTimelineEvent();
            });
        }

        // Event-Type Änderung behandeln
        const eventTypeSelect = this.getElementById('eventType');
        if (eventTypeSelect) {
            eventTypeSelect.addEventListener('change', () => {
                this.handleEventTypeChange();
            });
        }

        // Datum automatisch setzen
        this.setDefaultEventDate();
    }

    showTimelineEventForm() {
        const eventFormContainer = this.getElementById('eventFormContainer');
        if (eventFormContainer) {
            eventFormContainer.style.display = 'block';
            this.setDefaultEventDate();
            this.setDefaultBloomDay();
        }
    }

    hideTimelineEventForm() {
        const eventFormContainer = this.getElementById('eventFormContainer');
        if (eventFormContainer) {
            eventFormContainer.style.display = 'none';
            this.resetTimelineEventForm();
        }
    }

    setDefaultEventDate() {
        const eventDate = this.getElementById('eventDate');
        if (eventDate) {
            const today = new Date().toISOString().split('T')[0];
            eventDate.value = today;
        }
    }

    setDefaultBloomDay() {
        const eventBloomDay = this.getElementById('eventBloomDay');
        if (eventBloomDay && this.floweringData?.flowering_status?.current_day) {
            eventBloomDay.value = this.floweringData.flowering_status.current_day;
        }
    }

    handleEventTypeChange() {
        const eventTypeSelect = this.getElementById('eventType');
        const eventTitle = this.getElementById('eventTitle');
        const eventCategory = this.getElementById('eventCategory');
        const eventImportance = this.getElementById('eventImportance');

        if (!eventTypeSelect || !eventTitle) return;

        const selectedType = eventTypeSelect.value;
        
        // Automatische Vorschläge basierend auf Event-Typ
        const suggestions = {
            'stretch_end': {
                title: 'Stretch-Phase beendet',
                category: 'growth',
                importance: 'medium'
            },
            'trichome_milky': {
                title: 'Milchige Trichome beobachtet',
                category: 'maturity',
                importance: 'high'
            },
            'trichome_amber': {
                title: 'Bernstein Trichome beobachtet',
                category: 'maturity',
                importance: 'high'
            },
            'flush_start': {
                title: 'Flush gestartet',
                category: 'flush',
                importance: 'high'
            },
            'harvest_window': {
                title: 'Ernte-Fenster erreicht',
                category: 'harvest',
                importance: 'high'
            },
            'pistils_retracted': {
                title: 'Pistillen zurückgezogen',
                category: 'maturity',
                importance: 'medium'
            }
        };

        if (suggestions[selectedType]) {
            const suggestion = suggestions[selectedType];
            eventTitle.value = suggestion.title;
            if (eventCategory) eventCategory.value = suggestion.category;
            if (eventImportance) eventImportance.value = suggestion.importance;
        } else {
            eventTitle.value = '';
            if (eventCategory) eventCategory.value = '';
            if (eventImportance) eventImportance.value = '';
        }
    }

    resetTimelineEventForm() {
        const eventForm = this.getElementById('eventForm');
        if (eventForm) {
            eventForm.reset();
        }
    }

    async saveTimelineEvent() {
        const eventForm = this.getElementById('eventForm');
        if (!eventForm) return;

        const formData = new FormData(eventForm);
        const eventData = {
            plant_id: this.currentPlantId,
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            event_type: formData.get('event_type'),
            event_name: formData.get('event_name'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
            console.log('🌺 FloweringWidget: Speichere Timeline-Event:', eventData);
            
            const response = await fetch(`/flowering/marker/${this.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(eventData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('🌺 FloweringWidget: Timeline-Event gespeichert:', result);
                
                this.showSuccess('Event erfolgreich gespeichert!');
                this.hideTimelineEventForm();
                
                // Daten neu laden
                await this.loadMarkers();
                this.updateTimeline();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Speichern:', error);
                this.showError(`Fehler beim Speichern: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Speichern des Events:', error);
            this.showError('Fehler beim Speichern des Events');
        }
    }

    // Marker-Formular Funktionen
    setupMarkerForm() {
        const addMarkerBtn = this.getElementById('addMarkerBtn');
        const closeMarkerForm = this.getElementById('closeMarkerForm');
        const cancelMarkerForm = this.getElementById('cancelMarkerForm');
        const markerForm = this.getElementById('markerForm');
        const markerFormContainer = this.getElementById('markerFormContainer');

        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.showMarkerForm();
            });
        }

        if (closeMarkerForm) {
            closeMarkerForm.addEventListener('click', () => {
                this.hideMarkerForm();
            });
        }

        if (cancelMarkerForm) {
            cancelMarkerForm.addEventListener('click', () => {
                this.hideMarkerForm();
            });
        }

        if (markerForm) {
            markerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveMarker();
            });
        }

        // Marker-Type Änderung behandeln
        const markerTypeSelect = this.getElementById('markerType');
        if (markerTypeSelect) {
            markerTypeSelect.addEventListener('change', () => {
                this.handleMarkerTypeChange();
            });
        }

        // Datum automatisch setzen
        this.setDefaultMarkerDate();
    }

    showMarkerForm() {
        const markerFormContainer = this.getElementById('markerFormContainer');
        if (markerFormContainer) {
            markerFormContainer.style.display = 'block';
            this.setDefaultMarkerDate();
            this.setDefaultMarkerBloomDay();
        }
    }

    hideMarkerForm() {
        const markerFormContainer = this.getElementById('markerFormContainer');
        if (markerFormContainer) {
            markerFormContainer.style.display = 'none';
            this.resetMarkerForm();
        }
    }

    setDefaultMarkerDate() {
        const markerDate = this.getElementById('markerDate');
        if (markerDate) {
            const today = new Date().toISOString().split('T')[0];
            markerDate.value = today;
        }
    }

    setDefaultMarkerBloomDay() {
        const markerBloomDay = this.getElementById('markerBloomDay');
        if (markerBloomDay && this.floweringData?.flowering_status?.current_day) {
            markerBloomDay.value = this.floweringData.flowering_status.current_day;
        }
    }

    handleMarkerTypeChange() {
        const markerTypeSelect = this.getElementById('markerType');
        const markerTitle = this.getElementById('markerTitle');
        const markerCategory = this.getElementById('markerCategory');
        const markerImportance = this.getElementById('markerImportance');

        if (!markerTypeSelect || !markerTitle) return;

        const selectedType = markerTypeSelect.value;
        
        // Automatische Vorschläge basierend auf Event-Typ
        const suggestions = {
            'stretch_end': {
                title: 'Stretch-Phase beendet',
                category: 'growth',
                importance: 'medium'
            },
            'trichome_milky': {
                title: 'Milchige Trichome beobachtet',
                category: 'maturity',
                importance: 'high'
            },
            'trichome_amber': {
                title: 'Bernstein Trichome beobachtet',
                category: 'maturity',
                importance: 'high'
            },
            'flush_start': {
                title: 'Flush gestartet',
                category: 'flush',
                importance: 'high'
            },
            'harvest_window': {
                title: 'Ernte-Fenster erreicht',
                category: 'harvest',
                importance: 'high'
            },
            'pistils_retracted': {
                title: 'Pistillen zurückgezogen',
                category: 'maturity',
                importance: 'medium'
            }
        };

        if (suggestions[selectedType]) {
            const suggestion = suggestions[selectedType];
            markerTitle.value = suggestion.title;
            if (markerCategory) markerCategory.value = suggestion.category;
            if (markerImportance) markerImportance.value = suggestion.importance;
        } else {
            markerTitle.value = '';
            if (markerCategory) markerCategory.value = '';
            if (markerImportance) markerImportance.value = '';
        }
    }

    resetMarkerForm() {
        const markerForm = this.getElementById('markerForm');
        if (markerForm) {
            markerForm.reset();
        }
    }

    async saveMarker() {
        const markerForm = this.getElementById('markerForm');
        if (!markerForm) return;

        const formData = new FormData(markerForm);
        const markerData = {
            plant_id: this.currentPlantId,
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            event_type: formData.get('event_type'),
            event_name: formData.get('event_name'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
            console.log('🌺 FloweringWidget: Speichere Marker:', markerData);
            
            const response = await fetch(`/flowering/marker/${this.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(markerData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('🌺 FloweringWidget: Marker gespeichert:', result);
                
                this.showSuccess('Marker erfolgreich gespeichert!');
                this.hideMarkerForm();
                
                // Daten neu laden
                await this.loadMarkers();
                this.updateTimeline();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Speichern:', error);
                this.showError(`Fehler beim Speichern: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Speichern des Markers:', error);
            this.showError('Fehler beim Speichern des Markers');
        }
    }

    // Inline Marker-Bearbeitung und -Löschung
    showEditMarkerForm(markerId) {
        // Alle anderen Formulare schließen
        this.hideAllMarkerForms();
        
        const editForm = document.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'block';
        }
    }

    hideEditMarkerForm(markerId) {
        const editForm = document.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'none';
        }
    }

    showDeleteMarkerForm(markerId) {
        // Alle anderen Formulare schließen
        this.hideAllMarkerForms();
        
        const deleteForm = document.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'block';
        }
    }

    hideDeleteMarkerForm(markerId) {
        const deleteForm = document.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'none';
        }
    }

    hideAllMarkerForms() {
        // Alle Bearbeitungs- und Löschformulare schließen
        const editForms = document.querySelectorAll('.marker-edit-form');
        const deleteForms = document.querySelectorAll('.marker-delete-form');
        
        editForms.forEach(form => {
            form.style.display = 'none';
        });
        
        deleteForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    async submitEditMarker(event, markerId) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        // Finde den ursprünglichen Marker, um event_type zu beibehalten
        const originalMarker = this.markers.find(m => m.id == markerId);
        if (!originalMarker) {
            this.showError('Marker nicht gefunden');
            return;
        }
        
        const updateData = {
            event_type: originalMarker.event_type, // event_type beibehalten
            bloom_day: originalMarker.bloom_day, // bloom_day beibehalten
            event_name: formData.get('event_name'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
            console.log('🌺 FloweringWidget: Bearbeite Marker:', markerId, updateData);
            
            const response = await fetch(`/flowering/marker/${this.currentPlantId}/${markerId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('🌺 FloweringWidget: Marker bearbeitet:', result);
                
                this.showSuccess('Marker erfolgreich bearbeitet!');
                this.hideEditMarkerForm(markerId);
                
                // Daten neu laden
                await this.loadMarkers();
                this.updateTimeline();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Bearbeiten:', error);
                this.showError(`Fehler beim Bearbeiten: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Bearbeiten des Markers:', error);
            this.showError('Fehler beim Bearbeiten des Markers');
        }
    }

    async submitDeleteMarker(markerId) {
        try {
            console.log('🌺 FloweringWidget: Lösche Marker:', markerId);
            
            const response = await fetch(`/flowering/marker/${this.currentPlantId}/${markerId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const result = await response.json();
                console.log('🌺 FloweringWidget: Marker gelöscht:', result);
                
                this.showSuccess('Marker erfolgreich gelöscht!');
                this.hideDeleteMarkerForm(markerId);
                
                // Daten neu laden
                await this.loadMarkers();
                this.updateTimeline();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Löschen:', error);
                this.showError(`Fehler beim Löschen: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Löschen des Markers:', error);
            this.showError('Fehler beim Löschen des Markers');
        }
    }

    setupMarkerActionListeners() {
        // Event-Listener für Bearbeiten-Buttons
        document.querySelectorAll('.marker-btn.edit').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.showEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Löschen-Buttons
        document.querySelectorAll('.marker-btn.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.showDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Close-Buttons in Edit-Formularen
        document.querySelectorAll('.marker-edit-form .btn-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Close-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Abbrechen-Buttons in Edit-Formularen
        document.querySelectorAll('.marker-edit-form .btn-secondary').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Abbrechen-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-secondary').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Löschen-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-danger').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.submitDeleteMarker(markerId);
            });
        });

        // Event-Listener für Edit-Formulare
        document.querySelectorAll('.marker-edit-form-content').forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const markerId = e.target.getAttribute('data-marker-id');
                this.submitEditMarker(e, markerId);
            });
        });
    }
}
