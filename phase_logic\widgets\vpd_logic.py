"""
VPD Logic Module
Handles VPD (Vapor Pressure Deficit) calculations and recommendations
"""

import math
from typing import Dict, Any, List

class VPDLogic:
    """VPD calculation and optimization logic"""
    
    def __init__(self):
        self.vpd_ranges = {
            'germination': {'min': 0.4, 'max': 0.8, 'optimal': 0.6},
            'vegetative_early': {'min': 0.8, 'max': 1.2, 'optimal': 1.0},
            'vegetative_middle': {'min': 1.0, 'max': 1.4, 'optimal': 1.2},
            'vegetative_late': {'min': 1.2, 'max': 1.6, 'optimal': 1.4},
            'flowering_early': {'min': 1.0, 'max': 1.4, 'optimal': 1.2},
            'flowering_middle': {'min': 0.8, 'max': 1.2, 'optimal': 1.0},
            'flowering_late': {'min': 0.6, 'max': 1.0, 'optimal': 0.8},
            'flush': {'min': 0.4, 'max': 0.8, 'optimal': 0.6}
        }
        
        self.phase_descriptions = {
            'germination': 'Niedrige VPD für optimale Keimung',
            'vegetative_early': 'Moderate VPD für gesundes Wachstum',
            'vegetative_middle': 'Erhöhte VPD für kräftiges Wachstum',
            'vegetative_late': 'Hohe VPD für maximale Entwicklung',
            'flowering_early': 'Moderate VPD für Blütenbildung',
            'flowering_middle': 'Optimale VPD für Blütenentwicklung',
            'flowering_late': 'Niedrige VPD für Reifung',
            'flush': 'Minimale VPD für finale Reifung'
        }
        
        # Strain-spezifische Anpassungen
        self.strain_adjustments = {
            'autoflowering': {
                'vpd_reduction_factor': 0.9,  # 10% niedrigere VPD-Werte
                'temp_tolerance': 0.8,  # 20% geringere Temperaturtoleranz
                'humidity_tolerance': 1.1  # 10% höhere Luftfeuchtigkeitstoleranz
            },
            'photoperiodic': {
                'vpd_reduction_factor': 1.0,  # Keine Anpassung
                'temp_tolerance': 1.0,  # Normale Toleranz
                'humidity_tolerance': 1.0  # Normale Toleranz
            }
        }
    
    def calculate_saturation_vapor_pressure(self, temperature: float) -> float:
        """Calculate saturation vapor pressure at given temperature in hPa"""
        # Magnus formula for water vapor pressure (returns hPa)
        if temperature >= 0:
            return 6.112 * math.exp((17.67 * temperature) / (temperature + 243.5))
        else:
            return 6.112 * math.exp((22.46 * temperature) / (temperature + 272.62))
    
    def calculate_vpd(self, temperature: float, humidity: float, leaf_temperature: float = None) -> float:
        """Calculate VPD from temperature and humidity with optional leaf temperature"""
        # Use leaf temperature if provided, otherwise use air temperature
        calc_temp = leaf_temperature if leaf_temperature is not None else temperature
        
        # Convert humidity from percentage to decimal
        humidity_decimal = humidity / 100.0
        
        # Calculate saturation vapor pressure (in hPa)
        svp_hpa = self.calculate_saturation_vapor_pressure(calc_temp)
        
        # Calculate actual vapor pressure (in hPa)
        avp_hpa = svp_hpa * humidity_decimal
        
        # Calculate VPD (hPa)
        vpd_hpa = svp_hpa - avp_hpa
        
        # Convert hPa to kPa (1 kPa = 10 hPa)
        vpd_kpa = vpd_hpa / 10.0
        
        return round(vpd_kpa, 2)
    
    def perform_advanced_analysis(self, phase: str, temperature: float, humidity: float, 
                                strain_type: str = 'photoperiodic', leaf_temperature: float = None) -> Dict[str, Any]:
        """Perform advanced VPD analysis with strain-specific adjustments and recommendations"""
        
        # Strain-Anpassungen anwenden
        strain_adjustment = self.strain_adjustments.get(strain_type, self.strain_adjustments['photoperiodic'])
        
        # VPD berechnen
        vpd_value = self.calculate_vpd(temperature, humidity, leaf_temperature)
        
        # Zielbereiche mit Strain-Anpassung laden
        targets = self.get_target_ranges(phase, strain_type)
        
        # Erweiterte Analyse durchführen
        analysis = {
            'vpd': vpd_value,
            'target_range': targets['vpd'],
            'status': self.get_advanced_vpd_status(vpd_value, targets['vpd']),
            'strain_adjustment': strain_adjustment,
            'leaf_temp_offset': leaf_temperature - temperature if leaf_temperature else None,
            'recommendations': self.get_advanced_recommendations(vpd_value, temperature, humidity, targets),
            'warnings': self.get_vpd_warnings(vpd_value, targets['vpd']),
            'efficiency_score': self.calculate_vpd_efficiency(vpd_value, targets['vpd']),
            'trend_analysis': self.analyze_vpd_trend(vpd_value, targets['vpd'])
        }
        
        return analysis
    
    def get_target_ranges(self, phase: str, strain_type: str = 'photoperiodic') -> Dict[str, Any]:
        """Get VPD target ranges with strain-specific adjustments"""
        
        # Basis-Zielbereiche aus Guidelines laden
        guidelines = self.load_guidelines_from_json(phase)
        if not guidelines:
            # Fallback zu Standard-Bereichen
            base_ranges = self.vpd_ranges.get(phase, {'min': 0.8, 'max': 1.2, 'optimal': 1.0})
            targets = {
                'vpd': base_ranges,
                'temperature': {'min': 22, 'max': 28},
                'humidity': {'min': 50, 'max': 70}
            }
        else:
            targets = guidelines.get('targets', {})
        
        # Strain-Anpassungen anwenden
        strain_adjustment = self.strain_adjustments.get(strain_type, self.strain_adjustments['photoperiodic'])
        
        # VPD-Bereiche anpassen
        if 'vpd' in targets:
            vpd_range = targets['vpd']
            adjusted_vpd = {
                'min': vpd_range['min'] * strain_adjustment['vpd_reduction_factor'],
                'max': vpd_range['max'] * strain_adjustment['vpd_reduction_factor'],
                'optimal': vpd_range.get('optimal', (vpd_range['min'] + vpd_range['max']) / 2) * strain_adjustment['vpd_reduction_factor']
            }
            targets['vpd'] = adjusted_vpd
        
        return targets
    
    def get_advanced_vpd_status(self, vpd: float, target_range: Dict[str, float]) -> Dict[str, Any]:
        """Get advanced VPD status with detailed analysis"""
        
        min_vpd = target_range['min']
        max_vpd = target_range['max']
        optimal_vpd = target_range.get('optimal', (min_vpd + max_vpd) / 2)
        
        # Abweichung vom Optimum berechnen
        deviation = abs(vpd - optimal_vpd)
        deviation_percent = (deviation / optimal_vpd) * 100
        
        if vpd < min_vpd:
            status = 'low'
            severity = 'warning' if deviation_percent < 20 else 'danger'
            message = f'VPD zu niedrig ({vpd} kPa). Ziel: {min_vpd}-{max_vpd} kPa'
        elif vpd > max_vpd:
            status = 'high'
            severity = 'warning' if deviation_percent < 20 else 'danger'
            message = f'VPD zu hoch ({vpd} kPa). Ziel: {min_vpd}-{max_vpd} kPa'
        elif deviation_percent <= 5:
            status = 'optimal'
            severity = 'success'
            message = f'VPD optimal ({vpd} kPa)'
        else:
            status = 'good'
            severity = 'info'
            message = f'VPD im akzeptablen Bereich ({vpd} kPa)'
        
        return {
            'status': status,
            'severity': severity,
            'message': message,
            'deviation_percent': round(deviation_percent, 1),
            'optimal_value': optimal_vpd
        }
    
    def get_advanced_recommendations(self, vpd: float, temperature: float, humidity: float, 
                                   targets: Dict[str, Any]) -> List[str]:
        """Get advanced optimization recommendations"""
        
        recommendations = []
        target_vpd = targets['vpd']
        target_temp = targets.get('temperature', {'min': 22, 'max': 28})
        target_humidity = targets.get('humidity', {'min': 50, 'max': 70})
        
        # VPD-basierte Empfehlungen
        if vpd < target_vpd['min']:
            recommendations.extend([
                "Luftfeuchte reduzieren (z.B. Abluft erhöhen)",
                "Temperatur leicht erhöhen",
                "Umluft verbessern"
            ])
        elif vpd > target_vpd['max']:
            recommendations.extend([
                "Luftfeuchte erhöhen (z.B. Luftbefeuchter)",
                "Temperatur reduzieren",
                "Lichtleistung ggf. reduzieren"
            ])
        
        # Temperatur-basierte Empfehlungen
        if temperature < target_temp['min']:
            recommendations.append("Temperatur erhöhen (Heizung/Lichtleistung)")
        elif temperature > target_temp['max']:
            recommendations.append("Temperatur reduzieren (Kühlung/Lüftung)")
        
        # Luftfeuchte-basierte Empfehlungen
        if humidity < target_humidity['min']:
            recommendations.append("Luftfeuchte erhöhen")
        elif humidity > target_humidity['max']:
            recommendations.append("Luftfeuchte reduzieren (Entfeuchter)")
        
        return recommendations
    
    def get_vpd_warnings(self, vpd: float, target_range: Dict[str, float]) -> List[str]:
        """Get VPD-specific warnings for critical values"""
        
        warnings = []
        
        # Kritische Grenzwerte
        if vpd < 0.4:
            warnings.append("KRITISCH: VPD extrem niedrig - Schimmelgefahr!")
        elif vpd < 0.6:
            warnings.append("WARNUNG: VPD sehr niedrig - Transpiration reduziert")
        elif vpd > 1.6:
            warnings.append("KRITISCH: VPD extrem hoch - Blattschäden möglich!")
        elif vpd > 1.4:
            warnings.append("WARNUNG: VPD sehr hoch - Stress für Pflanzen")
        
        return warnings
    
    def calculate_vpd_efficiency(self, vpd: float, target_range: Dict[str, float]) -> float:
        """Calculate VPD efficiency score (0-100)"""
        
        optimal_vpd = target_range.get('optimal', (target_range['min'] + target_range['max']) / 2)
        deviation = abs(vpd - optimal_vpd)
        max_deviation = (target_range['max'] - target_range['min']) / 2
        
        if deviation <= max_deviation:
            efficiency = 100 - (deviation / max_deviation) * 50
        else:
            efficiency = max(0, 50 - (deviation - max_deviation) / max_deviation * 50)
        
        return round(efficiency, 1)
    
    def analyze_vpd_trend(self, current_vpd: float, target_range: Dict[str, float]) -> Dict[str, Any]:
        """Analyze VPD trend and provide predictions"""
        
        # Einfache Trend-Analyse (kann später mit historischen Daten erweitert werden)
        optimal_vpd = target_range.get('optimal', (target_range['min'] + target_range['max']) / 2)
        
        # Trend-Bewertung basierend auf aktueller Position
        if current_vpd < target_range['min']:
            trend = 'increasing_needed'
            trend_message = 'VPD sollte erhöht werden'
            confidence = 0.9
        elif current_vpd > target_range['max']:
            trend = 'decreasing_needed'
            trend_message = 'VPD sollte reduziert werden'
            confidence = 0.9
        elif abs(current_vpd - optimal_vpd) <= 0.1:
            trend = 'stable'
            trend_message = 'VPD ist stabil und optimal'
            confidence = 0.8
        else:
            trend = 'adjusting'
            trend_message = 'VPD sollte angepasst werden'
            confidence = 0.7
        
        return {
            'trend': trend,
            'message': trend_message,
            'confidence': confidence,
            'optimal_direction': 'increase' if current_vpd < optimal_vpd else 'decrease',
            'distance_to_optimal': round(abs(current_vpd - optimal_vpd), 2)
        }

    # NEU: Erweiterte VPD-Analyse mit Problemanalyse
    def perform_comprehensive_vpd_analysis(self, phase: str, temperature: float, humidity: float, 
                                         strain_type: str = 'photoperiodic', leaf_temperature: float = None,
                                         historical_data: List[Dict] = None) -> Dict[str, Any]:
        """Perform comprehensive VPD analysis with trend analysis, problem detection, and AI recommendations"""
        
        # Basis-VPD berechnen
        vpd_value = self.calculate_vpd(temperature, humidity, leaf_temperature)
        
        # Zielbereiche laden
        targets = self.get_target_ranges(phase, strain_type)
        
        # Erweiterte Analyse durchführen
        analysis = {
            'current': {
                'vpd': vpd_value,
                'temperature': temperature,
                'humidity': humidity,
                'leaf_temperature': leaf_temperature,
                'status': self.get_advanced_vpd_status(vpd_value, targets['vpd'])
            },
            'targets': targets,
            'trend_analysis': self.analyze_vpd_trend(vpd_value, targets['vpd']),
            'problem_analysis': self.analyze_vpd_problems(vpd_value, temperature, humidity, targets),
            'ai_recommendations': self.generate_ai_recommendations(vpd_value, temperature, humidity, targets, phase, strain_type),
            'efficiency_metrics': self.calculate_efficiency_metrics(vpd_value, targets['vpd']),
            'risk_assessment': self.assess_vpd_risks(vpd_value, targets['vpd'], phase),
            'optimization_score': self.calculate_optimization_score(vpd_value, temperature, humidity, targets),
            'historical_insights': self.analyze_historical_patterns(historical_data) if historical_data else None
        }
        
        return analysis

    # NEU: Problemanalyse für VPD
    def analyze_vpd_problems(self, vpd: float, temperature: float, humidity: float, 
                           targets: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze potential VPD problems and their causes"""
        
        problems = []
        severity = 'none'
        
        target_vpd = targets['vpd']
        target_temp = targets.get('temperature', {'min': 22, 'max': 28})
        target_humidity = targets.get('humidity', {'min': 50, 'max': 70})
        
        # VPD-Probleme analysieren
        if vpd < target_vpd['min']:
            problems.append({
                'type': 'low_vpd',
                'severity': 'high' if vpd < target_vpd['min'] * 0.8 else 'medium',
                'description': 'VPD ist zu niedrig',
                'causes': self.identify_low_vpd_causes(temperature, humidity, target_temp, target_humidity),
                'symptoms': ['Schimmelgefahr', 'Reduzierte Transpiration', 'Nährstoffaufnahme-Probleme'],
                'immediate_actions': ['Luftfeuchte reduzieren', 'Temperatur erhöhen', 'Umluft verbessern']
            })
            severity = 'high' if vpd < target_vpd['min'] * 0.8 else 'medium'
        
        elif vpd > target_vpd['max']:
            problems.append({
                'type': 'high_vpd',
                'severity': 'high' if vpd > target_vpd['max'] * 1.2 else 'medium',
                'description': 'VPD ist zu hoch',
                'causes': self.identify_high_vpd_causes(temperature, humidity, target_temp, target_humidity),
                'symptoms': ['Blattverbrennungen', 'Erhöhter Wasserverlust', 'Stress-Symptome'],
                'immediate_actions': ['Luftfeuchte erhöhen', 'Temperatur reduzieren', 'Lichtleistung anpassen']
            })
            severity = 'high' if vpd > target_vpd['max'] * 1.2 else 'medium'
        
        # Temperatur-Probleme
        if temperature < target_temp['min']:
            problems.append({
                'type': 'low_temperature',
                'severity': 'medium',
                'description': 'Temperatur ist zu niedrig',
                'causes': ['Heizung unzureichend', 'Lichtleistung zu niedrig', 'Abluft zu stark'],
                'symptoms': ['Verlangsamtes Wachstum', 'Niedrige VPD-Werte'],
                'immediate_actions': ['Temperatur erhöhen', 'Lichtleistung steigern']
            })
            severity = max(severity, 'medium')
        
        elif temperature > target_temp['max']:
            problems.append({
                'type': 'high_temperature',
                'severity': 'high',
                'description': 'Temperatur ist zu hoch',
                'causes': ['Heizung zu stark', 'Lichtleistung zu hoch', 'Abluft unzureichend'],
                'symptoms': ['Hitzestress', 'Hohe VPD-Werte', 'Blattschäden'],
                'immediate_actions': ['Temperatur reduzieren', 'Abluft erhöhen', 'Lichtleistung reduzieren']
            })
            severity = max(severity, 'high')
        
        return {
            'problems': problems,
            'overall_severity': severity,
            'problem_count': len(problems),
            'critical_issues': [p for p in problems if p['severity'] == 'high'],
            'recommendations': self.generate_problem_specific_recommendations(problems)
        }

    # NEU: Ursachen für niedrige VPD identifizieren
    def identify_low_vpd_causes(self, temperature: float, humidity: float, 
                               target_temp: Dict[str, float], target_humidity: Dict[str, float]) -> List[str]:
        """Identify causes for low VPD values"""
        causes = []
        
        if humidity > target_humidity['max']:
            causes.append('Luftfeuchte zu hoch')
        if temperature < target_temp['min']:
            causes.append('Temperatur zu niedrig')
        if humidity > 80:
            causes.append('Übermäßige Luftfeuchtigkeit')
        if temperature < 20:
            causes.append('Kritisch niedrige Temperatur')
        
        if not causes:
            causes.append('Kombination aus Temperatur und Luftfeuchte')
        
        return causes

    # NEU: Ursachen für hohe VPD identifizieren
    def identify_high_vpd_causes(self, temperature: float, humidity: float, 
                                target_temp: Dict[str, float], target_humidity: Dict[str, float]) -> List[str]:
        """Identify causes for high VPD values"""
        causes = []
        
        if humidity < target_humidity['min']:
            causes.append('Luftfeuchte zu niedrig')
        if temperature > target_temp['max']:
            causes.append('Temperatur zu hoch')
        if humidity < 40:
            causes.append('Kritisch niedrige Luftfeuchte')
        if temperature > 30:
            causes.append('Kritisch hohe Temperatur')
        
        if not causes:
            causes.append('Kombination aus Temperatur und Luftfeuchte')
        
        return causes

    # NEU: KI-basierte Optimierungsvorschläge
    def generate_ai_recommendations(self, vpd: float, temperature: float, humidity: float, 
                                  targets: Dict[str, Any], phase: str, strain_type: str) -> Dict[str, Any]:
        """Generate AI-based optimization recommendations"""
        
        target_vpd = targets['vpd']
        optimal_vpd = target_vpd.get('optimal', (target_vpd['min'] + target_vpd['max']) / 2)
        
        # Intelligente Anpassungsvorschläge
        vpd_diff = optimal_vpd - vpd
        temp_diff = targets.get('temperature', {}).get('optimal', 25) - temperature
        hum_diff = targets.get('humidity', {}).get('optimal', 60) - humidity
        
        recommendations = {
            'priority_actions': self.get_priority_actions(vpd_diff, temp_diff, hum_diff),
            'gradual_adjustments': self.get_gradual_adjustments(vpd_diff, temp_diff, hum_diff),
            'monitoring_focus': self.get_monitoring_focus(vpd, temperature, humidity, targets),
            'prevention_tips': self.get_prevention_tips(phase, strain_type),
            'efficiency_improvements': self.get_efficiency_improvements(vpd, temperature, humidity, targets)
        }
        
        return recommendations

    # NEU: Prioritäts-Aktionen
    def get_priority_actions(self, vpd_diff: float, temp_diff: float, hum_diff: float) -> List[str]:
        """Get priority actions based on current deviations"""
        actions = []
        
        # VPD hat höchste Priorität
        if abs(vpd_diff) > 0.3:
            if vpd_diff > 0:
                actions.append("🔴 KRITISCH: VPD erhöhen - Luftfeuchte reduzieren oder Temperatur erhöhen")
            else:
                actions.append("🔴 KRITISCH: VPD reduzieren - Luftfeuchte erhöhen oder Temperatur reduzieren")
        
        # Temperatur-Anpassungen
        if abs(temp_diff) > 3:
            if temp_diff > 0:
                actions.append("🟡 Temperatur erhöhen (Heizung/Lichtleistung)")
            else:
                actions.append("🟡 Temperatur reduzieren (Abluft/Lichtleistung)")
        
        # Luftfeuchte-Anpassungen
        if abs(hum_diff) > 10:
            if hum_diff > 0:
                actions.append("🟡 Luftfeuchte erhöhen (Luftbefeuchter)")
            else:
                actions.append("🟡 Luftfeuchte reduzieren (Abluft)")
        
        return actions

    # NEU: Graduelle Anpassungen
    def get_gradual_adjustments(self, vpd_diff: float, temp_diff: float, hum_diff: float) -> List[str]:
        """Get gradual adjustment recommendations"""
        adjustments = []
        
        if abs(vpd_diff) <= 0.3 and abs(vpd_diff) > 0.1:
            if vpd_diff > 0:
                adjustments.append("Luftfeuchte um 2-3% reduzieren")
            else:
                adjustments.append("Luftfeuchte um 2-3% erhöhen")
        
        if abs(temp_diff) <= 3 and abs(temp_diff) > 1:
            if temp_diff > 0:
                adjustments.append("Temperatur um 0.5-1°C erhöhen")
            else:
                adjustments.append("Temperatur um 0.5-1°C reduzieren")
        
        return adjustments

    # NEU: Monitoring-Fokus
    def get_monitoring_focus(self, vpd: float, temperature: float, humidity: float, 
                           targets: Dict[str, Any]) -> Dict[str, Any]:
        """Get monitoring focus areas"""
        target_vpd = targets['vpd']
        
        focus_areas = {
            'primary': [],
            'secondary': [],
            'frequency': 'normal'
        }
        
        # Primäre Überwachung
        if vpd < target_vpd['min'] * 0.8 or vpd > target_vpd['max'] * 1.2:
            focus_areas['primary'].append('VPD-Werte')
            focus_areas['frequency'] = 'high'
        elif vpd < target_vpd['min'] or vpd > target_vpd['max']:
            focus_areas['primary'].append('VPD-Werte')
        
        if temperature < 20 or temperature > 30:
            focus_areas['primary'].append('Temperatur')
            focus_areas['frequency'] = 'high'
        elif temperature < 22 or temperature > 28:
            focus_areas['secondary'].append('Temperatur')
        
        if humidity < 40 or humidity > 80:
            focus_areas['primary'].append('Luftfeuchte')
            focus_areas['frequency'] = 'high'
        elif humidity < 50 or humidity > 70:
            focus_areas['secondary'].append('Luftfeuchte')
        
        return focus_areas

    # NEU: Präventions-Tipps
    def get_prevention_tips(self, phase: str, strain_type: str) -> List[str]:
        """Get prevention tips based on phase and strain type"""
        tips = []
        
        if phase in ['germination', 'vegetative_early']:
            tips.extend([
                "Regelmäßige Luftfeuchte-Kontrolle",
                "Temperatur-Schwankungen vermeiden",
                "Gute Umluft sicherstellen"
            ])
        
        if phase in ['flowering_middle', 'flowering_late']:
            tips.extend([
                "VPD-Trends überwachen",
                "Blatttemperatur messen",
                "Schimmel-Prävention beachten"
            ])
        
        if strain_type == 'autoflowering':
            tips.extend([
                "Konservative VPD-Werte bevorzugen",
                "Plötzliche Änderungen vermeiden",
                "Höhere Luftfeuchte-Toleranz beachten"
            ])
        
        return tips

    # NEU: Effizienz-Verbesserungen
    def get_efficiency_improvements(self, vpd: float, temperature: float, humidity: float, 
                                  targets: Dict[str, Any]) -> List[str]:
        """Get efficiency improvement suggestions"""
        improvements = []
        
        target_vpd = targets['vpd']
        optimal_vpd = target_vpd.get('optimal', (target_vpd['min'] + target_vpd['max']) / 2)
        
        # Effizienz-basierte Vorschläge
        if abs(vpd - optimal_vpd) <= 0.1:
            improvements.append("✅ VPD ist optimal - Effizienz maximiert")
        else:
            improvements.append(f"🎯 VPD auf {optimal_vpd} kPa optimieren für maximale Effizienz")
        
        # Energie-Effizienz
        if temperature > 26:
            improvements.append("⚡ Temperatur reduzieren für bessere Energie-Effizienz")
        
        if humidity > 70:
            improvements.append("💧 Luftfeuchte reduzieren für bessere Transpiration")
        
        return improvements

    # NEU: Effizienz-Metriken
    def calculate_efficiency_metrics(self, vpd: float, target_range: Dict[str, float]) -> Dict[str, Any]:
        """Calculate VPD efficiency metrics"""
        optimal_vpd = target_range.get('optimal', (target_range['min'] + target_range['max']) / 2)
        
        # Effizienz-Score (0-100)
        deviation = abs(vpd - optimal_vpd)
        max_deviation = (target_range['max'] - target_range['min']) / 2
        efficiency_score = max(0, 100 - (deviation / max_deviation) * 100)
        
        # Stabilitäts-Score
        stability_score = 100 if abs(vpd - optimal_vpd) <= 0.1 else 80 if abs(vpd - optimal_vpd) <= 0.2 else 60
        
        return {
            'efficiency_score': round(efficiency_score, 1),
            'stability_score': stability_score,
            'optimality_score': round(100 - (deviation / optimal_vpd) * 100, 1),
            'deviation_from_optimal': round(deviation, 2),
            'efficiency_level': 'high' if efficiency_score >= 80 else 'medium' if efficiency_score >= 60 else 'low'
        }

    # NEU: Risiko-Bewertung
    def assess_vpd_risks(self, vpd: float, target_range: Dict[str, float], phase: str) -> Dict[str, Any]:
        """Assess VPD-related risks"""
        risks = []
        overall_risk = 'low'
        
        # Kritische Risiken
        if vpd < 0.3:
            risks.append({
                'type': 'critical_low_vpd',
                'severity': 'critical',
                'description': 'Kritisch niedriger VPD - Schimmelgefahr',
                'probability': 'high',
                'impact': 'severe'
            })
            overall_risk = 'critical'
        
        elif vpd > 2.0:
            risks.append({
                'type': 'critical_high_vpd',
                'severity': 'critical',
                'description': 'Kritisch hoher VPD - Pflanzenschäden',
                'probability': 'high',
                'impact': 'severe'
            })
            overall_risk = 'critical'
        
        # Moderate Risiken
        elif vpd < target_range['min']:
            risks.append({
                'type': 'low_vpd',
                'severity': 'moderate',
                'description': 'Niedriger VPD - Reduzierte Transpiration',
                'probability': 'medium',
                'impact': 'moderate'
            })
            overall_risk = max(overall_risk, 'moderate')
        
        elif vpd > target_range['max']:
            risks.append({
                'type': 'high_vpd',
                'severity': 'moderate',
                'description': 'Hoher VPD - Erhöhter Stress',
                'probability': 'medium',
                'impact': 'moderate'
            })
            overall_risk = max(overall_risk, 'moderate')
        
        return {
            'risks': risks,
            'overall_risk': overall_risk,
            'risk_count': len(risks),
            'critical_risks': [r for r in risks if r['severity'] == 'critical'],
            'recommendations': self.get_risk_mitigation_recommendations(risks)
        }

    # NEU: Risiko-Minderungs-Empfehlungen
    def get_risk_mitigation_recommendations(self, risks: List[Dict]) -> List[str]:
        """Get risk mitigation recommendations"""
        recommendations = []
        
        for risk in risks:
            if risk['type'] == 'critical_low_vpd':
                recommendations.extend([
                    "🔴 SOFORT: Luftfeuchte reduzieren",
                    "🔴 SOFORT: Temperatur erhöhen",
                    "🔴 SOFORT: Umluft verbessern"
                ])
            elif risk['type'] == 'critical_high_vpd':
                recommendations.extend([
                    "🔴 SOFORT: Luftfeuchte erhöhen",
                    "🔴 SOFORT: Temperatur reduzieren",
                    "🔴 SOFORT: Lichtleistung reduzieren"
                ])
            elif risk['type'] == 'low_vpd':
                recommendations.extend([
                    "🟡 Luftfeuchte um 5-10% reduzieren",
                    "🟡 Temperatur um 1-2°C erhöhen"
                ])
            elif risk['type'] == 'high_vpd':
                recommendations.extend([
                    "🟡 Luftfeuchte um 5-10% erhöhen",
                    "🟡 Temperatur um 1-2°C reduzieren"
                ])
        
        return list(set(recommendations))  # Duplikate entfernen

    # NEU: Optimierungs-Score
    def calculate_optimization_score(self, vpd: float, temperature: float, humidity: float, 
                                   targets: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall optimization score"""
        target_vpd = targets['vpd']
        target_temp = targets.get('temperature', {'min': 22, 'max': 28})
        target_humidity = targets.get('humidity', {'min': 50, 'max': 70})
        
        # Einzelne Scores berechnen
        vpd_score = self.calculate_parameter_score(vpd, target_vpd)
        temp_score = self.calculate_parameter_score(temperature, target_temp)
        hum_score = self.calculate_parameter_score(humidity, target_humidity)
        
        # Gesamt-Score (gewichtet)
        total_score = (vpd_score * 0.5 + temp_score * 0.3 + hum_score * 0.2)
        
        return {
            'total_score': round(total_score, 1),
            'vpd_score': round(vpd_score, 1),
            'temperature_score': round(temp_score, 1),
            'humidity_score': round(hum_score, 1),
            'grade': self.get_score_grade(total_score),
            'improvement_potential': round(100 - total_score, 1)
        }

    # NEU: Parameter-Score berechnen
    def calculate_parameter_score(self, value: float, target_range: Dict[str, float]) -> float:
        """Calculate score for a single parameter"""
        min_val = target_range['min']
        max_val = target_range['max']
        optimal = target_range.get('optimal', (min_val + max_val) / 2)
        
        if value < min_val or value > max_val:
            # Außerhalb des Bereichs
            deviation = min(abs(value - min_val), abs(value - max_val))
            max_deviation = (max_val - min_val) / 2
            score = max(0, 100 - (deviation / max_deviation) * 50)
        else:
            # Innerhalb des Bereichs
            deviation = abs(value - optimal)
            max_deviation = (max_val - min_val) / 2
            score = 100 - (deviation / max_deviation) * 20
        
        return max(0, min(100, score))

    # NEU: Score-Grade
    def get_score_grade(self, score: float) -> str:
        """Get grade based on score"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B'
        elif score >= 60:
            return 'C'
        elif score >= 50:
            return 'D'
        else:
            return 'F'

    # NEU: Historische Muster analysieren
    def analyze_historical_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analyze historical VPD patterns"""
        if not historical_data or len(historical_data) < 3:
            return {'available': False, 'message': 'Unzureichende historische Daten'}
        
        # Einfache Muster-Analyse
        vpd_values = [entry.get('vpd_value', 0) for entry in historical_data]
        temp_values = [entry.get('temperature', 0) for entry in historical_data]
        hum_values = [entry.get('humidity', 0) for entry in historical_data]
        
        # Trend-Analyse
        if len(vpd_values) >= 3:
            recent_trend = 'increasing' if vpd_values[-1] > vpd_values[-2] > vpd_values[-3] else \
                          'decreasing' if vpd_values[-1] < vpd_values[-2] < vpd_values[-3] else 'stable'
        else:
            recent_trend = 'unknown'
        
        # Stabilitäts-Analyse
        vpd_variance = self.calculate_variance(vpd_values)
        stability = 'high' if vpd_variance < 0.01 else 'medium' if vpd_variance < 0.05 else 'low'
        
        return {
            'available': True,
            'data_points': len(historical_data),
            'recent_trend': recent_trend,
            'stability': stability,
            'average_vpd': round(sum(vpd_values) / len(vpd_values), 2),
            'vpd_range': {'min': min(vpd_values), 'max': max(vpd_values)},
            'recommendations': self.get_historical_recommendations(vpd_values, temp_values, hum_values)
        }

    # NEU: Varianz berechnen
    def calculate_variance(self, values: List[float]) -> float:
        """Calculate variance of a list of values"""
        if len(values) < 2:
            return 0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance

    # NEU: Historische Empfehlungen
    def get_historical_recommendations(self, vpd_values: List[float], temp_values: List[float], 
                                     hum_values: List[float]) -> List[str]:
        """Get recommendations based on historical data"""
        recommendations = []
        
        # VPD-Trend-Empfehlungen
        if len(vpd_values) >= 3:
            recent_avg = sum(vpd_values[-3:]) / 3
            if recent_avg < 0.5:
                recommendations.append("📈 VPD-Trend zeigt zu niedrige Werte - Anpassung empfohlen")
            elif recent_avg > 1.5:
                recommendations.append("📉 VPD-Trend zeigt zu hohe Werte - Anpassung empfohlen")
        
        # Stabilitäts-Empfehlungen
        vpd_variance = self.calculate_variance(vpd_values)
        if vpd_variance > 0.05:
            recommendations.append("📊 VPD-Schwankungen reduzieren für bessere Stabilität")
        
        return recommendations

    # NEU: Problemspezifische Empfehlungen
    def generate_problem_specific_recommendations(self, problems: List[Dict]) -> List[str]:
        """Generate specific recommendations for identified problems"""
        recommendations = []
        
        for problem in problems:
            if problem['type'] == 'low_vpd':
                recommendations.extend([
                    "💧 Luftfeuchte um 5-10% reduzieren",
                    "🌡️ Temperatur um 1-2°C erhöhen",
                    "💨 Umluft verbessern"
                ])
            elif problem['type'] == 'high_vpd':
                recommendations.extend([
                    "💧 Luftfeuchte um 5-10% erhöhen",
                    "🌡️ Temperatur um 1-2°C reduzieren",
                    "💡 Lichtleistung ggf. reduzieren"
                ])
            elif problem['type'] == 'low_temperature':
                recommendations.extend([
                    "🌡️ Heizung erhöhen",
                    "💡 Lichtleistung steigern",
                    "❄️ Abluft reduzieren"
                ])
            elif problem['type'] == 'high_temperature':
                recommendations.extend([
                    "🌡️ Heizung reduzieren",
                    "❄️ Abluft erhöhen",
                    "💡 Lichtleistung reduzieren"
                ])
        
        return list(set(recommendations))  # Duplikate entfernen
    
    def get_vpd_status(self, vpd: float, phase: str) -> Dict[str, Any]:
        """Get VPD status and recommendations"""
        if phase not in self.vpd_ranges:
            return {
                'status': 'unknown',
                'message': 'Phase nicht bekannt',
                'severity': 'info'
            }
        
        range_data = self.vpd_ranges[phase]
        min_vpd = range_data['min']
        max_vpd = range_data['max']
        optimal_vpd = range_data['optimal']
        
        if vpd < min_vpd:
            return {
                'status': 'low',
                'message': f'VPD zu niedrig ({vpd} kPa). Ziel: {min_vpd}-{max_vpd} kPa',
                'severity': 'warning',
                'action': 'Luftfeuchte reduzieren oder Temperatur erhöhen'
            }
        elif vpd > max_vpd:
            return {
                'status': 'high',
                'message': f'VPD zu hoch ({vpd} kPa). Ziel: {min_vpd}-{max_vpd} kPa',
                'severity': 'warning',
                'action': 'Luftfeuchte erhöhen oder Temperatur reduzieren'
            }
        elif abs(vpd - optimal_vpd) <= 0.1:
            return {
                'status': 'optimal',
                'message': f'VPD optimal ({vpd} kPa)',
                'severity': 'success',
                'action': 'Aktuelle Bedingungen beibehalten'
            }
        else:
            return {
                'status': 'good',
                'message': f'VPD im akzeptablen Bereich ({vpd} kPa)',
                'severity': 'info',
                'action': 'Feine Anpassungen möglich'
            }
    
    def calculate_vpd_optimization(self, phase: str, temperature: float, humidity: float) -> Dict[str, Any]:
        """Calculate complete VPD optimization for a phase"""
        # Calculate current VPD
        current_vpd = self.calculate_vpd(temperature, humidity)
        
        # Get VPD status
        status = self.get_vpd_status(current_vpd, phase)
        
        # Get recommendations for the phase
        recommendations = self.get_phase_recommendations(phase)
        
        return {
            'phase': phase,
            'recommendations': recommendations,
            'current': {
                'temperature': temperature,
                'humidity': humidity,
                'vpd': current_vpd,
                'status': status
            }
        }
    
    def get_phase_recommendations(self, phase: str) -> Dict[str, Any]:
        """Get VPD recommendations for a specific phase"""
        if phase not in self.vpd_ranges:
            return {
                'range': 'Unbekannt',
                'optimal': 'Unbekannt',
                'description': 'Phase nicht unterstützt',
                'temperature_range': {'min': 20, 'max': 30},
                'humidity_range': {'min': 40, 'max': 70}
            }
        
        range_data = self.vpd_ranges[phase]
        
        # Calculate temperature and humidity ranges for optimal VPD
        optimal_vpd = range_data['optimal']
        temp_range = self.calculate_temperature_range_for_vpd(optimal_vpd)
        humidity_range = self.calculate_humidity_range_for_vpd(optimal_vpd, temp_range)
        
        return {
            'range': f"{range_data['min']}-{range_data['max']} kPa",
            'optimal': f"{optimal_vpd} kPa",
            'description': self.phase_descriptions[phase],
            'temperature_range': temp_range,
            'humidity_range': humidity_range
        }
    
    def calculate_temperature_range_for_vpd(self, target_vpd: float) -> Dict[str, float]:
        """Calculate temperature range for a target VPD"""
        # Simplified calculation - in practice this would be more complex
        base_temp = 25.0
        return {
            'min': max(18.0, base_temp - 3.0),
            'max': min(32.0, base_temp + 3.0)
        }
    
    def calculate_humidity_range_for_vpd(self, target_vpd: float, temp_range: Dict[str, float]) -> Dict[str, float]:
        """Calculate humidity range for a target VPD and temperature range"""
        # Simplified calculation
        base_humidity = 60.0
        return {
            'min': max(30.0, base_humidity - 15.0),
            'max': min(80.0, base_humidity + 15.0)
        }
    
    def get_available_phases(self) -> List[str]:
        """Get list of available phases for VPD optimization"""
        return list(self.vpd_ranges.keys())
    
    def get_friendly_phase_name(self, phase: str) -> str:
        """Get user-friendly phase name"""
        phase_names = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        }
        return phase_names.get(phase, phase.replace('_', ' '))

    def load_guidelines_from_json(self, phase: str) -> Dict[str, Any]:
        """Load VPD guidelines from JSON file for specific phase"""
        try:
            import json
            import os
            
            # Path to guidelines JSON file
            guidelines_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'static', 'data', 'vpd-guidelines.json'
            )
            
            if not os.path.exists(guidelines_path):
                return None
            
            with open(guidelines_path, 'r', encoding='utf-8') as f:
                guidelines = json.load(f)
            
            # Find phase-specific guidelines
            phases = guidelines.get('phases', [])
            for p in phases:
                if p.get('phase') == phase:
                    return p
            
            return None
            
        except Exception as e:
            print(f"Fehler beim Laden der VPD-Guidelines: {e}")
            return None

    def calculate_vpd_with_guidelines(self, phase: str, temperature: float, humidity: float, strain_type: str = 'photoperiodic', leaf_temperature: float = None) -> Dict[str, Any]:
        """Calculate VPD optimization using guidelines from JSON"""
        try:
            # Load guidelines for the phase
            phase_guideline = self.load_guidelines_from_json(phase)
            
            if not phase_guideline:
                # Fallback zu Default-Guidelines
                phase_guideline = {
                    'description': 'Keine spezifischen Empfehlungen für diese Phase vorhanden.',
                    'notes': ['Bitte orientiere dich an den allgemeinen VPD-Richtlinien.'],
                    'leaf_temp_offset': 1,
                    'targets': {
                        'vpd': {'min': 0.8, 'max': 1.2},
                        'temperature': {'min': 22, 'max': 28},
                        'humidity': {'min': 50, 'max': 70}
                    },
                    'grenzwerte': {}
                }
                guidelines_used = False
            else:
                guidelines_used = True
            
            # Get strain-specific adjustments
            strain_adjustment = self.strain_adjustments.get(strain_type, self.strain_adjustments['photoperiodic'])
            
            # Calculate current VPD (mit Blatt-Temp, falls vorhanden)
            if leaf_temperature is not None:
                current_vpd = self.calculate_vpd(temperature, humidity, leaf_temperature=leaf_temperature)
            else:
                current_vpd = self.calculate_vpd(temperature, humidity)
            
            # Get targets from guidelines
            targets = phase_guideline.get('targets', {})
            vpd_target = targets.get('vpd', {'min': 0.8, 'max': 1.2})
            temp_target = targets.get('temperature', {'min': 22, 'max': 28})
            humidity_target = targets.get('humidity', {'min': 50, 'max': 70})
            
            # Apply strain adjustments
            vpd_target = {
                'min': vpd_target['min'] * strain_adjustment['vpd_reduction_factor'],
                'max': vpd_target['max'] * strain_adjustment['vpd_reduction_factor']
            }
            
            # Get VPD status with guidelines
            vpd_status = self.get_vpd_status_with_guidelines(current_vpd, vpd_target, phase_guideline.get('grenzwerte', {}))
            
            # Calculate optimization recommendations
            optimization = self.calculate_optimization_recommendations(
                current_vpd, temperature, humidity, vpd_target, temp_target, humidity_target
            )
            
            # Empfehlungen für Kompatibilität immer bereitstellen (mit Rundung)
            vpd_min = phase_guideline['targets']['vpd']['min'] if 'targets' in phase_guideline and 'vpd' in phase_guideline['targets'] else 0.8
            vpd_max = phase_guideline['targets']['vpd']['max'] if 'targets' in phase_guideline and 'vpd' in phase_guideline['targets'] else 1.2
            vpd_opt = (vpd_min + vpd_max) / 2
            recommendations = {
                'range': f"{vpd_min:.2f}-{vpd_max:.2f} kPa",
                'optimal': f"{vpd_opt:.2f} kPa",
                'description': phase_guideline.get('description', ''),
                'temperature_range': phase_guideline['targets']['temperature'] if 'targets' in phase_guideline and 'temperature' in phase_guideline['targets'] else {'min': 22, 'max': 28},
                'humidity_range': phase_guideline['targets']['humidity'] if 'targets' in phase_guideline and 'humidity' in phase_guideline['targets'] else {'min': 50, 'max': 70}
            }
            
            return {
                'phase': phase,
                'strain_specific': True,
                'guidelines_used': guidelines_used,
                'targets': {
                    'vpd': vpd_target,
                    'temperature': temp_target,
                    'humidity': humidity_target
                },
                'guidelines': phase_guideline,
                'recommendations': recommendations,
                'current': {
                    'temperature': temperature,
                    'humidity': humidity,
                    'vpd': current_vpd,
                    'status': vpd_status,
                    'leaf_temperature': leaf_temperature
                },
                'optimization': optimization
            }
            
        except Exception as e:
            print(f"Fehler bei VPD-Berechnung mit Guidelines: {e}")
            # Fallback to basic calculation
            return self.calculate_vpd_optimization(phase, temperature, humidity)

    def get_vpd_status_with_guidelines(self, vpd: float, vpd_target: Dict[str, float], grenzwerte: Dict[str, Any]) -> Dict[str, Any]:
        """Get VPD status using guidelines and critical limits"""
        min_vpd = round(vpd_target['min'], 2)
        max_vpd = round(vpd_target['max'], 2)
        
        # Check critical limits first
        if grenzwerte:
            low_vpd = grenzwerte.get('lowVpd', {})
            high_vpd = grenzwerte.get('highVpd', {})
            
            if vpd < low_vpd.get('threshold', 0.4):
                return {
                    'status': 'critical_low',
                    'message': f'KRITISCH: VPD zu niedrig ({vpd} kPa)',
                    'severity': 'danger',
                    'action': 'Sofortige Maßnahmen erforderlich'
                }
            elif vpd > high_vpd.get('threshold', 1.6):
                return {
                    'status': 'critical_high',
                    'message': f'KRITISCH: VPD zu hoch ({vpd} kPa)',
                    'severity': 'danger',
                    'action': 'Sofortige Maßnahmen erforderlich'
                }
        
        # Regular status check
        if vpd < min_vpd:
            return {
                'status': 'low',
                'message': f'VPD zu niedrig ({vpd} kPa). Ziel: {min_vpd}-{max_vpd} kPa',
                'severity': 'warning',
                'action': 'Luftfeuchte reduzieren oder Temperatur erhöhen'
            }
        elif vpd > max_vpd:
            return {
                'status': 'high',
                'message': f'VPD zu hoch ({vpd} kPa). Ziel: {min_vpd}-{max_vpd} kPa',
                'severity': 'warning',
                'action': 'Luftfeuchte erhöhen oder Temperatur reduzieren'
            }
        else:
            return {
                'status': 'optimal',
                'message': f'VPD optimal ({vpd} kPa)',
                'severity': 'success',
                'action': 'Aktuelle Bedingungen beibehalten'
            }

    def calculate_optimization_recommendations(self, current_vpd: float, current_temp: float, current_humidity: float, 
                                             vpd_target: Dict[str, float], temp_target: Dict[str, float], 
                                             humidity_target: Dict[str, float]) -> Dict[str, Any]:
        """Calculate detailed optimization recommendations"""
        
        # Calculate differences
        vpd_diff = current_vpd - (vpd_target['min'] + vpd_target['max']) / 2
        temp_diff = current_temp - (temp_target['min'] + temp_target['max']) / 2
        humidity_diff = current_humidity - (humidity_target['min'] + humidity_target['max']) / 2
        
        return {
            'vpd_adjustment': {
                'action': self.get_vpd_adjustment_action(vpd_diff),
                'difference': round(vpd_diff, 2)
            },
            'temperature_adjustment': {
                'action': self.get_temperature_adjustment_action(temp_diff),
                'difference': round(temp_diff, 1)
            },
            'humidity_adjustment': {
                'action': self.get_humidity_adjustment_action(humidity_diff),
                'difference': round(humidity_diff, 0)
            }
        }

    def get_vpd_adjustment_action(self, vpd_diff: float) -> str:
        """Get VPD adjustment action based on difference"""
        if vpd_diff > 0.3:
            return "VPD deutlich reduzieren - Luftfeuchte erhöhen oder Temperatur senken"
        elif vpd_diff > 0.1:
            return "VPD leicht reduzieren - Feine Anpassungen vornehmen"
        elif vpd_diff < -0.3:
            return "VPD deutlich erhöhen - Luftfeuchte reduzieren oder Temperatur erhöhen"
        elif vpd_diff < -0.1:
            return "VPD leicht erhöhen - Feine Anpassungen vornehmen"
        else:
            return "VPD im optimalen Bereich - Keine Anpassungen nötig"

    def get_temperature_adjustment_action(self, temp_diff: float) -> str:
        """Get temperature adjustment action based on difference"""
        if temp_diff > 3:
            return "Temperatur deutlich reduzieren - Kühlung/Lüftung erhöhen"
        elif temp_diff > 1:
            return "Temperatur leicht reduzieren - Lüftung anpassen"
        elif temp_diff < -3:
            return "Temperatur deutlich erhöhen - Heizung/Lichtleistung erhöhen"
        elif temp_diff < -1:
            return "Temperatur leicht erhöhen - Heizung anpassen"
        else:
            return "Temperatur im optimalen Bereich - Keine Anpassungen nötig"

    def get_humidity_adjustment_action(self, humidity_diff: float) -> str:
        """Get humidity adjustment action based on difference"""
        if humidity_diff > 10:
            return "Luftfeuchte deutlich reduzieren - Entfeuchter/Abluft erhöhen"
        elif humidity_diff > 5:
            return "Luftfeuchte leicht reduzieren - Lüftung anpassen"
        elif humidity_diff < -10:
            return "Luftfeuchte deutlich erhöhen - Luftbefeuchter verwenden"
        elif humidity_diff < -5:
            return "Luftfeuchte leicht erhöhen - Befeuchtung anpassen"
        else:
            return "Luftfeuchte im optimalen Bereich - Keine Anpassungen nötig" 