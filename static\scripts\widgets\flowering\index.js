/**
 * Flowering Widget Module Index
 * Lädt alle Module für das Flowering Widget
 */

// Prüfe ob alle Module geladen sind
function checkModulesLoaded() {
    const requiredModules = [
        'FloweringEventHandler',
        'FloweringUIRenderer', 
        'FloweringTimelineManager',
        'FloweringTrichomeManager',
        'FloweringLightingManager',
        'FloweringPredictiveAnalytics',
        'FloweringAdvancedML',
        'FloweringIoTSensors'
    ];
    
    const missingModules = requiredModules.filter(moduleName => 
        typeof window[moduleName] === 'undefined'
    );
    
    if (missingModules.length > 0) {
        console.warn('🌺 Flowering Widget: Fehlende Module:', missingModules);
        return false;
    }
    
    console.log('🌺 Flowering Widget: Alle Module erfolgreich geladen');
    return true;
}

// Module-Loader
function loadFloweringModules() {
    const moduleFiles = [
        '/static/scripts/widgets/flowering/event-handler.js',
        '/static/scripts/widgets/flowering/ui-renderer.js',
        '/static/scripts/widgets/flowering/timeline-manager.js',
        '/static/scripts/widgets/flowering/trichome-manager.js',
        '/static/scripts/widgets/flowering/lighting-manager.js',
        '/static/scripts/widgets/flowering/predictive-analytics.js',
        '/static/scripts/widgets/flowering/advanced-ml.js',
        '/static/scripts/widgets/flowering/iot-sensors.js'
    ];
    
    return Promise.all(
        moduleFiles.map(file => {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = file;
                script.onload = () => {
                    console.log(`🌺 Flowering Widget: Modul geladen: ${file}`);
                    resolve();
                };
                script.onerror = () => {
                    console.error(`🌺 Flowering Widget: Fehler beim Laden: ${file}`);
                    reject(new Error(`Failed to load ${file}`));
                };
                document.head.appendChild(script);
            });
        })
    );
}

// Automatisches Laden beim DOM-Ready
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('🌺 Flowering Widget: Lade Module...');
        await loadFloweringModules();
        
        // Kurze Verzögerung um sicherzustellen, dass alle Module initialisiert sind
        setTimeout(() => {
            if (checkModulesLoaded()) {
                console.log('🌺 Flowering Widget: Bereit für Initialisierung');
                
                // Event für andere Skripte, dass Module bereit sind
                window.dispatchEvent(new CustomEvent('floweringModulesReady'));
            }
        }, 100);
        
    } catch (error) {
        console.error('🌺 Flowering Widget: Fehler beim Laden der Module:', error);
    }
});

// Export für andere Module
window.FloweringModules = {
    checkModulesLoaded,
    loadFloweringModules
};
