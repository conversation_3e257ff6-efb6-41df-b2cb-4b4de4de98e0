"""
Flush & Harvest Management Logic
Handles flush timing, harvest planning, and water quality tracking
"""

import sqlite3
from datetime import datetime, timedelta
import json
from typing import Dict, List, Optional, Tuple
from phase_logic.core import PhaseLogic


class FlushLogic:
    def __init__(self, db_path: str = 'grow_diary_basic.db'):
        self.db_path = db_path
        self.phase_logic = PhaseLogic()
        self.ensure_tables_exist()

    def ensure_tables_exist(self):
        """Ensure required database tables exist"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create flush_entries table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS flush_entries (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        plant_id TEXT NOT NULL,
                        flush_start_date TEXT,
                        flush_end_date TEXT,
                        water_ph REAL DEFAULT 6.0,
                        water_ec REAL DEFAULT 0.0,
                        notes TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create harvest_checklist table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS harvest_checklist (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        plant_id TEXT NOT NULL,
                        item_description TEXT NOT NULL,
                        category TEXT DEFAULT 'general',
                        completed BOOLEAN DEFAULT FALSE,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            print(f"Error creating tables: {e}")

    def get_flush_status(self, plant_id: str) -> Dict:
        """Get current flush status and recommendations for a plant"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get plant info - only use columns that exist
                cursor.execute('''
                    SELECT strain_type, flower_start_date, pot_size, substrate
                    FROM plants WHERE id = ?
                ''', (plant_id,))
                plant_data = cursor.fetchone()
                
                if not plant_data:
                    return {'error': 'Plant not found'}
                
                strain_type, flower_start_date, pot_size, substrate = plant_data
                
                # Calculate bloom day
                if flower_start_date:
                    bloom_day = self.calculate_bloom_day(flower_start_date)
                else:
                    bloom_day = 0
                
                # Get current flush status
                current_flush = self.get_current_flush_status(plant_id)
                
                # Get flush recommendations based on strain type and bloom day
                flush_recommendation = self.calculate_flush_recommendation(bloom_day, strain_type)
                
                # Get harvest checklist
                harvest_checklist = self.get_harvest_checklist(plant_id, bloom_day)
                
                return {
                    'plant_id': plant_id,
                    'strain_type': strain_type,
                    'bloom_day': bloom_day,
                    'pot_size': pot_size,
                    'substrate': substrate,
                    'current_flush': current_flush,
                    'flush_recommendation': flush_recommendation,
                    'harvest_checklist': harvest_checklist
                }
                
        except Exception as e:
            return {'error': f'Database error: {str(e)}'}

    def get_flush_guidelines(self, strain_type: str) -> Dict:
        """Get flush guidelines based on strain type"""
        guidelines = {
            'indica': {
                'flush_start_day': 50,
                'flush_duration': 10,
                'water_ph': 6.0,
                'water_ec': 0.0,
                'description': 'Indica-Sorten: 10 Tage Flush, pH 6.0'
            },
            'sativa': {
                'flush_start_day': 55,
                'flush_duration': 14,
                'water_ph': 6.2,
                'water_ec': 0.0,
                'description': 'Sativa-Sorten: 14 Tage Flush, pH 6.2'
            },
            'hybrid': {
                'flush_start_day': 52,
                'flush_duration': 12,
                'water_ph': 6.1,
                'water_ec': 0.0,
                'description': 'Hybrid-Sorten: 12 Tage Flush, pH 6.1'
            },
            'autoflower': {
                'flush_start_day': 45,
                'flush_duration': 10,
                'water_ph': 6.0,
                'water_ec': 0.0,
                'description': 'Autoflower: 10 Tage Flush, pH 6.0'
            }
        }
        
        return guidelines.get(strain_type.lower(), guidelines['hybrid'])

    def calculate_flush_recommendation(self, bloom_day: int, strain_type: str) -> Dict:
        """Calculate flush timing recommendations based on strain type and bloom day"""
        strain_type_lower = strain_type.lower()
        
        if 'auto' in strain_type_lower:
            # Autoflower guidelines
            flush_start_day = 45
            flush_duration = 10
            harvest_window_start = 60
            harvest_window_end = 75
        else:
            # Photoperiod guidelines
            flush_start_day = 49
            flush_duration = 14
            harvest_window_start = 56
            harvest_window_end = 70
        
        days_until_flush = flush_start_day - bloom_day
        days_until_harvest = harvest_window_start - bloom_day
        
        if bloom_day < flush_start_day:
            status = 'pre_flush'
            recommendation = f'Flush in {days_until_flush} Tagen starten'
            urgency = 'low' if days_until_flush > 7 else 'medium' if days_until_flush > 3 else 'high'
        elif bloom_day < flush_start_day + flush_duration:
            status = 'flushing'
            days_in_flush = bloom_day - flush_start_day
            recommendation = f'Flush läuft ({days_in_flush}/{flush_duration} Tage)'
            urgency = 'high'
        else:
            status = 'post_flush'
            recommendation = 'Flush abgeschlossen - Ernte bereit'
            urgency = 'critical'
        
        return {
            'status': status,
            'recommendation': recommendation,
            'urgency': urgency,
            'flush_start_day': flush_start_day,
            'flush_duration': flush_duration,
            'days_until_flush': max(0, days_until_flush),
            'days_until_harvest': max(0, days_until_harvest),
            'harvest_window_start': harvest_window_start,
            'harvest_window_end': harvest_window_end,
            'optimal_harvest_day': flush_start_day + flush_duration
        }

    def get_current_flush_status(self, plant_id: str) -> Dict:
        """Get current flush status from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT flush_start_date, flush_end_date, water_ph, water_ec, notes
                    FROM flush_entries 
                    WHERE plant_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT 1
                ''', (plant_id,))
                
                flush_data = cursor.fetchone()
                
                if flush_data:
                    flush_start_date, flush_end_date, water_ph, water_ec, notes = flush_data
                    
                    if flush_start_date and not flush_end_date:
                        # Flush is active
                        start_date = datetime.fromisoformat(flush_start_date)
                        current_day = (datetime.now() - start_date).days
                        
                        return {
                            'active': True,
                            'start_date': flush_start_date,
                            'current_day': current_day,
                            'water_ph': water_ph,
                            'water_ec': water_ec,
                            'notes': notes
                        }
                    else:
                        # Flush completed
                        return {
                            'active': False,
                            'start_date': flush_start_date,
                            'end_date': flush_end_date,
                            'water_ph': water_ph,
                            'water_ec': water_ec,
                            'notes': notes
                        }
                else:
                    return {'active': False}
                    
        except Exception as e:
            return {'error': f'Database error: {str(e)}'}

    def start_flush(self, plant_id: str, water_ph: float = 6.0, water_ec: float = 0.0, notes: str = '') -> Dict:
        """Start flush for a plant"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create flush entry
                cursor.execute('''
                    INSERT INTO flush_entries (plant_id, flush_start_date, water_ph, water_ec, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (plant_id, datetime.now().isoformat(), water_ph, water_ec, notes, datetime.now().isoformat()))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': 'Flush erfolgreich gestartet',
                    'flush_id': cursor.lastrowid
                }
                
        except Exception as e:
            return {'error': f'Failed to start flush: {str(e)}'}

    def end_flush(self, plant_id: str, notes: str = '') -> Dict:
        """End flush for a plant"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Update latest flush entry
                cursor.execute('''
                    UPDATE flush_entries 
                    SET flush_end_date = ?, notes = ?
                    WHERE plant_id = ? AND flush_end_date IS NULL
                ''', (datetime.now().isoformat(), notes, plant_id))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': 'Flush erfolgreich beendet'
                }
                
        except Exception as e:
            return {'error': f'Failed to end flush: {str(e)}'}

    def get_harvest_checklist(self, plant_id: str, bloom_day: int) -> List[Dict]:
        """Get harvest checklist based on bloom day and strain type"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get existing checklist items
                cursor.execute('''
                    SELECT id, item_description, category, completed
                    FROM harvest_checklist 
                    WHERE plant_id = ?
                    ORDER BY created_at
                ''', (plant_id,))
                
                existing_items = cursor.fetchall()
                
                if not existing_items:
                    # Create default checklist items
                    default_items = [
                        {'description': 'Trichome auf 70-80% milchig prüfen', 'category': 'trichome'},
                        {'description': 'EC-Wert im Drain unter 0.6', 'category': 'nutrients'},
                        {'description': 'pH-Wert stabil bei 6.0-6.2', 'category': 'nutrients'},
                        {'description': 'Gelbe Blätter entfernen', 'category': 'preparation'},
                        {'description': 'Ernte-Scheren reinigen', 'category': 'tools'},
                        {'description': 'Trocknungsraum vorbereiten', 'category': 'environment'},
                        {'description': 'Handschuhe bereitstellen', 'category': 'tools'},
                        {'description': 'Luftfeuchtigkeit im Raum prüfen', 'category': 'environment'}
                    ]
                    
                    for item in default_items:
                        cursor.execute('''
                            INSERT INTO harvest_checklist (plant_id, item_description, category, completed)
                            VALUES (?, ?, ?, ?)
                        ''', (plant_id, item['description'], item['category'], False))
                    
                    conn.commit()
                    
                    # Get the newly created items
                    cursor.execute('''
                        SELECT id, item_description, category, completed
                        FROM harvest_checklist 
                        WHERE plant_id = ?
                        ORDER BY created_at
                    ''', (plant_id,))
                    
                    existing_items = cursor.fetchall()
                
                checklist = []
                for item_id, description, category, completed in existing_items:
                    checklist.append({
                        'id': item_id,
                        'description': description,
                        'category': category,
                        'completed': bool(completed)
                    })
                
                return checklist
                
        except Exception as e:
            return []

    def update_checklist_item(self, plant_id: str, category: str, item_index: int, checked: bool) -> Dict:
        """Update checklist item status"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get current checklist
                cursor.execute('''
                    SELECT harvest_checklist FROM plants WHERE id = ?
                ''', (plant_id,))
                
                result = cursor.fetchone()
                if result and result[0]:
                    checklist = json.loads(result[0])
                else:
                    checklist = self.get_harvest_checklist(plant_id, 0)
                
                # Update item
                if category in checklist and 0 <= item_index < len(checklist[category]):
                    checklist[category][item_index]['checked'] = checked
                
                # Save updated checklist
                cursor.execute('''
                    UPDATE plants SET harvest_checklist = ? WHERE id = ?
                ''', (json.dumps(checklist), plant_id))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': 'Checklist aktualisiert',
                    'checklist': checklist
                }
                
        except Exception as e:
            return {'error': f'Failed to update checklist: {str(e)}'}

    def get_water_quality_history(self, plant_id: str, days: int = 30) -> List[Dict]:
        """Get water quality history during flush"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT water_ph, water_ec, created_at, notes
                    FROM flush_entries 
                    WHERE plant_id = ? AND created_at >= date('now', '-{} days')
                    ORDER BY created_at DESC
                '''.format(days), (plant_id,))
                
                history = []
                for row in cursor.fetchall():
                    water_ph, water_ec, created_at, notes = row
                    history.append({
                        'water_ph': water_ph,
                        'water_ec': water_ec,
                        'date': created_at,
                        'notes': notes
                    })
                
                return history
                
        except Exception as e:
            return []

    def calculate_bloom_day(self, flower_start_date: str) -> int:
        """Calculate bloom day from flower start date"""
        try:
            flower_date = datetime.fromisoformat(flower_start_date)
            current_date = datetime.now()
            bloom_day = (current_date - flower_date).days
            return max(0, bloom_day)
        except Exception:
            return 0

    def get_flush_guidelines_data(self) -> Dict:
        """Get comprehensive flush guidelines data from JSON file"""
        try:
            import json
            import os
            
            guidelines_path = os.path.join('static', 'data', 'flush-harvest-guidelines.json')
            if os.path.exists(guidelines_path):
                with open(guidelines_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('flushHarvestGuidelines', {})
            else:
                # Fallback to hardcoded guidelines
                return {
                    'flush': {
                        'startbedingungen': {
                            'autoflower': {
                                'indikatoren': [
                                    'Blütetag 45+ bei schneller Sorte',
                                    'EC des Drain sinkt kaum trotz reduzierter Nährstoffgabe',
                                    '80–90 % der weißen Härchen sind orange/braun'
                                ],
                                'zeitpunktTageVorErnte': 10
                            },
                            'photoperiod': {
                                'indikatoren': [
                                    'Blütetag 49–56 (Woche 7–8 bei 8–10-wöchiger Sorte)',
                                    'Trichome beginnen milchig zu werden',
                                    'Weniger Wasserverbrauch, langsameres Wachstum'
                                ],
                                'zeitpunktTageVorErnte': 14
                            }
                        }
                    },
                    'harvest': {
                        'kriterien': {
                            'trichomeAnalyse': {
                                'glasklar': 'noch nicht reif – kaum Wirkung',
                                'milchig': 'Maximum an THC, cerebrale Wirkung',
                                'bernstein': 'mehr CBN, sedativere Wirkung'
                            }
                        }
                    }
                }
                
        except Exception as e:
            return {'error': f'Failed to load guidelines: {str(e)}'}

    def get_flush_history(self, plant_id: str) -> List[Dict]:
        """Get flush history for a plant"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT flush_start_date, flush_end_date, water_ph, water_ec, notes, created_at
                    FROM flush_entries 
                    WHERE plant_id = ? 
                    ORDER BY created_at DESC
                ''', (plant_id,))
                
                history = []
                for row in cursor.fetchall():
                    start_date, end_date, water_ph, water_ec, notes, created_at = row
                    
                    if start_date and end_date:
                        start = datetime.fromisoformat(start_date)
                        end = datetime.fromisoformat(end_date)
                        duration = (end - start).days
                        status = 'completed'
                    elif start_date:
                        start = datetime.fromisoformat(start_date)
                        duration = (datetime.now() - start).days
                        status = 'active'
                    else:
                        continue
                    
                    history.append({
                        'start_date': start_date,
                        'end_date': end_date,
                        'duration': duration,
                        'status': status,
                        'water_ph': water_ph,
                        'water_ec': water_ec,
                        'notes': notes
                    })
                
                return history
                
        except Exception as e:
            return []

    def complete_flush(self, plant_id: str, notes: str = '') -> Dict:
        """Complete flush for a plant"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Update the most recent flush entry
                cursor.execute('''
                    UPDATE flush_entries 
                    SET flush_end_date = ?, notes = CASE 
                        WHEN notes IS NULL OR notes = '' THEN ? 
                        ELSE notes || ' | ' || ? 
                    END
                    WHERE plant_id = ? AND flush_end_date IS NULL
                    ORDER BY created_at DESC
                    LIMIT 1
                ''', (datetime.now().isoformat(), notes, notes, plant_id))
                
                if cursor.rowcount == 0:
                    return {'error': 'No active flush found'}
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': 'Flush completed successfully',
                    'end_date': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {'error': f'Database error: {str(e)}'}

    def start_harvest(self, plant_id: str, notes: str = '') -> Dict:
        """Start harvest for a plant"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Update plant status to harvested
                cursor.execute('''
                    UPDATE plants 
                    SET harvest_date = ?, notes = CASE 
                        WHEN notes IS NULL OR notes = '' THEN ? 
                        ELSE notes || ' | ' || ? 
                    END
                    WHERE id = ?
                ''', (datetime.now().isoformat(), notes, notes, plant_id))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': 'Harvest started successfully',
                    'harvest_date': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {'error': f'Database error: {str(e)}'}

    def update_checklist_item(self, plant_id: str, item_id: str, completed: bool) -> Dict:
        """Update checklist item status"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Update checklist item
                cursor.execute('''
                    UPDATE harvest_checklist 
                    SET completed = ?, updated_at = ?
                    WHERE plant_id = ? AND id = ?
                ''', (completed, datetime.now().isoformat(), plant_id, item_id))
                
                if cursor.rowcount == 0:
                    return {'error': 'Checklist item not found'}
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': 'Checklist item updated successfully',
                    'completed': completed
                }
                
        except Exception as e:
            return {'error': f'Database error: {str(e)}'} 