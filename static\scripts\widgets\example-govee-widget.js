/**
 * Beispiel-Widget für Govee Integration
 * Zeigt, wie andere Widgets die globale Govee-Integration nutzen können
 */

class ExampleGoveeWidget {
    constructor(containerId = 'example-govee-widget') {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.widgetId = `example-govee-${Date.now()}`;
        
        if (this.container) {
            this.init();
        } else {
            console.warn(`Example Govee Widget: Container ${containerId} nicht gefunden`);
        }
    }

    init() {

        this.renderWidget();
        this.setupGoveeIntegration();
    }

    // Widget rendern
    renderWidget() {
        this.container.innerHTML = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-thermometer-half me-2"></i>Beispiel Govee Widget
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-info" id="example-govee-setup-btn">
                        <i class="fas fa-cog"></i> Govee Setup
                    </button>
                </div>
                <div class="card-body">
                    <div id="example-govee-status">
                        <div class="text-center text-muted">
                            <i class="fas fa-thermometer-half fa-2x mb-2"></i>
                            <p>Govee Integration wird geprüft...</p>
                        </div>
                    </div>
                    <div id="example-govee-data" style="display: none;">
                        <div class="row text-center">
                            <div class="col-4">
                                <h4 id="example-temp-value">--</h4>
                                <small class="text-muted">Temperatur</small>
                            </div>
                            <div class="col-4">
                                <h4 id="example-humidity-value">--</h4>
                                <small class="text-muted">Luftfeuchte</small>
                            </div>
                            <div class="col-4">
                                <h4 id="example-battery-value">--</h4>
                                <small class="text-muted">Batterie</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted" id="example-last-update">
                                Letztes Update: --
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Event-Listener für Setup-Button
        const setupBtn = document.getElementById('example-govee-setup-btn');
        if (setupBtn) {
            setupBtn.addEventListener('click', () => {
                if (window.goveeSensorManager) {
                    window.goveeSensorManager.showSetupModal();
                }
            });
        }
    }

    // Govee Integration einrichten
    async setupGoveeIntegration() {
        if (!window.goveeSensorManager) {
            console.error('Govee Sensor Manager nicht verfügbar');
            this.showError('Govee Sensor Manager nicht verfügbar');
            return;
        }

        // Als Subscriber registrieren
        window.goveeSensorManager.subscribe(this.widgetId, (reading) => {
            this.updateWidgetData(reading);
        });

        // Status prüfen
        const status = await window.goveeSensorManager.checkConnectionStatus();
        this.updateWidgetStatus(status);

        // Wenn bereits verbunden, aktuelle Daten abrufen
        if (status && status.connected && window.goveeSensorManager.currentDevice) {
            const device = window.goveeSensorManager.currentDevice;
            const reading = await window.goveeSensorManager.getCurrentReading(device.id, device.model);
            if (reading) {
                this.updateWidgetData(reading);
            }
        }
    }

    // Widget-Status aktualisieren
    updateWidgetStatus(status) {
        const statusElement = document.getElementById('example-govee-status');
        const dataElement = document.getElementById('example-govee-data');
        
        if (!statusElement || !dataElement) return;

        if (status && status.connected) {
            statusElement.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Govee verbunden</strong><br>
                    <small>${status.devices_count} Geräte verfügbar</small>
                </div>
            `;
            dataElement.style.display = 'block';
        } else {
            statusElement.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Govee nicht verbunden</strong><br>
                    <small>Klicke auf "Govee Setup" um die Verbindung einzurichten</small>
                </div>
            `;
            dataElement.style.display = 'none';
        }
    }

    // Widget-Daten aktualisieren
    updateWidgetData(reading) {
        const tempElement = document.getElementById('example-temp-value');
        const humidityElement = document.getElementById('example-humidity-value');
        const batteryElement = document.getElementById('example-battery-value');
        const lastUpdateElement = document.getElementById('example-last-update');
        
        if (tempElement) tempElement.textContent = `${reading.temperature}°C`;
        if (humidityElement) humidityElement.textContent = `${reading.humidity}%`;
        if (batteryElement) batteryElement.textContent = `${reading.battery}%`;
        
        if (lastUpdateElement) {
            const now = new Date();
            lastUpdateElement.textContent = `Letztes Update: ${now.toLocaleTimeString()}`;
        }

        // Status auf verbunden setzen
        this.updateWidgetStatus({ connected: true, devices_count: 1 });
    }

    // Fehler anzeigen
    showError(message) {
        const statusElement = document.getElementById('example-govee-status');
        if (statusElement) {
            statusElement.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Fehler:</strong> ${message}
                </div>
            `;
        }
    }

    // Widget zerstören (Cleanup)
    destroy() {
        if (window.goveeSensorManager) {
            window.goveeSensorManager.unsubscribe(this.widgetId);
        }
        
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Global verfügbar machen
window.ExampleGoveeWidget = ExampleGoveeWidget;

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExampleGoveeWidget;
} 