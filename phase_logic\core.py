#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PhaseLogic Core - Hauptklasse für das Grow-Tagebuch
Modularisierte Version der ursprünglichen phase_logic.py
"""

from .calculators.phase_calculator import PhaseCalculator
from .calculators.fertilizer_calculator import FertilizerCalculator
from .utils.date_utils import format_date_german, calculate_days_between
from .utils.validation_utils import validate_date_format, validate_phase_name, validate_brand_name
from .data.stress_management import get_stress_management_recommendation, get_lst_techniques, get_hst_techniques, get_training_timeline

class PhaseLogic:
    """
    Hauptklasse für die Phasenlogik des Grow-Tagebuchs
    Kombiniert alle Module zu einer einheitlichen API
    """
    
    def __init__(self):
        """Initialisiert die PhaseLogic mit allen Modulen"""
        self.phase_calculator = PhaseCalculator()
        self.fertilizer_calculator = FertilizerCalculator()
    
    def get_current_phase(self, start_date, flowering_date=None, current_date=None, strain_type='photoperiodic'):
        """
        Berechnet die aktuelle Phase einer Pflanze
        
        Args:
            start_date (str): Startdatum im Format 'YYYY-MM-DD'
            flowering_date (str, optional): Blütedatum im Format 'YYYY-MM-DD'
            current_date (str, optional): Aktuelles Datum im Format 'YYYY-MM-DD'
            strain_type (str): Sortentyp ('autoflowering' oder 'photoperiodic')
        
        Returns:
            dict: Aktuelle Phasen-Informationen
        """
        # Validierung
        if not validate_date_format(start_date):
            return {'error': 'Ungültiges Startdatum-Format. Verwende YYYY-MM-DD'}
        
        if flowering_date and not validate_date_format(flowering_date):
            return {'error': 'Ungültiges Blütedatum-Format. Verwende YYYY-MM-DD'}
        
        if current_date and not validate_date_format(current_date):
            return {'error': 'Ungültiges aktuelles Datum-Format. Verwende YYYY-MM-DD'}
        
        # Strain-Typ validieren
        if strain_type not in ['autoflowering', 'photoperiodic']:
            strain_type = 'photoperiodic'  # Standardwert
        
        return self.phase_calculator.calculate_current_phase(start_date, flowering_date, current_date, strain_type)
    
    def get_fertilizer_recommendations(self, phase, brand='biobizz'):
        """
        Gibt Dünger-Empfehlungen für eine bestimmte Phase und Marke zurück
        
        Args:
            phase (str): Aktuelle Phase
            brand (str): Düngermarke ('biobizz', 'canna', 'plagron')
        
        Returns:
            dict: Dünger-Empfehlungen
        """
        # Validierung
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        if not validate_brand_name(brand):
            return {'error': f'Ungültige Marke: {brand}'}
        
        return self.fertilizer_calculator.get_fertilizer_recommendations(phase, brand)
    
    def get_all_brand_recommendations(self, phase):
        """
        Gibt Dünger-Empfehlungen für alle Marken zurück
        
        Args:
            phase (str): Aktuelle Phase
        
        Returns:
            dict: Dünger-Empfehlungen für alle Marken
        """
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        return self.fertilizer_calculator.get_all_brand_recommendations(phase)
    
    def check_fertilizer_combinations(self, brand, fertilizers):
        """
        Prüft Dünger-Kombinationen auf Konflikte
        
        Args:
            brand (str): Düngermarke
            fertilizers (list): Liste der Dünger-Namen
        
        Returns:
            dict: Kombinationsprüfung-Ergebnis
        """
        if not validate_brand_name(brand):
            return {'error': f'Ungültige Marke: {brand}'}
        
        return self.fertilizer_calculator.check_fertilizer_combinations(brand, fertilizers)
    
    def get_ec_guidelines(self, phase, brand='biobizz'):
        """
        Gibt EC-Wert-Richtlinien für eine bestimmte Phase zurück
        
        Args:
            phase (str): Aktuelle Phase
            brand (str): Düngermarke
        
        Returns:
            dict: EC-Wert-Richtlinien
        """
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        if not validate_brand_name(brand):
            return {'error': f'Ungültige Marke: {brand}'}
        
        return self.fertilizer_calculator.get_ec_guidelines(phase, brand)
    
    def get_phase_timeline(self, start_date, flowering_date=None, total_weeks=16):
        """
        Erstellt eine Timeline aller Phasen
        
        Args:
            start_date (str): Startdatum
            flowering_date (str, optional): Blütedatum
            total_weeks (int): Gesamtdauer in Wochen
        
        Returns:
            list: Timeline mit allen Phasen
        """
        if not validate_date_format(start_date):
            return {'error': 'Ungültiges Startdatum-Format. Verwende YYYY-MM-DD'}
        
        if flowering_date and not validate_date_format(flowering_date):
            return {'error': 'Ungültiges Blütedatum-Format. Verwende YYYY-MM-DD'}
        
        return self.phase_calculator.get_phase_timeline(start_date, flowering_date, total_weeks)
    
    def calculate_vpd_for_phase(self, temperature, humidity, phase):
        """
        Berechnet VPD für eine bestimmte Phase
        
        Args:
            temperature (float): Temperatur in Celsius
            humidity (float): Luftfeuchtigkeit in Prozent
            phase (str): Aktuelle Phase
        
        Returns:
            dict: VPD-Informationen
        """
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        return self.phase_calculator.calculate_vpd_for_phase(temperature, humidity, phase)
    
    def get_complete_phase_info(self, start_date, flowering_date=None, current_date=None, brand='biobizz'):
        """
        Gibt vollständige Phasen-Informationen zurück
        
        Args:
            start_date (str): Startdatum
            flowering_date (str, optional): Blütedatum
            current_date (str, optional): Aktuelles Datum
            brand (str): Düngermarke
        
        Returns:
            dict: Vollständige Phasen-Informationen
        """
        # Aktuelle Phase berechnen
        phase_info = self.get_current_phase(start_date, flowering_date, current_date)
        
        if 'error' in phase_info:
            return phase_info
        
        # Dünger-Empfehlungen
        fertilizer_info = self.get_fertilizer_recommendations(phase_info['sub_phase'], brand)
        
        # EC-Wert-Richtlinien
        ec_info = self.get_ec_guidelines(phase_info['sub_phase'], brand)
        
        # Timeline
        timeline = self.get_phase_timeline(start_date, flowering_date)
        
        return {
            'phase_info': phase_info,
            'fertilizer_recommendations': fertilizer_info,
            'ec_guidelines': ec_info,
            'timeline': timeline,
            'brand': brand,
            'start_date': start_date,
            'flowering_date': flowering_date,
            'current_date': current_date or 'heute'
        }
    
    def get_available_brands(self):
        """
        Gibt alle verfügbaren Düngermarken zurück
        
        Returns:
            list: Liste der verfügbaren Marken
        """
        return self.fertilizer_calculator.get_available_brands()
    
    def get_available_phases(self, brand='biobizz'):
        """
        Gibt alle verfügbaren Phasen für eine Marke zurück
        
        Args:
            brand (str): Düngermarke
        
        Returns:
            list: Liste der verfügbaren Phasen
        """
        if not validate_brand_name(brand):
            return []
        
        return self.fertilizer_calculator.get_available_phases(brand)
    
    def get_fertilizer_summary(self, brand='biobizz'):
        """
        Gibt eine Zusammenfassung aller Dünger einer Marke zurück
        
        Args:
            brand (str): Düngermarke
        
        Returns:
            dict: Dünger-Zusammenfassung
        """
        if not validate_brand_name(brand):
            return {'error': f'Ungültige Marke: {brand}'}
        
        return self.fertilizer_calculator.get_fertilizer_summary(brand)
    
    def get_trichome_development(self, plant):
        """
        Gibt Trichome-Entwicklung für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Trichome-Entwicklungsdaten
        """
        try:
            from .data.phase_details import get_trichome_development, calculate_bloom_week
            
            if not plant:
                return None
            
            flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
            bloom_week = calculate_bloom_week(flowering_date)
            
            return get_trichome_development(bloom_week)
        except Exception as e:
            print(f"Fehler in get_trichome_development: {e}")
            return None
    
    def get_environment_conditions(self, plant):
        """
        Gibt Umgebungsbedingungen für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Umgebungsbedingungen
        """
        try:
            from .data.phase_details import get_environment_conditions
            
            if not plant or 'start_date' not in plant:
                return None
            
            start_date = plant['start_date']
            flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
            current_date = datetime.now().strftime('%Y-%m-%d')
            
            current_phase = self.get_current_phase(start_date, flowering_date, current_date)
            phase_key = current_phase.get('current_phase', current_phase.get('sub_phase'))
            
            return get_environment_conditions(phase_key)
        except Exception as e:
            print(f"Fehler in get_environment_conditions: {e}")
            return None
    
    def get_extended_nutrients(self, plant):
        """
        Gibt erweiterte Nährstoff-Empfehlungen für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Erweiterte Nährstoff-Empfehlungen
        """
        try:
            from .data.phase_details import get_extended_nutrients
            
            if not plant or 'start_date' not in plant:
                return None
            
            start_date = plant['start_date']
            flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
            current_date = datetime.now().strftime('%Y-%m-%d')
            
            current_phase = self.get_current_phase(start_date, flowering_date, current_date)
            phase_key = current_phase.get('current_phase', current_phase.get('sub_phase'))
            
            return get_extended_nutrients(phase_key)
        except Exception as e:
            print(f"Fehler in get_extended_nutrients: {e}")
            return None
    
    def get_harvest_reason(self, plant):
        """
        Gibt Ernte-Begründung für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Ernte-Begründung
        """
        try:
            from .data.phase_details import get_harvest_reason, get_trichome_development, calculate_bloom_week
            
            if not plant:
                return None
            
            flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
            bloom_week = calculate_bloom_week(flowering_date)
            
            trichome_data = get_trichome_development(bloom_week)
            return get_harvest_reason(trichome_data['trichome_state'])
        except Exception as e:
            print(f"Fehler in get_harvest_reason: {e}")
            return None
    
    def get_complete_phase_details(self, plant, brand='biobizz'):
        """
        Gibt vollständige Phasen-Details für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
            brand (str): Düngermarke
        
        Returns:
            dict: Vollständige Phasen-Details
        """
        try:
            from .data.phase_details import get_complete_phase_details
            
            return get_complete_phase_details(plant, brand)
        except Exception as e:
            print(f"Fehler in get_complete_phase_details: {e}")
            return None
    
    def format_date_german(self, date_str):
        """
        Formatiert ein Datum im deutschen Format
        
        Args:
            date_str (str): Datum im Format 'YYYY-MM-DD'
        
        Returns:
            str: Datum im deutschen Format
        """
        return format_date_german(date_str)
    
    def calculate_days_between(self, start_date, end_date):
        """
        Berechnet die Anzahl der Tage zwischen zwei Daten
        
        Args:
            start_date (str): Startdatum
            end_date (str): Enddatum
        
        Returns:
            int: Anzahl der Tage
        """
        return calculate_days_between(start_date, end_date)
    
    @staticmethod
    def calculate_plant_phase(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Berechnet die aktuelle Phase einer Pflanze basierend auf Pflanzendaten
        
        Args:
            plant (dict): Pflanzendaten mit start_date und flowering_date
        
        Returns:
            dict: Aktuelle Phasen-Informationen (vollständige Kompatibilität)
        """
        from datetime import date, datetime, timedelta
        
        # Validierung
        if not plant or 'start_date' not in plant:
            return None
        
        try:
            start_date = datetime.strptime(plant['start_date'], '%Y-%m-%d').date()
            flower_start = None
            # Priorität: bloom_start > flowering_date > flower_start_date
            if plant.get('bloom_start'):
                flower_start = datetime.strptime(plant['bloom_start'], '%Y-%m-%d').date()
            elif plant.get('flowering_date'):
                flower_start = datetime.strptime(plant['flowering_date'], '%Y-%m-%d').date()
            elif plant.get('flower_start_date'):
                flower_start = datetime.strptime(plant['flower_start_date'], '%Y-%m-%d').date()
            
            today = date.today()
            
            # Grow-Dauer berechnen
            grow_duration = (today - start_date).days
            grow_weeks = grow_duration // 7
            grow_days = grow_duration % 7
            
            # Grow-Fortschritt berechnen basierend auf tatsächlichem Blüte-Datum
            if flower_start:
                # Wenn ein Blüte-Datum vorhanden ist, berechne den Fortschritt bis zum Blüte-Start
                grow_duration_until_flower = (flower_start - start_date).days
                grow_progress = min(100, (grow_duration / grow_duration_until_flower) * 100) if grow_duration_until_flower > 0 else 100
            else:
                # Fallback: angenommen 8 Wochen vegetative Phase (realistischer)
                total_grow_weeks = 8
                grow_progress = min(100, (grow_duration / (total_grow_weeks * 7)) * 100)
            
            # Neue PhaseLogic-Instanz erstellen
            phase_logic = PhaseLogic()
            
            # Aktuelle Phase berechnen - verwende das echte Blüte-Datum
            flowering_date_for_calc = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
            phase_info = phase_logic.get_current_phase(
                plant['start_date'], 
                flowering_date_for_calc, 
                today.strftime('%Y-%m-%d')
            )
            
            if 'error' in phase_info:
                return None
            
            # Dünger-Empfehlungen abrufen
            fertilizer_recommendations = phase_logic.get_fertilizer_recommendations(
                phase_info['sub_phase'], 
                'biobizz'
            )
            
            # Benutzerfreundliche Stage und Sub-Stage Namen
            stage_mapping = {
                'germination': 'Keimung',
                'vegetative': 'Wachstumsphase',
                'flowering': 'Blütephase'
            }
            
            sub_stage_mapping = {
                'germination': 'Keimung',
                'vegetative_early': 'Frühe Wachstumsphase',
                'vegetative_middle': 'Mittlere Wachstumsphase', 
                'vegetative_late': 'Späte Wachstumsphase',
                'flowering_early': 'Frühe Blüte',
                'flowering_middle': 'Mittlere Blüte',
                'flowering_late': 'Späte Blüte'
            }
            
            # Vollständige Kompatibilität mit ursprünglicher Struktur
            result = {
                'phase_name': phase_info['phase_name'],
                'current_phase': phase_info['sub_phase'],
                'stage': stage_mapping.get(phase_info['main_phase'], phase_info['main_phase']),
                'sub_stage': sub_stage_mapping.get(phase_info['sub_phase'], phase_info['sub_phase'].split('_')[-1] if '_' in phase_info['sub_phase'] else phase_info['sub_phase']),
                'grow_duration_weeks': grow_weeks,
                'grow_duration_days': grow_days,
                'grow_day': grow_duration,
                'grow_week': grow_weeks + 1,
                'grow_progress': round(max(0, grow_progress), 1),
                'flower_day': 0,
                'flower_week': 0,
                'progress_percent': 0,
                'next_phase': phase_info.get('next_phase', 'Nächste Phase'),
                'days_to_next': phase_info.get('days_to_next', 0),
                'phase_description': phase_info['description'],
                'vpd_range': phase_info.get('vpd_range', ''),
                'optimal_vpd': phase_info.get('optimal_vpd', ''),
                'optimal_conditions': phase_info.get('optimal_conditions', {}),
                'start_date': plant['start_date'],
                'flower_start_date': plant.get('flowering_date') or plant.get('flower_start_date'),
                'bloom_start': plant.get('bloom_start') or plant.get('flowering_date'),
                'blüte_day': 0,
                'blüte_week': 0,
                'blüte_progress': 0,
                'sub_stage_day': phase_info.get('days_in_phase', 0),
                'sub_stage_total_days': 7,  # Standard-Unterphase-Dauer
                'sub_stage_progress': 0,
                'fertilizer_recommendations': fertilizer_recommendations
            }
            
            # Blüte-spezifische Berechnungen
            if flower_start:
                # Echte Blüte-Daten verwenden
                blüte_duration = (today - flower_start).days
                blüte_weeks = blüte_duration // 7
                blüte_days = blüte_duration % 7
                
                # Blütefortschritt berechnen (angenommen 8-10 Wochen Blüte)
                total_blüte_weeks = 9  # Durchschnitt
                blüte_progress = min(100, (blüte_duration / (total_blüte_weeks * 7)) * 100)
                
                # Sub-Stage Fortschritt basierend auf aktueller Woche
                sub_stage_progress = round(max(0, (blüte_days / 7) * 100))
                
                result.update({
                    'flower_start_date': flower_start.strftime('%Y-%m-%d'),
                    'bloom_start': flower_start.strftime('%Y-%m-%d'),
                    'flower_day': max(0, blüte_duration),
                    'flower_week': max(1, blüte_weeks + 1),
                    'blüte_day': max(0, blüte_duration),
                    'blüte_week': max(1, blüte_weeks + 1),
                    'progress_percent': round(max(0, blüte_progress), 1),
                    'blüte_progress': round(max(0, blüte_progress), 1),
                    'sub_stage_day': max(0, blüte_days),
                    'sub_stage_total_days': 7,
                    'sub_stage_progress': sub_stage_progress
                })
            elif phase_info['main_phase'] == 'flowering':
                # Blüte-Phase ohne explizites Blüte-Datum - schätze es basierend auf der aktuellen Phase
                # Für flowering_middle: etwa 8-9 Wochen nach Start
                estimated_flower_start = start_date + timedelta(days=56)  # 8 Wochen
                blüte_duration = (today - estimated_flower_start).days
                blüte_weeks = blüte_duration // 7
                blüte_days = blüte_duration % 7
                
                # Blütefortschritt berechnen
                total_blüte_weeks = 9  # Durchschnitt
                blüte_progress = min(100, (blüte_duration / (total_blüte_weeks * 7)) * 100)
                
                # Sub-Stage Fortschritt basierend auf aktueller Woche
                sub_stage_progress = round(max(0, (blüte_days / 7) * 100))
                
                result.update({
                    'flower_start_date': estimated_flower_start.strftime('%Y-%m-%d'),
                    'bloom_start': estimated_flower_start.strftime('%Y-%m-%d'),
                    'flower_day': max(0, blüte_duration),
                    'flower_week': max(1, blüte_weeks + 1),
                    'blüte_day': max(0, blüte_duration),
                    'blüte_week': max(1, blüte_weeks + 1),
                    'progress_percent': round(max(0, blüte_progress), 1),
                    'blüte_progress': round(max(0, blüte_progress), 1),
                    'sub_stage_day': max(0, blüte_days),
                    'sub_stage_total_days': 7,
                    'sub_stage_progress': sub_stage_progress
                })
            
            return result
            
        except Exception as e:
            print(f"Fehler bei Phasenberechnung: {e}")
            return None
    
    @staticmethod
    def get_phase_history(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt den Phasenverlauf einer Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            list: Phasenverlauf
        """
        from datetime import date, timedelta
        
        if not plant or 'start_date' not in plant:
            return []
        
        start_date = plant['start_date']
        flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
        current_date = date.today().strftime('%Y-%m-%d')
        
        # Neue PhaseLogic-Instanz erstellen
        phase_logic = PhaseLogic()
        
        # Timeline erstellen
        timeline = phase_logic.get_phase_timeline(start_date, flowering_date)
        
        # Aktuelle Phase berechnen
        current_phase = phase_logic.get_current_phase(start_date, flowering_date, current_date)
        
        # Aktuelle Woche finden
        current_week = None
        for week_info in timeline:
            if week_info['phase'] == current_phase.get('sub_phase', ''):
                current_week = week_info['week']
                break
        
        # Phasenverlauf formatieren - ähnliche Phasen zusammenfassen
        history = []
        current_phase_group = None
        
        for week_info in timeline:
            is_current = week_info['phase'] == current_phase.get('sub_phase', '')
            
            # Dauer berechnen
            from datetime import datetime
            start_dt = datetime.strptime(week_info['start_date'], '%Y-%m-%d')
            end_dt = datetime.strptime(week_info['end_date'], '%Y-%m-%d')
            duration = (end_dt - start_dt).days + 1
            
            # Status bestimmen
            status = 'active' if is_current else 'completed' if current_week and week_info['week'] < current_week else 'upcoming'
            
            # Prüfen ob es eine neue Phase ist oder die gleiche Phase weitergeht
            phase_key = week_info['phase']
            
            if current_phase_group is None or current_phase_group['phase'] != phase_key:
                # Neue Phase - neuen Eintrag erstellen
                current_phase_group = {
                    'week': week_info['week'],
                    'phase': week_info['phase'],
                    'name': week_info['phase_name'],
                    'start_date': week_info['start_date'],
                    'end_date': week_info['end_date'],
                    'description': week_info['description'],
                    'duration': duration,
                    'stage': week_info.get('stage', ''),
                    'sub_stage': week_info.get('sub_stage', ''),
                    'status': status,
                    'is_current': is_current,
                    'week_count': 1
                }
                history.append(current_phase_group)
            else:
                # Gleiche Phase - bestehenden Eintrag erweitern
                current_phase_group['end_date'] = week_info['end_date']
                current_phase_group['duration'] += duration
                current_phase_group['week_count'] += 1
                current_phase_group['status'] = status  # Aktualisiere Status falls nötig
                current_phase_group['is_current'] = is_current  # Aktualisiere current-Status
        
        return history
    
    @staticmethod
    def get_next_phases(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt die nächsten Phasen einer Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            list: Nächste Phasen
        """
        from datetime import date
        
        if not plant or 'start_date' not in plant:
            return []
        
        start_date = plant['start_date']
        flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
        current_date = date.today().strftime('%Y-%m-%d')
        
        # Neue PhaseLogic-Instanz erstellen
        phase_logic = PhaseLogic()
        
        # Timeline erstellen
        timeline = phase_logic.get_phase_timeline(start_date, flowering_date)
        
        # Aktuelle Phase finden
        current_phase = phase_logic.get_current_phase(start_date, flowering_date, current_date)
        current_week = None
        
        # Aktuelle Woche finden
        for week_info in timeline:
            if week_info['phase'] == current_phase.get('sub_phase', ''):
                current_week = week_info['week']
                break
        
        if current_week is None:
            return []
        
        # Nächste Phasen (max. 3) - ähnliche Phasen zusammenfassen
        next_phases = []
        current_phase_group = None
        
        for week_info in timeline:
            if week_info['week'] > current_week:
                # Dauer berechnen
                from datetime import datetime
                start_dt = datetime.strptime(week_info['start_date'], '%Y-%m-%d')
                end_dt = datetime.strptime(week_info['end_date'], '%Y-%m-%d')
                duration = (end_dt - start_dt).days + 1
                
                # Prüfen ob es eine neue Phase ist oder die gleiche Phase weitergeht
                phase_key = week_info['phase']
                
                if current_phase_group is None or current_phase_group['phase'] != phase_key:
                    # Neue Phase - neuen Eintrag erstellen
                    current_phase_group = {
                        'week': week_info['week'],
                        'phase': week_info['phase'],
                        'name': week_info['phase_name'],
                        'start_date': week_info['start_date'],
                        'end_date': week_info['end_date'],
                        'description': week_info['description'],
                        'requirements': week_info['description'],
                        'duration': duration,
                        'stage': week_info.get('stage', ''),
                        'sub_stage': week_info.get('sub_stage', ''),
                        'estimated_start': week_info['start_date'],
                        'estimated_end': week_info['end_date'],
                        'week_count': 1
                    }
                    next_phases.append(current_phase_group)
                    
                    # Maximal 3 verschiedene Phasen
                    if len(next_phases) >= 3:
                        break
                else:
                    # Gleiche Phase - bestehenden Eintrag erweitern
                    current_phase_group['end_date'] = week_info['end_date']
                    current_phase_group['duration'] += duration
                    current_phase_group['week_count'] += 1
                    current_phase_group['estimated_end'] = week_info['end_date']
        
        # Ernte-Eintrag hinzufügen falls Blüte-Phase erreicht wurde
        if flowering_date and current_phase.get('main_phase') == 'flowering':
            from datetime import datetime, timedelta
            
            # Blüte-Start-Datum parsen
            bloom_start = datetime.strptime(flowering_date, '%Y-%m-%d').date()
            
            # Ernte-Zeitpunkt berechnen (typisch 8-10 Wochen nach Blüte-Start)
            harvest_weeks = 9  # Durchschnitt
            harvest_start = bloom_start + timedelta(weeks=harvest_weeks)
            harvest_end = harvest_start + timedelta(weeks=1)  # 1 Woche Ernte-Fenster
            
            # Prüfen ob Ernte noch in der Zukunft liegt
            today = date.today()
            if harvest_start > today:
                harvest_duration = 7  # 1 Woche
                
                harvest_phase = {
                    'week': current_week + harvest_weeks,
                    'phase': 'harvest',
                    'name': '🌾 Ernte',
                    'start_date': harvest_start.strftime('%Y-%m-%d'),
                    'end_date': harvest_end.strftime('%Y-%m-%d'),
                    'description': 'Erntezeit! Trichome sollten milchig bis bernsteinfarben sein. Reduziere Bewässerung in den letzten Tagen.',
                    'requirements': 'Trichome prüfen, Bewässerung reduzieren, Ernte-Vorbereitung',
                    'duration': harvest_duration,
                    'stage': 'harvest',
                    'sub_stage': 'ready',
                    'estimated_start': harvest_start.strftime('%Y-%m-%d'),
                    'estimated_end': harvest_end.strftime('%Y-%m-%d'),
                    'week_count': 1,
                    'is_harvest': True
                }
                
                # Ernte-Eintrag hinzufügen
                next_phases.append(harvest_phase)
                
                # Maximal 3 Einträge beibehalten
                if len(next_phases) > 3:
                    next_phases = next_phases[:3]
        
        # Chronologisch sortieren (nächste zuerst)
        next_phases.sort(key=lambda x: x['estimated_start'])
        
        return next_phases
    
    @staticmethod
    def get_fertilizer_recommendations(plant, brand=None):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt Dünger-Empfehlungen für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
            brand (str, optional): Düngermarke
        
        Returns:
            dict: Dünger-Empfehlungen
        """
        if not plant or 'start_date' not in plant:
            return None
        
        # Standard-Marke verwenden
        brand = brand or 'biobizz'
        
        # Phase berechnen falls nicht vorhanden
        if not plant.get('phase_key'):
            current_phase = PhaseLogic.calculate_plant_phase(plant)
            if current_phase:
                phase_key = PhaseLogic._determine_fertilizer_phase_key(current_phase, plant)
                plant['phase_key'] = phase_key
        
        phase = plant.get('phase_key')
        if not phase:
            return None
        
        # Direkt auf die Dünger-Daten zugreifen
        from .data.fertilizer_brands import FERTILIZER_BRANDS
        
        if brand not in FERTILIZER_BRANDS:
            return None
            
        brand_data = FERTILIZER_BRANDS[brand]
        recommendations = brand_data['recommendations'].get(phase)
        
        return recommendations
    
    @staticmethod
    def check_fertilizer_combinations(fertilizers, brand='biobizz'):
        """
        Statische Methode für Rückwärtskompatibilität
        Prüft Dünger-Kombinationen auf Konflikte
        
        Args:
            fertilizers (list): Liste der Dünger-Namen
            brand (str): Düngermarke
        
        Returns:
            dict: Kombinationsprüfung-Ergebnis
        """
        # Neue PhaseLogic-Instanz erstellen
        phase_logic = PhaseLogic()
        
        return phase_logic.check_fertilizer_combinations(fertilizers, brand)
    
    @staticmethod
    def _determine_fertilizer_phase_key(current_phase, plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Bestimmt den Phasenschlüssel für Dünger-Empfehlungen
        
        Args:
            current_phase (dict): Aktuelle Phase
            plant (dict): Pflanzendaten
        
        Returns:
            str: Phasenschlüssel
        """
        if not current_phase:
            return None
        
        return current_phase.get('current_phase', current_phase.get('sub_phase'))
    
    @staticmethod
    def get_trichome_development(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt Trichome-Entwicklung für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Trichome-Entwicklungsdaten
        """
        phase_logic = PhaseLogic()
        return phase_logic.get_trichome_development(plant)
    
    @staticmethod
    def get_environment_conditions(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt Umgebungsbedingungen für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Umgebungsbedingungen
        """
        phase_logic = PhaseLogic()
        return phase_logic.get_environment_conditions(plant)
    
    @staticmethod
    def get_extended_nutrients(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt erweiterte Nährstoff-Empfehlungen für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Erweiterte Nährstoff-Empfehlungen
        """
        phase_logic = PhaseLogic()
        return phase_logic.get_extended_nutrients(plant)
    
    @staticmethod
    def get_harvest_reason(plant):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt Ernte-Begründung für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
        
        Returns:
            dict: Ernte-Begründung
        """
        phase_logic = PhaseLogic()
        return phase_logic.get_harvest_reason(plant)
    
    @staticmethod
    def get_complete_phase_details(plant, brand='biobizz'):
        """
        Statische Methode für Rückwärtskompatibilität
        Gibt vollständige Phasen-Details für eine Pflanze zurück
        
        Args:
            plant (dict): Pflanzendaten
            brand (str): Düngermarke
        
        Returns:
            dict: Vollständige Phasen-Details
        """
        phase_logic = PhaseLogic()
        return phase_logic.get_complete_phase_details(plant, brand) 

    def get_vpd_optimization_info(self, phase, temperature=None, humidity=None):
        """
        Gibt VPD-Optimierungsinfos für eine Phase zurück (inkl. Statusbewertung bei Angabe von Temperatur/Luftfeuchte)
        Args:
            phase (str): Phasenname (z.B. 'vegetative_early')
            temperature (float, optional): Temperatur in °C
            humidity (float, optional): Luftfeuchtigkeit in %
        Returns:
            dict: VPD-Infos (Empfehlungen, Status, aktuelle Werte)
        """
        from .data.vpd_guidelines import calculate_vpd, get_vpd_status, get_vpd_recommendations
        result = {
            'phase': phase,
            'recommendations': get_vpd_recommendations(phase)
        }
        if temperature is not None and humidity is not None:
            vpd = calculate_vpd(temperature, humidity)
            status = get_vpd_status(vpd, phase)
            result['current'] = {
                'temperature': temperature,
                'humidity': humidity,
                'vpd': vpd,
                'status': status
            }
        return result

    def get_watering_recommendation(self, phase, substrate=None, pot_size_l=None):
        """
        Gibt Bewässerungsempfehlungen für eine Phase zurück
        
        Args:
            phase (str): Phasenname (z.B. 'vegetative_early')
            substrate (str, optional): Substrat-Typ (z.B. 'erde', 'coco', 'hydro')
            pot_size_l (float, optional): Topfgröße in Litern
        
        Returns:
            dict: Bewässerungsempfehlungen mit Menge, Häufigkeit und Hinweisen
        """
        # Validierung
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        from .data.watering_guidelines import get_watering_recommendation
        
        result = get_watering_recommendation(phase, substrate, pot_size_l)
        
        if not result:
            return {'error': f'Keine Bewässerungsempfehlung für Phase: {phase}'}
        
        return {
            'phase': phase,
            'substrate': substrate,
            'pot_size_l': pot_size_l,
            'recommendation': result
        }

    def get_lighting_recommendation(self, phase, lamp_power_w=None, lamp_distance_cm=None, light_hours=None, ppfd_measured=None, strain_type='photoperiodic'):
        """
        Gibt Beleuchtungsempfehlungen für eine Phase zurück
        
        Args:
            phase (str): Phasenname (z.B. 'vegetative_early')
            lamp_power_w (float, optional): Lampenleistung in Watt
            lamp_distance_cm (float, optional): Lampenabstand in cm
            light_hours (int, optional): Beleuchtungsstunden pro Tag
            ppfd_measured (float, optional): Gemessener PPFD-Wert in μmol/m²/s
            strain_type (str): Sortentyp ('autoflowering' oder 'photoperiodic')
        
        Returns:
            dict: Beleuchtungsempfehlungen mit PPFD-Berechnungen
        """
        # Validierung
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        # Strain-Typ validieren
        if strain_type not in ['autoflowering', 'photoperiodic']:
            strain_type = 'photoperiodic'  # Standardwert
        
        from .data.lighting_guidelines import get_lighting_recommendation
        
        result = get_lighting_recommendation(phase, lamp_power_w, lamp_distance_cm, light_hours, ppfd_measured, strain_type)
        
        if not result:
            return {'error': f'Keine Beleuchtungsempfehlung für Phase: {phase}'}
        
        return result
    
    def get_stress_management_recommendation(self, phase):
        """
        Gibt Stress-Management-Empfehlungen für eine bestimmte Phase zurück
        
        Args:
            phase (str): Aktuelle Phase
        
        Returns:
            dict: Stress-Management-Empfehlungen
        """
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        return get_stress_management_recommendation(phase)
    
    def get_lst_techniques(self):
        """
        Gibt alle verfügbaren LST-Techniken zurück
        
        Returns:
            dict: Alle LST-Techniken
        """
        return get_lst_techniques()
    
    def get_hst_techniques(self):
        """
        Gibt alle verfügbaren HST-Techniken zurück
        
        Returns:
            dict: Alle HST-Techniken
        """
        return get_hst_techniques()
    
    def get_training_timeline(self, phase):
        """
        Gibt eine Timeline für Training-Empfehlungen zurück
        
        Args:
            phase (str): Aktuelle Phase
        
        Returns:
            dict: Training-Timeline
        """
        if not validate_phase_name(phase):
            return {'error': f'Ungültige Phase: {phase}'}
        
        return get_training_timeline(phase)
    
    # Neue Methoden für interaktive Features
    
    def get_phase_checklist_template(self, phase_name):
        """
        Gibt eine Vorlage für Phase-spezifische Checklisten zurück
        
        Args:
            phase_name (str): Name der Phase
        
        Returns:
            list: Liste von Checklist-Items
        """
        checklist_templates = {
            'germination': [
                'Samen in feuchtes Substrat legen',
                'Temperatur auf 22-26°C einstellen',
                'Luftfeuchtigkeit auf 70-80% halten',
                'Substrat feucht aber nicht nass halten',
                'Keimung nach 3-7 Tagen überprüfen'
            ],
            'vegetative_early': [
                'Lichtzyklus auf 18/6 einstellen',
                'VPD auf 0.8-1.2 kPa optimieren',
                'Bio Grow Dünger einschleichen (1ml/L)',
                'Wurzelbildung mit Root Juice fördern',
                'Pflanzenabstand überprüfen',
                'Lüftung für frische Luft sicherstellen'
            ],
            'vegetative_middle': [
                'Bio Grow auf 3-4ml/L erhöhen',
                'Top Max für Blüte-Vorbereitung hinzufügen',
                'LST-Training beginnen',
                'VPD auf 1.0-1.4 kPa optimieren',
                'Pflanzenhöhe und -breite kontrollieren',
                'Heaven für bessere Nährstoffaufnahme verwenden'
            ],
            'vegetative_late': [
                'Bio Bloom einschleichen (1ml/L)',
                'Top Max auf 2ml/L erhöhen',
                'LST-Training fortsetzen',
                'VPD auf 1.2-1.6 kPa optimieren',
                'Pflanzenform für Blüte vorbereiten',
                'Lichtabstand an Pflanzenhöhe anpassen'
            ],
            'flowering_early': [
                'Lichtzyklus auf 12/12 umstellen (nur bei photoperiodic)',
                'Bio Bloom auf 2-3ml/L erhöhen',
                'Bio Grow auf 2ml/L reduzieren',
                'Top Max auf 3ml/L erhöhen',
                'VPD auf 1.4-1.8 kPa optimieren',
                'LST-Training beenden',
                'Blütenbildung beobachten'
            ],
            'flowering_middle': [
                'Bio Bloom auf 4ml/L erhöhen',
                'Bio Grow auf 1ml/L reduzieren',
                'Top Max auf 4ml/L erhöhen',
                'VPD auf 1.6-2.0 kPa optimieren',
                'Blütenentwicklung dokumentieren',
                'Trichome-Entwicklung überprüfen',
                'Lüftung für Geruchskontrolle verstärken'
            ],
            'flowering_late': [
                'Bio Bloom auf 2ml/L reduzieren',
                'Top Max auf 2ml/L reduzieren',
                'VPD auf 1.4-1.6 kPa optimieren',
                'Trichome-Farbe und -Reife prüfen',
                'Ernte-Zeitpunkt bestimmen',
                'Flush-Vorbereitung beginnen'
            ]
        }
        
        return checklist_templates.get(phase_name, [
            'Phase-spezifische Aufgaben überprüfen',
            'Umgebungsbedingungen optimieren',
            'Dünger-Dosierung anpassen',
            'Pflanzenentwicklung dokumentieren'
        ])
    
    def get_phase_warnings(self, plant, current_phase):
        """
        Generiert automatische Warnungen basierend auf Phase und Pflanzenzustand
        
        Args:
            plant (dict): Pflanzendaten
            current_phase (dict): Aktuelle Phasen-Informationen
        
        Returns:
            list: Liste von Warnungen
        """
        warnings = []
        
        # Phase-spezifische Warnungen
        phase_name = current_phase.get('sub_phase', '')
        days_in_phase = current_phase.get('sub_stage_day', 0)
        
        # Warnungen für kritische Zeitpunkte
        if phase_name == 'vegetative_late' and days_in_phase >= 5:
            warnings.append({
                'type': 'phase_transition',
                'message': 'Blütephase beginnt bald - Lichtzyklus vorbereiten!',
                'level': 'warning'
            })
        
        if phase_name == 'flowering_early' and days_in_phase <= 3:
            warnings.append({
                'type': 'phase_start',
                'message': 'Blütephase hat begonnen - Dünger anpassen!',
                'level': 'info'
            })
        
        if phase_name == 'flowering_middle' and days_in_phase >= 20:
            warnings.append({
                'type': 'trichome_check',
                'message': 'Trichome-Entwicklung überprüfen - Ernte nähert sich!',
                'level': 'warning'
            })
        
        # VPD-basierte Warnungen (falls VPD-Daten verfügbar)
        if hasattr(plant, 'vpd_current') and plant.vpd_current:
            vpd = float(plant.vpd_current)
            if vpd < 0.5:
                warnings.append({
                    'type': 'vpd_low',
                    'message': f'VPD zu niedrig ({vpd:.1f} kPa) - Luftfeuchtigkeit reduzieren!',
                    'level': 'warning'
                })
            elif vpd > 2.5:
                warnings.append({
                    'type': 'vpd_high',
                    'message': f'VPD zu hoch ({vpd:.1f} kPa) - Luftfeuchtigkeit erhöhen!',
                    'level': 'warning'
                })
        
        # Temperatur-basierte Warnungen (falls verfügbar)
        if hasattr(plant, 'temperature') and plant.temperature:
            temp = float(plant.temperature)
            if temp < 18:
                warnings.append({
                    'type': 'temp_low',
                    'message': f'Temperatur zu niedrig ({temp}°C) - Heizung prüfen!',
                    'level': 'warning'
                })
            elif temp > 30:
                warnings.append({
                    'type': 'temp_high',
                    'message': f'Temperatur zu hoch ({temp}°C) - Lüftung verstärken!',
                    'level': 'danger'
                })
        
        # Countdown-Warnungen für Phasenwechsel
        days_to_next = current_phase.get('days_to_next', 0)
        if days_to_next <= 1:
            next_phase = current_phase.get('next_phase', '')
            warnings.append({
                'type': 'phase_countdown',
                'message': f'Morgen beginnt: {next_phase}',
                'level': 'info'
            })
        elif days_to_next <= 3:
            next_phase = current_phase.get('next_phase', '')
            warnings.append({
                'type': 'phase_countdown',
                'message': f'In {days_to_next} Tagen beginnt: {next_phase}',
                'level': 'info'
            })
        
        return warnings
    
    def get_phase_notes_template(self, phase_name):
        """
        Gibt Vorlagen für Phase-Notizen zurück
        
        Args:
            phase_name (str): Name der Phase
        
        Returns:
            str: Vorlage für Phase-Notizen
        """
        note_templates = {
            'germination': 'Keimung: Samen zeigen sich nach X Tagen. Substrat-Feuchtigkeit: [ ]. Temperatur: [ ]°C. Besonderheiten: [ ]',
            'vegetative_early': 'Frühe Wachstumsphase: Pflanzenhöhe: [ ]cm. Neue Blätter: [ ]. Dünger-Reaktion: [ ]. Besonderheiten: [ ]',
            'vegetative_middle': 'Mittlere Wachstumsphase: Pflanzenhöhe: [ ]cm. LST-Training: [ ]. Dünger-Dosierung: [ ]. Besonderheiten: [ ]',
            'vegetative_late': 'Späte Wachstumsphase: Pflanzenform: [ ]. Blüte-Vorbereitung: [ ]. Dünger-Umstellung: [ ]. Besonderheiten: [ ]',
            'flowering_early': 'Frühe Blüte: Blütenbildung: [ ]. Lichtzyklus-Umstellung: [ ]. Dünger-Anpassung: [ ]. Besonderheiten: [ ]',
            'flowering_middle': 'Mittlere Blüte: Blütenentwicklung: [ ]. Trichome: [ ]. Geruch: [ ]. Besonderheiten: [ ]',
            'flowering_late': 'Späte Blüte: Trichome-Farbe: [ ]. Ernte-Vorbereitung: [ ]. Flush: [ ]. Besonderheiten: [ ]'
        }
        
        return note_templates.get(phase_name, f'{phase_name}: [Notizen hier eingeben...]')