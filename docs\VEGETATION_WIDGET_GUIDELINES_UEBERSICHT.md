# 🌱 Vegetation-Widget Guidelines Übersicht

**Erstellt:** 13.07.2025  
**Status:** Konzeptphase  
**Zweck:** Zentrale Guidelines-Übersicht für das Vegetations-Management Widget

---

## 📋 **Übersicht**

Das **Vegetations-Management Widget** kombiniert 6 Hauptmodule in einer einheitlichen Oberfläche:

1. **Substratdatenbank & Gießverhalten**
2. **Bewässerungs-Analyse & Planung**
3. **Klimamanagement (VPD/Temperatur/Luftfeuchte)**
4. **Nährstoff-Guidelines & EC/pH-Tracking**
5. **Stress-Management & Trainingszeitpunkt**
6. **Trainingszeitpunkt- & Eingriffs-Widget**

---

## 🗂️ **Guidelines-Dateien Übersicht**

### **✅ Bereits vorhandene Guidelines:**

| Modul | Guidelines-Datei | Status | Beschreibung |
|-------|------------------|--------|--------------|
| **Stress-Management** | `static/data/stress-guidelines.json` | ✅ Implementiert | Strain- & phasenspezifische Stresstoleranz |
| **Klimamanagement** | `static/data/klimamanagement-guidelines.json` | ✅ Implementiert | VPD, Temperatur, Luftfeuchte je Phase |
| **VPD-Berechnung** | `phase_logic/data/vpd_guidelines.py` | ✅ Implementiert | VPD-Berechnungen und Zielwerte |

### **⏳ Geplante Guidelines:**

| Modul | Guidelines-Datei | Status | Beschreibung |
|-------|------------------|--------|--------------|
| **Substratdatenbank** | `static/data/substrates.json` | ⏳ Geplant | Substrat-spezifische Eigenschaften |
| **Trainings-Guidelines** | `static/data/training-guidelines.json` | ⏳ Geplant | Trainingszeitpunkte und Methoden |
| **Bewässerungs-Guidelines** | `static/data/watering-guidelines.json` | ⏳ Geplant | Phase-spezifische Gießempfehlungen |
| **Nährstoff-Guidelines** | `static/data/nutrient-guidelines.json` | ⏳ Geplant | EC/pH-Ziele je Phase und Strain |

---

## 📊 **Detaillierte Guidelines-Struktur**

### **1. Substratdatenbank (`substrates.json`)**

```json
{
  "substrates": [
    {
      "id": "biobizz_lightmix",
      "type": "soil",
      "name": "BioBizz Light Mix",
      "ph_range": [6.2, 6.6],
      "buffer_capacity": "mittel",
      "ec_initial": 0.4,
      "drain_target_ec": [1.2, 1.8],
      "recommended_frequency": "alle 3–5 Tage",
      "drain_percentage": "5–15%",
      "notes": [
        "Reagiert empfindlich auf Überwässerung",
        "Nicht vorgedüngt – Zusätze ab Woche 1 empfohlen"
      ],
      "suitable_for": ["autoflower", "photoperiod"]
    },
    {
      "id": "coco_coir",
      "type": "coco",
      "name": "Coco Coir",
      "ph_range": [5.8, 6.2],
      "buffer_capacity": "niedrig",
      "ec_initial": 0.0,
      "drain_target_ec": [1.5, 2.2],
      "recommended_frequency": "täglich bis alle 2 Tage",
      "drain_percentage": "10–20%",
      "notes": [
        "Höhere EC-Werte als Erde",
        "Regelmäßige Bewässerung wichtig",
        "pH 5.8–6.2 optimal"
      ],
      "suitable_for": ["autoflower", "photoperiod"]
    }
  ]
}
```

### **2. Trainings-Guidelines (`training-guidelines.json`)**

```json
{
  "trainingGuidelines": {
    "phases": [
      {
        "phase": "vegetative_early",
        "description": "Frühe Vegetation – sanfte Eingriffe",
        "autoflower": {
          "allowed_methods": ["leichte LST"],
          "forbidden_methods": ["Topping", "HST", "Fimming"],
          "optimal_nodes": [3, 4],
          "stress_tolerance": "niedrig",
          "recovery_time": "2–3 Tage"
        },
        "photoperiod": {
          "allowed_methods": ["LST", "leichte Entlaubung"],
          "forbidden_methods": ["HST"],
          "optimal_nodes": [4, 5],
          "stress_tolerance": "mittel",
          "recovery_time": "3–5 Tage"
        }
      },
      {
        "phase": "vegetative_middle",
        "description": "Mittlere Vegetation – aktive Formung",
        "autoflower": {
          "allowed_methods": ["LST", "leichte Entlaubung"],
          "forbidden_methods": ["Topping", "HST"],
          "optimal_nodes": [4, 6],
          "stress_tolerance": "mittel",
          "recovery_time": "3–4 Tage"
        },
        "photoperiod": {
          "allowed_methods": ["LST", "Topping", "Entlaubung", "HST"],
          "forbidden_methods": [],
          "optimal_nodes": [5, 7],
          "stress_tolerance": "hoch",
          "recovery_time": "5–7 Tage"
        }
      }
    ]
  }
}
```

### **3. Bewässerungs-Guidelines (`watering-guidelines.json`)**

```json
{
  "wateringGuidelines": {
    "phases": [
      {
        "phase": "germination",
        "description": "Keimungsphase – minimale Bewässerung",
        "autoflower": {
          "frequency": "alle 2–3 Tage",
          "amount_per_pot_liter": 0.05,
          "drain_target": "0%",
          "ph_range": [6.0, 6.5],
          "ec_range": [0.0, 0.2],
          "notes": [
            "Substrat feucht, aber nicht nass halten",
            "Keine Nährstoffe in ersten 7 Tagen"
          ]
        },
        "photoperiod": {
          "frequency": "alle 2–3 Tage",
          "amount_per_pot_liter": 0.05,
          "drain_target": "0%",
          "ph_range": [6.0, 6.5],
          "ec_range": [0.0, 0.2],
          "notes": [
            "Ähnlich wie Autoflower",
            "Längere Keimungszeit möglich"
          ]
        }
      },
      {
        "phase": "vegetative_early",
        "description": "Frühe Vegetation – moderate Bewässerung",
        "autoflower": {
          "frequency": "alle 3–4 Tage",
          "amount_per_pot_liter": 0.1,
          "drain_target": "5–10%",
          "ph_range": [6.2, 6.6],
          "ec_range": [0.4, 0.8],
          "notes": [
            "Langsam steigern",
            "Topfgewicht beobachten"
          ]
        },
        "photoperiod": {
          "frequency": "alle 3–5 Tage",
          "amount_per_pot_liter": 0.12,
          "drain_target": "10–15%",
          "ph_range": [6.2, 6.6],
          "ec_range": [0.6, 1.0],
          "notes": [
            "Robustere Bewässerung möglich",
            "Längere Trocknungszyklen"
          ]
        }
      }
    ]
  }
}
```

### **4. Nährstoff-Guidelines (`nutrient-guidelines.json`)**

```json
{
  "nutrientGuidelines": {
    "phases": [
      {
        "phase": "germination",
        "description": "Keimungsphase – keine Nährstoffe",
        "autoflower": {
          "ec_target": [0.0, 0.2],
          "ph_target": [6.0, 6.5],
          "nutrients": {
            "grow": 0,
            "bloom": 0,
            "top_max": 0,
            "cal_mag": 0
          },
          "notes": [
            "Keine Nährstoffe in ersten 7 Tagen",
            "Reines Wasser oder sehr schwache Lösung"
          ]
        },
        "photoperiod": {
          "ec_target": [0.0, 0.2],
          "ph_target": [6.0, 6.5],
          "nutrients": {
            "grow": 0,
            "bloom": 0,
            "top_max": 0,
            "cal_mag": 0
          },
          "notes": [
            "Ähnlich wie Autoflower",
            "Längere Nährstoffpause möglich"
          ]
        }
      },
      {
        "phase": "vegetative_early",
        "description": "Frühe Vegetation – leichte Nährstoffgabe",
        "autoflower": {
          "ec_target": [0.4, 0.8],
          "ph_target": [6.2, 6.6],
          "nutrients": {
            "grow": 1,
            "bloom": 0,
            "top_max": 0,
            "cal_mag": 0.5
          },
          "notes": [
            "Langsam mit Nährstoffen beginnen",
            "Cal-Mag bei Bedarf"
          ]
        },
        "photoperiod": {
          "ec_target": [0.6, 1.0],
          "ph_target": [6.2, 6.6],
          "nutrients": {
            "grow": 1.5,
            "bloom": 0,
            "top_max": 0,
            "cal_mag": 0.5
          },
          "notes": [
            "Höhere Nährstoffgabe möglich",
            "Stickstoff-Fokus"
          ]
        }
      }
    ]
  }
}
```

---

## 🔧 **API-Integration**

### **Backend-Routes für Vegetation-Widget:**

```python
# Substrat-Management
GET /api/substrates                    # Alle Substrate
GET /api/substrates/<id>               # Einzelnes Substrat
POST /api/substrates/set/<plant_id>    # Substrat für Pflanze setzen

# Trainings-Management
GET /api/training/allowed-methods      # Erlaubte Trainingsmethoden
POST /api/training/analyse             # Trainingsanalyse
GET /api/training/history/<plant_id>   # Trainingsverlauf

# Bewässerungs-Management
POST /api/watering/analyse             # Bewässerungsanalyse
GET /api/watering/recommendation       # Gießempfehlung
GET /api/watering/history/<plant_id>   # Gießverlauf

# Nährstoff-Management
POST /api/nutrients/analyse            # Nährstoffanalyse
GET /api/nutrients/recommendations     # Nährstoffempfehlungen
GET /api/nutrients/history/<plant_id>  # Nährstoffverlauf

# Stress-Management
POST /api/stress/analyse               # Stressanalyse
GET /api/stress/recommendations        # Stressempfehlungen
GET /api/stress/history/<plant_id>     # Stressverlauf
```

### **Frontend-Widget-Struktur:**

```javascript
class VegetationManagementWidget {
    constructor() {
        this.substrateManager = new SubstrateManager();
        this.trainingManager = new TrainingManager();
        this.wateringManager = new WateringManager();
        this.nutrientManager = new NutrientManager();
        this.stressManager = new StressManager();
        this.climateManager = new ClimateManager();
    }

    async initialize(plantId) {
        // Alle Guidelines laden
        await this.loadAllGuidelines();
        
        // Pflanzendaten laden
        await this.loadPlantData(plantId);
        
        // Widget initialisieren
        this.setupTabs();
        this.setupEventListeners();
    }

    async loadAllGuidelines() {
        // Alle Guidelines-Dateien parallel laden
        const guidelines = await Promise.all([
            this.loadGuidelines('substrates'),
            this.loadGuidelines('training'),
            this.loadGuidelines('watering'),
            this.loadGuidelines('nutrients'),
            this.loadGuidelines('stress'),
            this.loadGuidelines('klimamanagement')
        ]);
        
        return guidelines;
    }
}
```

---

## 📊 **Tab-Struktur Details**

### **Tab 1: 📊 Übersicht**
- **Phasen-Status** mit Fortschrittsbalken
- **Wachstumsdaten** (Höhe, Nodes, etc.)
- **Aktuelle Empfehlungen** aus allen Modulen
- **Warnungen** und kritische Werte
- **Schnellaktionen** für häufige Aufgaben

### **Tab 2: 💧 Gießen & Klima**
- **Bewässerungsplan** mit nächstem Gießtermin
- **VPD-Optimierung** mit Live-Werten
- **Klima-Daten** (Temperatur, Luftfeuchte)
- **Substrat-Status** (Feuchtigkeit, Gewicht)
- **Drain-Analyse** mit EC/pH-Werten

### **Tab 3: 🌿 Nährstoffe**
- **EC/pH-Tracking** mit Zielbereichen
- **Dünger-Empfehlungen** je Phase
- **Nährstoff-Verlauf** mit Charts
- **Drain-Analyse** mit Korrekturen
- **Mangel-Erkennung** automatisch

### **Tab 4: ✂️ Training**
- **Trainingszeitpunkte** mit Empfehlungen
- **Eingriffs-Empfehlungen** je Strain/Phase
- **Stress-Management** mit Toleranz-Anzeige
- **Trainingsverlauf** mit Erfolgs-Tracking
- **Verbotene Methoden** Warnungen

### **Tab 5: 🔍 Früh-Indikatoren**
- **Wachstumsanalyse** mit Trends
- **Stress-Erkennung** automatisch
- **Prognosen** für nächste Phase
- **Anomalie-Erkennung** bei Abweichungen
- **Optimierungsvorschläge** proaktiv

---

## 🚀 **Implementierungs-Priorität**

### **Phase 1: Kern-Guidelines (Woche 1-2)**
1. **Substratdatenbank** (`substrates.json`) erstellen
2. **Trainings-Guidelines** (`training-guidelines.json`) definieren
3. **Bewässerungs-Guidelines** (`watering-guidelines.json`) erweitern
4. **Nährstoff-Guidelines** (`nutrient-guidelines.json`) vervollständigen

### **Phase 2: Backend-Integration (Woche 3-4)**
1. **API-Routes** für alle Module implementieren
2. **Guidelines-Loading** System entwickeln
3. **Datenbank-Integration** für Verläufe
4. **Berechnungs-Engine** für Empfehlungen

### **Phase 3: Frontend-Widget (Woche 5-6)**
1. **Tab-Struktur** implementieren
2. **Guidelines-Integration** in UI
3. **Live-Updates** und Charts
4. **Responsive Design** für Mobile

### **Phase 4: Testing & Optimierung (Woche 7-8)**
1. **End-to-End Testing** aller Module
2. **Performance-Optimierung**
3. **User-Feedback** Integration
4. **Dokumentation** vervollständigen

---

## 📋 **Nächste Schritte**

1. **Substratdatenbank** Guidelines erstellen
2. **Trainings-Guidelines** definieren
3. **Bewässerungs-Guidelines** erweitern
4. **Nährstoff-Guidelines** vervollständigen
5. **Backend-API** für alle Module implementieren
6. **Frontend-Widget** mit Tab-Struktur entwickeln

---

**Das Vegetations-Management Widget wird alle Wachstumsphase-Funktionen in einer übersichtlichen, guidelines-basierten Oberfläche zusammenfassen.** 