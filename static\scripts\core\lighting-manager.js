/**
 * 🌱 Zentrales Beleuchtungs-Management-System
 * 
 * Verwaltet die Beleuchtungsdaten und -einstellungen über alle Widgets hinweg
 * Ermöglicht Cross-Widget-Kommunikation und automatische Synchronisation
 */

class LightingManager {
    constructor() {
        this.subscribers = new Map();
        this.lightingData = new Map();
        this.plantStates = new Map();
        this.smartDimmingStates = new Map();
        this.energyData = new Map();
        
        // Event-Listener für Cross-Widget-Kommunikation
        this.setupEventListeners();
        
        console.log('🌱 LightingManager: Initialisiert');
    }
    
    /**
     * Event-Listener für Cross-Widget-Kommunikation einrichten
     */
    setupEventListeners() {
        // Beleuchtungsdaten-Update Event
        window.addEventListener('lightingDataUpdated', (event) => {
            this.handleLightingDataUpdate(event.detail);
        });
        
        // Pflanzenstatus-Update Event
        window.addEventListener('plantStateUpdated', (event) => {
            this.handlePlantStateUpdate(event.detail);
        });
        
        // Smart Dimming-Update Event
        window.addEventListener('smartDimmingUpdated', (event) => {
            this.handleSmartDimmingUpdate(event.detail);
        });
        
        // Energieverbrauch-Update Event
        window.addEventListener('energyDataUpdated', (event) => {
            this.handleEnergyDataUpdate(event.detail);
        });
    }
    
    /**
     * Widget als Subscriber registrieren
     */
    subscribe(widgetId, callback) {
        this.subscribers.set(widgetId, callback);
        console.log(`🌱 LightingManager: Widget ${widgetId} registriert`);
    }
    
    /**
     * Widget als Subscriber entfernen
     */
    unsubscribe(widgetId) {
        this.subscribers.delete(widgetId);
        console.log(`🌱 LightingManager: Widget ${widgetId} entfernt`);
    }
    
    /**
     * Beleuchtungsdaten für eine Pflanze speichern
     */
    setLightingData(plantId, data) {
        this.lightingData.set(plantId, {
            ...data,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
        
        // Event auslösen für andere Widgets
        this.triggerLightingDataUpdate(plantId, data);
        
        console.log(`🌱 LightingManager: Beleuchtungsdaten für Pflanze ${plantId} aktualisiert`);
    }
    
    /**
     * Beleuchtungsdaten für eine Pflanze abrufen
     */
    getLightingData(plantId) {
        return this.lightingData.get(plantId);
    }
    
    /**
     * Pflanzenstatus für eine Pflanze speichern
     */
    setPlantState(plantId, state) {
        this.plantStates.set(plantId, {
            ...state,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
        
        // Event auslösen für andere Widgets
        this.triggerPlantStateUpdate(plantId, state);
        
        console.log(`🌱 LightingManager: Pflanzenstatus für Pflanze ${plantId} aktualisiert`);
    }
    
    /**
     * Pflanzenstatus für eine Pflanze abrufen
     */
    getPlantState(plantId) {
        return this.plantStates.get(plantId);
    }
    
    /**
     * Smart Dimming Status für eine Pflanze speichern
     */
    setSmartDimmingState(plantId, state) {
        this.smartDimmingStates.set(plantId, {
            ...state,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
        
        // Event auslösen für andere Widgets
        this.triggerSmartDimmingUpdate(plantId, state);
        
        console.log(`🌱 LightingManager: Smart Dimming Status für Pflanze ${plantId} aktualisiert`);
    }
    
    /**
     * Smart Dimming Status für eine Pflanze abrufen
     */
    getSmartDimmingState(plantId) {
        return this.smartDimmingStates.get(plantId);
    }
    
    /**
     * Energieverbrauch-Daten für eine Pflanze speichern
     */
    setEnergyData(plantId, data) {
        this.energyData.set(plantId, {
            ...data,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
        
        // Event auslösen für andere Widgets
        this.triggerEnergyDataUpdate(plantId, data);
        
        console.log(`🌱 LightingManager: Energieverbrauch-Daten für Pflanze ${plantId} aktualisiert`);
    }
    
    /**
     * Energieverbrauch-Daten für eine Pflanze abrufen
     */
    getEnergyData(plantId) {
        return this.energyData.get(plantId);
    }
    
    /**
     * Alle Subscriber über Beleuchtungsdaten-Update informieren
     */
    triggerLightingDataUpdate(plantId, data) {
        const event = new CustomEvent('lightingDataUpdated', {
            detail: { plantId, data }
        });
        window.dispatchEvent(event);
        
        // Direkte Benachrichtigung aller Subscriber
        this.subscribers.forEach((callback, widgetId) => {
            try {
                callback('lightingDataUpdated', { plantId, data });
            } catch (error) {
                console.error(`🌱 LightingManager: Fehler beim Benachrichtigen von Widget ${widgetId}:`, error);
            }
        });
    }
    
    /**
     * Alle Subscriber über Pflanzenstatus-Update informieren
     */
    triggerPlantStateUpdate(plantId, state) {
        const event = new CustomEvent('plantStateUpdated', {
            detail: { plantId, state }
        });
        window.dispatchEvent(event);
        
        // Direkte Benachrichtigung aller Subscriber
        this.subscribers.forEach((callback, widgetId) => {
            try {
                callback('plantStateUpdated', { plantId, state });
            } catch (error) {
                console.error(`🌱 LightingManager: Fehler beim Benachrichtigen von Widget ${widgetId}:`, error);
            }
        });
    }
    
    /**
     * Alle Subscriber über Smart Dimming-Update informieren
     */
    triggerSmartDimmingUpdate(plantId, state) {
        const event = new CustomEvent('smartDimmingUpdated', {
            detail: { plantId, state }
        });
        window.dispatchEvent(event);
        
        // Direkte Benachrichtigung aller Subscriber
        this.subscribers.forEach((callback, widgetId) => {
            try {
                callback('smartDimmingUpdated', { plantId, state });
            } catch (error) {
                console.error(`🌱 LightingManager: Fehler beim Benachrichtigen von Widget ${widgetId}:`, error);
            }
        });
    }
    
    /**
     * Alle Subscriber über Energieverbrauch-Update informieren
     */
    triggerEnergyDataUpdate(plantId, data) {
        const event = new CustomEvent('energyDataUpdated', {
            detail: { plantId, data }
        });
        window.dispatchEvent(event);
        
        // Direkte Benachrichtigung aller Subscriber
        this.subscribers.forEach((callback, widgetId) => {
            try {
                callback('energyDataUpdated', { plantId, data });
            } catch (error) {
                console.error(`🌱 LightingManager: Fehler beim Benachrichtigen von Widget ${widgetId}:`, error);
            }
        });
    }
    
    /**
     * Event-Handler für Beleuchtungsdaten-Updates
     */
    handleLightingDataUpdate(detail) {
        const { plantId, data } = detail;
        this.lightingData.set(plantId, {
            ...data,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
    }
    
    /**
     * Event-Handler für Pflanzenstatus-Updates
     */
    handlePlantStateUpdate(detail) {
        const { plantId, state } = detail;
        this.plantStates.set(plantId, {
            ...state,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
    }
    
    /**
     * Event-Handler für Smart Dimming-Updates
     */
    handleSmartDimmingUpdate(detail) {
        const { plantId, state } = detail;
        this.smartDimmingStates.set(plantId, {
            ...state,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
    }
    
    /**
     * Event-Handler für Energieverbrauch-Updates
     */
    handleEnergyDataUpdate(detail) {
        const { plantId, data } = detail;
        this.energyData.set(plantId, {
            ...data,
            timestamp: new Date().toISOString(),
            lastUpdate: Date.now()
        });
    }
    
    /**
     * Automatische Beleuchtungsanpassung basierend auf Pflanzenwachstum
     */
    async autoAdjustLighting(plantId) {
        const plantState = this.getPlantState(plantId);
        const currentLighting = this.getLightingData(plantId);
        
        if (!plantState || !currentLighting) {
            console.warn(`🌱 LightingManager: Keine Daten für Pflanze ${plantId} verfügbar`);
            return null;
        }
        
        const adjustments = this.calculateAutoAdjustments(plantState, currentLighting);
        
        if (adjustments) {
            // Anpassungen anwenden
            const updatedLighting = this.applyAdjustments(currentLighting, adjustments);
            
            // Aktualisierte Daten speichern
            this.setLightingData(plantId, updatedLighting);
            
            // Anpassungen in localStorage speichern
            this.saveAutoAdjustments(plantId, adjustments);
            
            console.log(`🌱 LightingManager: Automatische Anpassungen für Pflanze ${plantId} angewendet:`, adjustments);
            
            return adjustments;
        }
        
        return null;
    }
    
    /**
     * Automatische Anpassungen berechnen
     */
    calculateAutoAdjustments(plantState, currentLighting) {
        const adjustments = {
            ppfd_adjustment: 0,
            light_hours_adjustment: 0,
            color_temp_adjustment: 0,
            reason: '',
            confidence: 0
        };
        
        const currentPhase = plantState.phase;
        const currentPPFD = currentLighting.current?.ppfd_calculated || 0;
        const currentHours = currentLighting.current?.light_hours || 12;
        const currentTemp = currentLighting.current?.color_temperature_k || 3000;
        
        // Phase-spezifische Anpassungen
        switch (currentPhase) {
            case 'vegetative_early':
                if (currentPPFD < 300) {
                    adjustments.ppfd_adjustment = Math.min(100, 300 - currentPPFD);
                    adjustments.reason = 'PPFD zu niedrig für frühe Vegetation';
                    adjustments.confidence = 0.9;
                }
                break;
                
            case 'vegetative_middle':
                if (currentPPFD < 450) {
                    adjustments.ppfd_adjustment = Math.min(150, 450 - currentPPFD);
                    adjustments.reason = 'PPFD zu niedrig für mittlere Vegetation';
                    adjustments.confidence = 0.8;
                }
                break;
                
            case 'vegetative_late':
                if (currentPPFD < 550) {
                    adjustments.ppfd_adjustment = Math.min(200, 550 - currentPPFD);
                    adjustments.reason = 'PPFD zu niedrig für späte Vegetation';
                    adjustments.confidence = 0.7;
                }
                break;
                
            case 'flowering_early':
                if (currentPPFD < 650) {
                    adjustments.ppfd_adjustment = Math.min(150, 650 - currentPPFD);
                    adjustments.reason = 'PPFD zu niedrig für frühe Blüte';
                    adjustments.confidence = 0.8;
                }
                if (currentHours > 12) {
                    adjustments.light_hours_adjustment = -2;
                    adjustments.reason += '; Photoperiode für Blüte anpassen';
                }
                break;
                
            case 'flowering_middle':
                if (currentPPFD < 750) {
                    adjustments.ppfd_adjustment = Math.min(200, 750 - currentPPFD);
                    adjustments.reason = 'PPFD zu niedrig für mittlere Blüte';
                    adjustments.confidence = 0.9;
                }
                break;
                
            case 'flowering_late':
                if (currentPPFD > 800) {
                    adjustments.ppfd_adjustment = -100;
                    adjustments.reason = 'PPFD zu hoch für späte Blüte';
                    adjustments.confidence = 0.7;
                }
                break;
                
            case 'flush':
                if (currentPPFD > 600) {
                    adjustments.ppfd_adjustment = -200;
                    adjustments.reason = 'PPFD zu hoch für Flush-Phase';
                    adjustments.confidence = 0.9;
                }
                if (currentHours > 10) {
                    adjustments.light_hours_adjustment = -2;
                    adjustments.reason += '; Beleuchtungsstunden für Flush reduzieren';
                }
                break;
        }
        
        // Nur Anpassungen mit hoher Konfidenz zurückgeben
        return adjustments.confidence > 0.6 ? adjustments : null;
    }
    
    /**
     * Anpassungen auf aktuelle Beleuchtungsdaten anwenden
     */
    applyAdjustments(currentLighting, adjustments) {
        const updatedLighting = JSON.parse(JSON.stringify(currentLighting));
        
        if (adjustments.ppfd_adjustment !== 0) {
            updatedLighting.current.ppfd_calculated = Math.max(0, 
                (updatedLighting.current.ppfd_calculated || 0) + adjustments.ppfd_adjustment
            );
        }
        
        if (adjustments.light_hours_adjustment !== 0) {
            updatedLighting.current.light_hours = Math.max(6, Math.min(18,
                (updatedLighting.current.light_hours || 12) + adjustments.light_hours_adjustment
            ));
        }
        
        if (adjustments.color_temp_adjustment !== 0) {
            updatedLighting.current.color_temperature_k = Math.max(2700, Math.min(6500,
                (updatedLighting.current.color_temperature_k || 3000) + adjustments.color_temp_adjustment
            ));
        }
        
        // Anpassungs-Metadaten hinzufügen
        updatedLighting.autoAdjustment = {
            timestamp: new Date().toISOString(),
            adjustments: adjustments,
            applied: true
        };
        
        return updatedLighting;
    }
    
    /**
     * Automatische Anpassungen speichern
     */
    saveAutoAdjustments(plantId, adjustments) {
        const history = JSON.parse(localStorage.getItem(`auto_adjustments_${plantId}`) || '[]');
        history.push({
            ...adjustments,
            timestamp: new Date().toISOString()
        });
        
        // Nur die letzten 50 Anpassungen behalten
        if (history.length > 50) {
            history.splice(0, history.length - 50);
        }
        
        localStorage.setItem(`auto_adjustments_${plantId}`, JSON.stringify(history));
    }
    
    /**
     * Alle Beleuchtungsdaten für eine Pflanze abrufen
     */
    getAllLightingData(plantId) {
        return {
            lighting: this.getLightingData(plantId),
            plantState: this.getPlantState(plantId),
            smartDimming: this.getSmartDimmingState(plantId),
            energy: this.getEnergyData(plantId)
        };
    }
    
    /**
     * Status-Übersicht für alle Pflanzen
     */
    getStatusOverview() {
        const overview = {
            totalPlants: this.lightingData.size,
            activePlants: 0,
            smartDimmingActive: 0,
            autoAdjustmentsToday: 0,
            totalEnergyConsumption: 0
        };
        
        const today = new Date().toDateString();
        
        this.lightingData.forEach((data, plantId) => {
            if (data.lastUpdate > Date.now() - 24 * 60 * 60 * 1000) {
                overview.activePlants++;
            }
            
            const smartDimming = this.getSmartDimmingState(plantId);
            if (smartDimming?.enabled) {
                overview.smartDimmingActive++;
            }
            
            const energy = this.getEnergyData(plantId);
            if (energy?.dailyConsumption) {
                overview.totalEnergyConsumption += energy.dailyConsumption;
            }
            
            // Auto-Adjustments heute zählen
            const adjustments = JSON.parse(localStorage.getItem(`auto_adjustments_${plantId}`) || '[]');
            const todayAdjustments = adjustments.filter(adj => 
                new Date(adj.timestamp).toDateString() === today
            );
            overview.autoAdjustmentsToday += todayAdjustments.length;
        });
        
        return overview;
    }
    
    /**
     * Debug-Informationen ausgeben
     */
    debug() {
        console.log('🌱 LightingManager Debug Info:');
        console.log('Subscribers:', this.subscribers.size);
        console.log('Plants with lighting data:', this.lightingData.size);
        console.log('Plants with plant state:', this.plantStates.size);
        console.log('Plants with smart dimming:', this.smartDimmingStates.size);
        console.log('Plants with energy data:', this.energyData.size);
        console.log('Status Overview:', this.getStatusOverview());
    }
}

// Globale Instanz erstellen
window.lightingManager = new LightingManager();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LightingManager;
} 