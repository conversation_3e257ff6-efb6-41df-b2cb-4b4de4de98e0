#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPD-Richtlinien für das Grow-Tagebuch
Enthält alle VPD-bezogenen Berechnungen und Empfehlungen
"""

# VPD-Richtlinien für verschiedene Phasen
VPD_GUIDELINES = {
    'germination': {
        'range': '0.4 – 0.8 kPa',
        'optimal': '0.6 kPa',
        'description': 'Sehr niedrige VPD für empfindliche Jungpflanzen',
        'temperature_range': {'min': 22, 'max': 26},
        'humidity_range': {'min': 70, 'max': 80}
    },
    'vegetative_early': {
        'range': '0.4 – 0.8 kPa',
        'optimal': '0.6 kPa',
        'description': 'Niedrige VPD für sanftes Wachstum',
        'temperature_range': {'min': 22, 'max': 26},
        'humidity_range': {'min': 70, 'max': 80}
    },
    'vegetative_middle': {
        'range': '0.8 – 1.2 kPa',
        'optimal': '1.0 kPa',
        'description': 'Moderate VPD für starkes Wachstum',
        'temperature_range': {'min': 22, 'max': 28},
        'humidity_range': {'min': 60, 'max': 70}
    },
    'vegetative_late': {
        'range': '1.0 – 1.4 kPa',
        'optimal': '1.2 kPa',
        'description': 'Erhöhte VPD für Vorbereitung auf Blüte',
        'temperature_range': {'min': 22, 'max': 28},
        'humidity_range': {'min': 55, 'max': 65}
    },
    'flowering_early': {
        'range': '1.0 – 1.4 kPa',
        'optimal': '1.2 kPa',
        'description': 'Moderate VPD für Blütenbildung',
        'temperature_range': {'min': 20, 'max': 26},
        'humidity_range': {'min': 50, 'max': 60}
    },
    'flowering_middle': {
        'range': '1.54 – 1.73 kPa',
        'optimal': '1.64 kPa',
        'description': 'Höhere VPD für Trichombildung',
        'temperature_range': {'min': 20, 'max': 26},
        'humidity_range': {'min': 40, 'max': 50}
    },
    'flowering_late': {
        'range': '1.2 – 1.6 kPa',
        'optimal': '1.4 kPa',
        'description': 'Moderate VPD für Reifung',
        'temperature_range': {'min': 20, 'max': 26},
        'humidity_range': {'min': 40, 'max': 50}
    }
}

# VPD-Berechnungsfunktionen
def calculate_vpd(temperature, humidity):
    """
    Berechnet den VPD-Wert basierend auf Temperatur und Luftfeuchtigkeit
    
    Args:
        temperature (float): Temperatur in Celsius
        humidity (float): Luftfeuchtigkeit in Prozent (0-100)
    
    Returns:
        float: VPD-Wert in kPa
    """
    # Sättigungsdampfdruck bei gegebener Temperatur (Magnus-Formel)
    # e_s = 6.112 * exp((17.67 * T) / (T + 243.5))
    # T in Celsius, e_s in hPa
    
    # Magnus-Formel für Sättigungsdampfdruck
    e_s = 6.112 * (2.71828 ** ((17.67 * temperature) / (temperature + 243.5)))
    
    # Tatsächlicher Dampfdruck
    e_a = e_s * (humidity / 100.0)
    
    # VPD = e_s - e_a (in kPa)
    vpd = (e_s - e_a) / 10.0  # Konvertierung von hPa zu kPa
    
    return round(vpd, 2)

def get_vpd_status(vpd, phase):
    """
    Bewertet den VPD-Wert für eine bestimmte Phase
    
    Args:
        vpd (float): Berechneter VPD-Wert
        phase (str): Aktuelle Phase
    
    Returns:
        dict: Status-Informationen
    """
    if phase not in VPD_GUIDELINES:
        return {
            'status': 'unknown',
            'message': f'Keine VPD-Richtlinien für Phase: {phase}',
            'optimal_range': 'Unbekannt'
        }
    
    guidelines = VPD_GUIDELINES[phase]
    optimal = float(guidelines['optimal'].split()[0])
    range_str = guidelines['range']
    
    # Parse range (e.g., "0.4 – 0.8 kPa")
    range_parts = range_str.split('–')
    min_vpd = float(range_parts[0].strip())
    max_vpd = float(range_parts[1].split()[0].strip())
    
    if vpd < min_vpd:
        status = 'low'
        message = f'VPD zu niedrig ({vpd} kPa). Erhöhe Temperatur oder reduziere Luftfeuchtigkeit.'
    elif vpd > max_vpd:
        status = 'high'
        message = f'VPD zu hoch ({vpd} kPa). Reduziere Temperatur oder erhöhe Luftfeuchtigkeit.'
    else:
        status = 'optimal'
        message = f'VPD optimal ({vpd} kPa) für {phase}.'
    
    return {
        'status': status,
        'message': message,
        'optimal_range': range_str,
        'current_vpd': vpd,
        'optimal_vpd': optimal
    }

def get_vpd_recommendations(phase):
    """
    Gibt VPD-Empfehlungen für eine bestimmte Phase zurück
    
    Args:
        phase (str): Aktuelle Phase
    
    Returns:
        dict: VPD-Empfehlungen
    """
    if phase not in VPD_GUIDELINES:
        return None
    
    guidelines = VPD_GUIDELINES[phase]
    
    return {
        'phase': phase,
        'range': guidelines['range'],
        'optimal': guidelines['optimal'],
        'description': guidelines['description'],
        'temperature_range': guidelines['temperature_range'],
        'humidity_range': guidelines['humidity_range'],
        'recommendations': {
            'temperature': f"Temperatur: {guidelines['temperature_range']['min']}°C - {guidelines['temperature_range']['max']}°C",
            'humidity': f"Luftfeuchtigkeit: {guidelines['humidity_range']['min']}% - {guidelines['humidity_range']['max']}%",
            'vpd_target': f"VPD-Ziel: {guidelines['optimal']}"
        }
    } 