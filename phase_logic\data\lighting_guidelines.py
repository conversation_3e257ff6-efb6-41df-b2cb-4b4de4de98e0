# Beleuchtungs-Guidelines pro Sub-Phase (PPFD-basiert)
LIGHTING_GUIDELINES = {
    # Photoperiodic Guidelines (Standard)
    'germination': {
        'ppfd_target': 100,  # Photosynthetic Photon Flux Density in μmol/m²/s
        'ppfd_range': '50-150',
        'light_hours': 18,
        'intensity': 'Niedrig',
        'distance': '30-40 cm',
        'note': 'Keimlinge benötigen wenig Licht, aber lange Photoperiode für Wurzelbildung.'
    },
    'vegetative_early': {
        'ppfd_target': 200,
        'ppfd_range': '150-300',
        'light_hours': 18,
        'intensity': 'Mittel',
        'distance': '25-35 cm',
        'note': 'Jungpflanzen brauchen mehr Licht für kräftiges Wachstum.'
    },
    'vegetative_middle': {
        'ppfd_target': 400,
        'ppfd_range': '300-500',
        'light_hours': 18,
        'intensity': 'Hoch',
        'distance': '20-30 cm',
        'note': '<PERSON><PERSON> erfordert hohe Lichtintensität.'
    },
    'vegetative_late': {
        'ppfd_target': 600,
        'ppfd_range': '500-700',
        'light_hours': 18,
        'intensity': 'Sehr hoch',
        'distance': '15-25 cm',
        'note': 'Maximale Lichtintensität für finales Wachstum vor der Blüte.'
    },
    'flowering_early': {
        'ppfd_target': 700,
        'ppfd_range': '600-800',
        'light_hours': 12,
        'intensity': 'Sehr hoch',
        'distance': '15-25 cm',
        'note': 'Blüte-Start benötigt maximale Lichtintensität bei 12/12 Photoperiode.'
    },
    'flowering_middle': {
        'ppfd_target': 800,
        'ppfd_range': '700-900',
        'light_hours': 12,
        'intensity': 'Maximal',
        'distance': '10-20 cm',
        'note': 'Höchste PPFD für optimale Blütenbildung und Trichome-Entwicklung.'
    },
    'flowering_late': {
        'ppfd_target': 600,
        'ppfd_range': '500-700',
        'light_hours': 12,
        'intensity': 'Hoch',
        'distance': '15-25 cm',
        'note': 'Lichtintensität reduzieren für bessere Terpene-Entwicklung.'
    },
    'flush': {
        'ppfd_target': 400,
        'ppfd_range': '300-500',
        'light_hours': 12,
        'intensity': 'Mittel',
        'distance': '20-30 cm',
        'note': 'Reduzierte Lichtintensität für Stress-freie Ernte-Vorbereitung.'
    },
    
    # Autoflowering Guidelines (18/6 oder 20/4 Photoperiode)
    'germination_auto': {
        'ppfd_target': 100,
        'ppfd_range': '50-150',
        'light_hours': 18,
        'intensity': 'Niedrig',
        'distance': '30-40 cm',
        'note': 'Autoflowering-Keimlinge: Gleiche PPFD, aber 18/6 Photoperiode für bessere Entwicklung.'
    },
    'vegetative_early_auto': {
        'ppfd_target': 250,
        'ppfd_range': '200-350',
        'light_hours': 18,
        'intensity': 'Mittel',
        'distance': '25-35 cm',
        'note': 'Autoflowering-Veg: Höhere PPFD für schnelleres Wachstum in kürzerer Zeit.'
    },
    'vegetative_late_auto': {
        'ppfd_target': 500,
        'ppfd_range': '400-600',
        'light_hours': 18,
        'intensity': 'Hoch',
        'distance': '20-30 cm',
        'note': 'Autoflowering-Veg: Optimale PPFD für maximale Entwicklung vor Blüte-Start.'
    },
    'flowering_early_auto': {
        'ppfd_target': 700,
        'ppfd_range': '600-800',
        'light_hours': 18,
        'intensity': 'Sehr hoch',
        'distance': '15-25 cm',
        'note': 'Autoflowering-Blüte: Maximale PPFD bei 18/6 für optimale Blütenbildung.'
    },
    'flowering_middle_auto': {
        'ppfd_target': 800,
        'ppfd_range': '700-900',
        'light_hours': 18,
        'intensity': 'Maximal',
        'distance': '10-20 cm',
        'note': 'Autoflowering-Blüte: Höchste PPFD für beste Trichome-Entwicklung.'
    },
    'flowering_late_auto': {
        'ppfd_target': 600,
        'ppfd_range': '500-700',
        'light_hours': 18,
        'intensity': 'Hoch',
        'distance': '15-25 cm',
        'note': 'Autoflowering-Blüte: Reduzierte PPFD für Terpene-Entwicklung.'
    }
}

def get_lighting_recommendation(phase, lamp_power_w=None, lamp_distance_cm=None, light_hours=None, ppfd_measured=None, strain_type='photoperiodic'):
    """
    Gibt die Beleuchtungsempfehlung für eine Phase zurück.
    Args:
        phase (str): Sub-Phase-Key (z. B. 'vegetative_early')
        lamp_power_w (float, optional): Lampenleistung in Watt
        lamp_distance_cm (float, optional): Lampenabstand in cm
        light_hours (int, optional): Beleuchtungsstunden pro Tag
        ppfd_measured (float, optional): Gemessener PPFD-Wert in μmol/m²/s
        strain_type (str): Sortentyp ('autoflowering' oder 'photoperiodic')
    Returns:
        dict: PPFD-Empfehlungen, berechnete Werte und Anpassungen
    """
    # Autoflowering-Phase-Key anpassen
    if strain_type == 'autoflowering':
        if phase == 'germination':
            phase_key = 'germination_auto'
        elif phase.startswith('vegetative_'):
            if 'early' in phase:
                phase_key = 'vegetative_early_auto'
            else:
                phase_key = 'vegetative_late_auto'
        elif phase.startswith('flowering_'):
            if 'early' in phase:
                phase_key = 'flowering_early_auto'
            elif 'middle' in phase:
                phase_key = 'flowering_middle_auto'
            else:
                phase_key = 'flowering_late_auto'
        else:
            phase_key = phase
    else:
        phase_key = phase
    
    rec = LIGHTING_GUIDELINES.get(phase_key, {}).copy()
    if not rec:
        return None
    
    result = {
        'phase': phase,
        'phase_key': phase_key,
        'strain_type': strain_type,
        'recommendations': rec,
        'calculated': {}
    }
    
    # Berechnete Werte hinzufügen
    if ppfd_measured:
        # Verwende gemessenen PPFD-Wert
        result['calculated'] = {
            'current_ppfd': ppfd_measured,
            'lamp_power_w': lamp_power_w,
            'lamp_distance_cm': lamp_distance_cm,
            'light_hours': light_hours,
            'ppfd_status': get_ppfd_status(ppfd_measured, rec['ppfd_target'])
        }
    elif lamp_power_w and lamp_distance_cm:
        # Schätze PPFD basierend auf Lampenleistung und Abstand
        estimated_ppfd = estimate_ppfd_from_power(lamp_power_w, lamp_distance_cm)
        
        result['calculated'] = {
            'current_ppfd': round(estimated_ppfd, 0),
            'lamp_power_w': lamp_power_w,
            'lamp_distance_cm': lamp_distance_cm,
            'light_hours': light_hours,
            'ppfd_status': get_ppfd_status(estimated_ppfd, rec['ppfd_target']),
            'is_estimated': True
        }
    
    return result

def get_ppfd_status(current_ppfd, target_ppfd):
    """
    Bewertet den aktuellen PPFD-Wert im Vergleich zum Ziel.
    Args:
        current_ppfd (float): Aktueller PPFD-Wert in μmol/m²/s
        target_ppfd (float): Ziel-PPFD-Wert in μmol/m²/s
    Returns:
        dict: Status und Empfehlungen
    """
    diff_percent = ((current_ppfd - target_ppfd) / target_ppfd) * 100
    
    if abs(diff_percent) <= 15:
        status = 'optimal'
        message = 'PPFD ist optimal für diese Phase'
        recommendation = 'Aktuelle Einstellungen beibehalten'
    elif diff_percent > 15:
        status = 'zu_hoch'
        message = f'PPFD ist {abs(diff_percent):.0f}% zu hoch'
        recommendation = 'Lampenabstand erhöhen oder Leistung reduzieren'
    else:
        status = 'zu_niedrig'
        message = f'PPFD ist {abs(diff_percent):.0f}% zu niedrig'
        recommendation = 'Lampenabstand verringern oder Leistung erhöhen'
    
    return {
        'status': status,
        'message': message,
        'recommendation': recommendation,
        'diff_percent': round(diff_percent, 1)
    }

def estimate_ppfd_from_power(lamp_power_w, lamp_distance_cm):
    """
    Schätzt PPFD basierend auf Lampenleistung und Abstand.
    Basierend auf typischen LED-Effizienzen und Abstandsgesetzen.
    Args:
        lamp_power_w (float): Lampenleistung in Watt
        lamp_distance_cm (float): Lampenabstand in cm
    Returns:
        float: Geschätzter PPFD-Wert in μmol/m²/s
    """
    # Typische LED-Effizienz: 2.5-3.0 μmol/J (Mikromol pro Joule)
    efficiency_umol_per_j = 2.7
    
    # Umrechnung: PPFD = (Leistung * Effizienz) / (Abstand² * π)
    # Vereinfachte Formel für typische LED-Panels
    ppfd = (lamp_power_w * efficiency_umol_per_j * 100) / (lamp_distance_cm * lamp_distance_cm)
    
    return ppfd

def calculate_optimal_distance(lamp_power_w, target_ppfd):
    """
    Berechnet den optimalen Lampenabstand für eine Ziel-PPFD.
    Args:
        lamp_power_w (float): Lampenleistung in Watt
        target_ppfd (float): Gewünschte PPFD in μmol/m²/s
    Returns:
        float: Optimaler Abstand in cm
    """
    efficiency_umol_per_j = 2.7
    
    # Umgekehrte PPFD-Formel
    optimal_distance = ((lamp_power_w * efficiency_umol_per_j * 100) / target_ppfd) ** 0.5
    
    return round(optimal_distance, 1) 