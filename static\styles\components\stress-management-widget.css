/* Stress-Management Widget Styles */
.stress-management-widget-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
    border: 2px solid #6f42c1;
    border-radius: 12px;
    padding: 0;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(111, 66, 193, 0.15);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stress-management-widget-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.25);
}

.stress-management-widget-header {
    background: linear-gradient(135deg, #6f42c1 0%, #8b5cf6 100%);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid #5a32a3;
}

.stress-management-widget-header i {
    font-size: 1.2rem;
    color: #e9d5ff;
}

.stress-management-widget-body {
    padding: 20px;
}

.stress-management-section-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 4px solid;
}

.stress-management-section-title.lst {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
}

.stress-management-section-title.hst {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
}

.stress-management-section-title.info {
    color: #6f42c1;
    background: rgba(111, 66, 193, 0.1);
    border-left-color: #6f42c1;
}

.stress-management-section-title.warning {
    color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
}

.stress-management-content {
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid rgba(111, 66, 193, 0.2);
}

.stress-management-content.lst {
    background: rgba(40, 167, 69, 0.05);
    border-color: rgba(40, 167, 69, 0.3);
}

.stress-management-content.hst {
    background: rgba(220, 53, 69, 0.05);
    border-color: rgba(220, 53, 69, 0.3);
}

.stress-management-content.info {
    background: rgba(111, 66, 193, 0.05);
    border-color: rgba(111, 66, 193, 0.3);
}

.stress-management-content.warning {
    background: rgba(255, 193, 7, 0.05);
    border-color: rgba(255, 193, 7, 0.3);
}

.stress-management-label {
    font-weight: 600;
    color: #495057;
    margin-right: 8px;
    min-width: 120px;
    display: inline-block;
}

.stress-management-value {
    color: #6f42c1;
    font-weight: 500;
}

.stress-management-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stress-management-status.recommended {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.stress-management-status.not-recommended {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}

.stress-management-status.careful {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.stress-management-techniques {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.stress-management-techniques li {
    padding: 8px 12px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    border-left: 3px solid #6f42c1;
    font-size: 0.9rem;
    color: #495057;
}

.stress-management-warnings {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.stress-management-warnings li {
    padding: 8px 12px;
    margin: 5px 0;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ffc107;
    font-size: 0.9rem;
    color: #856404;
}

.stress-management-timeline {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.stress-management-timeline-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(111, 66, 193, 0.2);
}

.stress-management-timeline-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    font-weight: bold;
}

.stress-management-timeline-icon.past {
    background: #6c757d;
}

.stress-management-timeline-icon.current {
    background: #6f42c1;
}

.stress-management-timeline-icon.future {
    background: #adb5bd;
}

.stress-management-timeline-content {
    flex: 1;
}

.stress-management-timeline-phase {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.stress-management-timeline-status {
    font-size: 0.8rem;
    color: #6c757d;
}

.stress-management-timeline-status.lst {
    color: #28a745;
}

.stress-management-timeline-status.hst {
    color: #dc3545;
}

.stress-management-timeline-status.none {
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stress-management-widget-header {
        font-size: 1rem;
        padding: 12px 15px;
    }
    
    .stress-management-widget-body {
        padding: 15px;
    }
    
    .stress-management-label {
        min-width: 100px;
        font-size: 0.9rem;
    }
    
    .stress-management-value {
        font-size: 0.9rem;
    }
    
    .stress-management-techniques li,
    .stress-management-warnings li {
        font-size: 0.85rem;
        padding: 6px 10px;
    }
}

/* Animation für Status-Badges */
.stress-management-status {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Hover-Effekte für Timeline-Items */
.stress-management-timeline-item:hover {
    background: rgba(111, 66, 193, 0.05);
    transform: translateX(5px);
    transition: all 0.3s ease;
}

/* Spezielle Styles für Training-Techniken */
.training-technique-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(111, 66, 193, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    transition: all 0.3s ease;
}

.training-technique-card:hover {
    background: rgba(111, 66, 193, 0.05);
    border-color: #6f42c1;
    transform: translateY(-2px);
}

.training-technique-name {
    font-weight: 600;
    color: #6f42c1;
    font-size: 1rem;
    margin-bottom: 5px;
}

.training-technique-description {
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.training-technique-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.85rem;
}

.training-technique-detail {
    color: #6c757d;
}

.training-technique-detail strong {
    color: #495057;
}

.training-technique-tips {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(111, 66, 193, 0.2);
}

.training-technique-tips h6 {
    color: #6f42c1;
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.training-technique-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.training-technique-tips li {
    font-size: 0.8rem;
    color: #6c757d;
    padding: 2px 0;
    position: relative;
    padding-left: 15px;
}

.training-technique-tips li:before {
    content: "•";
    color: #6f42c1;
    position: absolute;
    left: 0;
    font-weight: bold;
} 