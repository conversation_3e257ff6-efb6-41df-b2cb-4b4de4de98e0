"""
VPD Widget API Routes
Handles VPD (Vapor Pressure Deficit) optimization API endpoints
"""

from flask import Blueprint, request, jsonify
from phase_logic.widgets.vpd_logic import VPDLogic
import sqlite3
import json
from datetime import datetime

# Create blueprint
vpd_bp = Blueprint('vpd', __name__, url_prefix='/api/vpd')

@vpd_bp.route('/optimization/<phase>', methods=['GET'])
def vpd_optimization(phase):
    print("DEBUG: vpd_optimization() wurde mit save_entry:", request.args.get('save_entry'))
    """
    VPD-Optimierung für eine bestimmte Phase
    """
    try:
        # Parameter aus Query-String holen
        temperature = request.args.get('temperature', type=float, default=25.0)
        humidity = request.args.get('humidity', type=int, default=50)
        strain_type = request.args.get('strain_type', default='photoperiodic')
        leaf_temperature = request.args.get('leaf_temperature', type=float)
        
        # Plant ID aus Query-String holen (falls vorhanden)
        plant_id = request.args.get('plant_id')
        
        # Strain-Type aus Datenbank laden falls nicht übergeben und Plant ID vorhanden
        if strain_type == 'photoperiodic' and plant_id:
            try:
                from database_basic import GrowDiaryBasicDB
                db = GrowDiaryBasicDB()
                conn = db.get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT strain_type, strain FROM plants WHERE id = ?", (plant_id,))
                row = cursor.fetchone()
                if row:
                    # Erst prüfen ob strain_type explizit gesetzt ist
                    if row[0]:  # strain_type
                        strain_type_db = row[0].lower()
                        # Prüfe auf Autoflower-Werte im strain_type Feld
                        auto_type_keywords = ['auto', 'autoflower', 'autoflowering', 'automatic']
                        if any(keyword in strain_type_db for keyword in auto_type_keywords):
                            strain_type = 'autoflowering'
                        else:
                            strain_type = strain_type_db
                    # Dann nach Autoflower-Keywords in strain suchen
                    elif row[1]:  # strain
                        strain_text = row[1].lower()
                        auto_keywords = ['auto', 'autoflower', 'automatic', 'ruderalis']
                        if any(keyword in strain_text for keyword in auto_keywords):
                            strain_type = 'autoflowering'
            except Exception as e:
                pass  # Silently handle database errors
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # VPD-Optimierung mit Guidelines berechnen
        # NEU: Blatttemperatur an Logik übergeben, falls vorhanden
        if leaf_temperature is not None:
            result = vpd_logic.calculate_vpd_with_guidelines(phase, temperature, humidity, strain_type, leaf_temperature=leaf_temperature)
        else:
            result = vpd_logic.calculate_vpd_with_guidelines(phase, temperature, humidity, strain_type)
        
        # NEU: leaf_temperature im Ergebnis unter 'current' ergänzen
        if 'current' in result:
            result['current']['leaf_temperature'] = leaf_temperature
        
        # Automatisches Speichern des VPD-Eintrags nur wenn explizit gewünscht
        save_entry_raw = request.args.get('save_entry', default='false')
        save_entry = save_entry_raw.lower() == 'true'
        print(f"DEBUG: save_entry_raw='{save_entry_raw}', save_entry={save_entry}, plant_id={plant_id}, 'current' in result={'current' in result}")
        print(f"DEBUG: Bedingung wird geprüft: plant_id={bool(plant_id)}, 'current' in result={'current' in result}, save_entry={save_entry}")
        print(f"DEBUG: Vollständige Bedingung: {bool(plant_id)} AND {'current' in result} AND {save_entry} = {bool(plant_id) and 'current' in result and save_entry}")
        if plant_id and 'current' in result and save_entry:
            print("DEBUG: Bedingung ist TRUE - Eintrag wird gespeichert!")
            try:
                conn = sqlite3.connect('grow_diary_basic.db')
                cursor = conn.cursor()
                
                # VPD-Wert aus dem Ergebnis holen
                vpd_value = result['current']['vpd']
                
                cursor.execute("""
                    INSERT INTO vpd_entries 
                    (plant_id, entry_date, temperature, humidity, leaf_temperature, 
                     vpd_value, phase, strain_type, analysis_result, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    plant_id, 
                    datetime.now().isoformat(), 
                    temperature, 
                    humidity,
                    leaf_temperature, 
                    vpd_value, 
                    phase, 
                    strain_type,
                    json.dumps(result), 
                    f'Automatisch gespeichert beim VPD-Berechnen - {phase}'
                ))
                
                conn.commit()
                entry_id = cursor.lastrowid
                
                # Entry ID zum Ergebnis hinzufügen
                result['entry_id'] = entry_id
                result['entry_saved'] = True
                
                print(f"VPD-Eintrag automatisch gespeichert: ID {entry_id} für Pflanze {plant_id}")
                
            except Exception as e:
                print(f"Fehler beim automatischen Speichern des VPD-Eintrags: {e}")
                result['entry_saved'] = False
                result['save_error'] = str(e)
            finally:
                if 'conn' in locals():
                    conn.close()
        else:
            print("DEBUG: Bedingung ist FALSE - KEIN Eintrag wird gespeichert!")
            result['entry_saved'] = False
        
        return jsonify({
            'success': True,
            'phase': phase,
            **result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der VPD-Optimierung: {str(e)}'
        }), 500

@vpd_bp.route('/analyse', methods=['POST'])
def vpd_analyse():
    """
    Erweiterte VPD-Analyse mit Blatttemperatur und Optimierungsempfehlungen
    """
    try:
        data = request.get_json()
        
        # Validierung der Pflichtfelder
        required_fields = ['temperature', 'humidity', 'phase', 'strain_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'message': f'Pflichtfeld fehlt: {field}'}), 400
        
        # Werte extrahieren
        temperature = float(data['temperature'])
        humidity = int(data['humidity'])
        phase = data['phase']
        strain_type = data['strain_type']
        leaf_temperature = data.get('leaf_temperature')  # Optional
        plant_id = data.get('plant_id')
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # Erweiterte VPD-Analyse durchführen
        analysis = vpd_logic.perform_advanced_analysis(
            phase, temperature, humidity, strain_type, leaf_temperature
        )
        
        # Eintrag in Datenbank speichern nur wenn explizit gewünscht
        save_entry = data.get('save_entry', False)
        if plant_id and save_entry:
            print(f"DEBUG: /analyse Route - Speichere Eintrag für plant_id={plant_id}, save_entry={save_entry}")
            try:
                conn = sqlite3.connect('grow_diary_basic.db')
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO vpd_entries 
                    (plant_id, entry_date, temperature, humidity, leaf_temperature, 
                     vpd_value, phase, strain_type, analysis_result, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    plant_id, datetime.now().isoformat(), temperature, humidity,
                    leaf_temperature, analysis['vpd'], phase, strain_type,
                    json.dumps(analysis), data.get('notes', '')
                ))
                
                conn.commit()
                analysis['entry_id'] = cursor.lastrowid
                print(f"DEBUG: /analyse Route - Eintrag gespeichert: ID {analysis['entry_id']}")
                
            except Exception as e:
                print(f"Fehler beim Speichern des VPD-Eintrags: {e}")
            finally:
                conn.close()
        else:
            print(f"DEBUG: /analyse Route - KEIN Eintrag gespeichert: plant_id={plant_id}, save_entry={save_entry}")
        
        return jsonify({
            'success': True,
            'phase': phase,
            'strain_type': strain_type,
            'temperature': temperature,
            'humidity': humidity,
            'leaf_temperature': leaf_temperature,
            'analysis': analysis
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der VPD-Analyse: {str(e)}'
        }), 500

@vpd_bp.route('/targets', methods=['GET'])
def vpd_targets():
    """
    VPD-Zielbereiche für verschiedene Phasen und Strain-Typen
    """
    try:
        phase = request.args.get('phase', 'flowering_middle')
        strain_type = request.args.get('strain_type', 'photoperiodic')
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # Zielbereiche laden
        targets = vpd_logic.get_target_ranges(phase, strain_type)
        
        return jsonify({
            'success': True,
            'phase': phase,
            'strain_type': strain_type,
            'targets': targets
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der VPD-Ziele: {str(e)}'
        }), 500

@vpd_bp.route('/history/<plant_id>', methods=['GET'])
def vpd_history(plant_id):
    """
    VPD-Historie für eine Pflanze laden
    """
    try:
        limit = request.args.get('limit', 20, type=int)
        
        conn = sqlite3.connect('grow_diary_basic.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, entry_date, temperature, humidity, leaf_temperature, 
                   vpd_value, phase, strain_type, analysis_result, notes
            FROM vpd_entries 
            WHERE plant_id = ? 
            ORDER BY entry_date DESC 
            LIMIT ?
        """, (plant_id, limit))
        
        entries = cursor.fetchall()
        history = [{
            'id': entry[0],
            'entry_date': entry[1],
            'temperature': entry[2],
            'humidity': entry[3],
            'leaf_temperature': entry[4],
            'vpd_value': entry[5],
            'phase': entry[6],
            'strain_type': entry[7],
            'analysis_result': json.loads(entry[8]) if entry[8] else None,
            'notes': entry[9]
        } for entry in entries]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'plant_id': plant_id,
            'history': history,
            'count': len(history)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der VPD-Historie: {str(e)}'
        }), 500

@vpd_bp.route('/history/delete/<int:entry_id>', methods=['DELETE'])
def delete_vpd_entry(entry_id):
    """
    VPD-Eintrag löschen
    """
    try:
        conn = sqlite3.connect('grow_diary_basic.db')
        cursor = conn.cursor()
        
        # Erst prüfen ob der Eintrag existiert
        cursor.execute("SELECT id, plant_id FROM vpd_entries WHERE id = ?", (entry_id,))
        entry = cursor.fetchone()
        
        if not entry:
            return jsonify({
                'success': False,
                'message': f'VPD-Eintrag mit ID {entry_id} nicht gefunden'
            }), 404
        
        # Eintrag löschen
        cursor.execute("DELETE FROM vpd_entries WHERE id = ?", (entry_id,))
        conn.commit()
        
        deleted_count = cursor.rowcount
        
        conn.close()
        
        if deleted_count > 0:
            return jsonify({
                'success': True,
                'message': f'VPD-Eintrag {entry_id} erfolgreich gelöscht',
                'deleted_id': entry_id,
                'plant_id': entry[1]
            })
        else:
            return jsonify({
                'success': False,
                'message': f'VPD-Eintrag {entry_id} konnte nicht gelöscht werden'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Löschen des VPD-Eintrags: {str(e)}'
        }), 500

@vpd_bp.route('/phases', methods=['GET'])
def vpd_phases():
    """
    Verfügbare Phasen für VPD-Optimierung
    """
    try:
        vpd_logic = VPDLogic()
        phases = vpd_logic.get_available_phases()
        
        return jsonify({
            'success': True,
            'phases': phases
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Phasen: {str(e)}'
        }), 500

@vpd_bp.route('/calculate', methods=['POST'])
def vpd_calculate():
    """
    VPD-Berechnung mit benutzerdefinierten Werten
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not data.get('temperature'):
            return jsonify({'success': False, 'message': 'Temperatur ist erforderlich'}), 400
        
        if not data.get('humidity'):
            return jsonify({'success': False, 'message': 'Luftfeuchte ist erforderlich'}), 400
        
        temperature = float(data['temperature'])
        humidity = int(data['humidity'])
        phase = data.get('phase', 'flowering_middle')
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # VPD berechnen
        result = vpd_logic.calculate_vpd_optimization(phase, temperature, humidity)
        
        return jsonify({
            'success': True,
            'vpd_value': result['current']['vpd'],
            'status': result['current']['status'],
            'recommendations': result['recommendations']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der VPD-Berechnung: {str(e)}'
        }), 500

@vpd_bp.route('/guidelines', methods=['GET'])
def vpd_guidelines():
    """
    VPD-Richtlinien und Faustregeln laden
    """
    try:
        import json
        import os
        
        # JSON-Datei mit Guidelines laden
        guidelines_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'static', 'data', 'vpd-guidelines.json'
        )
        
        if not os.path.exists(guidelines_path):
            return jsonify({
                'success': False,
                'message': 'VPD-Richtlinien nicht gefunden'
            }), 404
        
        with open(guidelines_path, 'r', encoding='utf-8') as f:
            guidelines = json.load(f)
        
        return jsonify({
            'success': True,
            'guidelines': guidelines
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der VPD-Richtlinien: {str(e)}'
        }), 500 

@vpd_bp.route('/analysis/comprehensive', methods=['POST'])
def comprehensive_vpd_analysis():
    """
    Erweiterte VPD-Analyse mit Problemanalyse, KI-Empfehlungen und Trend-Analyse
    """
    try:
        data = request.get_json()
        
        # Validierung der Pflichtfelder
        required_fields = ['temperature', 'humidity', 'phase', 'strain_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'message': f'Pflichtfeld fehlt: {field}'}), 400
        
        # Werte extrahieren
        temperature = float(data['temperature'])
        humidity = int(data['humidity'])
        phase = data['phase']
        strain_type = data['strain_type']
        leaf_temperature = data.get('leaf_temperature')  # Optional
        plant_id = data.get('plant_id')
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # Historische Daten laden falls plant_id vorhanden
        historical_data = None
        if plant_id:
            try:
                conn = sqlite3.connect('grow_diary_basic.db')
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT temperature, humidity, leaf_temperature, vpd_value, phase, entry_date
                    FROM vpd_entries 
                    WHERE plant_id = ? 
                    ORDER BY entry_date DESC 
                    LIMIT 20
                """, (plant_id,))
                
                entries = cursor.fetchall()
                historical_data = [{
                    'temperature': entry[0],
                    'humidity': entry[1],
                    'leaf_temperature': entry[2],
                    'vpd_value': entry[3],
                    'phase': entry[4],
                    'entry_date': entry[5]
                } for entry in entries]
                
            except Exception as e:
                print(f"Fehler beim Laden der historischen Daten: {e}")
            finally:
                if 'conn' in locals():
                    conn.close()
        
        # Erweiterte VPD-Analyse durchführen
        analysis = vpd_logic.perform_comprehensive_vpd_analysis(
            phase, temperature, humidity, strain_type, leaf_temperature, historical_data
        )
        
        return jsonify({
            'success': True,
            'phase': phase,
            'strain_type': strain_type,
            'temperature': temperature,
            'humidity': humidity,
            'leaf_temperature': leaf_temperature,
            'analysis': analysis
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der erweiterten VPD-Analyse: {str(e)}'
        }), 500

@vpd_bp.route('/analysis/problems', methods=['POST'])
def vpd_problem_analysis():
    """
    Spezielle VPD-Problemanalyse
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not all(field in data for field in ['temperature', 'humidity', 'phase']):
            return jsonify({'success': False, 'message': 'Pflichtfelder fehlen'}), 400
        
        temperature = float(data['temperature'])
        humidity = int(data['humidity'])
        phase = data['phase']
        strain_type = data.get('strain_type', 'photoperiodic')
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # VPD berechnen
        vpd_value = vpd_logic.calculate_vpd(temperature, humidity, data.get('leaf_temperature'))
        
        # Zielbereiche laden
        targets = vpd_logic.get_target_ranges(phase, strain_type)
        
        # Problemanalyse durchführen
        problem_analysis = vpd_logic.analyze_vpd_problems(vpd_value, temperature, humidity, targets)
        
        return jsonify({
            'success': True,
            'vpd_value': vpd_value,
            'problem_analysis': problem_analysis
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der VPD-Problemanalyse: {str(e)}'
        }), 500

@vpd_bp.route('/analysis/ai-recommendations', methods=['POST'])
def vpd_ai_recommendations():
    """
    KI-basierte VPD-Optimierungsvorschläge
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not all(field in data for field in ['temperature', 'humidity', 'phase']):
            return jsonify({'success': False, 'message': 'Pflichtfelder fehlen'}), 400
        
        temperature = float(data['temperature'])
        humidity = int(data['humidity'])
        phase = data['phase']
        strain_type = data.get('strain_type', 'photoperiodic')
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # VPD berechnen
        vpd_value = vpd_logic.calculate_vpd(temperature, humidity, data.get('leaf_temperature'))
        
        # Zielbereiche laden
        targets = vpd_logic.get_target_ranges(phase, strain_type)
        
        # KI-Empfehlungen generieren
        ai_recommendations = vpd_logic.generate_ai_recommendations(
            vpd_value, temperature, humidity, targets, phase, strain_type
        )
        
        return jsonify({
            'success': True,
            'vpd_value': vpd_value,
            'ai_recommendations': ai_recommendations
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei den KI-Empfehlungen: {str(e)}'
        }), 500

@vpd_bp.route('/analysis/efficiency/<plant_id>', methods=['GET'])
def vpd_efficiency_analysis(plant_id):
    """
    VPD-Effizienz-Analyse für eine Pflanze
    """
    try:
        limit = request.args.get('limit', 30, type=int)
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # Historische Daten laden
        conn = sqlite3.connect('grow_diary_basic.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT temperature, humidity, leaf_temperature, vpd_value, phase, strain_type, entry_date
            FROM vpd_entries 
            WHERE plant_id = ? 
            ORDER BY entry_date DESC 
            LIMIT ?
        """, (plant_id, limit))
        
        entries = cursor.fetchall()
        conn.close()
        
        if not entries:
            return jsonify({
                'success': False,
                'message': 'Keine VPD-Daten für diese Pflanze gefunden'
            }), 404
        
        # Effizienz-Analyse durchführen
        efficiency_data = []
        total_efficiency = 0
        
        for entry in entries:
            temperature, humidity, leaf_temp, vpd_value, phase, strain_type, entry_date = entry
            
            # Zielbereiche für diese Phase laden
            targets = vpd_logic.get_target_ranges(phase, strain_type or 'photoperiodic')
            
            # Effizienz-Metriken berechnen
            efficiency_metrics = vpd_logic.calculate_efficiency_metrics(vpd_value, targets['vpd'])
            
            efficiency_data.append({
                'entry_date': entry_date,
                'vpd_value': vpd_value,
                'temperature': temperature,
                'humidity': humidity,
                'phase': phase,
                'efficiency_score': efficiency_metrics['efficiency_score'],
                'stability_score': efficiency_metrics['stability_score'],
                'efficiency_level': efficiency_metrics['efficiency_level']
            })
            
            total_efficiency += efficiency_metrics['efficiency_score']
        
        # Durchschnittliche Effizienz berechnen
        avg_efficiency = total_efficiency / len(efficiency_data) if efficiency_data else 0
        
        # Trend-Analyse
        recent_efficiency = efficiency_data[0]['efficiency_score'] if efficiency_data else 0
        trend = 'improving' if recent_efficiency > avg_efficiency else 'declining' if recent_efficiency < avg_efficiency else 'stable'
        
        return jsonify({
            'success': True,
            'plant_id': plant_id,
            'efficiency_data': efficiency_data,
            'summary': {
                'average_efficiency': round(avg_efficiency, 1),
                'recent_efficiency': round(recent_efficiency, 1),
                'trend': trend,
                'data_points': len(efficiency_data),
                'efficiency_grade': vpd_logic.get_score_grade(avg_efficiency)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der VPD-Effizienz-Analyse: {str(e)}'
        }), 500

@vpd_bp.route('/analysis/trends/<plant_id>', methods=['GET'])
def vpd_trend_analysis(plant_id):
    """
    VPD-Trend-Analyse für eine Pflanze
    """
    try:
        limit = request.args.get('limit', 20, type=int)
        
        # Historische Daten laden
        conn = sqlite3.connect('grow_diary_basic.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT temperature, humidity, vpd_value, phase, entry_date
            FROM vpd_entries 
            WHERE plant_id = ? 
            ORDER BY entry_date ASC
            LIMIT ?
        """, (plant_id, limit))
        
        entries = cursor.fetchall()
        conn.close()
        
        if not entries:
            return jsonify({
                'success': False,
                'message': 'Keine VPD-Daten für diese Pflanze gefunden'
            }), 404
        
        # VPD-Logik initialisieren
        vpd_logic = VPDLogic()
        
        # Daten für Trend-Analyse aufbereiten
        vpd_values = [entry[2] for entry in entries]
        temp_values = [entry[0] for entry in entries]
        hum_values = [entry[1] for entry in entries]
        
        # Historische Muster analysieren
        historical_analysis = vpd_logic.analyze_historical_patterns([
            {
                'temperature': temp,
                'humidity': hum,
                'vpd_value': vpd,
                'phase': phase,
                'entry_date': date
            }
            for temp, hum, vpd, phase, date in entries
        ])
        
        # Trend-Berechnung
        if len(vpd_values) >= 3:
            recent_trend = 'increasing' if vpd_values[-1] > vpd_values[-2] > vpd_values[-3] else \
                          'decreasing' if vpd_values[-1] < vpd_values[-2] < vpd_values[-3] else 'stable'
        else:
            recent_trend = 'insufficient_data'
        
        return jsonify({
            'success': True,
            'plant_id': plant_id,
            'trend_analysis': {
                'recent_trend': recent_trend,
                'vpd_values': vpd_values,
                'temperature_values': temp_values,
                'humidity_values': hum_values,
                'historical_insights': historical_analysis,
                'data_points': len(entries)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der VPD-Trend-Analyse: {str(e)}'
        }), 500 