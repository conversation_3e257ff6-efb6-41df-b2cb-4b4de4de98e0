/**
 * Fertilizer Tabs Module
 * Verwaltet die Tab-basierte Anzeige von Dünger-Empfehlungen für verschiedene Marken
 */

class FertilizerTabs {
    constructor() {
        this.currentPlant = null;
        this.currentPhase = null;
        this.brands = ['biobizz', 'canna', 'plagron'];
        this.init();
    }

    init() {
        // Event-Listener für Tab-Wechsel
        document.addEventListener('DOMContentLoaded', () => {
            this.setupTabListeners();
            this.loadInitialData();
        });
    }

    setupTabListeners() {
        // Tab-Wechsel Event-Listener
        const tabButtons = document.querySelectorAll('#fertilizerTabs .nav-link');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Alle Tabs inaktiv machen
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });
                
                // Aktuellen Tab aktiv machen
                button.classList.add('active');
                button.setAttribute('aria-selected', 'true');
                
                // Alle Tab-Panes ausblenden
                const tabPanes = document.querySelectorAll('#fertilizerTabContent .tab-pane');
                tabPanes.forEach(pane => {
                    pane.classList.remove('show', 'active');
                });
                
                // Ziel-Tab-Pane anzeigen
                const targetId = button.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);
                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                }
                
                const targetBrand = targetId.replace('#', '').replace('-content', '');
                this.loadFertilizerRecommendations(targetBrand);
            });
        });
    }

    loadInitialData() {
        // Lade initiale Daten für die aktuelle Pflanze
        const plantId = this.getPlantIdFromUrl();
        if (plantId) {
            this.loadPlantData(plantId);
        }
    }

    getPlantIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        // URL ist /plant/<plant_id>, also ist plant_id der letzte Teil
        return pathParts[pathParts.length - 1];
    }

    async loadPlantData(plantId) {
        try {
            const response = await fetch(`/api/plants/${plantId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.plant) {
                    this.currentPlant = data.plant;
                    this.currentPhase = data.plant.current_phase;
                    
                    // Initialisiere den ersten Tab (biobizz) als aktiv
                    this.initializeFirstTab();
                    
                    // Lade initial Biobizz-Empfehlungen
                    this.loadFertilizerRecommendations('biobizz');
                } else {
                    console.error('Fehler in API-Antwort:', data.message);
                }
            } else {
                console.error('HTTP-Fehler beim Laden der Pflanzendaten:', response.status);
            }
        } catch (error) {
            console.error('Fehler beim Laden der Pflanzendaten:', error);
        }
    }

    initializeFirstTab() {
        // Ersten Tab als aktiv markieren
        const tabButtons = document.querySelectorAll('#fertilizerTabs .nav-link');
        if (tabButtons.length > 0) {
            // Alle Tabs inaktiv machen
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.setAttribute('aria-selected', 'false');
            });
            
            // Ersten Tab aktiv machen
            tabButtons[0].classList.add('active');
            tabButtons[0].setAttribute('aria-selected', 'true');
            
            // Alle Tab-Panes ausblenden
            const tabPanes = document.querySelectorAll('#fertilizerTabContent .tab-pane');
            tabPanes.forEach(pane => {
                pane.classList.remove('show', 'active');
            });
            
            // Ersten Tab-Pane anzeigen
            const firstTargetId = tabButtons[0].getAttribute('data-bs-target');
            const firstTargetPane = document.querySelector(firstTargetId);
            if (firstTargetPane) {
                firstTargetPane.classList.add('show', 'active');
            }
        }
    }

    async loadFertilizerRecommendations(brand) {
        if (!this.currentPlant || !this.currentPhase) {
            console.error('Keine Plant- oder Phase-Daten verfügbar');
            return;
        }

        try {
            const url = `/api/fertilizer-recommendations/${this.currentPlant.id}?brand=${brand}`;
            
            const response = await fetch(url);
            
            if (response.ok) {
                const data = await response.json();
                
                if (data.success && data.recommendations) {
                    this.displayRecommendations(brand, data.recommendations);
                } else {
                    this.displayError(brand, data.message || 'Keine Empfehlungen verfügbar');
                }
            } else {
                const errorText = await response.text();
                console.error('API-Fehler:', errorText);
                this.displayError(brand, 'Fehler beim Laden der Empfehlungen');
            }
        } catch (error) {
            console.error(`Fehler beim Laden der ${brand}-Empfehlungen:`, error);
            this.displayError(brand, 'Netzwerkfehler');
        }
    }

    displayRecommendations(brand, recommendations) {
        const container = document.getElementById(`${brand}-recommendations`);
        
        if (!container) {
            console.error(`Container ${brand}-recommendations nicht gefunden!`);
            return;
        }

        if (!recommendations) {
            container.innerHTML = '<div class="text-muted">Keine Empfehlungen für diese Phase verfügbar.</div>';
            return;
        }

        const brandColors = {
            'biobizz': { primary: '#4caf50', secondary: '#81c784' },
            'canna': { primary: '#ff9800', secondary: '#ffb74d' },
            'plagron': { primary: '#e91e63', secondary: '#f48fb1' }
        };

        const colors = brandColors[brand] || brandColors.biobizz;

        let html = `
            <div class="mb-3">
                <span class="badge" style="background-color: ${colors.primary}; color: white;">${recommendations.name}</span>
            </div>
        `;

        // EC-Werte anzeigen
        if (recommendations.ec_target || recommendations.ec_range) {
            html += `
                <div class="mb-3 p-3 rounded" style="background: linear-gradient(135deg, ${colors.primary}15, ${colors.secondary}15); border: 2px solid ${colors.primary}30;">
                    <h6 class="mb-2" style="color: ${colors.primary};">
                        <i class="fa-solid fa-flask me-2"></i>EC-Wert Empfehlungen
                    </h6>
                    <div class="row g-2">
                        ${recommendations.ec_target ? `
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center p-2 rounded" style="background-color: ${colors.primary}20;">
                                    <span class="fw-bold">Ziel-EC:</span>
                                    <span class="badge" style="background-color: ${colors.primary}; color: white;">${recommendations.ec_target}</span>
                                </div>
                            </div>
                        ` : ''}
                        ${recommendations.ec_range ? `
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center p-2 rounded" style="background-color: ${colors.secondary}20;">
                                    <span class="fw-bold">Bereich:</span>
                                    <span class="badge" style="background-color: ${colors.secondary}; color: white;">${recommendations.ec_range}</span>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // Dünger-Empfehlungen
        if (recommendations.fertilizers && recommendations.fertilizers.length > 0) {
            html += '<div class="row g-3 mb-3">';
            recommendations.fertilizers.forEach(fertilizer => {
                html += `
                    <div class="col-md-6 col-lg-4">
                        <div class="p-3 border rounded h-100" style="border-color: ${colors.secondary} !important; background-color: ${colors.secondary}10;">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0" style="color: ${colors.primary};">${fertilizer.name}</h6>
                                <span class="badge" style="background-color: ${colors.primary}; color: white;">${fertilizer.dosage}</span>
                            </div>
                            <div class="small">
                                <div><strong>Häufigkeit:</strong> ${fertilizer.frequency}</div>
                                <div><strong>Zweck:</strong> ${fertilizer.purpose}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            // Kombinationsprüfung
            const fertilizerNames = recommendations.fertilizers.map(f => f.name);
            this.checkCombinations(brand, fertilizerNames).then(combinationResult => {
                if (combinationResult.has_conflicts) {
                    const combinationHtml = `
                        <div class="mb-3 p-3 bg-danger bg-opacity-10 rounded border border-danger">
                            <h6 class="text-danger mb-2">
                                <i class="fa-solid fa-exclamation-triangle me-2"></i>Dünger-Kombinationswarnung
                            </h6>
                            <ul class="mb-0 small">
                                ${combinationResult.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                    container.insertAdjacentHTML('beforeend', combinationHtml);
                }
            });
        } else {
            html += '<div class="text-muted mb-3">Keine Dünger für diese Phase empfohlen.</div>';
        }

        // Hinweise und Warnungen
        html += '<div class="row g-3">';
        
        if (recommendations.notes) {
            html += `
                <div class="col-md-6">
                    <div class="p-3 bg-info bg-opacity-10 rounded">
                        <h6 class="text-info mb-2"><i class="fa-solid fa-info-circle me-2"></i>Hinweise</h6>
                        <p class="mb-0 small">${recommendations.notes}</p>
                    </div>
                </div>
            `;
        }
        
        if (recommendations.warnings && recommendations.warnings.length > 0) {
            html += `
                <div class="col-md-6">
                    <div class="p-3 bg-warning bg-opacity-10 rounded">
                        <h6 class="text-warning mb-2"><i class="fa-solid fa-exclamation-triangle me-2"></i>Warnungen</h6>
                        <ul class="mb-0 small">
                            ${recommendations.warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
        
        html += '</div>';

        // Aktuelle Phase Info
        if (this.currentPhase) {
            html += `
                <div class="mt-3 p-2 rounded" style="background-color: ${colors.secondary}10;">
                    <small style="color: ${colors.primary};">
                        <i class="fa-solid fa-calendar me-1"></i>
                        <strong>Aktuelle Phase:</strong> ${this.currentPhase.phase_name || 'Unbekannt'} 
                        (Grow-Woche: ${this.currentPhase.grow_week || 0}
                        ${this.currentPhase.flower_week > 0 ? `, Blüte-Woche: ${this.currentPhase.flower_week}` : ''})
                    </small>
                </div>
            `;
        }

        container.innerHTML = html;
    }

    displayError(brand, message) {
        const container = document.getElementById(`${brand}-recommendations`);
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa-solid fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }
    }

    async checkCombinations(brand, fertilizerNames) {
        try {
            const response = await fetch(`/api/fertilizer-combinations`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    brand: brand,
                    fertilizers: fertilizerNames
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data;
            } else {
                console.error('Fehler bei Kombinationsprüfung:', response.status);
                return { has_conflicts: false, recommendations: [] };
            }
        } catch (error) {
            console.error('Fehler bei Kombinationsprüfung:', error);
            return { has_conflicts: false, recommendations: [] };
        }
    }
}

// Initialisiere das Modul
const fertilizerTabs = new FertilizerTabs(); 