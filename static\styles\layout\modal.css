/* ===== MODAL LAYOUT ===== */

/* Bootstrap 5 Modal Anpassungen */
.modal {
  /* Bootstrap 5 übernimmt die Grundlagen */
  z-index: 1055; /* <PERSON><PERSON>her als Backdrop */
}

/* Modal Backdrop */
.modal-backdrop {
  z-index: 1050; /* Unter dem <PERSON> */
}

/* Modal Dialog für Zentrierung */
.modal-dialog {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
  margin: 0.5rem auto;
}

/* Modal Content Anpassungen für unser Design */
.modal-content {
  background: var(--card-bg);
  color: var(--text);
  border: 1.5px solid var(--card-border);
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
  z-index: 1056; /* <PERSON><PERSON>her als <PERSON> */
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header Anpassungen */
.modal-header {
  background-color: var(--accent);
  color: white;
  border-bottom: 1px solid var(--accent-dark);
  border-radius: 8px 8px 0 0;
}

.modal-header .modal-title {
  color: white;
  font-weight: 600;
}

/* Modal Body */
.modal-body {
  /* Bootstrap 5 übernimmt das Padding */
}

/* Modal Footer */
.modal-footer {
  background-color: var(--card-bg);
  border-top: 1px solid var(--card-border);
  border-radius: 0 0 8px 8px;
}

/* Form Groups in Modals */
.modal .form-group {
  margin-bottom: 1rem;
}

.modal .form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text);
}

.modal .form-group input,
.modal .form-group select,
.modal .form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--card-border);
  border-radius: 4px;
  background: var(--input-bg);
  color: var(--text);
}

.modal .form-group input:focus,
.modal .form-group select:focus,
.modal .form-group textarea:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
}

/* Buttons in Modals */
.modal .btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.modal .btn-primary {
  background-color: var(--accent);
  color: white;
}

.modal .btn-primary:hover {
  background-color: var(--accent-dark);
}

.modal .btn-secondary {
  background-color: var(--card-border);
  color: var(--text);
}

.modal .btn-secondary:hover {
  background-color: var(--text-muted);
}

.modal .btn-danger {
  background-color: #dc3545;
  color: white;
}

.modal .btn-danger:hover {
  background-color: #c82333;
}

/* Body Modal Open */
body.modal-open {
  overflow: hidden;
}

/* Recommendation Content */
.recommendation-content {
  margin-bottom: 1rem;
}

.recommendation-message {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--card-border);
  border-radius: 4px;
}

.recommendation-details {
  margin-top: 1rem;
}

.detail-item {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 4px;
}

.detail-item strong {
  color: var(--accent);
} 