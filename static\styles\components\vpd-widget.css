/* VPD-Optimierungs-Widget (modular) */
.vpd-widget-card {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 2rem 0 2.5rem 0;
    box-shadow: 0 8px 32px rgba(0, 188, 212, 0.3);
    overflow: hidden;
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
    transition: all 0.3s ease;
    position: relative;
}

.vpd-widget-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 188, 212, 0.4);
}
.vpd-widget-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: #fff;
    font-size: 1.3rem;
    font-weight: bold;
    padding: 1.1rem 1.7rem;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}
.vpd-widget-header .fa-droplet {
    font-size: 1.5em;
    color: #ffe082;
}
.vpd-widget-body {
    padding: 1.5rem 1.7rem 1.2rem 1.7rem;
    color: #fff;
    font-size: 1rem;
    line-height: 1.6;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
}
.vpd-widget-body b,
.vpd-widget-body strong {
    color: #fff3cd;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
.vpd-widget-body .vpd-value {
    color: #00e676;
    font-weight: bold;
    font-size: 1.05em;
}
.vpd-widget-body .vpd-status {
    margin-top: 1.2rem;
    display: inline-block;
    padding: 0.5em 1.2em;
    border-radius: 1.2em;
    font-weight: bold;
    font-size: 1rem;
    letter-spacing: 0.2px;
}
.vpd-widget-body .vpd-status.success {
    background: #1b5e20;
    color: #b9f6ca;
}
.vpd-widget-body .vpd-status.warning {
    background: #ffb300;
    color: #23272b;
}
.vpd-widget-body .vpd-status.danger {
    background: #b71c1c;
    color: #fff;
}
.vpd-widget-body hr {
    border-top: 1px solid #444a;
    margin: 1.5em 0 1.2em 0;
}
.vpd-widget-body .vpd-label {
    display: inline-block;
    min-width: 160px;
    color: #e3f2fd;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
.vpd-widget-body .vpd-section-title {
    margin-top: 1.2em;
    margin-bottom: 0.5em;
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 0.2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
} 