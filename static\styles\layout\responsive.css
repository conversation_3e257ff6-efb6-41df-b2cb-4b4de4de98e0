/* ===== RESPONSIVE DESIGN MODULES ===== */
/* Modulare responsive Design-Regeln für verschiedene Bildschirmgrößen */

/* ===== MOBILE FIRST APPROACH ===== */
/* Basis-Styles für mobile Geräte (320px und größer) */

/* Grid System für Mobile */
.grid-mobile {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0.5rem;
}

/* Card Layout für Mobile */
.card-mobile {
    margin-bottom: 1rem;
    padding: 0.75rem;
}

.card-mobile__header {
    padding: 0.5rem;
    font-size: 1rem;
}

.card-mobile__body {
    padding: 0.5rem;
}

.card-mobile__footer {
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* Navigation für Mobile */
.nav-mobile {
    flex-direction: column;
    padding: 0.5rem;
}

.nav-mobile__item {
    padding: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

/* ===== TABLET BREAKPOINT (768px) ===== */
@media (min-width: 768px) {
    /* Grid System für Tablet */
    .grid-tablet {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 1rem;
    }
    
    /* Card Layout für Tablet */
    .card-tablet {
        margin-bottom: 1.5rem;
        padding: 1rem;
    }
    
    .card-tablet__header {
        padding: 0.75rem;
        font-size: 1.125rem;
    }
    
    .card-tablet__body {
        padding: 0.75rem;
    }
    
    .card-tablet__footer {
        padding: 0.75rem;
        font-size: 1rem;
    }
    
    /* Navigation für Tablet */
    .nav-tablet {
        flex-direction: row;
        padding: 1rem;
    }
    
    .nav-tablet__item {
        padding: 0.75rem;
        border-bottom: none;
        border-right: 1px solid #e9ecef;
    }
    
    /* Widget Layout für Tablet */
    .widget-layout-tablet {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    /* Phase Info Layout für Tablet */
    .phase-info-tablet {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

/* ===== DESKTOP BREAKPOINT (1024px) ===== */
@media (min-width: 1024px) {
    /* Grid System für Desktop */
    .grid-desktop {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        padding: 1.5rem;
    }
    
    /* Card Layout für Desktop */
    .card-desktop {
        margin-bottom: 2rem;
        padding: 1.5rem;
    }
    
    .card-desktop__header {
        padding: 1rem;
        font-size: 1.25rem;
    }
    
    .card-desktop__body {
        padding: 1rem;
    }
    
    .card-desktop__footer {
        padding: 1rem;
        font-size: 1.125rem;
    }
    
    /* Navigation für Desktop */
    .nav-desktop {
        flex-direction: row;
        padding: 1.5rem;
    }
    
    .nav-desktop__item {
        padding: 1rem;
        border-right: 1px solid #e9ecef;
    }
    
    /* Widget Layout für Desktop */
    .widget-layout-desktop {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
    
    /* Phase Info Layout für Desktop */
    .phase-info-desktop {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    
    /* Sidebar Layout für Desktop */
    .layout-with-sidebar {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 2rem;
    }
    
    .sidebar {
        position: sticky;
        top: 1rem;
        height: fit-content;
    }
}

/* ===== LARGE DESKTOP BREAKPOINT (1440px) ===== */
@media (min-width: 1440px) {
    /* Grid System für Large Desktop */
    .grid-large {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
        padding: 2rem;
    }
    
    /* Widget Layout für Large Desktop */
    .widget-layout-large {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }
    
    /* Phase Info Layout für Large Desktop */
    .phase-info-large {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
    
    /* Container Max Width */
    .container-large {
        max-width: 1400px;
        margin: 0 auto;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    /* Verstecke Navigation und Footer beim Drucken */
    .nav-mobile,
    .nav-tablet,
    .nav-desktop,
    .footer {
        display: none !important;
    }
    
    /* Optimiere Layout für Drucken */
    .grid-mobile,
    .grid-tablet,
    .grid-desktop,
    .grid-large {
        display: block;
        padding: 0;
    }
    
    .card-mobile,
    .card-tablet,
    .card-desktop {
        break-inside: avoid;
        margin-bottom: 1rem;
        padding: 0.5rem;
        border: 1px solid #ccc;
    }
    
    /* Entferne Schatten und Hintergründe */
    .card-mobile,
    .card-tablet,
    .card-desktop,
    .widget-container {
        box-shadow: none !important;
        background: white !important;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    /* Reduziere Animationen für Benutzer mit Bewegungssensibilität */
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    /* Erhöhe Kontrast für bessere Lesbarkeit */
    .card-mobile,
    .card-tablet,
    .card-desktop,
    .widget-container {
        border: 2px solid #000;
    }
    
    .nav-mobile__item,
    .nav-tablet__item,
    .nav-desktop__item {
        border-color: #000;
    }
}

/* ===== DARK MODE RESPONSIVE ===== */
@media (min-width: 768px) {
    [data-theme="dark"] .nav-tablet__item {
        border-right-color: #718096;
    }
}

@media (min-width: 1024px) {
    [data-theme="dark"] .nav-desktop__item {
        border-right-color: #718096;
    }
    
    [data-theme="dark"] .sidebar {
        background-color: #2d3748;
        border-right: 1px solid #718096;
    }
} 