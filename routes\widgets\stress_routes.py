"""
Stress Management Widget API Routes
Handles stress management and training techniques API endpoints
"""

from flask import Blueprint, request, jsonify
from phase_logic.widgets.stress_logic import StressLogic

# Create blueprint
stress_bp = Blueprint('stress', __name__, url_prefix='/api/stress')

@stress_bp.route('/management/<phase>', methods=['GET'])
def stress_management(phase):
    """
    Stress-Management-Empfehlungen für eine bestimmte Phase
    """
    try:
        # Parameter aus Query-String holen
        strain_type = request.args.get('strain_type', default='photoperiodic')
        experience_level = request.args.get('experience_level', default='intermediate')
        plant_id = request.args.get('plant_id', default=None)
        
        # Stress-Logik initialisieren
        stress_logic = StressLogic()
        
        # Stress-Management berechnen
        result = stress_logic.calculate_stress_management(phase, strain_type, experience_level)
        
        return jsonify({
            'success': True,
            'phase': phase,
            **result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'<PERSON><PERSON> beim <PERSON> Stress-Managements: {str(e)}'
        }), 500

@stress_bp.route('/lst-techniques', methods=['GET'])
def lst_techniques():
    """
    LST (Low Stress Training) Techniken
    """
    try:
        stress_logic = StressLogic()
        techniques = stress_logic.get_lst_techniques()
        
        return jsonify({
            'success': True,
            'techniques': techniques
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der LST-Techniken: {str(e)}'
        }), 500

@stress_bp.route('/hst-techniques', methods=['GET'])
def hst_techniques():
    """
    HST (High Stress Training) Techniken
    """
    try:
        stress_logic = StressLogic()
        techniques = stress_logic.get_hst_techniques()
        
        return jsonify({
            'success': True,
            'techniques': techniques
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der HST-Techniken: {str(e)}'
        }), 500

@stress_bp.route('/training-timeline/<phase>', methods=['GET'])
def training_timeline(phase):
    """
    Training-Timeline für eine bestimmte Phase
    """
    try:
        strain_type = request.args.get('strain_type', default='photoperiodic')
        
        stress_logic = StressLogic()
        timeline = stress_logic.get_training_timeline(phase, strain_type)
        
        return jsonify({
            'success': True,
            'phase': phase,
            'timeline': timeline
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Training-Timeline: {str(e)}'
        }), 500

@stress_bp.route('/techniques/<technique_type>', methods=['GET'])
def training_techniques(technique_type):
    """
    Spezifische Training-Techniken (LST oder HST)
    """
    try:
        stress_logic = StressLogic()
        
        if technique_type.lower() == 'lst':
            techniques = stress_logic.get_lst_techniques()
        elif technique_type.lower() == 'hst':
            techniques = stress_logic.get_hst_techniques()
        else:
            return jsonify({'success': False, 'message': 'Ungültiger Technik-Typ'}), 400
        
        return jsonify({
            'success': True,
            'technique_type': technique_type,
            'techniques': techniques
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Techniken: {str(e)}'
        }), 500

@stress_bp.route('/recommendations', methods=['POST'])
def stress_recommendations():
    """
    Personalisierte Stress-Management-Empfehlungen
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not data.get('phase'):
            return jsonify({'success': False, 'message': 'Phase ist erforderlich'}), 400
        
        phase = data['phase']
        strain_type = data.get('strain_type', 'photoperiodic')
        experience_level = data.get('experience_level', 'intermediate')
        plant_size = data.get('plant_size', 'medium')
        
        # Stress-Logik initialisieren
        stress_logic = StressLogic()
        
        # Empfehlungen berechnen
        result = stress_logic.get_personalized_recommendations(
            phase, strain_type, experience_level, plant_size
        )
        
        return jsonify({
            'success': True,
            'recommendations': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der Empfehlungsberechnung: {str(e)}'
        }), 500

@stress_bp.route('/guidelines/<phase>', methods=['GET'])
def stress_guidelines(phase):
    """
    Strain-spezifische Stress-Guidelines für eine Phase
    """
    try:
        strain_type = request.args.get('strain_type', default='photoperiodic')
        
        stress_logic = StressLogic()
        guidelines = stress_logic.get_stress_guidelines_summary(phase, strain_type)
        
        if 'error' in guidelines:
            return jsonify({
                'success': False,
                'message': guidelines['error']
            }), 400
        
        return jsonify({
            'success': True,
            'phase': phase,
            'strain_type': strain_type,
            'guidelines': guidelines
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Stress-Guidelines: {str(e)}'
        }), 500

@stress_bp.route('/analyze-conditions', methods=['POST'])
def analyze_stress_conditions():
    """
    Analysiert aktuelle Bedingungen auf Stress-Indikatoren
    """
    try:
        data = request.get_json()
        
        if not data.get('phase'):
            return jsonify({'success': False, 'message': 'Phase ist erforderlich'}), 400
        
        phase = data['phase']
        strain_type = data.get('strain_type', 'photoperiodic')
        sensor_data = data.get('sensor_data', {})
        
        stress_logic = StressLogic()
        analysis = stress_logic.analyze_stress_conditions(phase, strain_type, sensor_data)
        
        if 'error' in analysis:
            return jsonify({
                'success': False,
                'message': analysis['error']
            }), 400
        
        return jsonify({
            'success': True,
            'analysis': analysis
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der Stress-Analyse: {str(e)}'
        }), 500 