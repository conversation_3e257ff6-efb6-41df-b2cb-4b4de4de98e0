/* ===== PLANT DETAIL COMPONENT STYLES ===== */

.breadcrumb {
    color: var(--text-secondary);
    font-size: var(--font-size-small);
}

.breadcrumb a {
    color: var(--primary-color);
}

.breadcrumb-spacing {
    margin-bottom: var(--spacing-lg);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-small);
}

.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-small);
    font-weight: 500;
}

.status-active {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success);
}

.status-deleted {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--error);
}

.plant-notes {
    border-top: 1px solid var(--border);
    padding-top: var(--spacing-md);
}

.plant-notes-spacing {
    margin-top: var(--spacing-lg);
}

.plant-notes label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-small);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.card-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-md);
}

.entry-card {
    margin-bottom: var(--spacing-md);
}

.entry-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.entry-title {
    font-size: var(--font-size-h3);
    margin-bottom: var(--spacing-xs);
}

.entry-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-small);
    color: var(--text-secondary);
}

.entry-content {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border);
}

.empty-entries {
    text-align: center;
    padding: var(--spacing-lg);
}

.empty-entries p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header-content,
    .entry-header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .plant-actions,
    .entry-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
} 

/* Plant-Detail: Einheitliche Farben und Abstände für alle Elemente */
.card, .card *, .info-grid, .info-item, .status-badge, .plant-notes, .entries-list, .card-title, .btn, .entry-title, .entry-meta, .entry-content, .details-link, .card-header, .card-footer, .card-body {
  /* color: var(--text) !important; */
}

.card .text-muted {
    color:#aaa !important
}

.card h6 {
    color: var(--text) !important; 
}

.status-badge {
  background: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: 1rem;
  padding: 0.2em 0.8em;
  font-size: 0.95em;
  font-weight: 600;
}

.plant-notes {
  background: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: 0.5rem;
  padding: 1em;
  margin-top: 1em;
}

.entries-list .card {
  margin-bottom: 1em;
}

.entry-title {
  font-weight: 600;
  font-size: 1.1em;
}

.entry-meta {
  font-size: 0.95em;
  opacity: 0.8;
}

.entry-content {
  margin-top: 0.5em;
  color: var(--text) !important;
}

.details-link {
  color: var(--accent) !important;
  text-decoration: underline;
} 

/* Phase-Info-Box für Phaseninformationen */
.phase-info-box {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 1em;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}
[data-theme="dark"] .phase-info-box {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-color: rgba(255,255,255,0.1);
}
[data-theme="dark"] .phase-info-box:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.2);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}
[data-theme="light"] .phase-info-box {
    background: #f8f9fa;
    color: #333;
    border-color: #dee2e6;
}
[data-theme="light"] .phase-info-box:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
} 

.icon-xl {
    font-size: 2.2rem;
    line-height: 1;
    vertical-align: middle;
} 

/* Phasen-Timeline Styles */
.phase-timeline {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    margin: 2rem 0;
    padding: 0 1rem;
}

.phase-timeline::before {
    content: '';
    position: absolute;
    top: 25px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #e0e0e0 0%, #1976d2 50%, #e0e0e0 100%);
    z-index: 1;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    text-align: center;
}

.timeline-marker {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.timeline-marker i {
    font-size: 1.2rem;
    color: #666;
    transition: all 0.3s ease;
}

.timeline-item.completed .timeline-marker {
    background: #4caf50;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.timeline-item.completed .timeline-marker i {
    color: #fff;
}

.timeline-item.active .timeline-marker {
    background: #1976d2;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
    transform: scale(1.1);
}

.timeline-item.active .timeline-marker i {
    color: #fff;
}

.timeline-item.upcoming .timeline-marker {
    background: #6c757d;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.timeline-item.upcoming .timeline-marker i {
    color: #fff;
}

.timeline-content {
    text-align: center;
    max-width: 260px;
    min-width: 180px;
    margin: 0 auto 1.5rem auto;
    padding: 1.2rem 1.2rem 1rem 1.2rem;
    background: rgba(255,255,255,0.04);
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1.5px solid #23242a;
    transition: all 0.2s;
}

.timeline-content h6 {
    margin: 0.5rem 0 0.2rem 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: #fff;
    letter-spacing: 0.01em;
}

.timeline-content small, .timeline-content .timeline-date {
    font-size: 0.85rem;
    color: #b0b0b0;
    display: block;
    margin-bottom: 0.2rem;
}

.timeline-content .timeline-title {
    font-size: 1.05rem;
    font-weight: 600;
    color: #7dd3fc;
    margin-bottom: 0.2rem;
}

.timeline-content .timeline-desc {
    font-size: 0.98rem;
    color: #e0e0e0;
    margin-top: 0.2rem;
}

.timeline-item.completed .timeline-content h6 {
    color: #4caf50;
}

.timeline-item.active .timeline-content h6 {
    color: #1976d2;
    font-weight: 700;
}

.timeline-item.upcoming .timeline-content h6 {
    color: #6c757d;
}

/* Progress Bar Styles */
.timeline-item .progress {
    background-color: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.timeline-item .progress-bar {
    background: linear-gradient(90deg, #4caf50, #66bb6a);
    transition: width 0.6s ease;
}

/* Responsive Timeline */
@media (max-width: 768px) {
    .phase-timeline {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .phase-timeline::before {
        display: none;
    }
    
    .timeline-item {
        flex: none;
        width: 100%;
        max-width: 300px;
    }
    
    .timeline-marker {
        width: 40px;
        height: 40px;
    }
    
    .timeline-marker i {
        font-size: 1rem;
    }
    
    .timeline-content {
        max-width: none;
        min-width: 0;
        padding: 1rem 0.5rem;
    }
}

/* External Data Box Styles */
.external-data-box {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 1em;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}
[data-theme="dark"] .external-data-box {
    background: rgba(25, 118, 210, 0.08);
    color: #fff;
    border-color: rgba(25, 118, 210, 0.2);
}
[data-theme="dark"] .external-data-box:hover {
    background: rgba(25, 118, 210, 0.15);
    border-color: rgba(25, 118, 210, 0.3);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}
[data-theme="light"] .external-data-box {
    background: rgba(25, 118, 210, 0.05);
    color: #333;
    border-color: rgba(25, 118, 210, 0.2);
}
[data-theme="light"] .external-data-box:hover {
    background: rgba(25, 118, 210, 0.1);
    border-color: rgba(25, 118, 210, 0.3);
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

/* Countdown Badge Animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.badge.bg-warning {
    animation: pulse 2s infinite;
}

/* Phase Color Coding */
.phase-germination { color: #ff9800; }
.phase-vegetative { color: #4caf50; }
.phase-flowering { color: #e91e63; }
.phase-harvest { color: #9c27b0; } 

/* Info Data Box Styles */
.info-data-box {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0;
    font-size: 1em;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}
[data-theme="dark"] .info-data-box {
    background: rgba(76, 175, 80, 0.08);
    color: #fff;
    border-color: rgba(76, 175, 80, 0.15);
}
[data-theme="dark"] .info-data-box:hover {
    background: rgba(76, 175, 80, 0.15);
    border-color: rgba(76, 175, 80, 0.25);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}
[data-theme="light"] .info-data-box {
    background: #f4f8f4;
    color: #333;
    border-color: #c8e6c9;
}
[data-theme="light"] .info-data-box:hover {
    background: #e8f5e9;
    border-color: #a5d6a7;
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.08);
} 

/* Info-Card/Parent-Container für Widgets */
.info-card, .card, .widget-panel {
  /* min-height: 420px; */
  height: auto !important;
  overflow: visible;
} 