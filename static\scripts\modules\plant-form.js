/**
 * Plant Form Module
 * Handles plant creation form functionality
 */

class PlantForm {
    constructor() {
        this.isSubmitting = false;
        this.form = document.getElementById('plant-form');
        this.submitBtn = document.getElementById('submit-plant-btn');
        this.dateInput = document.getElementById('plant-start-date');
        
        this.init();
    }
    
    init() {
        if (this.form && this.submitBtn) {
            this.setupFormHandling();
            this.setDefaultDate();
        }
    }
    
    setupFormHandling() {
        this.form.addEventListener('submit', (e) => {
            if (this.isSubmitting) {
                e.preventDefault();
                return false;
            }
            
            this.isSubmitting = true;
            this.submitBtn.disabled = true;
            this.submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Wird erstellt...';
            
            return true;
        });
    }
    
    setDefaultDate() {
        if (this.dateInput && !this.dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            this.dateInput.value = today;
        }
    }
    
    reset() {
        this.isSubmitting = false;
        if (this.submitBtn) {
            this.submitBtn.disabled = false;
            this.submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> Pflanze erstellen';
        }
    }
}

// Export for use in other modules
window.PlantForm = PlantForm; 