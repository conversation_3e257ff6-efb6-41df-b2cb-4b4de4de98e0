# -*- coding: utf-8 -*-
"""
Detaillierte Phasen-Informationen für Phase 2
Trichome-Entwicklung, Ernte-Zeitpunkt, Umgebungsbedingungen, erweiterte Nährstoffe
"""

from datetime import datetime, timedelta

# Trichome-Entwicklung pro Blüte-Woche
TRICHOME_DEVELOPMENT = {
    'week_1': {
        'stage': 'Frühe Blüte',
        'trichome_state': 'Klar und durchsichtig',
        'description': 'Trichome beginnen sich zu bilden, sind noch klar und durchsichtig',
        'harvest_ready': False,
        'recommendation': '<PERSON><PERSON> düngen, Trichome sind noch nicht reif'
    },
    'week_2': {
        'stage': 'Frühe Blüte',
        'trichome_state': 'Klar, erste milchige',
        'description': 'Erste milchige Trichome erscheinen, meist noch klar',
        'harvest_ready': False,
        'recommendation': '<PERSON><PERSON> düngen, noch nicht erntefähig'
    },
    'week_3': {
        'stage': 'Mittle<PERSON> Blüte',
        'trichome_state': 'Milchig-klar gemischt',
        'description': 'Mischung aus klaren und milchigen Trichomen',
        'harvest_ready': False,
        'recommendation': 'Weiter düngen, Trichome entwickeln sich'
    },
    'week_4': {
        'stage': 'Mittlere Blüte',
        'trichome_state': 'Überwiegend milchig',
        'description': 'Die meisten Trichome sind milchig, einige noch klar',
        'harvest_ready': False,
        'recommendation': 'Weiter düngen, noch nicht optimal für Ernte'
    },
    'week_5': {
        'stage': 'Mittlere Blüte',
        'trichome_state': 'Milchig mit ersten bernsteinfarbenen',
        'description': 'Milchige Trichome dominieren, erste bernsteinfarbene erscheinen',
        'harvest_ready': False,
        'recommendation': 'Weiter düngen, Entwicklung läuft gut'
    },
    'week_6': {
        'stage': 'Späte Blüte',
        'trichome_state': 'Milchig-bernsteinfarben gemischt',
        'description': 'Gute Mischung aus milchigen und bernsteinfarbenen Trichomen',
        'harvest_ready': True,
        'recommendation': 'Erste Ernte möglich, aber noch nicht optimal'
    },
    'week_7': {
        'stage': 'Späte Blüte',
        'trichome_state': 'Überwiegend milchig-bernsteinfarben',
        'description': 'Optimaler Erntezeitpunkt: milchig-bernsteinfarbene Trichome',
        'harvest_ready': True,
        'recommendation': 'Optimaler Erntezeitpunkt erreicht'
    },
    'week_8': {
        'stage': 'Späte Blüte',
        'trichome_state': 'Bernsteinfarben dominierend',
        'description': 'Bernsteinfarbene Trichome dominieren, sehr reif',
        'harvest_ready': True,
        'recommendation': 'Sehr reif, bald ernten um Überreife zu vermeiden'
    },
    'week_9': {
        'stage': 'Überreife',
        'trichome_state': 'Überwiegend bernsteinfarben',
        'description': 'Pflanze ist überreif, THC beginnt zu degradieren',
        'harvest_ready': True,
        'recommendation': 'Sofort ernten, Überreife vermeiden'
    }
}

# Umgebungsbedingungen pro Phase
ENVIRONMENT_CONDITIONS = {
    'germination': {
        'temperature': {'min': 20, 'max': 25, 'optimal': 22},
        'humidity': {'min': 70, 'max': 90, 'optimal': 80},
        'light_hours': {'min': 18, 'max': 24, 'optimal': 20},
        'vpd_target': 0.8,
        'notes': 'Hohe Luftfeuchtigkeit für Keimung wichtig, moderate Temperaturen'
    },
    'vegetative_early': {
        'temperature': {'min': 22, 'max': 28, 'optimal': 25},
        'humidity': {'min': 60, 'max': 80, 'optimal': 70},
        'light_hours': {'min': 16, 'max': 20, 'optimal': 18},
        'vpd_target': 1.0,
        'notes': 'Wachstumsfördernde Bedingungen, moderate Luftfeuchtigkeit'
    },
    'vegetative_mid': {
        'temperature': {'min': 22, 'max': 28, 'optimal': 25},
        'humidity': {'min': 55, 'max': 75, 'optimal': 65},
        'light_hours': {'min': 16, 'max': 20, 'optimal': 18},
        'vpd_target': 1.2,
        'notes': 'Starkes Wachstum, Luftfeuchtigkeit langsam reduzieren'
    },
    'vegetative_late': {
        'temperature': {'min': 22, 'max': 28, 'optimal': 25},
        'humidity': {'min': 50, 'max': 70, 'optimal': 60},
        'light_hours': {'min': 16, 'max': 20, 'optimal': 18},
        'vpd_target': 1.4,
        'notes': 'Vorbereitung auf Blüte, Luftfeuchtigkeit weiter reduzieren'
    },
    'flowering_early': {
        'temperature': {'min': 20, 'max': 26, 'optimal': 23},
        'humidity': {'min': 45, 'max': 65, 'optimal': 55},
        'light_hours': {'min': 10, 'max': 12, 'optimal': 12},
        'vpd_target': 1.6,
        'notes': 'Blüte-Start, kühlere Temperaturen, niedrigere Luftfeuchtigkeit'
    },
    'flowering_mid': {
        'temperature': {'min': 20, 'max': 26, 'optimal': 23},
        'humidity': {'min': 40, 'max': 60, 'optimal': 50},
        'light_hours': {'min': 10, 'max': 12, 'optimal': 12},
        'vpd_target': 1.8,
        'notes': 'Hauptblüte, optimale Bedingungen für Trichome-Entwicklung'
    },
    'flowering_late': {
        'temperature': {'min': 18, 'max': 24, 'optimal': 21},
        'humidity': {'min': 35, 'max': 55, 'optimal': 45},
        'light_hours': {'min': 10, 'max': 12, 'optimal': 12},
        'vpd_target': 2.0,
        'notes': 'Späte Blüte, kühlere Temperaturen für bessere Qualität'
    },
    'harvest': {
        'temperature': {'min': 18, 'max': 24, 'optimal': 21},
        'humidity': {'min': 30, 'max': 50, 'optimal': 40},
        'light_hours': {'min': 10, 'max': 12, 'optimal': 12},
        'vpd_target': 2.2,
        'notes': 'Erntezeit, niedrige Luftfeuchtigkeit für Trocknung vorbereiten'
    }
}

# Erweiterte Nährstoff-Empfehlungen
EXTENDED_NUTRIENTS = {
    'germination': {
        'primary_nutrients': ['Root Juice', 'Alg-A-Mic'],
        'secondary_nutrients': [],
        'micronutrients': ['CalMag'],
        'notes': 'Sanfter Start, nur Wurzelbildung und Stress-Schutz',
        'warnings': ['Keine starken Dünger verwenden', 'Überdüngung vermeiden']
    },
    'vegetative_early': {
        'primary_nutrients': ['Bio Grow', 'Root Juice'],
        'secondary_nutrients': ['Alg-A-Mic'],
        'micronutrients': ['CalMag'],
        'notes': 'Wachstumsförderung, Wurzelbildung, Stress-Schutz',
        'warnings': ['Langsam steigern', 'Pflanzenreaktion beobachten']
    },
    'vegetative_mid': {
        'primary_nutrients': ['Bio Grow', 'Fish Mix'],
        'secondary_nutrients': ['Root Juice', 'Top Max', 'Heaven'],
        'micronutrients': ['CalMag'],
        'notes': 'Starkes Wachstum, Blüte-Vorbereitung, Bodenaktivierung',
        'warnings': ['Fish Mix ODER Bio Grow, nicht beide', 'Top Max einschleichen']
    },
    'vegetative_late': {
        'primary_nutrients': ['Bio Grow', 'Bio Bloom'],
        'secondary_nutrients': ['Top Max', 'Root Juice'],
        'micronutrients': ['CalMag'],
        'notes': 'Finales Wachstum, Blüte-Vorbereitung, Bio Bloom einschleichen',
        'warnings': ['Bio Bloom langsam steigern', 'Bio Grow reduzieren']
    },
    'flowering_early': {
        'primary_nutrients': ['Bio Bloom'],
        'secondary_nutrients': ['Bio Grow', 'Top Max'],
        'micronutrients': ['CalMag'],
        'notes': 'Blütenbildung, Bio Grow reduzieren, Bio Bloom steigern',
        'warnings': ['Bio Grow weiter reduzieren', 'Top Max optimal dosieren']
    },
    'flowering_mid': {
        'primary_nutrients': ['Bio Bloom'],
        'secondary_nutrients': ['Top Max', 'Alg-A-Mic', 'Acti Vera'],
        'micronutrients': ['CalMag'],
        'notes': 'Hauptblüte, maximale Blütenbildung, Stress-Schutz',
        'warnings': ['Bio Grow minimal oder weglassen', 'Überdüngung vermeiden']
    },
    'flowering_late': {
        'primary_nutrients': ['Bio Bloom'],
        'secondary_nutrients': ['Top Max'],
        'micronutrients': [],
        'notes': 'Späte Blüte, Dünger reduzieren, Qualität fördern',
        'warnings': ['Alle Dünger reduzieren', 'Flush vorbereiten']
    },
    'harvest': {
        'primary_nutrients': [],
        'secondary_nutrients': ['Alg-A-Mic', 'Acti Vera'],
        'micronutrients': [],
        'notes': 'Nur Wasser oder Stress-Schutz, alle Dünger weglassen',
        'warnings': ['Keine Dünger mehr', 'Nur Wasser verwenden']
    }
}

# Ernte-Zeitpunkt-Begründungen
HARVEST_REASONS = {
    'early_harvest': {
        'trichome_state': 'Überwiegend milchig',
        'effect': 'Energisch, kreativ, fokussiert',
        'thc_content': 'Hoch',
        'cbd_content': 'Niedrig',
        'flavor': 'Frisch, zitrusartig',
        'recommendation': 'Für energetische Effekte, tagsüber',
        'warning': 'Kann zu Paranoia führen bei empfindlichen Personen'
    },
    'optimal_harvest': {
        'trichome_state': 'Milchig-bernsteinfarben gemischt',
        'effect': 'Ausgewogen, entspannt, euphorisch',
        'thc_content': 'Optimal',
        'cbd_content': 'Ausgewogen',
        'flavor': 'Vollmundig, komplex',
        'recommendation': 'Für die meisten Anwender optimal',
        'warning': 'Keine spezielle Warnung'
    },
    'late_harvest': {
        'trichome_state': 'Überwiegend bernsteinfarben',
        'effect': 'Sedierend, entspannend, schlaffördernd',
        'thc_content': 'Degradiert zu CBN',
        'cbd_content': 'Höher',
        'flavor': 'Erdig, würzig',
        'recommendation': 'Für Schlaf, Entspannung, abends',
        'warning': 'Sehr sedierend, nicht für tagsüber'
    }
}

def get_trichome_development(bloom_week):
    """
    Gibt Trichome-Entwicklung für eine bestimmte Blüte-Woche zurück
    
    Args:
        bloom_week (int): Blüte-Woche (1-9)
    
    Returns:
        dict: Trichome-Entwicklungsdaten
    """
    week_key = f'week_{bloom_week}'
    return TRICHOME_DEVELOPMENT.get(week_key, TRICHOME_DEVELOPMENT['week_1'])

def get_environment_conditions(phase):
    """
    Gibt Umgebungsbedingungen für eine Phase zurück
    
    Args:
        phase (str): Phasenschlüssel
    
    Returns:
        dict: Umgebungsbedingungen
    """
    return ENVIRONMENT_CONDITIONS.get(phase, ENVIRONMENT_CONDITIONS['vegetative_mid'])

def get_extended_nutrients(phase):
    """
    Gibt erweiterte Nährstoff-Empfehlungen für eine Phase zurück
    
    Args:
        phase (str): Phasenschlüssel
    
    Returns:
        dict: Erweiterte Nährstoff-Empfehlungen
    """
    return EXTENDED_NUTRIENTS.get(phase, EXTENDED_NUTRIENTS['vegetative_mid'])

def get_harvest_reason(trichome_state):
    """
    Gibt Ernte-Begründung basierend auf Trichome-Zustand zurück
    
    Args:
        trichome_state (str): Trichome-Zustand
    
    Returns:
        dict: Ernte-Begründung
    """
    if 'milchig' in trichome_state and 'bernstein' not in trichome_state:
        return HARVEST_REASONS['early_harvest']
    elif 'bernstein' in trichome_state and 'milchig' in trichome_state:
        return HARVEST_REASONS['optimal_harvest']
    elif 'bernstein' in trichome_state and 'milchig' not in trichome_state:
        return HARVEST_REASONS['late_harvest']
    else:
        return HARVEST_REASONS['optimal_harvest']

def calculate_bloom_week(bloom_start_date, current_date=None):
    """
    Berechnet die aktuelle Blüte-Woche
    
    Args:
        bloom_start_date (str): Blüte-Start-Datum (YYYY-MM-DD)
        current_date (str, optional): Aktuelles Datum
    
    Returns:
        int: Blüte-Woche (1-9)
    """
    if not bloom_start_date:
        return 1
    
    if not current_date:
        current_date = datetime.now().strftime('%Y-%m-%d')
    
    try:
        bloom_start = datetime.strptime(bloom_start_date, '%Y-%m-%d')
        current = datetime.strptime(current_date, '%Y-%m-%d')
        
        days_diff = (current - bloom_start).days
        weeks = (days_diff // 7) + 1
        
        # Begrenzen auf 1-9 Wochen
        return max(1, min(9, weeks))
    except:
        return 1

def get_complete_phase_details(plant, brand='biobizz'):
    """
    Gibt vollständige Phasen-Details zurück
    
    Args:
        plant (dict): Pflanzendaten
        brand (str): Düngermarke
    
    Returns:
        dict: Vollständige Phasen-Details
    """
    if not plant or 'start_date' not in plant:
        return None
    
    # Aktuelle Phase berechnen
    from .core import PhaseLogic
    phase_logic = PhaseLogic()
    
    start_date = plant['start_date']
    flowering_date = plant.get('bloom_start') or plant.get('flowering_date') or plant.get('flower_start_date')
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    current_phase = phase_logic.get_current_phase(start_date, flowering_date, current_date)
    phase_key = current_phase.get('current_phase', current_phase.get('sub_phase'))
    
    # Blüte-Woche berechnen
    bloom_week = 1
    if flowering_date:
        bloom_week = calculate_bloom_week(flowering_date, current_date)
    
    # Alle Details sammeln
    details = {
        'current_phase': current_phase,
        'phase_key': phase_key,
        'bloom_week': bloom_week,
        'trichome_development': get_trichome_development(bloom_week),
        'environment_conditions': get_environment_conditions(phase_key),
        'extended_nutrients': get_extended_nutrients(phase_key),
        'harvest_reason': get_harvest_reason(get_trichome_development(bloom_week)['trichome_state'])
    }
    
    return details 