/**
 * Plant Detail Module
 * Handles plant detail page functionality with Widget Manager integration
 */

class PlantDetail {
    constructor() {
        // Globale Instanz für Event-Handler verfügbar machen
        window.plantDetailInstance = this;
        this.setDefaultDate();
        this.initWidgetManager();
        this.initWidgetsAsync();
        this.checkForPlantUpdate();
    }

    /**
     * Asynchrone Widget-Initialisierung
     */
    async initWidgetsAsync() {
        await this.initWidgets();
    }
    
    setDefaultDate() {
        const today = new Date().toISOString().split('T')[0];
        const dateInput = document.getElementById('entry-date');
        if (dateInput && !dateInput.value) {
            dateInput.value = today;
        }
    }

    /**
     * Prüfen ob Pflanze aktualisiert wurde und Events auslösen
     */
    checkForPlantUpdate() {
        // Prüfen ob URL-Parameter 'updated' vorhanden ist
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('updated') === 'true') {
            
            // Event für Pflanzendaten-Update auslösen
            document.dispatchEvent(new CustomEvent('plantDataUpdated'));
            
            // URL-Parameter entfernen (damit Event nicht mehrfach ausgelöst wird)
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('updated');
            window.history.replaceState({}, '', newUrl);
            
            // Kurze Verzögerung für Widget-Update
            setTimeout(() => {
                this.reloadAllWidgets();
            }, 500);
        }
    }

    /**
     * Widget Manager initialisieren und Event-Listener einrichten
     */
    initWidgetManager() {
        // Warten bis Widget Manager verfügbar ist
        if (window.widgetManager) {
            this.setupWidgetManagerEvents();
        } else {
            // Fallback: Warten und erneut versuchen
            setTimeout(() => this.initWidgetManager(), 100);
        }
    }

    /**
     * Warten bis Widget-Klassen registriert sind
     */
    waitForWidgetRegistration() {
        return new Promise((resolve) => {
            const checkRegistration = () => {
                if (window.widgetManager && window.widgetManager.widgetRegistry.size > 0) {
                    resolve();
                } else {
                    setTimeout(checkRegistration, 50);
                }
            };
            checkRegistration();
        });
    }

    /**
     * Event-Listener für Widget Manager einrichten
     */
    setupWidgetManagerEvents() {
        const manager = window.widgetManager;
        
        // Event-Listener für Widget-Laden
        manager.on('widget:loaded', (data) => {
        });

        // Event-Listener für Widget-Entladen
        manager.on('widget:unloaded', (data) => {
        });

        // Event-Listener für Widget-Fehler
        manager.on('widget:error', (data) => {
            console.error(`Widget-Fehler: ${data.widgetType} - ${data.error}`);
        });
    }

    /**
     * Widgets über Widget Manager initialisieren
     */
    async initWidgets() {
        if (!window.widgetManager) {
            console.warn('Widget Manager nicht verfügbar, verwende Fallback-Initialisierung');
            this.initWidgetsFallback();
            return;
        }

        // Warten bis Widget-Klassen registriert sind
        await this.waitForWidgetRegistration();

        const manager = window.widgetManager;

        // Strain-Type-Widget über Widget Manager laden
        const strainTypeContainer = document.getElementById('strainTypeContainer');
        if (strainTypeContainer) {
            const plantId = strainTypeContainer.getAttribute('data-plant-id');
            if (plantId) {
                manager.loadWidget('strain-type', 'strainTypeContainer', plantId);
            }
        }

        // VPD-Widget über Widget Manager laden
        const vpdContainer = document.getElementById('vpd-optimization-box');
        if (vpdContainer) {
            const currentPhase = window.currentPhaseKey || window.currentPhase || 'flowering_late';
            manager.loadWidget('vpd', 'vpd-optimization-box', { currentPhase });
        }

        // Watering-Widget über Widget Manager laden
        const wateringContainer = document.getElementById('watering-plan-box');
        if (wateringContainer) {
            const currentPhase = window.currentPhaseKey || window.currentPhase || 'flowering_late';
            manager.loadWidget('watering', 'watering-plan-box', { currentPhase });
        }

        // Lighting-Widget über Widget Manager laden
        const lightingContainer = document.getElementById('lighting-plan-box');
        if (lightingContainer) {
            const currentPhase = window.currentPhaseKey || window.currentPhase || 'flowering_late';
            const plantId = document.getElementById('strainTypeContainer')?.getAttribute('data-plant-id');
            manager.loadWidget('lighting', 'lighting-plan-box', { 
                currentPhase,
                plantId 
            });
        }

        // Stress-Management-Widget über Widget Manager laden
        const stressContainer = document.getElementById('stressManagementContent');
        if (stressContainer) {
            // Hole plantId wie beim Strain-Type-Widget
            const plantId = document.getElementById('strainTypeContainer')?.getAttribute('data-plant-id');
            // Hole currentPhase aus globalem Kontext (wird im Template gesetzt)
            const currentPhase = window.currentPhase || null;
            if (plantId && currentPhase) {
                manager.loadWidget('stress-management', 'stressManagementContent', {
                    plantId,
                    currentPhase
                });
            } else {
                console.error('Stress-Widget: plantId oder currentPhase fehlt!', { plantId, currentPhase });
            }
        }

        // Nährstoff-Diagnose-Widget über Widget Manager laden
        const nutrientContainer = document.getElementById('nutrient-diagnosis-box');
        if (nutrientContainer) {
            const currentPhase = window.currentPhaseKey || window.currentPhase || 'vegetative_middle';
            manager.loadWidget('nutrient', 'nutrient-diagnosis-box', { currentPhase });
        }

        // Trichom-Analyse-Widget über Widget Manager laden
        const trichomeContainer = document.getElementById('trichome-analysis-box');
        if (trichomeContainer) {
            const plantId = document.getElementById('strainTypeContainer')?.getAttribute('data-plant-id');
            if (plantId) {
                manager.loadWidget('trichome', 'trichome-analysis-box', { plantId });
            }
        }

        // Flowering-Widget über Widget Manager laden
        const floweringContainer = document.getElementById('flowering-timeline-box');
        if (floweringContainer) {
            const plantId = document.getElementById('strainTypeContainer')?.getAttribute('data-plant-id');
            if (plantId) {
                manager.loadWidget('flowering', 'flowering-timeline-box', { plantId });
            }
        }

        // Vegetation-Widget über Widget Manager laden
        const vegetationContainer = document.getElementById('vegetationManagementWidget');
        if (vegetationContainer) {
            const plantId = document.getElementById('strainTypeContainer')?.getAttribute('data-plant-id');
            if (plantId) {
                manager.loadWidget('vegetation', 'vegetationManagementWidget', { plantId });
            } else {
                console.warn('Plant-Detail: Keine plantId für Vegetation-Widget gefunden');
            }
        } else {
            console.warn('Plant-Detail: vegetationManagementWidget Container nicht gefunden');
        }

        // Neue interaktive Widgets initialisieren
        this.initInteractiveWidgets();

        // Widget Manager Status loggen
    }

    /**
     * Tooltips für statische Templates initialisieren
     */
    initTooltips() {
        // Warten bis Bootstrap verfügbar ist
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        } else {
            // Fallback: Warten und erneut versuchen
            setTimeout(() => this.initTooltips(), 500);
        }
    }

    /**
     * Interaktive Widgets initialisieren
     */
    initInteractiveWidgets() {
        const plantId = document.getElementById('strainTypeContainer')?.getAttribute('data-plant-id');
        const currentPhase = window.currentPhaseData || window.currentPhase || null;

        if (!plantId) {
            console.error('Plant ID nicht gefunden für interaktive Widgets');
            return;
        }

        // Phase-Notizen Widget
        const notesContainer = document.getElementById('phase-notes-container');
        if (notesContainer && typeof initPhaseNotesWidget === 'function') {
            window.phaseNotesWidget = initPhaseNotesWidget('phase-notes-container', plantId);
            if (currentPhase && window.phaseNotesWidget) {
                window.phaseNotesWidget.setCurrentPhase(currentPhase);
            }
        }

        // Phase-Checklisten Widget
        const checklistContainer = document.getElementById('phase-checklist-container');
        if (checklistContainer && typeof initPhaseChecklistWidget === 'function') {
            window.phaseChecklistWidget = initPhaseChecklistWidget('phase-checklist-container', plantId);
            if (currentPhase && window.phaseChecklistWidget) {
                window.phaseChecklistWidget.setCurrentPhase(currentPhase);
            }
        }

        // Phase-Warnungen Widget
        const warningsContainer = document.getElementById('phase-warnings-container');
        if (warningsContainer && typeof initPhaseWarningsWidget === 'function') {
            window.phaseWarningsWidget = initPhaseWarningsWidget('phase-warnings-container', plantId);
            if (currentPhase && window.phaseWarningsWidget) {
                window.phaseWarningsWidget.setCurrentPhase(currentPhase);
            }
        }
        
        // Tooltips für statische Templates initialisieren
        this.initTooltips();
    }

    /**
     * Fallback-Initialisierung für Widgets (alte Methode)
     */
    initWidgetsFallback() {
        // Strain-Type-Widget wird jetzt über Widget Manager geladen
        // Keine Fallback-Initialisierung mehr nötig
    }

    // Hilfsfunktion für benutzerfreundliche Phase-Namen
    getFriendlyPhaseName(phaseKey) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phaseKey] || phaseKey.replace('_', ' ');
    }

    /**
     * Widget über Widget Manager neu laden
     * @param {string} widgetType - Typ des Widgets
     * @param {string} containerId - Container-ID
     */
    reloadWidget(widgetType, containerId) {
        if (window.widgetManager) {
            // Widget entladen und neu laden
            window.widgetManager.unloadWidget(widgetType, containerId);
            setTimeout(() => {
                window.widgetManager.loadWidget(widgetType, containerId);
            }, 100);
        }
    }

    /**
     * Alle Widgets neu laden
     */
    reloadAllWidgets() {
        if (window.widgetManager) {
            const status = window.widgetManager.getStatus();
            
            // Alle Widgets entladen
            window.widgetManager.unloadAllWidgets();
            
            // Nach kurzer Pause alle Widgets neu laden
            setTimeout(() => {
                this.initWidgets();
            }, 200);
        }
    }

    deletePlant(plantId) {
        if (window.growDiaryApp && window.growDiaryApp.modules.deleteModal) {
            // Get plant name from the page
            const plantNameElement = document.querySelector('.card-title');
            const plantName = plantNameElement ? plantNameElement.textContent : 'diese Pflanze';
            
            window.growDiaryApp.modules.deleteModal.open(plantId, plantName);
        } else {
            // Fallback for direct modal usage
            const modal = document.getElementById('delete-modal');
            const message = document.getElementById('delete-message');
            const confirmBtn = document.getElementById('confirm-delete');
            
            if (modal && message && confirmBtn) {
                message.textContent = 'Möchtest du diese Pflanze wirklich löschen? Alle zugehörigen Einträge werden ebenfalls gelöscht.';
                
                confirmBtn.onclick = () => {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/plant/${plantId}/delete`;
                    document.body.appendChild(form);
                    form.submit();
                };
                
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();
            }
        }
    }
    
    deleteEntry(entryId) {
        if (window.growDiaryApp && window.growDiaryApp.modules.deleteModal) {
            window.growDiaryApp.modules.deleteModal.open(entryId, 'diesen Eintrag');
        } else {
            // Fallback for direct modal usage
            const modal = document.getElementById('delete-modal');
            const message = document.getElementById('delete-message');
            const confirmBtn = document.getElementById('confirm-delete');
            
            if (modal && message && confirmBtn) {
                message.textContent = 'Möchtest du diesen Eintrag wirklich löschen?';
                
                confirmBtn.onclick = () => {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/entry/${entryId}/delete`;
                    document.body.appendChild(form);
                    form.submit();
                };
                
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();
            }
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new PlantDetail();
});

// Export for use in other modules
window.PlantDetail = PlantDetail;

// Global functions for onclick handlers
window.deletePlant = (plantId) => {
    const plantDetail = new PlantDetail();
    plantDetail.deletePlant(plantId);
};

window.deleteEntry = (entryId) => {
    const plantDetail = new PlantDetail();
    plantDetail.deleteEntry(entryId);
}; 