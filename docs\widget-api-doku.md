# 🔌 Widget-API-Dokumentation

## 1. Übersicht
Alle Widget-spezifischen APIs sind modular unter `/api/widgets/<widget>` erreichbar.

## 2. Endpunkte & Beispiele

### VPD-Widget
- **GET** `/api/widgets/vpd/<plant_id>`
  - Liefert aktuelle VPD-Daten für eine Pflanze
  - **Beispiel-Response:**
    ```json
    { "vpd": 1.2, "temperature": 25, "humidity": 60 }
    ```
- **POST** `/api/widgets/vpd/<plant_id>`
  - Aktualisiert VPD-Parameter
  - **Body:** `{ "temperature": 26, "humidity": 65 }`
  - **Response:** `{ "success": true }`

### Lighting-Widget
- **GET** `/api/widgets/lighting/<plant_id>`
  - Liefert aktuelle Beleuchtungsdaten
- **POST** `/api/widgets/lighting/<plant_id>`
  - Aktualisiert Beleuchtungsparameter

### Watering-Widget
- **GET** `/api/widgets/watering/<plant_id>`
- **POST** `/api/widgets/watering/<plant_id>`

### Stress-Widget
- **GET** `/api/widgets/stress/<plant_id>`
- **POST** `/api/widgets/stress/<plant_id>`

## 3. Authentifizierung
- Standardmäßig keine Authentifizierung für lokale Nutzung.
- Bei Cloud- oder Multi-User-Betrieb: Auth-Token im Header senden.

## 4. Fehlerbehandlung
- Fehler werden als JSON mit `error`-Feld zurückgegeben:
  ```json
  { "error": "Ungültige Eingabe" }
  ```
- HTTP-Statuscodes werden korrekt gesetzt (400, 404, 500 etc.)

## 5. Hinweise
- Alle Endpunkte akzeptieren und liefern JSON.
- Bei Änderungen an der API bitte diese Doku aktualisieren!

---
*Letzte Aktualisierung: {{ "now" | date("%d.%m.%Y %H:%M") }}* 