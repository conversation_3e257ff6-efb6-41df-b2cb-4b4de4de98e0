/* ===== EDIT PLANT FORM STYLES ===== */

/* Custom input fields */
.custom-input-field {
  display: none;
}

/* Autopot options section */
.autopot-options-section {
  display: none;
}

/* External data rows */
.external-data-row {
  margin-bottom: 0.5rem;
}

/* Form validation styles */
.form-control.is-invalid {
  border-color: var(--bs-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
  border-color: var(--bs-success);
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

/* Custom select styling */
.form-select:focus {
  border-color: var(--grow-primary);
  box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Autopot checkbox styling */
.form-check-input:checked {
  background-color: var(--grow-primary);
  border-color: var(--grow-primary);
}

/* Button spacing */
.btn-group .btn {
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

/* Dark mode adjustments */
[data-theme="dark"] .custom-input-field {
  background-color: var(--bs-dark);
  border-color: var(--bs-gray-600);
  color: var(--bs-light);
}

[data-theme="dark"] .form-control:focus {
  background-color: var(--bs-dark);
  border-color: var(--grow-primary);
  color: var(--bs-light);
} 