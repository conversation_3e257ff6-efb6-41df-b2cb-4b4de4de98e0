#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validation-Utilities für das Grow-Tagebuch
"""

import re
from datetime import datetime

def validate_date_format(date_str):
    """
    Validiert das Datumsformat (YYYY-MM-DD)
    
    Args:
        date_str (str): Zu validierendes Datum
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    if not date_str:
        return False
    
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def validate_phase_name(phase_name):
    """
    Validiert einen Phasennamen
    
    Args:
        phase_name (str): Zu validierender Phasename
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    valid_phases = [
        'germination',
        'vegetative_early',
        'vegetative_middle', 
        'vegetative_late',
        'flowering_early',
        'flowering_middle',
        'flowering_late',
        'flush'
    ]
    
    return phase_name in valid_phases

def validate_brand_name(brand_name):
    """
    Validiert einen Markennamen
    
    Args:
        brand_name (str): Zu validierender Markenname
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    valid_brands = ['biobizz', 'canna', 'plagron']
    return brand_name in valid_brands

def validate_fertilizer_name(fertilizer_name, brand='biobizz'):
    """
    Validiert einen Düngernamen für eine bestimmte Marke
    
    Args:
        fertilizer_name (str): Zu validierender Düngername
        brand (str): Düngermarke
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    from ..data.fertilizer_brands import FERTILIZER_BRANDS
    
    if brand not in FERTILIZER_BRANDS:
        return False
    
    valid_fertilizers = list(FERTILIZER_BRANDS[brand]['fertilizers'].keys())
    return fertilizer_name in valid_fertilizers

def validate_ec_value(ec_value):
    """
    Validiert einen EC-Wert
    
    Args:
        ec_value (float): Zu validierender EC-Wert
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    try:
        ec = float(ec_value)
        return 0.0 <= ec <= 5.0  # Realistischer Bereich
    except (ValueError, TypeError):
        return False

def validate_ph_value(ph_value):
    """
    Validiert einen pH-Wert
    
    Args:
        ph_value (float): Zu validierender pH-Wert
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    try:
        ph = float(ph_value)
        return 0.0 <= ph <= 14.0
    except (ValueError, TypeError):
        return False

def validate_temperature(temperature):
    """
    Validiert eine Temperatur
    
    Args:
        temperature (float): Zu validierende Temperatur
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    try:
        temp = float(temperature)
        return -10.0 <= temp <= 50.0  # Realistischer Bereich für Pflanzen
    except (ValueError, TypeError):
        return False

def validate_humidity(humidity):
    """
    Validiert eine Luftfeuchtigkeit
    
    Args:
        humidity (float): Zu validierende Luftfeuchtigkeit
    
    Returns:
        bool: True wenn gültig, False sonst
    """
    try:
        hum = float(humidity)
        return 0.0 <= hum <= 100.0
    except (ValueError, TypeError):
        return False

def sanitize_input(input_str):
    """
    Bereinigt Benutzereingaben
    
    Args:
        input_str (str): Zu bereinigende Eingabe
    
    Returns:
        str: Bereinigte Eingabe
    """
    if not input_str:
        return ""
    
    # Entferne gefährliche Zeichen
    sanitized = re.sub(r'[<>"\']', '', str(input_str))
    
    # Entferne mehrfache Leerzeichen
    sanitized = re.sub(r'\s+', ' ', sanitized)
    
    return sanitized.strip() 