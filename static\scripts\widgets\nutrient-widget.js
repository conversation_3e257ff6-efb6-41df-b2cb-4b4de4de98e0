/**
 * Nutrient Widget Module
 * Handles nutrient diagnosis and monitoring functionality
 */

class NutrientWidget {
    constructor(containerId = 'nutrient-diagnosis-box', options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        // Stelle sicher, dass currentPhase ein String ist
        let phase = options.currentPhase || window.currentPhaseKey || 'vegetative_middle';
        if (typeof phase === 'object' && phase !== null) {
            phase = 'flowering_middle'; // Fallback wenn es ein Objekt ist
        }
        this.currentPhase = phase;
        this.substrate = 'soil'; // soil, coco, hydro
        this.currentEC = 1.2;
        this.currentPH = 6.0;
        this.drainEC = null;
        this.drainPH = null;
        
        if (this.container) {
            this.init();
        } else {
            console.warn(`Nutrient Widget: Container ${containerId} nicht gefunden`);
        }
    }

    init() {
        // Guidelines laden, falls noch nicht geschehen
        if (!NutrientWidget.nutrientGuidelinesLoaded) {
            NutrientWidget.loadNutrientGuidelines();
        }
        this.loadNutrientDiagnosis();
        this.setupEventListeners();
    }

    // Hilfsfunktion für benutzerfreundliche Phase-Namen
    getFriendlyPhaseName(phaseKey) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phaseKey] || phaseKey.replace('_', ' ');
    }

    // Event-Listener für Widget-Interaktionen
    setupEventListeners() {
        // Event-Delegation für dynamisch erstellte Elemente
        this.container.addEventListener('click', (e) => {
            if (e.target.id === 'nutrient-calc-btn') {
                this.calculateNutrients();
            }
        });

        // Input-Event-Listener für Live-Updates
        this.container.addEventListener('input', (e) => {
            if (e.target.id === 'substrate-input' || e.target.id === 'ec-input' || 
                e.target.id === 'ph-input' || e.target.id === 'drain-ec-input' || 
                e.target.id === 'drain-ph-input') {
                this.updateInputValues();
            }
        });
    }

    // Aktuelle Input-Werte aktualisieren
    updateInputValues() {
        const substrateInput = document.getElementById('substrate-input');
        const ecInput = document.getElementById('ec-input');
        const phInput = document.getElementById('ph-input');
        const drainECInput = document.getElementById('drain-ec-input');
        const drainPHInput = document.getElementById('drain-ph-input');
        
        if (substrateInput) this.substrate = substrateInput.value;
        if (ecInput) this.currentEC = parseFloat(ecInput.value) || 1.2;
        if (phInput) this.currentPH = parseFloat(phInput.value) || 6.0;
        if (drainECInput) this.drainEC = parseFloat(drainECInput.value) || null;
        if (drainPHInput) this.drainPH = parseFloat(drainPHInput.value) || null;
    }

    // Nährstoff-Berechnung auslösen
    async calculateNutrients() {
        this.updateInputValues();
        await this.performAdvancedAnalysis();
    }

    // Erweiterte Analyse durchführen
    async performAdvancedAnalysis() {
        if (!this.container) return;

        this.container.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Führe erweiterte Analyse durch...</div>';

        try {
            // Plant ID aus DOM oder URL holen
            let plantId = null;
            const plantIdElement = document.querySelector('[data-plant-id]');
            if (plantIdElement) {
                plantId = plantIdElement.getAttribute('data-plant-id');
            }

            // Strain-Type ermitteln
            const strainType = this.getStrainType();
            
            // API-Aufruf für erweiterte Analyse
            const analysisData = {
                input_ec: this.currentEC,
                input_ph: this.currentPH,
                drain_ec: this.drainEC,
                drain_ph: this.drainPH,
                phase: this.currentPhase,
                strain_type: strainType,
                substrate: this.substrate,
                plant_id: plantId
            };

            const response = await fetch('/api/nutrients/analyse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(analysisData)
            });

            if (!response.ok) {
                throw new Error('Erweiterte Analyse konnte nicht durchgeführt werden');
            }
            
            const data = await response.json();
            
            // Widget mit Analyse-Ergebnissen rendern
            this.renderAdvancedAnalysis(data);
            
        } catch (err) {
            this.container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler bei der erweiterten Analyse: ${err.message}
                </div>
            `;
        }
    }

    // Hauptfunktion: Nährstoff-Diagnose laden
    async loadNutrientDiagnosis() {
        if (!this.container) return;

        this.container.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Lade Nährstoff-Diagnose...</div>';

        try {
            // Plant ID aus DOM oder URL holen
            let plantId = null;
            const plantIdElement = document.querySelector('[data-plant-id]');
            if (plantIdElement) {
                plantId = plantIdElement.getAttribute('data-plant-id');
            } else {
                const urlParams = new URLSearchParams(window.location.search);
                plantId = urlParams.get('plant_id');
            }

            // Strain-Type ermitteln
            const strainType = this.getStrainType();
            
            // API-Aufruf für erweiterte Diagnose
            const url = `/api/nutrients/diagnose?phase=${encodeURIComponent(this.currentPhase)}&strain_type=${encodeURIComponent(strainType)}&substrate=${encodeURIComponent(this.substrate)}${plantId ? `&plant_id=${plantId}` : ''}`;
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error('Nährstoff-Diagnose konnte nicht geladen werden');
            }
            
            const data = await response.json();
            
            // Aktuelle Werte hinzufügen
            data.currentEC = this.currentEC;
            data.currentPH = this.currentPH;
            data.drainEC = this.drainEC;
            data.drainPH = this.drainPH;
            
            // Diagnose durchführen falls Werte vorhanden
            if (this.currentEC && this.currentPH && data.targets && data.targets.ec && data.targets.ph) {
                data.diagnosis = this.performDiagnosis(
                    this.currentEC, 
                    this.currentPH, 
                    data.targets.ec.min, 
                    data.targets.ec.max, 
                    data.targets.ph
                );
            }

            this.renderNutrientWidget(data);
            
        } catch (err) {
            this.container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler beim Laden der Nährstoff-Diagnose: ${err.message}
                </div>
            `;
        }
    }

    // Strain-Type ermitteln
    getStrainType() {
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type') || 'photoperiodic';
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText && strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        return strainType || 'photoperiodic';
    }

    // Diagnose durchführen
    performDiagnosis(ec, ph, ecMin, ecMax, phRange) {
        const issues = [];
        let status = 'optimal';
        let statusMessage = 'Nährstoffwerte im optimalen Bereich!';
        let recommendations = [];

        // EC-Diagnose
        if (ec < ecMin) {
            issues.push('EC zu niedrig');
            status = 'low';
            statusMessage = 'EC-Wert zu niedrig';
        } else if (ec > ecMax) {
            issues.push('EC zu hoch');
            status = 'high';
            statusMessage = 'EC-Wert zu hoch';
        }

        // pH-Diagnose
        if (ph < phRange.min) {
            issues.push('pH zu niedrig');
            if (status === 'optimal') {
                status = 'low';
                statusMessage = 'pH-Wert zu niedrig';
            } else {
                statusMessage = 'Mehrere Probleme: ' + issues.join(', ');
            }
        } else if (ph > phRange.max) {
            issues.push('pH zu hoch');
            if (status === 'optimal') {
                status = 'high';
                statusMessage = 'pH-Wert zu hoch';
            } else {
                statusMessage = 'Mehrere Probleme: ' + issues.join(', ');
            }
        }

        // Korrekturstrategien basierend auf Guidelines
        const guidelines = NutrientWidget.nutrientGuidelines;
        if (guidelines && guidelines.correctionStrategies) {
            if (ec > ecMax) {
                recommendations.push(...guidelines.correctionStrategies.highEC);
            } else if (ec < ecMin) {
                recommendations.push(...guidelines.correctionStrategies.lowEC);
            }
            
            if (ph > phRange.max) {
                recommendations.push(...guidelines.correctionStrategies.phTooHigh);
            } else if (ph < phRange.min) {
                recommendations.push(...guidelines.correctionStrategies.phTooLow);
            }
        }

        return {
            status: status,
            statusMessage: statusMessage,
            issues: issues,
            recommendations: recommendations
        };
    }

    // Nährstoff-Widget rendern
    renderNutrientWidget(data) {
        const phaseName = this.getFriendlyPhaseName(data.phase);
        const strainType = data.strain_type || data.strainType || 'photoperiodic';
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        const html = `
            <div class="nutrient-widget-card">
                <div class="nutrient-widget-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fa-solid fa-flask"></i>
                        Nährstoff-Diagnose <span class="nutrient-phase-name">${phaseName}</span>
                        <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
                        <span class="badge bg-success ms-2">${this.substrate.toUpperCase()}</span>
                    </div>
                    <div>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-light me-2" 
                            data-bs-toggle="modal" 
                            data-bs-target="#nutrient-guidelines-modal"
                            title="Nährstoff-Richtlinien & Korrekturstrategien anzeigen"
                        >
                            <i class="fas fa-book-open"></i>
                        </button>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-light" 
                            data-bs-toggle="modal" 
                            data-bs-target="#nutrient-history-modal"
                            title="Nährstoff-Historie anzeigen"
                        >
                            <i class="fas fa-chart-line"></i>
                        </button>
                    </div>
                </div>
                <div class="nutrient-widget-body">
                    ${this.renderPhaseGuidelines(data)}
                    ${this.renderCurrentValues(data)}
                    ${this.renderDiagnosis(data)}
                    ${this.renderNutrientSettings()}
                    ${this.renderGuidelinesPreview()}
                    ${data.history && data.history.length > 0 ? this.renderHistoryPreview(data.history) : ''}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        
        // Guidelines-Modal erstellen
        this.createGuidelinesModal();
        this.createHistoryModal();
    }

    // Erweiterte Analyse rendern
    renderAdvancedAnalysis(data) {
        const phaseName = this.getFriendlyPhaseName(data.phase);
        const strainType = data.strain || data.strain_type || 'photoperiodic';
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        const analysis = data.analysis;
        
        const html = `
            <div class="nutrient-widget-card">
                <div class="nutrient-widget-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fa-solid fa-microscope"></i>
                        Erweiterte Nährstoff-Analyse <span class="nutrient-phase-name">${phaseName}</span>
                        <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
                        <span class="badge bg-success ms-2">${data.substrate.toUpperCase()}</span>
                    </div>
                    <div>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-light me-2" 
                            data-bs-toggle="modal" 
                            data-bs-target="#nutrient-guidelines-modal"
                            title="Nährstoff-Richtlinien & Korrekturstrategien anzeigen"
                        >
                            <i class="fas fa-book-open"></i>
                        </button>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-light" 
                            data-bs-toggle="modal" 
                            data-bs-target="#nutrient-history-modal"
                            title="Nährstoff-Historie anzeigen"
                        >
                            <i class="fas fa-chart-line"></i>
                        </button>
                    </div>
                </div>
                <div class="nutrient-widget-body">
                    ${this.renderAnalysisOverview(data)}
                    ${this.renderComparisonTable(data)}
                    ${this.renderAlertsAndWarnings(analysis)}
                    ${this.renderRecommendations(analysis)}
                    ${this.renderNutrientSettings()}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        
        // Guidelines-Modal erstellen
        this.createGuidelinesModal();
        this.createHistoryModal();
    }

    // Phasen-spezifische Guidelines rendern
    renderPhaseGuidelines(data) {
        const guideline = data.guidelines || data.phaseGuideline;
        const strainAdjustment = data.strain_adjustment || data.strainAdjustment;
        
        if (!guideline) {
            return `
                <div class="nutrient-section-title nutrient-guidelines">
                    <i class="fa-solid fa-exclamation-triangle me-2"></i>Keine Guidelines verfügbar
                </div>
                <div class="nutrient-section-content nutrient-guidelines-content">
                    <div class="alert alert-warning">
                        <i class="fa fa-info-circle me-2"></i>
                        Keine Guidelines für Phase "${data.phase}" gefunden.
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="nutrient-section-title nutrient-guidelines">
                <i class="fa-solid fa-check-circle me-2"></i>Phasen-spezifische Empfehlungen
                <span class="badge bg-info ms-2">${this.getFriendlyPhaseName(data.phase)}</span>
            </div>
            <div class="nutrient-section-content nutrient-guidelines-content">
                <div class="nutrient-phase-description">
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>Phase:</strong> ${guideline.description || 'Keine Beschreibung verfügbar'}
                </div>
                <div><span class="nutrient-label">EC-Bereich:</span> <b>${data.targets.ec.min.toFixed(1)}-${data.targets.ec.max.toFixed(1)} mS/cm</b></div>
                <div><span class="nutrient-label">pH-Bereich:</span> <b>${data.targets.ph.min}-${data.targets.ph.max}</b></div>
                <div><span class="nutrient-label">Strain-Anpassung:</span> <b>${strainAdjustment?.note || 'Keine Anpassung verfügbar'}</b></div>
                ${(Array.isArray(guideline.macronutrients) && guideline.macronutrients.length > 0) ? `
                    <div class="nutrient-macronutrients">
                        <strong>Makronährstoffe:</strong>
                        <ul class="nutrient-macro-list">
                            ${(Array.isArray(guideline.macronutrients) ? guideline.macronutrients : []).map(n => `<li>${n}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                ${(Array.isArray(guideline.micronutrients) && guideline.micronutrients.length > 0) ? `
                    <div class="nutrient-micronutrients">
                        <strong>Mikronährstoffe:</strong>
                        <ul class="nutrient-micro-list">
                            ${(Array.isArray(guideline.micronutrients) ? guideline.micronutrients : []).map(n => `<li>${n}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                ${(Array.isArray(guideline.notes) && guideline.notes.length > 0) ? `
                    <div class="nutrient-phase-notes">
                        <strong>Wichtige Hinweise:</strong>
                        <ul class="nutrient-phase-notes-list">
                            ${(Array.isArray(guideline.notes) ? guideline.notes : []).map(note => `<li>${note}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Aktuelle Werte rendern
    renderCurrentValues(data) {
        return `
            <div class="nutrient-section-title nutrient-current">
                <i class="fa-solid fa-chart-line me-2"></i>Aktuelle Werte
            </div>
            <div class="nutrient-section-content nutrient-current-content">
                <div class="nutrient-values-grid">
                    <div class="nutrient-value-item">
                        <span class="nutrient-label">EC:</span> <span class="nutrient-value">${data.currentEC} mS/cm</span>
                    </div>
                    <div class="nutrient-value-item">
                        <span class="nutrient-label">pH:</span> <span class="nutrient-value">${data.currentPH}</span>
                    </div>
                    ${data.drainEC ? `
                        <div class="nutrient-value-item">
                            <span class="nutrient-label">Drain EC:</span> <span class="nutrient-value">${data.drainEC} mS/cm</span>
                        </div>
                    ` : ''}
                    ${data.drainPH ? `
                        <div class="nutrient-value-item">
                            <span class="nutrient-label">Drain pH:</span> <span class="nutrient-value">${data.drainPH}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Diagnose rendern
    renderDiagnosis(data) {
        const diagnosis = data.diagnosis;
        
        // Prüfen ob diagnosis existiert und gültig ist
        if (!diagnosis || typeof diagnosis !== 'object') {
            return `
                <div class="nutrient-section-title nutrient-diagnosis">
                    <i class="fa-solid fa-stethoscope me-2"></i>Diagnose
                </div>
                <div class="nutrient-section-content nutrient-diagnosis-content">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle me-2"></i>
                        Keine Diagnose verfügbar. Bitte EC und pH Werte eingeben und "Diagnose Berechnen" klicken.
                    </div>
                </div>
            `;
        }
        
        let statusClass = 'text-success';
        let statusIcon = 'fa-check-circle';
        
        if (diagnosis.status === 'low') {
            statusClass = 'text-warning';
            statusIcon = 'fa-exclamation-triangle';
        } else if (diagnosis.status === 'high') {
            statusClass = 'text-danger';
            statusIcon = 'fa-exclamation-circle';
        }

        return `
            <div class="nutrient-section-title nutrient-diagnosis">
                <i class="fa-solid fa-stethoscope me-2"></i>Diagnose
            </div>
            <div class="nutrient-section-content nutrient-diagnosis-content">
                <div class="nutrient-status ${statusClass}">
                    <i class="fa ${statusIcon} me-2"></i>
                    <strong>${diagnosis.statusMessage}</strong>
                </div>
                ${(Array.isArray(diagnosis.issues) && diagnosis.issues.length > 0) ? `
                    <div class="nutrient-issues">
                        <strong>Erkannte Probleme:</strong>
                        <ul class="nutrient-issues-list">
                            ${diagnosis.issues.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                ${(Array.isArray(diagnosis.recommendations) && diagnosis.recommendations.length > 0) ? `
                    <div class="nutrient-recommendations">
                        <strong>Korrekturstrategien:</strong>
                        <ul class="nutrient-recommendations-list">
                            ${diagnosis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Nährstoff-Einstellungen rendern
    renderNutrientSettings() {
        return `
            <div class="nutrient-section-title nutrient-settings">
                <i class="fa-solid fa-cog me-2"></i>Einstellungen
            </div>
            <div class="nutrient-section-content nutrient-settings-content">
                <div class="nutrient-input-grid">
                    <div class="nutrient-input-group">
                        <label for="substrate-input">Substrat</label>
                        <select id="substrate-input" class="nutrient-input">
                            <option value="soil" ${this.substrate === 'soil' ? 'selected' : ''}>Erde</option>
                            <option value="coco" ${this.substrate === 'coco' ? 'selected' : ''}>Coco</option>
                            <option value="hydro" ${this.substrate === 'hydro' ? 'selected' : ''}>Hydro</option>
                        </select>
                    </div>
                    <div class="nutrient-input-group">
                        <label for="ec-input">EC (mS/cm)</label>
                        <input type="number" min="0" max="5" step="0.1" 
                               id="ec-input" value="${this.currentEC}" 
                               class="nutrient-input">
                    </div>
                    <div class="nutrient-input-group">
                        <label for="ph-input">pH</label>
                        <input type="number" min="4" max="9" step="0.1" 
                               id="ph-input" value="${this.currentPH}" 
                               class="nutrient-input">
                    </div>
                </div>
                <div class="nutrient-input-grid">
                    <div class="nutrient-input-group">
                        <label for="drain-ec-input">Drain EC (optional)</label>
                        <input type="number" min="0" max="5" step="0.1" 
                               id="drain-ec-input" value="${this.drainEC || ''}" 
                               class="nutrient-input" placeholder="z.B. 1.8">
                    </div>
                    <div class="nutrient-input-group">
                        <label for="drain-ph-input">Drain pH (optional)</label>
                        <input type="number" min="4" max="9" step="0.1" 
                               id="drain-ph-input" value="${this.drainPH || ''}" 
                               class="nutrient-input" placeholder="z.B. 6.2">
                    </div>
                </div>
                <div class="nutrient-calc-section">
                    <button id="nutrient-calc-btn" class="btn btn-sm btn-primary">
                        <i class="fa fa-calculator"></i> Diagnose Berechnen
                    </button>
                    <span class="nutrient-input-hint">
                        <i class="fa fa-info-circle"></i> (EC/pH-Meter)
                    </span>
                </div>
            </div>
        `;
    }

    // Guidelines-Vorschau rendern
    renderGuidelinesPreview() {
        const isAutoflower = this.getStrainType().toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        return `
            <div class="nutrient-section-title nutrient-guidelines">
                <i class="fa-solid fa-lightbulb me-2"></i>Wichtige Faustregeln
                <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
            </div>
            <div class="nutrient-section-content nutrient-guidelines-content">
                <div class="nutrient-guidelines-preview">
                    <ul class="nutrient-guidelines-list">
                        <li><strong>EC-Monitoring:</strong> Regelmäßig messen, besonders in der Blüte</li>
                        <li><strong>pH-Stabilität:</strong> Drift vermeiden, Nährstoffblockaden verhindern</li>
                        <li><strong>Drain-Kontrolle:</strong> EC im Drain sollte nicht zu stark ansteigen</li>
                        <li><strong>Strain-Anpassung:</strong> ${isAutoflower ? 'Autoflowers: 10-20% weniger Nährstoffe' : 'Photoperiodisch: Volle Nährstoffgabe möglich'}</li>
                    </ul>
                    <div class="nutrient-guidelines-more">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#nutrient-guidelines-modal"
                        >
                            <i class="fas fa-book-open"></i> Alle Richtlinien anzeigen
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Historie-Vorschau rendern
    renderHistoryPreview(history) {
        if (!Array.isArray(history) || history.length === 0) return '';
        
        const latestEntry = history[0];
        const trend = this.calculateTrend(history);
        
        return `
            <div class="nutrient-section-title nutrient-history">
                <i class="fa-solid fa-chart-line me-2"></i>Letzte Messung & Trend
            </div>
            <div class="nutrient-section-content nutrient-history-content">
                <div class="nutrient-history-preview">
                    <div class="nutrient-history-latest">
                        <strong>Letzte Messung:</strong> ${new Date(latestEntry.entry_date).toLocaleDateString('de-DE')}
                        <div class="nutrient-history-values">
                            <span>EC: ${latestEntry.ec_input} mS/cm</span>
                            <span>pH: ${latestEntry.ph_input}</span>
                        </div>
                    </div>
                    <div class="nutrient-history-trend">
                        <strong>Trend:</strong> ${trend}
                    </div>
                    <div class="nutrient-history-more">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#nutrient-history-modal"
                        >
                            <i class="fas fa-chart-line"></i> Vollständige Historie
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Trend berechnen
    calculateTrend(history) {
        if (!Array.isArray(history) || history.length < 2) return 'Keine Daten für Trend';
        
        const recent = history.slice(0, 3);
        const ecValues = recent.map(h => h.ec_input).filter(v => v !== null);
        const phValues = recent.map(h => h.ph_input).filter(v => v !== null);
        
        if (ecValues.length < 2) return 'Unzureichende Daten';
        
        const ecTrend = ecValues[0] > ecValues[ecValues.length - 1] ? '↘️ Fallend' : 
                       ecValues[0] < ecValues[ecValues.length - 1] ? '↗️ Steigend' : '→ Stabil';
        
        return `EC ${ecTrend}`;
    }

    // Analyse-Übersicht rendern
    renderAnalysisOverview(data) {
        const analysis = data.analysis;
        const ecStatus = analysis.ec_status;
        const phStatus = analysis.ph_status;
        
        const getStatusIcon = (status) => {
            switch(status) {
                case 'optimal': return '🟢';
                case 'niedrig': return '🟡';
                case 'hoch': return '🔴';
                default: return '⚪';
            }
        };
        
        const alerts = Array.isArray(analysis.alerts) ? analysis.alerts : [];
        const recommendations = Array.isArray(analysis.recommendations) ? analysis.recommendations : [];
        
        return `
            <div class="nutrient-section-title nutrient-analysis">
                <i class="fa-solid fa-microscope me-2"></i>Analyse-Übersicht
            </div>
            <div class="nutrient-section-content nutrient-analysis-content">
                <div class="nutrient-analysis-grid">
                    <div class="nutrient-analysis-item">
                        <div class="nutrient-analysis-label">EC-Status</div>
                        <div class="nutrient-analysis-value ${ecStatus}">
                            ${getStatusIcon(ecStatus)} ${ecStatus.toUpperCase()}
                        </div>
                    </div>
                    <div class="nutrient-analysis-item">
                        <div class="nutrient-analysis-label">pH-Status</div>
                        <div class="nutrient-analysis-value ${phStatus}">
                            ${getStatusIcon(phStatus)} ${phStatus.toUpperCase()}
                        </div>
                    </div>
                    <div class="nutrient-analysis-item">
                        <div class="nutrient-analysis-label">Alerts</div>
                        <div class="nutrient-analysis-value">
                            ${alerts.length} Problem${alerts.length !== 1 ? 'e' : ''}
                        </div>
                    </div>
                    <div class="nutrient-analysis-item">
                        <div class="nutrient-analysis-label">Empfehlungen</div>
                        <div class="nutrient-analysis-value">
                            ${recommendations.length} Maßnahme${recommendations.length !== 1 ? 'n' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Vergleichstabelle rendern
    renderComparisonTable(data) {
        const analysis = data.analysis;
        const targets = analysis.targets;
        
        const getDeviationClass = (current, targetMin, targetMax) => {
            if (current >= targetMin && current <= targetMax) return 'optimal';
            if (current < targetMin) return 'low';
            return 'high';
        };
        
        const ecDeviationClass = getDeviationClass(data.input_ec, targets.ec.min, targets.ec.max);
        const phDeviationClass = getDeviationClass(data.input_ph, targets.ph.min, targets.ph.max);
        
        return `
            <div class="nutrient-section-title nutrient-comparison">
                <i class="fa-solid fa-table me-2"></i>Soll/Ist-Vergleich
            </div>
            <div class="nutrient-section-content nutrient-comparison-content">
                <div class="nutrient-comparison-table">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Soll-Bereich</th>
                                <th>Ist-Wert</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="${ecDeviationClass}">
                                <td><strong>EC</strong></td>
                                <td>${targets.ec.min.toFixed(1)} - ${targets.ec.max.toFixed(1)} mS/cm</td>
                                <td>${data.input_ec} mS/cm</td>
                                <td>
                                    <span class="badge bg-${ecDeviationClass === 'optimal' ? 'success' : ecDeviationClass === 'low' ? 'warning' : 'danger'}">
                                        ${ecDeviationClass === 'optimal' ? 'Optimal' : ecDeviationClass === 'low' ? 'Niedrig' : 'Hoch'}
                                    </span>
                                </td>
                            </tr>
                            <tr class="${phDeviationClass}">
                                <td><strong>pH</strong></td>
                                <td>${targets.ph.min} - ${targets.ph.max}</td>
                                <td>${data.input_ph}</td>
                                <td>
                                    <span class="badge bg-${phDeviationClass === 'optimal' ? 'success' : phDeviationClass === 'low' ? 'warning' : 'danger'}">
                                        ${phDeviationClass === 'optimal' ? 'Optimal' : phDeviationClass === 'low' ? 'Niedrig' : 'Hoch'}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Alerts und Warnungen rendern
    renderAlertsAndWarnings(analysis) {
        const alerts = Array.isArray(analysis.alerts) ? analysis.alerts : [];
        const warnings = Array.isArray(analysis.warnings) ? analysis.warnings : [];
        
        if (alerts.length === 0 && warnings.length === 0) {
            return `
                <div class="nutrient-section-title nutrient-alerts">
                    <i class="fa-solid fa-check-circle me-2"></i>Status
                </div>
                <div class="nutrient-section-content nutrient-alerts-content">
                    <div class="alert alert-success">
                        <i class="fa fa-check-circle me-2"></i>
                        <strong>Alle Werte im optimalen Bereich!</strong>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="nutrient-section-title nutrient-alerts">
                <i class="fa-solid fa-exclamation-triangle me-2"></i>Alerts & Warnungen
            </div>
            <div class="nutrient-section-content nutrient-alerts-content">
                ${alerts.length > 0 ? `
                    <div class="nutrient-alerts-section">
                        <h6 class="text-danger"><i class="fa fa-exclamation-circle me-2"></i>Kritische Probleme</h6>
                        <ul class="nutrient-alerts-list">
                            ${alerts.map(alert => `<li class="text-danger">${alert}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                ${warnings.length > 0 ? `
                    <div class="nutrient-warnings-section">
                        <h6 class="text-warning"><i class="fa fa-exclamation-triangle me-2"></i>Warnungen</h6>
                        <ul class="nutrient-warnings-list">
                            ${warnings.map(warning => `<li class="text-warning">${warning}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Empfehlungen rendern
    renderRecommendations(analysis) {
        const recommendations = Array.isArray(analysis.recommendations) ? analysis.recommendations : [];
        
        if (recommendations.length === 0) {
            return `
                <div class="nutrient-section-title nutrient-recommendations">
                    <i class="fa-solid fa-lightbulb me-2"></i>Empfehlungen
                </div>
                <div class="nutrient-section-content nutrient-recommendations-content">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle me-2"></i>
                        <strong>Keine Korrekturmaßnahmen erforderlich.</strong>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="nutrient-section-title nutrient-recommendations">
                <i class="fa-solid fa-lightbulb me-2"></i>Korrekturmaßnahmen
            </div>
            <div class="nutrient-section-content nutrient-recommendations-content">
                <div class="nutrient-recommendations-list">
                    ${recommendations.map((rec, index) => `
                        <div class="nutrient-recommendation-item">
                            <div class="nutrient-recommendation-number">${index + 1}</div>
                            <div class="nutrient-recommendation-text">${rec}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Guidelines-Modal erstellen
    createGuidelinesModal() {
        // Prüfen ob Modal bereits existiert
        if (document.getElementById('nutrient-guidelines-modal')) {
            return;
        }

        const guidelines = NutrientWidget.nutrientGuidelines;
        const modalHtml = `
            <div class="modal fade" id="nutrient-guidelines-modal" tabindex="-1" aria-labelledby="nutrient-guidelines-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="nutrient-guidelines-modal-label">
                                <i class="fa-solid fa-flask me-2"></i>Nährstoff-Richtlinien & Korrekturstrategien
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="nutrient-guidelines-content">
                                <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>Korrekturstrategien</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-danger">EC zu hoch</h6>
                                        <ul class="nutrient-correction-list">
                                            ${(guidelines && guidelines.correctionStrategies && Array.isArray(guidelines.correctionStrategies.highEC) ? guidelines.correctionStrategies.highEC.map(s => `<li>${s}</li>`).join('') : '')}
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-warning">EC zu niedrig</h6>
                                        <ul class="nutrient-correction-list">
                                            ${(guidelines && guidelines.correctionStrategies && Array.isArray(guidelines.correctionStrategies.lowEC) ? guidelines.correctionStrategies.lowEC.map(s => `<li>${s}</li>`).join('') : '')}
                                        </ul>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-danger">pH zu hoch</h6>
                                        <ul class="nutrient-correction-list">
                                            ${(guidelines && guidelines.correctionStrategies && Array.isArray(guidelines.correctionStrategies.phTooHigh) ? guidelines.correctionStrategies.phTooHigh.map(s => `<li>${s}</li>`).join('') : '')}
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-warning">pH zu niedrig</h6>
                                        <ul class="nutrient-correction-list">
                                            ${(guidelines && guidelines.correctionStrategies && Array.isArray(guidelines.correctionStrategies.phTooLow) ? guidelines.correctionStrategies.phTooLow.map(s => `<li>${s}</li>`).join('') : '')}
                                        </ul>
                                    </div>
                                </div>
                                <hr class="my-4">
                                <div class="nutrient-meta">
                                    <h6><i class="fa-solid fa-link me-2"></i>Quellen</h6>
                                    <ul class="nutrient-meta-quellen-list">
                                        ${(guidelines && guidelines.meta && Array.isArray(guidelines.meta.quellen) ? guidelines.meta.quellen.map(q => `<li>${q}</li>`).join('') : '')}
                                    </ul>
                                    <p class="text-muted small mt-2">
                                        <i class="fa-solid fa-calendar me-1"></i>Stand: ${(guidelines && guidelines.meta ? guidelines.meta.erstelltAm : '')} | 
                                        <i class="fa-solid fa-user me-1"></i>Erstellt von: ${(guidelines && guidelines.meta ? guidelines.meta.erstelltVon : '')}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Historie-Modal erstellen
    createHistoryModal() {
        // Prüfen ob Modal bereits existiert
        if (document.getElementById('nutrient-history-modal')) {
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="nutrient-history-modal" tabindex="-1" aria-labelledby="nutrient-history-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="nutrient-history-modal-label">
                                <i class="fa-solid fa-chart-line me-2"></i>Nährstoff-Historie
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="nutrient-history-content">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="nutrient-chart-container">
                                            <canvas id="nutrient-chart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="nutrient-history-stats">
                                            <h6><i class="fa-solid fa-chart-bar me-2"></i>Statistiken</h6>
                                            <div id="nutrient-stats-content">
                                                <div class="text-center">
                                                    <i class="fa fa-spinner fa-spin"></i> Lade Statistiken...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6><i class="fa-solid fa-table me-2"></i>Detaillierte Historie</h6>
                                        <div id="nutrient-history-table">
                                            <div class="text-center">
                                                <i class="fa fa-spinner fa-spin"></i> Lade Historie...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                            <button type="button" class="btn btn-primary" id="export-nutrient-history">
                                <i class="fas fa-download"></i> Export CSV
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Event-Listener für Modal-Öffnung
        document.getElementById('nutrient-history-modal').addEventListener('shown.bs.modal', () => {
            this.loadNutrientHistory();
        });
    }

    // Nährstoff-Historie laden
    async loadNutrientHistory() {
        try {
            // Plant ID holen
            let plantId = null;
            const plantIdElement = document.querySelector('[data-plant-id]');
            if (plantIdElement) {
                plantId = plantIdElement.getAttribute('data-plant-id');
            }

            if (!plantId) {
                document.getElementById('nutrient-history-table').innerHTML = 
                    '<div class="alert alert-warning">Keine Pflanzen-ID verfügbar</div>';
                return;
            }

            // Historie von API laden
            const response = await fetch(`/api/nutrients/history/${plantId}?limit=50`);
            if (!response.ok) {
                throw new Error('Historie konnte nicht geladen werden');
            }

            const data = await response.json();
            
            // Historie-Tabelle rendern
            this.renderHistoryTable(data.history);
            
            // Statistiken rendern
            this.renderHistoryStats(data.history);
            
            // Chart rendern (falls Chart.js verfügbar)
            if (typeof Chart !== 'undefined') {
                this.renderHistoryChart(data.history);
            }
            
        } catch (err) {
            document.getElementById('nutrient-history-table').innerHTML = 
                `<div class="alert alert-danger">Fehler beim Laden der Historie: ${err.message}</div>`;
        }
    }

    // Historie-Tabelle rendern
    renderHistoryTable(history) {
        if (!Array.isArray(history) || history.length === 0) {
            document.getElementById('nutrient-history-table').innerHTML = 
                '<div class="alert alert-info">Keine Historie-Daten verfügbar</div>';
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Datum</th>
                            <th>Phase</th>
                            <th>EC Input</th>
                            <th>EC Drain</th>
                            <th>pH Input</th>
                            <th>pH Drain</th>
                            <th>Substrat</th>
                            <th>Notizen</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${history.map(entry => `
                            <tr>
                                <td>${new Date(entry.entry_date).toLocaleDateString('de-DE')}</td>
                                <td>${this.getFriendlyPhaseName(entry.phase)}</td>
                                <td>${entry.ec_input || '-'} mS/cm</td>
                                <td>${entry.ec_drain || '-'} mS/cm</td>
                                <td>${entry.ph_input || '-'}</td>
                                <td>${entry.ph_drain || '-'}</td>
                                <td>${entry.substrate || '-'}</td>
                                <td>${entry.notes || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        document.getElementById('nutrient-history-table').innerHTML = tableHtml;
    }

    // Historie-Statistiken rendern
    renderHistoryStats(history) {
        if (!Array.isArray(history) || history.length === 0) {
            document.getElementById('nutrient-stats-content').innerHTML = 
                '<div class="alert alert-info">Keine Daten für Statistiken</div>';
            return;
        }

        // Statistiken berechnen
        const ecValues = history.map(h => h.ec_input).filter(v => v !== null);
        const phValues = history.map(h => h.ph_input).filter(v => v !== null);
        
        const ecStats = this.calculateStats(ecValues);
        const phStats = this.calculateStats(phValues);

        const statsHtml = `
            <div class="nutrient-stats-grid">
                <div class="nutrient-stat-item">
                    <div class="nutrient-stat-label">EC Durchschnitt</div>
                    <div class="nutrient-stat-value">${ecStats.average.toFixed(2)} mS/cm</div>
                </div>
                <div class="nutrient-stat-item">
                    <div class="nutrient-stat-label">EC Min/Max</div>
                    <div class="nutrient-stat-value">${ecStats.min.toFixed(2)} / ${ecStats.max.toFixed(2)}</div>
                </div>
                <div class="nutrient-stat-item">
                    <div class="nutrient-stat-label">pH Durchschnitt</div>
                    <div class="nutrient-stat-value">${phStats.average.toFixed(2)}</div>
                </div>
                <div class="nutrient-stat-item">
                    <div class="nutrient-stat-label">pH Min/Max</div>
                    <div class="nutrient-stat-value">${phStats.min.toFixed(2)} / ${phStats.max.toFixed(2)}</div>
                </div>
                <div class="nutrient-stat-item">
                    <div class="nutrient-stat-label">Messungen</div>
                    <div class="nutrient-stat-value">${history.length}</div>
                </div>
                <div class="nutrient-stat-item">
                    <div class="nutrient-stat-label">Zeitraum</div>
                    <div class="nutrient-stat-value">${this.calculateTimeRange(history)}</div>
                </div>
            </div>
        `;

        document.getElementById('nutrient-stats-content').innerHTML = statsHtml;
    }

    // Statistiken berechnen
    calculateStats(values) {
        if (values.length === 0) {
            return { average: 0, min: 0, max: 0 };
        }
        
        const sum = values.reduce((a, b) => a + b, 0);
        const average = sum / values.length;
        const min = Math.min(...values);
        const max = Math.max(...values);
        
        return { average, min, max };
    }

    // Zeitraum berechnen
    calculateTimeRange(history) {
        if (history.length < 2) return '1 Messung';
        
        const firstDate = new Date(history[history.length - 1].entry_date);
        const lastDate = new Date(history[0].entry_date);
        const diffDays = Math.ceil((lastDate - firstDate) / (1000 * 60 * 60 * 24));
        
        return `${diffDays} Tage`;
    }

    // Historie-Chart rendern (falls Chart.js verfügbar)
    renderHistoryChart(history) {
        const canvas = document.getElementById('nutrient-chart');
        if (!canvas) return;

        // Prüfen ob history ein Array ist
        if (!Array.isArray(history) || history.length === 0) {
            console.warn('NutrientWidget: Keine Historie-Daten für Chart verfügbar');
            return;
        }

        // Bestehenden Chart zerstören
        if (this.nutrientChart) {
            this.nutrientChart.destroy();
        }

        // Daten für Chart vorbereiten
        const labels = history.map(entry => new Date(entry.entry_date).toLocaleDateString('de-DE')).reverse();
        const ecData = history.map(entry => entry.ec_input).reverse();
        const phData = history.map(entry => entry.ph_input).reverse();

        // Chart erstellen
        this.nutrientChart = new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'EC (mS/cm)',
                        data: ecData,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        yAxisID: 'y'
                    },
                    {
                        label: 'pH',
                        data: phData,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Datum'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'EC (mS/cm)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'pH'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    // Phase aktualisieren (für externe Aufrufe)
    updatePhase(newPhase) {
        this.currentPhase = newPhase;
        this.loadNutrientDiagnosis();
    }

    // Widget zerstören (Cleanup)
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// Guidelines laden und cachen
NutrientWidget.nutrientGuidelines = null;
NutrientWidget.nutrientGuidelinesLoaded = false;
NutrientWidget.loadNutrientGuidelines = async function() {
    if (NutrientWidget.nutrientGuidelinesLoaded) return NutrientWidget.nutrientGuidelines;
    try {
        const response = await fetch('/static/data/nutrient-guidelines.json');
        if (response.ok) {
            const json = await response.json();
            NutrientWidget.nutrientGuidelines = json.nutrientGuidelines;
            NutrientWidget.nutrientGuidelinesLoaded = true;
            return NutrientWidget.nutrientGuidelines;
        }
    } catch (e) {}
    return null;
};

// Global verfügbar machen für Widget Manager
window.NutrientWidget = NutrientWidget;

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NutrientWidget;
} 