/**
 * Flowering UI Renderer - Verwaltet alle UI-Rendering-Funktionen
 */

class FloweringUIRenderer {
    constructor(widget) {
        this.widget = widget;
    }

    /**
     * Aktualisiert die Übersicht mit Flowering-Daten
     */
    updateOverview(data) {
        if (!data || !data.flowering_status) return;

        const status = data.flowering_status;
        const strain = data.strain_profile || {};
        
        // Grunddaten
        const strainNameEl = document.getElementById('strainName');
        if (strainNameEl) strainNameEl.textContent = strain.strain || strain.name || 'Sorte nicht angegeben';
        
        // Aktueller Tag
        const currentDayEl = document.getElementById('currentDay');
        if (currentDayEl) currentDayEl.textContent = status.current_day || 0;
        
        // Phasen-Beschreibung aktualisieren
        const phaseDescriptionEl = document.getElementById('phaseDescription');
        if (phaseDescriptionEl) {
            const phaseName = this.getPhaseName(status.phase);
            phaseDescriptionEl.textContent = phaseName;
        }
        
        // Fortschrittswerte
        const growProgress = status.grow_progress_percentage || 0;
        const bloomProgress = status.bloom_progress_percentage || status.progress_percentage || 0;
        
        // Header mit beiden Werten aktualisieren
        const phasePercentageEl = document.getElementById('phasePercentage');
        if (phasePercentageEl) {
            phasePercentageEl.textContent = `(Grow: ${growProgress.toFixed(1)}% | Blüte: ${bloomProgress.toFixed(1)}%)`;
            phasePercentageEl.removeAttribute('title');
        }
        
        // Fortschrittsbalken und Label mit Blüte-Fortschritt
        const phaseProgressTextEl = document.getElementById('phaseProgressText');
        if (phaseProgressTextEl) {
            phaseProgressTextEl.textContent = `${bloomProgress.toFixed(1)}%`;
            phaseProgressTextEl.removeAttribute('title');
        }
        
        const phaseProgressEl = document.getElementById('phaseProgress');
        if (phaseProgressEl) phaseProgressEl.style.width = `${bloomProgress}%`;
        
        // Progress Circle im Header aktualisieren
        const progressTextEl = document.getElementById('progressText');
        if (progressTextEl) {
            progressTextEl.textContent = `${bloomProgress.toFixed(1)}%`;
            progressTextEl.removeAttribute('title');
        }
        
        // Flush-Status
        this.updateFlushStatus(data);
        
        // Ernte-Prognose
        this.updateHarvestPrediction(data);
        
        // Meilensteine
        this.updateMilestones(data);
    }

    /**
     * Aktualisiert den Flush-Status
     */
    updateFlushStatus(data) {
        const flushStatus = data.flush_status;
        const flushCard = document.getElementById('flushCard');
        const flushStatusEl = document.getElementById('flushStatus');
        
        if (flushCard && flushStatus) {
            if (flushStatus.triggered) {
                flushCard.classList.add('triggered');
                if (flushStatusEl) flushStatusEl.textContent = 'Aktiv';
            } else {
                flushCard.classList.remove('triggered');
                if (flushStatusEl) flushStatusEl.textContent = 'Inaktiv';
            }
        }
    }

    /**
     * Aktualisiert die Ernte-Prognose
     */
    updateHarvestPrediction(data) {
        const recommendation = data.recommendation;
        if (!recommendation) return;
        
        const harvestEarlyEl = document.getElementById('harvestEarly');
        if (harvestEarlyEl) harvestEarlyEl.textContent = `Tag ${recommendation.harvest_early[0]}-${recommendation.harvest_early[1]}`;
        
        const harvestOptimalEl = document.getElementById('harvestOptimal');
        if (harvestOptimalEl) harvestOptimalEl.textContent = `Tag ${recommendation.harvest_optimal[0]}-${recommendation.harvest_optimal[1]}`;
        
        const harvestLateEl = document.getElementById('harvestLate');
        if (harvestLateEl) harvestLateEl.textContent = `Tag ${recommendation.harvest_late[0]}-${recommendation.harvest_late[1]}`;
    }

    /**
     * Aktualisiert die Meilensteine
     */
    updateMilestones(data) {
        const milestonesList = document.getElementById('milestonesList');
        if (!milestonesList) return;

        const currentDay = data.flowering_status.current_day;
        const milestones = [];

        // Flush-Meilenstein
        if (data.flush_status.start_recommended > currentDay) {
            const daysUntilFlush = data.flush_status.start_recommended - currentDay;
            milestones.push({
                icon: '🚿',
                title: 'Flush empfohlen',
                description: `in ${daysUntilFlush} Tagen`,
                type: 'flush'
            });
        }

        // Ernte-Meilenstein
        const optimalHarvest = data.recommendation.harvest_optimal[0];
        if (optimalHarvest > currentDay) {
            const daysUntilHarvest = optimalHarvest - currentDay;
            milestones.push({
                icon: '✂️',
                title: 'Optimale Ernte',
                description: `in ${daysUntilHarvest} Tagen`,
                type: 'harvest'
            });
        }

        // Marker-basierte Meilensteine
        const recentMarkers = this.widget.markers
            .filter(m => m.bloom_day > currentDay - 7 && m.bloom_day <= currentDay)
            .slice(0, 2);

        recentMarkers.forEach(marker => {
            milestones.push({
                icon: '📌',
                title: marker.title,
                description: `Tag ${marker.bloom_day}`,
                type: 'marker'
            });
        });

        // Meilensteine rendern
        milestonesList.innerHTML = milestones.map(milestone => `
            <div class="milestone-item">
                <div class="milestone-icon">${milestone.icon}</div>
                <div class="milestone-content">
                    <div class="milestone-title">${milestone.title}</div>
                    <div class="milestone-description">${milestone.description}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Aktualisiert die Timeline
     */
    updateTimeline() {
        const timelineContent = document.getElementById('timelineContent');
        if (!timelineContent) return;
        
        // Timeline-Events aus den Markern erstellen
        const events = this.widget.markers.map(marker => ({
            day: marker.bloom_day,
            date: marker.date,
            title: marker.title,
            category: marker.category,
            importance: marker.importance
        }));

        // Timeline rendern
        timelineContent.innerHTML = events.map(event => `
            <div class="timeline-event ${event.category}">
                <div class="event-marker ${this.getEventMarkerClass(event)}"></div>
                <div class="event-content">
                    <div class="event-title">${event.title}</div>
                    <div class="event-meta">Tag ${event.day} • ${event.date}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Aktualisiert den Progress Circle
     */
    updateProgressCircle(percentage) {
        const circle = document.getElementById('progressCircle');
        if (!circle) return;
        
        const circumference = 2 * Math.PI * 25; // r=25
        const offset = circumference - (percentage / 100) * circumference;
        
        circle.style.strokeDasharray = `${circumference} ${circumference}`;
        circle.style.strokeDashoffset = offset;
        
        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = `${percentage.toFixed(1)}%`;
            progressText.removeAttribute('title');
        }
    }

    /**
     * Zeigt Erfolgsmeldung an
     */
    showSuccess(message) {
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    /**
     * Zeigt Fehlermeldung an
     */
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    /**
     * Hilfsfunktionen
     */
    getPhaseName(phaseKey) {
        const phaseNames = {
            'preflower': 'Vorblüte',
            'stretch': 'Stretchphase',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush-Phase'
        };
        return phaseNames[phaseKey] || 'Unbekannte Phase';
    }

    getEventMarkerClass(event) {
        const importanceClasses = {
            'high': 'high-importance',
            'medium': 'medium-importance',
            'low': 'low-importance'
        };
        return importanceClasses[event.importance] || 'medium-importance';
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('de-DE');
    }
}
