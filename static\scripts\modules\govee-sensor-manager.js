/**
 * Global Govee Sensor Manager
 * Zentrale Verwaltung der Govee Sensor Integration für alle Widgets
 */

class GoveeSensorManager {
    constructor() {
        this.isConnected = false;
        this.currentDevice = null;
        this.autoUpdate = false;
        this.updateInterval = null;
        this.subscribers = new Set(); // Widgets die Updates erhalten wollen
        this.lastReading = null;
        
        // Singleton Pattern
        if (GoveeSensorManager.instance) {
            return GoveeSensorManager.instance;
        }
        GoveeSensorManager.instance = this;
    }

    // Widget als Subscriber registrieren
    subscribe(widgetId, callback) {
        this.subscribers.add({
            id: widgetId,
            callback: callback
        });

    }

    // Widget als Subscriber entfernen
    unsubscribe(widgetId) {
        for (let subscriber of this.subscribers) {
            if (subscriber.id === widgetId) {
                this.subscribers.delete(subscriber);
                break;
            }
        }

    }

    // Alle Subscriber über neue Daten informieren
    notifySubscribers(reading) {
        this.lastReading = reading;
        this.subscribers.forEach(subscriber => {
            try {
                subscriber.callback(reading);
            } catch (error) {
                console.error(`<PERSON><PERSON> beim <PERSON>tigen von Widget ${subscriber.id}:`, error);
            }
        });
    }

    // Verbindungsstatus prüfen
    async checkConnectionStatus() {
        try {
            const response = await fetch('/api/sensor/govee/status');
            const data = await response.json();
            
            if (data.success) {
                this.isConnected = data.status.connected;
                return data.status;
            }
            return null;
        } catch (error) {
            console.error('Fehler beim Prüfen des Govee Status:', error);
            return null;
        }
    }

    // Geräte auflisten
    async getDevices() {
        try {
            const response = await fetch('/api/sensor/govee/devices');
            const data = await response.json();
            
            if (data.success) {
                return data.hygrometers;
            }
            return [];
        } catch (error) {
            console.error('Fehler beim Abrufen der Govee-Geräte:', error);
            return [];
        }
    }

    // Aktuelle Messwerte abrufen
    async getCurrentReading(deviceId, model) {
        try {
            const response = await fetch(`/api/sensor/govee/reading/${deviceId}?model=${model}`);
            const data = await response.json();
            
            if (data.success) {
                const reading = data.reading;
                this.notifySubscribers(reading);
                return reading;
            }
            return null;
        } catch (error) {
            console.error('Fehler beim Abrufen der Govee-Daten:', error);
            return null;
        }
    }

    // Automatische Updates starten
    startAutoUpdate(deviceId, model, intervalMs = 30000) {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.autoUpdate = true;
        this.currentDevice = { id: deviceId, model: model };
        
        // Sofort erste Messung
        this.getCurrentReading(deviceId, model);
        
        // Regelmäßige Updates
        this.updateInterval = setInterval(() => {
            this.getCurrentReading(deviceId, model);
        }, intervalMs);
        

    }

    // Automatische Updates stoppen
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.autoUpdate = false;
        this.currentDevice = null;

    }

    // Setup-Modal anzeigen
    showSetupModal() {
        // Prüfen ob Modal bereits existiert
        let modal = document.getElementById('govee-global-setup-modal');
        if (modal) {
            modal.remove();
        }

        const modalHtml = `
            <div class="modal fade" id="govee-global-setup-modal" tabindex="-1" aria-labelledby="govee-global-setup-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="govee-global-setup-modal-label">
                                <i class="fas fa-thermometer-half me-2"></i>Govee Sensor Integration (Global)
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="govee-global-setup-content">
                                <div class="text-center">
                                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                                    <p class="mt-2">Lade Govee Setup...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Modal anzeigen
        const modalElement = document.getElementById('govee-global-setup-modal');
        const bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.show();

        // Setup-Content laden
        this.loadGlobalSetupContent();

        // Modal nach dem Schließen entfernen
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });
    }

    // Global Setup Content laden
    async loadGlobalSetupContent() {
        const content = document.getElementById('govee-global-setup-content');
        if (!content) return;

        try {
            // Status prüfen
            const status = await this.checkConnectionStatus();
            
            if (status && status.connected) {
                // Bereits verbunden - Geräte anzeigen
                await this.showGlobalDevices();
            } else {
                // Setup erforderlich
                this.showGlobalApiKeyForm();
            }
        } catch (error) {
            console.error('Fehler beim Laden des Govee Status:', error);
            this.showGlobalApiKeyForm();
        }
    }

    // Global API Key Form anzeigen
    showGlobalApiKeyForm() {
        const content = document.getElementById('govee-global-setup-content');
        if (!content) return;

        content.innerHTML = `
            <div class="govee-setup-step">
                <h6><i class="fas fa-key me-2"></i>Govee API Key einrichten (Global)</h6>
                <p class="text-muted">
                    Diese Einstellung wird für alle Widgets verwendet, die Govee-Sensoren nutzen.
                </p>
                
                <div class="alert alert-info">
                    <strong>So bekommst Du Deinen API Key:</strong>
                    <ol class="mb-0 mt-2">
                        <li>Gehe zu <a href="https://developer.govee.com" target="_blank">developer.govee.com</a></li>
                        <li>Erstelle ein kostenloses Konto</li>
                        <li>Erstelle eine neue App</li>
                        <li>Kopiere den API Key</li>
                    </ol>
                </div>
                
                <div class="mb-3">
                    <label for="govee-global-api-key" class="form-label">Govee API Key</label>
                    <input type="password" class="form-control" id="govee-global-api-key" 
                           placeholder="Dein Govee API Key">
                    <div class="form-text">Der API Key wird sicher gespeichert und für alle Widgets verwendet.</div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" id="govee-global-test-connection">
                        <i class="fas fa-plug me-2"></i>Verbindung testen
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Abbrechen
                    </button>
                </div>
            </div>
        `;

        // Event-Listener für Test-Button
        const testBtn = document.getElementById('govee-global-test-connection');
        if (testBtn) {
            testBtn.addEventListener('click', () => this.testGlobalConnection());
        }
    }

    // Global Verbindung testen
    async testGlobalConnection() {
        const apiKeyInput = document.getElementById('govee-global-api-key');
        const testBtn = document.getElementById('govee-global-test-connection');
        
        if (!apiKeyInput || !testBtn) return;
        
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            this.showNotification('Bitte gib einen API Key ein', 'error');
            return;
        }
        
        // Button deaktivieren
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Teste Verbindung...';
        
        try {
            const response = await fetch('/api/sensor/govee/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ api_key: apiKey })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(`Verbindung erfolgreich! ${data.hygrometers_found} Hygrometer gefunden.`, 'success');
                // API Key speichern und Geräte anzeigen
                await this.saveGlobalApiKey(apiKey);
                await this.showGlobalDevices();
            } else {
                this.showNotification(`Verbindung fehlgeschlagen: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Fehler beim Testen der Govee Verbindung:', error);
            this.showNotification('Fehler beim Testen der Verbindung', 'error');
        } finally {
            // Button wieder aktivieren
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-plug me-2"></i>Verbindung testen';
        }
    }

    // Global API Key speichern
    async saveGlobalApiKey(apiKey) {
        try {
            const response = await fetch('/api/sensor/govee/setup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ api_key: apiKey })
            });
            
            const data = await response.json();
            if (data.success) {
                this.isConnected = true;
                return true;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Fehler beim Speichern des API Keys:', error);
            this.showNotification('Fehler beim Speichern des API Keys', 'error');
            return false;
        }
    }

    // Global Geräte anzeigen
    async showGlobalDevices() {
        const content = document.getElementById('govee-global-setup-content');
        if (!content) return;

        try {
            const devices = await this.getDevices();
            
            if (devices.length > 0) {
                const devicesHtml = devices.map(device => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="govee-global-device" 
                                       id="global-device-${device.device_id}" value="${device.device_id}" 
                                       data-model="${device.model}">
                                <label class="form-check-label" for="global-device-${device.device_id}">
                                    <strong>${device.name}</strong><br>
                                    <small class="text-muted">
                                        ID: ${device.device_id} | Model: ${device.model}
                                    </small>
                                </label>
                            </div>
                        </div>
                    </div>
                `).join('');

                content.innerHTML = `
                    <div class="govee-setup-step">
                        <h6><i class="fas fa-thermometer-half me-2"></i>Hygrometer auswählen (Global)</h6>
                        <p class="text-muted">
                            Wähle das Hygrometer aus, das für alle Widgets verwendet werden soll.
                        </p>
                        
                        <div class="mb-3">
                            ${devicesHtml}
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="govee-global-auto-update">
                            <label class="form-check-label" for="govee-global-auto-update">
                                <strong>Automatische Updates aktivieren</strong><br>
                                <small class="text-muted">Daten alle 30 Sekunden automatisch für alle Widgets aktualisieren</small>
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="govee-global-save-device">
                                <i class="fas fa-save me-2"></i>Gerät speichern
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                Abbrechen
                            </button>
                        </div>
                    </div>
                `;

                // Event-Listener für Speichern-Button
                const saveBtn = document.getElementById('govee-global-save-device');
                if (saveBtn) {
                    saveBtn.addEventListener('click', () => this.saveGlobalDevice());
                }
            } else {
                content.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Keine Hygrometer gefunden</strong><br>
                        Stelle sicher, dass Dein Govee Hygrometer mit der Govee App verbunden ist.
                    </div>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Schließen
                    </button>
                `;
            }
        } catch (error) {
            console.error('Fehler beim Laden der Govee-Geräte:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Fehler beim Laden der Geräte</strong><br>
                    ${error.message}
                </div>
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    Schließen
                </button>
            `;
        }
    }

    // Global Gerät speichern
    async saveGlobalDevice() {
        const selectedDevice = document.querySelector('input[name="govee-global-device"]:checked');
        const autoUpdate = document.getElementById('govee-global-auto-update').checked;
        
        if (!selectedDevice) {
            this.showNotification('Bitte wähle ein Hygrometer aus', 'error');
            return;
        }
        
        const deviceId = selectedDevice.value;
        const model = selectedDevice.getAttribute('data-model');
        
        // Gerät speichern
        this.currentDevice = {
            id: deviceId,
            model: model
        };
        
        // Automatische Updates starten falls aktiviert
        if (autoUpdate) {
            this.startAutoUpdate(deviceId, model);
        }
        
        // Modal schließen
        const modal = bootstrap.Modal.getInstance(document.getElementById('govee-global-setup-modal'));
        if (modal) {
            modal.hide();
        }
        
        this.showNotification('Govee Hygrometer global eingerichtet!', 'success');
    }

    // Benachrichtigung anzeigen
    showNotification(message, type = 'info') {
        // Einfache Benachrichtigung erstellen
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Automatisch nach 3 Sekunden entfernen
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Status-Informationen abrufen
    getStatus() {
        return {
            isConnected: this.isConnected,
            currentDevice: this.currentDevice,
            autoUpdate: this.autoUpdate,
            subscriberCount: this.subscribers.size,
            lastReading: this.lastReading
        };
    }
}

// Globale Instanz erstellen
window.goveeSensorManager = new GoveeSensorManager();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GoveeSensorManager;
} 