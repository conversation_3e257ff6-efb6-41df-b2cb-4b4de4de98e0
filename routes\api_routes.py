#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API Routes für das Grow-Diary-Basic
Zentrale API-Endpunkte für alle Widgets und Funktionen
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import json
import uuid

api_bp = Blueprint('api', __name__, url_prefix='/api')

# ===== FLUSH API ENDPOINTS =====

@api_bp.route('/flush/status/<plant_id>')
def get_flush_status(plant_id):
    """Flush-Status abrufen"""
    try:
        # Fallback-Daten für fehlende Implementierung
        flush_status = {
            "plant_id": plant_id,
            "flush_active": False,
            "flush_start_day": 52,
            "current_bloom_day": 41,
            "days_until_flush": 11,
            "status": "planning",
            "message": "Flush geplant in 11 Tagen",
            "recommendations": {
                "immediate_actions": [],
                "preparation_actions": [
                    "Flush-Medium vorbereiten",
                    "Bewässerungsplan anpassen",
                    "EC/pH-Sensoren kalibrieren"
                ],
                "monitoring": [
                    "Trichome-Status überwachen",
                    "Drain-EC und pH messen",
                    "Pistillen-Entwicklung beobachten"
                ]
            }
        }
        
        return jsonify(flush_status)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden des Flush-Status: {str(e)}"}), 500

@api_bp.route('/flush/history/<plant_id>')
def get_flush_history(plant_id):
    """Flush-Historie abrufen"""
    try:
        # Fallback-Daten
        flush_history = {
            "plant_id": plant_id,
            "history": [
                {
                    "date": "2025-07-10",
                    "bloom_day": 38,
                    "action": "flush_planning",
                    "notes": "Flush für Tag 52 geplant"
                }
            ]
        }
        
        return jsonify(flush_history)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Flush-Historie: {str(e)}"}), 500

@api_bp.route('/flush/harvest-checklist/<plant_id>')
def get_harvest_checklist(plant_id):
    """Ernte-Checkliste abrufen"""
    try:
        # Fallback-Daten
        harvest_checklist = {
            "plant_id": plant_id,
            "checklist": [
                {
                    "category": "Trichome",
                    "items": [
                        {"task": "Milchige Trichome prüfen", "completed": False},
                        {"task": "Bernstein-Anteil messen", "completed": False},
                        {"task": "Mikroskop-Kalibrierung", "completed": False}
                    ]
                },
                {
                    "category": "Umgebung",
                    "items": [
                        {"task": "Temperatur senken", "completed": False},
                        {"task": "Luftfeuchte reduzieren", "completed": False},
                        {"task": "Lüftung erhöhen", "completed": False}
                    ]
                }
            ]
        }
        
        return jsonify(harvest_checklist)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Ernte-Checkliste: {str(e)}"}), 500

@api_bp.route('/flush/guidelines')
def get_flush_guidelines():
    """Flush-Guidelines abrufen"""
    try:
        # Fallback-Daten
        flush_guidelines = {
            "success": True,
            "guidelines": {
                "startbedingungen": {
                    "autoflower": {
                        "indikatoren": [
                            "Trichome: ≥60% milchig",
                            "Pistillen: ≥70% bräunlich zurückgezogen",
                            "Drain-EC: sinkend (< 2.0 mS/cm)",
                            "pH-Stabilisierung im Drain (6.0–6.4)"
                        ]
                    },
                    "photoperiod": {
                        "indikatoren": [
                            "Trichome: ≥70% milchig",
                            "Pistillen: ≥80% bräunlich zurückgezogen",
                            "Drain-EC: sinkend (< 1.8 mS/cm)",
                            "pH-Stabilisierung im Drain (6.0–6.4)"
                        ]
                    }
                },
                "methoden": [
                    "Wasser-Flush: 14 Tage reines Wasser",
                    "CalMag-Flush: Wasser + minimal CalMag (0.2 mS)",
                    "Enzym-Flush: Wasser + Enzyme für Wurzelreinigung"
                ],
                "fehlerquellen": [
                    "Zu früher Flush-Start",
                    "Unzureichende Flush-Dauer",
                    "Falsche pH-Werte im Drain"
                ]
            }
        }
        
        return jsonify(flush_guidelines)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Flush-Guidelines: {str(e)}"}), 500

# ===== STRESS API ENDPOINTS =====

@api_bp.route('/stress/guidelines/<phase>')
def get_stress_guidelines(phase):
    """Stress-Guidelines für eine Phase abrufen"""
    try:
        strain_type = request.args.get('strain_type', 'photoperiodic')
        
        # Fallback-Daten
        stress_guidelines = {
            "phase": phase,
            "strain_type": strain_type,
            "stress_indicators": [
                "Blattvergilbung",
                "Wachstumsstopp",
                "Verwelkung",
                "Verfärbungen"
            ],
            "stress_management": [
                "Temperatur reduzieren",
                "Luftfeuchte anpassen",
                "Bewässerung optimieren",
                "Nährstoffe reduzieren"
            ],
            "prevention_tips": [
                "Regelmäßige Überwachung",
                "Langsame Anpassungen",
                "Strain-spezifische Pflege"
            ]
        }
        
        return jsonify(stress_guidelines)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Stress-Guidelines: {str(e)}"}), 500

# ===== TRICHOME API ENDPOINTS =====

@api_bp.route('/trichome/status/<plant_id>')
def get_trichome_status(plant_id):
    """Trichome-Status abrufen"""
    try:
        # Fallback-Daten
        trichome_status = {
            "plant_id": plant_id,
            "has_data": True,
            "current_status": {
                "clear": 20,
                "milky": 65,
                "amber": 15,
                "total": 100
            },
            "maturity_level": "late_flowering",
            "harvest_recommendation": {
                "optimal_harvest": "2-4 days",
                "harvest_window": [2, 4],
                "urgency": "medium",
                "reason": "Körperbetonte Wirkung optimal"
            },
            "flush_status": {
                "flush_active": True,
                "flush_days_remaining": 3,
                "flush_recommendation": "Flush fortsetzen"
            }
        }
        
        return jsonify(trichome_status)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden des Trichome-Status: {str(e)}"}), 500

@api_bp.route('/trichome/history/<plant_id>')
def get_trichome_history(plant_id):
    """Trichome-Historie abrufen"""
    try:
        # Fallback-Daten
        trichome_history = {
            "plant_id": plant_id,
            "history": [
                {
                    "date": "2025-07-10",
                    "bloom_day": 38,
                    "clear": 40,
                    "milky": 55,
                    "amber": 5,
                    "location": "top_buds"
                },
                {
                    "date": "2025-07-13",
                    "bloom_day": 41,
                    "clear": 20,
                    "milky": 65,
                    "amber": 15,
                    "location": "top_buds"
                }
            ],
            "averages": {
                "clear": 30,
                "milky": 60,
                "amber": 10
            },
            "trend": {
                "direction": "maturing",
                "speed": "normal",
                "predicted_harvest": 70
            }
        }
        
        return jsonify(trichome_history)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Trichome-Historie: {str(e)}"}), 500

# ===== PHASE API ENDPOINTS =====

@api_bp.route('/phase-details/<plant_id>')
def get_phase_details(plant_id):
    """Phase-Details abrufen"""
    try:
        # Fallback-Daten für fehlende Implementierung
        phase_details = {
            "success": True,
            "details": {
                "phase_info": {
                    "name": "Mittlere Blüte",
                    "description": "Hauptblütephase mit intensiver Trichom-Entwicklung",
                    "duration": "21-35 Tage",
                    "current_day": 25,
                    "total_days": 35
                },
                "environmental_targets": {
                    "temperature": {"min": 22, "max": 26, "optimal": 24},
                    "humidity": {"min": 45, "max": 55, "optimal": 50},
                    "vpd": {"min": 0.8, "max": 1.2, "optimal": 1.0}
                },
                "lighting_targets": {
                    "ppfd": {"min": 600, "max": 1000, "optimal": 800},
                    "photoperiod": {"hours": 12},
                    "color_temp": {"min": 2700, "max": 3500, "optimal": 3000}
                },
                "nutrient_targets": {
                    "ec": {"min": 1.2, "max": 1.8, "optimal": 1.5},
                    "ph": {"min": 5.8, "max": 6.4, "optimal": 6.1}
                }
            }
        }
        
        return jsonify(phase_details)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Phase-Details: {str(e)}"}), 500

@api_bp.route('/phase-notes/<plant_id>', methods=['GET', 'POST'])
def manage_phase_notes(plant_id):
    """Phase-Notizen verwalten"""
    try:
        if request.method == 'GET':
            phase_name = request.args.get('phase_name')
            
            if phase_name and phase_name != 'null':
                # Einzelne Phase-Notiz abrufen
                note = {
                    "id": "note_1",
                    "phase_name": phase_name,
                    "note_text": "Beispiel-Notiz für diese Phase",
                    "created_at": "2025-07-12T10:00:00"
                }
                return jsonify({
                    "success": True,
                    "note": note
                })
            else:
                # Alle Phase-Notizen abrufen
                notes = [
                    {
                        "id": "note_1",
                        "phase_name": "vegetative",
                        "note_text": "Vegetationsphase läuft gut",
                        "created_at": "2025-07-10T10:00:00"
                    },
                    {
                        "id": "note_2", 
                        "phase_name": "flowering_middle",
                        "note_text": "Blütephase entwickelt sich optimal",
                        "created_at": "2025-07-12T10:00:00"
                    }
                ]
                return jsonify({
                    "success": True,
                    "notes": notes
                })
        
        elif request.method == 'POST':
            data = request.get_json()
            phase_name = data.get('phase_name')
            note_text = data.get('note_text', '')
            
            if not phase_name:
                return jsonify({"success": False, "message": "Phase-Name erforderlich"}), 400
            
            # Fallback-Response
            return jsonify({
                "success": True,
                "message": "Notiz erfolgreich gespeichert",
                "note_id": "note_new"
            })
            
    except Exception as e:
        return jsonify({"error": f"Fehler bei Phase-Notizen: {str(e)}"}), 500

@api_bp.route('/phase-notes/<plant_id>/<phase_name>', methods=['DELETE'])
def delete_phase_note(plant_id, phase_name):
    """Phase-Notiz löschen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        success = db.delete_phase_note(plant_id, phase_name)
        
        return jsonify({
            "success": success,
            "message": "Notiz erfolgreich gelöscht" if success else "Notiz nicht gefunden"
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Löschen der Notiz: {str(e)}"}), 500

@api_bp.route('/phase-checklist/<plant_id>', methods=['GET', 'POST'])
def manage_phase_checklist(plant_id):
    """Phase-Checklisten verwalten"""
    try:
        if request.method == 'GET':
            phase_name = request.args.get('phase_name')
            
            if not phase_name:
                return jsonify({"success": False, "message": "Phase-Name erforderlich"}), 400
            
            # Fallback-Daten für stabile Funktionalität
            checklist = [
                {
                    "id": "item_1",
                    "checklist_item": "EC-Werte messen",
                    "is_completed": False,
                    "created_at": "2025-07-12T10:00:00"
                },
                {
                    "id": "item_2",
                    "checklist_item": "pH-Werte anpassen",
                    "is_completed": True,
                    "completed_at": "2025-07-12T11:00:00",
                    "created_at": "2025-07-12T10:00:00"
                }
            ]
            
            return jsonify({
                "success": True,
                "checklist": checklist
            })
        
        elif request.method == 'POST':
            data = request.get_json()
            phase_name = data.get('phase_name')
            item_text = data.get('item_text')
            
            if not phase_name or not item_text:
                return jsonify({"success": False, "message": "Phase-Name und Item-Text erforderlich"}), 400
            
            # Fallback-Response
            return jsonify({
                "success": True,
                "message": "Checklist-Item hinzugefügt",
                "item_id": "item_new"
            })
            
    except Exception as e:
        return jsonify({"error": f"Fehler bei Phase-Checklisten: {str(e)}"}), 500

@api_bp.route('/phase-checklist/toggle/<item_id>', methods=['POST'])
def toggle_checklist_item(item_id):
    """Checklist-Item umschalten"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        success = db.toggle_checklist_item(item_id)
        
        return jsonify({
            "success": success,
            "message": "Item-Status geändert" if success else "Item nicht gefunden"
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Umschalten: {str(e)}"}), 500

@api_bp.route('/phase-checklist/delete/<item_id>', methods=['DELETE'])
def delete_checklist_item(item_id):
    """Checklist-Item löschen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        success = db.delete_checklist_item(item_id)
        
        return jsonify({
            "success": success,
            "message": "Item erfolgreich gelöscht" if success else "Item nicht gefunden"
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Löschen: {str(e)}"}), 500

@api_bp.route('/phase-warnings/<plant_id>', methods=['GET', 'POST'])
def manage_phase_warnings(plant_id):
    """Phase-Warnungen verwalten"""
    try:
        if request.method == 'GET':
            # Fallback-Daten für stabile Funktionalität
            warnings = [
                {
                    "id": "warning_1",
                    "warning_type": "phase_transition",
                    "warning_message": "Blütephase beginnt bald - Lichtzyklus vorbereiten!",
                    "warning_level": "info",
                    "triggered_at": "2025-07-12T10:00:00"
                }
            ]
            
            return jsonify({
                "success": True,
                "warnings": warnings
            })
        
        elif request.method == 'POST':
            data = request.get_json()
            warning_type = data.get('warning_type')
            warning_message = data.get('warning_message')
            warning_level = data.get('warning_level', 'info')
            
            if not warning_type or not warning_message:
                return jsonify({"success": False, "message": "Warning-Type und Message erforderlich"}), 400
            
            # Fallback-Response
            return jsonify({
                "success": True,
                "message": "Warnung hinzugefügt",
                "warning_id": "warning_new"
            })
            
    except Exception as e:
        return jsonify({"error": f"Fehler bei Phase-Warnungen: {str(e)}"}), 500

@api_bp.route('/phase-warnings/acknowledge/<warning_id>', methods=['POST'])
def acknowledge_warning(warning_id):
    """Warnung bestätigen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        success = db.acknowledge_warning(warning_id)
        
        return jsonify({
            "success": success,
            "message": "Warnung bestätigt" if success else "Warnung nicht gefunden"
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Bestätigen: {str(e)}"}), 500

@api_bp.route('/phase-warnings/delete/<warning_id>', methods=['DELETE'])
def delete_warning(warning_id):
    """Warnung löschen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        success = db.delete_warning(warning_id)
        
        return jsonify({
            "success": success,
            "message": "Warnung erfolgreich gelöscht" if success else "Warnung nicht gefunden"
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Löschen: {str(e)}"}), 500

# ===== FLOWERING API ENDPOINTS =====

@api_bp.route('/flowering/status/<plant_id>')
def get_flowering_status(plant_id):
    """Blüte-Status abrufen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"success": False, "message": "Pflanze nicht gefunden"}), 404
        
        # Fallback-Daten für Blüte-Status
        flowering_status = {
            "success": True,
            "plant_id": plant_id,
            "current_phase": plant.get('current_phase', 'vegetative'),
            "flower_start_date": plant.get('flower_start_date'),
            "days_in_flower": 25,  # Fallback
            "total_flower_days": 70,  # Fallback
            "trichome_status": {
                "clear": 20,
                "milky": 65,
                "amber": 15
            },
            "harvest_recommendation": {
                "optimal_harvest": "2-4 weeks",
                "urgency": "medium"
            },
            "flush_status": {
                "flush_active": False,
                "days_until_flush": 15
            }
        }
        
        return jsonify(flowering_status)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden des Blüte-Status: {str(e)}"}), 500

# ===== NUTRIENT API ENDPOINTS =====

@api_bp.route('/nutrients/analyse', methods=['POST'])
def analyse_nutrients():
    """Nährstoff-Analyse durchführen"""
    try:
        data = request.get_json()
        
        # Fallback-Analyse
        analysis = {
            "input_ec": data.get('input_ec', 1.2),
            "input_ph": data.get('input_ph', 6.0),
            "drain_ec": data.get('drain_ec'),
            "drain_ph": data.get('drain_ph'),
            "analysis": {
                "diagnosis": "Optimal",
                "targets": {
                    "ec_min": 1.0,
                    "ec_max": 1.8,
                    "ph_min": 5.8,
                    "ph_max": 6.4
                },
                "phase_notes": "Nährstoffversorgung optimal für aktuelle Phase",
                "alerts": [],
                "recommendations": [
                    "Aktuelle Einstellungen beibehalten",
                    "Regelmäßige EC/pH-Messungen fortsetzen"
                ],
                "warnings": []
            }
        }
        
        return jsonify(analysis)
        
    except Exception as e:
        return jsonify({"error": f"Fehler bei der Nährstoff-Analyse: {str(e)}"}), 500

# ===== GENERAL API ENDPOINTS =====

@api_bp.route('/plants/<plant_id>')
def get_plant(plant_id):
    """Pflanze abrufen"""
    try:
        # Fallback-Daten für stabile Funktionalität
        plant_data = {
            "success": True,
            "plant": {
                "id": plant_id,
                "plant_name": "Test-Pflanze",
                "strain": "White Widow XXL Auto",
                "strain_type": "autoflowering",
                "current_phase": "flowering_middle",
                "flower_start_date": "2025-06-02",
                "pot_size": "11L",
                "substrate": "soil",
                "fertilizer_brand": "biobizz"
            }
        }
        
        return jsonify(plant_data)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Pflanze: {str(e)}"}), 500

@api_bp.route('/health')
def health_check():
    """Health Check für API"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }) 

# ===== PLANT ENTRIES API ENDPOINTS =====

@api_bp.route('/plants/<plant_id>/entries')
def get_plant_entries(plant_id):
    """Pflanzen-Einträge abrufen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        entry_type = request.args.get('entry_type')
        limit = request.args.get('limit', type=int)
        
        if entry_type == 'measurement':
            entries = db.get_measurements_by_plant(plant_id, limit)
        else:
            entries = db.get_entries_by_plant(plant_id)
            if limit:
                entries = entries[:limit]
        
        return jsonify({
            "success": True,
            "entries": entries
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Einträge: {str(e)}"}), 500

# ===== FERTILIZER API ENDPOINTS =====

@api_bp.route('/fertilizer/plant-data/<plant_id>')
def get_fertilizer_plant_data(plant_id):
    """Dünger-Pflanzendaten abrufen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"success": False, "message": "Pflanze nicht gefunden"}), 404
        
        # Fallback-Daten für Dünger-Management
        fertilizer_data = {
            "success": True,
            "plant": plant,
            "current_phase": plant.get('current_phase', 'vegetative'),
            "strain_type": plant.get('strain_type', 'photoperiodic'),
            "pot_size": plant.get('pot_size', '11L'),
            "substrate": plant.get('substrate', 'Coco'),
            "fertilizer_brand": plant.get('fertilizer_brand', 'Canna'),
            "last_fertilizer_date": plant.get('last_fertilizer_date'),
            "fertilizer_schedule": {
                "vegetative": {
                    "ec_target": 1.2,
                    "ph_target": 6.0,
                    "frequency": "every_watering"
                },
                "flowering": {
                    "ec_target": 1.5,
                    "ph_target": 6.1,
                    "frequency": "every_watering"
                }
            }
        }
        
        return jsonify(fertilizer_data)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Dünger-Daten: {str(e)}"}), 500

@api_bp.route('/fertilizer-recommendations/<plant_id>')
def get_fertilizer_recommendations(plant_id):
    """Dünger-Empfehlungen abrufen"""
    try:
        from database_basic import GrowDiaryBasicDB
        db = GrowDiaryBasicDB()
        
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"success": False, "message": "Pflanze nicht gefunden"}), 404
        
        brand = request.args.get('brand', 'biobizz')
        current_phase = plant.get('current_phase', 'vegetative')
        strain_type = plant.get('strain_type', 'photoperiodic')
        
        # Fallback-Empfehlungen basierend auf Marke und Phase
        recommendations = {
            "success": True,
            "recommendations": {
                "name": f"{brand.title()} - {current_phase.title()}",
                "phase": current_phase,
                "strain_type": strain_type,
                "ec_target": 1.2 if current_phase == 'vegetative' else 1.5,
                "ec_range": "1.0-1.4" if current_phase == 'vegetative' else "1.3-1.8",
                "ph_target": 6.0,
                "fertilizers": [
                    {
                        "name": "Grow" if current_phase == 'vegetative' else "Bloom",
                        "dosage": "2ml/L",
                        "frequency": "Jede Bewässerung",
                        "purpose": "Hauptnährstoffe"
                    },
                    {
                        "name": "CalMag",
                        "dosage": "1ml/L",
                        "frequency": "Jede Bewässerung",
                        "purpose": "Calcium & Magnesium"
                    }
                ],
                "notes": f"Optimale Düngung für {current_phase} Phase mit {strain_type} Strain",
                "warnings": [
                    "EC-Werte regelmäßig überprüfen",
                    "pH-Werte zwischen 5.8-6.4 halten"
                ]
            }
        }
        
        return jsonify(recommendations)
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Laden der Dünger-Empfehlungen: {str(e)}"}), 500 