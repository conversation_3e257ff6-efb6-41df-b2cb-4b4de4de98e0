#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Stress-Management (LST/HST) Empfehlungen für das Grow-Tagebuch
Enthält Timing und Techniken für Low Stress Training und High Stress Training
"""

# Stress-Management Empfehlungen pro Phase
STRESS_MANAGEMENT_RECOMMENDATIONS = {
    'germination': {
        'name': 'Keim<PERSON>',
        'lst_recommended': False,
        'hst_recommended': False,
        'reason': 'Pflanzen sind noch zu empfindlich für Training',
        'notes': 'Lass die Pflanzen in Ruhe keimen und erste Blätter entwickeln',
        'next_training': 'vegetative_early'
    },
    'vegetative_early': {
        'name': '<PERSON>ühe Wachstumsphase',
        'lst_recommended': True,
        'hst_recommended': False,
        'lst_techniques': [
            'Sanftes Binden der Haupttriebe',
            'Langsames Biegen der Stängel',
            'Verteilen der Äste für bessere Lichtverteilung'
        ],
        'lst_timing': 'Ab 3-4 Blattpaaren',
        'lst_frequency': 'Alle 2-3 Tage anpassen',
        'hst_reason': 'P<PERSON>lanzen sind noch zu jung für HST',
        'notes': 'Langsam und vorsichtig beginnen, Pflanzen beobachten',
        'warnings': [
            'Nicht zu stark biegen',
            'Auf Risse im Stängel achten',
            'Bei Stress sofort stoppen'
        ]
    },
    'vegetative_middle': {
        'name': 'Mittlere Wachstumsphase',
        'lst_recommended': True,
        'hst_recommended': True,
        'lst_techniques': [
            'Intensives LST für bessere Verzweigung',
            'Binden der Seitenäste',
            'Schaffen einer gleichmäßigen Krone'
        ],
        'lst_timing': 'Kontinuierlich während der Phase',
        'lst_frequency': 'Alle 1-2 Tage anpassen',
        'hst_techniques': [
            'Topping (Haupttrieb kappen)',
            'Fimming (sanfteres Topping)',
            'Super Cropping (Stängel knicken)'
        ],
        'hst_timing': 'Ab 5-6 Blattpaaren',
        'hst_frequency': 'Maximal 2-3 mal in dieser Phase',
        'notes': 'Beste Zeit für HST, Pflanzen sind robust genug',
        'warnings': [
            'HST nur bei gesunden Pflanzen',
            'Ausreichend Zeit zwischen HST-Eingriffen',
            'Bei Krankheit oder Stress kein HST'
        ]
    },
    'vegetative_late': {
        'name': 'Späte Wachstumsphase',
        'lst_recommended': True,
        'hst_recommended': False,
        'lst_techniques': [
            'Finale Formung vor der Blüte',
            'Optimierung der Lichtverteilung',
            'Vorbereitung für Blüte-Stretch'
        ],
        'lst_timing': 'Kontinuierlich bis Blüte-Start',
        'lst_frequency': 'Alle 2-3 Tage anpassen',
        'hst_reason': 'Zu spät für HST, könnte Blüte verzögern',
        'notes': 'Fokus auf LST und finale Formung',
        'warnings': [
            'Kein HST mehr in dieser Phase',
            'LST sanfter durchführen',
            'Auf Blüte-Start vorbereiten'
        ]
    },
    'flowering_early': {
        'name': 'Frühe Blüte',
        'lst_recommended': True,
        'hst_recommended': False,
        'lst_techniques': [
            'Sanftes LST während Stretch-Phase',
            'Unterstützung der wachsenden Äste',
            'Lichtverteilung optimieren'
        ],
        'lst_timing': 'Während der ersten 2 Wochen Blüte',
        'lst_frequency': 'Alle 3-4 Tage anpassen',
        'hst_reason': 'HST würde Blütenentwicklung stören',
        'notes': 'Stretch-Phase nutzen für finale Formung',
        'warnings': [
            'Sehr sanft vorgehen',
            'Keine HST-Techniken',
            'Auf Blütenbildung achten'
        ]
    },
    'flowering_middle': {
        'name': 'Mittlere Blüte',
        'lst_recommended': False,
        'hst_recommended': False,
        'reason': 'Pflanzen sind in voller Blüte, kein Training mehr',
        'notes': 'Fokus auf Blütenentwicklung und Trichombildung',
        'warnings': [
            'Kein Training mehr in dieser Phase',
            'Pflanzen in Ruhe lassen',
            'Nur bei Notwendigkeit (Stütze) eingreifen'
        ]
    },
    'flowering_late': {
        'name': 'Späte Blüte',
        'lst_recommended': False,
        'hst_recommended': False,
        'reason': 'Blüten reifen, kein Training mehr',
        'notes': 'Pflanzen in Ruhe lassen für optimale Reifung',
        'warnings': [
            'Absolut kein Training mehr',
            'Pflanzen nicht mehr bewegen',
            'Fokus auf Ernte-Vorbereitung'
        ]
    },
    'flush': {
        'name': 'Flush',
        'lst_recommended': False,
        'hst_recommended': False,
        'reason': 'Finale Phase vor der Ernte',
        'notes': 'Pflanzen in Ruhe lassen für optimale Qualität',
        'warnings': [
            'Keine Manipulation der Pflanzen',
            'Nur Wasser geben',
            'Auf Ernte vorbereiten'
        ]
    }
}

# Allgemeine LST-Techniken
LST_TECHNIQUES = {
    'bending': {
        'name': 'Sanftes Biegen',
        'description': 'Haupttriebe vorsichtig zur Seite biegen',
        'when': 'Vegetative Phase',
        'frequency': 'Alle 2-3 Tage',
        'tips': [
            'Langsam und vorsichtig vorgehen',
            'Nicht zu stark biegen',
            'Auf Risse achten'
        ]
    },
    'tying': {
        'name': 'Binden',
        'description': 'Äste mit weichem Material festbinden',
        'when': 'Vegetative Phase',
        'frequency': 'Bei Bedarf',
        'tips': [
            'Weiches Material verwenden (Gartenband)',
            'Nicht zu fest binden',
            'Regelmäßig kontrollieren'
        ]
    },
    'spreading': {
        'name': 'Verteilen',
        'description': 'Äste gleichmäßig verteilen für bessere Lichtverteilung',
        'when': 'Vegetative Phase',
        'frequency': 'Kontinuierlich',
        'tips': [
            'Gleichmäßige Verteilung anstreben',
            'Lichtabstand beachten',
            'Überlappung vermeiden'
        ]
    }
}

# Allgemeine HST-Techniken
HST_TECHNIQUES = {
    'topping': {
        'name': 'Topping',
        'description': 'Haupttrieb kappen für mehr Verzweigung',
        'when': 'Vegetative Phase (Mitte)',
        'frequency': '1-2 mal pro Pflanze',
        'tips': [
            'Sauberes Werkzeug verwenden',
            'Über 4-5 Blattpaar schneiden',
            'Ausreichend Zeit zwischen Eingriffen'
        ],
        'risks': [
            'Kann Wachstum verzögern',
            'Bei falscher Ausführung schädlich',
            'Nur bei gesunden Pflanzen'
        ]
    },
    'fimming': {
        'name': 'Fimming',
        'description': 'Sanfteres Topping, nur obere Blätter entfernen',
        'when': 'Vegetative Phase (Mitte)',
        'frequency': '1-2 mal pro Pflanze',
        'tips': [
            'Nur obere Blätter entfernen',
            'Weniger stressig als Topping',
            'Gut für Anfänger geeignet'
        ],
        'risks': [
            'Weniger effektiv als Topping',
            'Kann ungleichmäßig wachsen',
            'Erfordert Erfahrung'
        ]
    },
    'super_cropping': {
        'name': 'Super Cropping',
        'description': 'Stängel knicken ohne zu brechen',
        'when': 'Vegetative Phase (Mitte)',
        'frequency': 'Maximal 2-3 mal',
        'tips': [
            'Stängel zwischen Fingern rollen',
            'Nicht komplett durchbrechen',
            'Nur bei dickeren Stängeln'
        ],
        'risks': [
            'Kann Stängel beschädigen',
            'Erfordert viel Erfahrung',
            'Nicht für Anfänger geeignet'
        ]
    }
}

def get_stress_management_recommendation(phase):
    """
    Gibt Stress-Management-Empfehlungen für eine bestimmte Phase zurück
    
    Args:
        phase (str): Aktuelle Phase (z.B. 'vegetative_middle')
    
    Returns:
        dict: Stress-Management-Empfehlungen
    """
    if phase not in STRESS_MANAGEMENT_RECOMMENDATIONS:
        return {
            'error': f'Unbekannte Phase: {phase}',
            'phase': phase
        }
    
    return {
        'phase': phase,
        'phase_name': STRESS_MANAGEMENT_RECOMMENDATIONS[phase]['name'],
        'recommendations': STRESS_MANAGEMENT_RECOMMENDATIONS[phase]
    }

def get_lst_techniques():
    """
    Gibt alle verfügbaren LST-Techniken zurück
    
    Returns:
        dict: Alle LST-Techniken
    """
    return LST_TECHNIQUES

def get_hst_techniques():
    """
    Gibt alle verfügbaren HST-Techniken zurück
    
    Returns:
        dict: Alle HST-Techniken
    """
    return HST_TECHNIQUES

def get_training_timeline(phase):
    """
    Gibt eine Timeline für Training-Empfehlungen zurück
    
    Args:
        phase (str): Aktuelle Phase
    
    Returns:
        dict: Training-Timeline
    """
    phases = list(STRESS_MANAGEMENT_RECOMMENDATIONS.keys())
    
    if phase not in phases:
        return {'error': f'Unbekannte Phase: {phase}'}
    
    current_index = phases.index(phase)
    timeline = {
        'current_phase': phase,
        'current_recommendations': STRESS_MANAGEMENT_RECOMMENDATIONS[phase],
        'past_phases': [],
        'future_phases': []
    }
    
    # Vergangene Phasen
    for i in range(current_index):
        past_phase = phases[i]
        timeline['past_phases'].append({
            'phase': past_phase,
            'name': STRESS_MANAGEMENT_RECOMMENDATIONS[past_phase]['name'],
            'lst_recommended': STRESS_MANAGEMENT_RECOMMENDATIONS[past_phase]['lst_recommended'],
            'hst_recommended': STRESS_MANAGEMENT_RECOMMENDATIONS[past_phase]['hst_recommended']
        })
    
    # Zukünftige Phasen
    for i in range(current_index + 1, len(phases)):
        future_phase = phases[i]
        timeline['future_phases'].append({
            'phase': future_phase,
            'name': STRESS_MANAGEMENT_RECOMMENDATIONS[future_phase]['name'],
            'lst_recommended': STRESS_MANAGEMENT_RECOMMENDATIONS[future_phase]['lst_recommended'],
            'hst_recommended': STRESS_MANAGEMENT_RECOMMENDATIONS[future_phase]['hst_recommended']
        })
    
    return timeline 