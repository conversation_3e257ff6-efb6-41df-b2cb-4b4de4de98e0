#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flush-Planungs-Routes für praktische Flush-Planung
Basierend auf echten Daten von Blüte-Tag 41
"""

from flask import Blueprint, request, jsonify, render_template
from datetime import datetime, timedelta
import json

flush_bp = Blueprint('flush', __name__, url_prefix='/flush')

# Beispiel-Daten basierend auf deinen JSON-Daten
SAMPLE_FLUSH_DATA = {
    "plant_id": "GG25L",
    "flush_status": {
        "flush_planned": True,
        "flush_start_day": 52,
        "estimated_flush_window": [52, 64],
        "flush_trigger_conditions": [
            "Trichome: ≥60 % milchig",
            "Pistillen: ≥70 % bräunlich zurückgezogen",
            "Drain-EC: sinkend (< 2.0 mS/cm)",
            "pH-Stabilisierung im Drain (6.0–6.4)"
        ],
        "flush_medium": "Wasser + ggf. minimal CalMag (0.2 mS)",
        "notes": [
            "Flush frühestens ab BT52 starten.",
            "Falls Zielwirkung eher aktiv: Flush früher beginnen.",
            "Bei Zielwirkung 'Couchlock': Flush max. bis BT58 verzögern."
        ]
    },
    "last_updated": "2025-07-13T15:30:00Z"
}

@flush_bp.route('/')
def flush_dashboard():
    """Hauptseite für Flush-Planung"""
    return render_template('widgets/flush-widget.html')

@flush_bp.route('/plan/<plant_id>')
def get_flush_plan(plant_id):
    """Flush-Plan für eine Pflanze abrufen"""
    if plant_id == "GG25L":
        return jsonify(SAMPLE_FLUSH_DATA)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@flush_bp.route('/plan/<plant_id>', methods=['POST'])
def create_flush_plan(plant_id):
    """Neuen Flush-Plan erstellen"""
    try:
        data = request.get_json()
        
        # Validierung der Daten
        required_fields = ['flush_start_day', 'estimated_flush_window']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Feld '{field}' fehlt"}), 400
        
        # Flush-Plan erstellen
        flush_plan = {
            "plant_id": plant_id,
            "flush_status": {
                "flush_planned": True,
                "flush_start_day": data['flush_start_day'],
                "estimated_flush_window": data['estimated_flush_window'],
                "flush_trigger_conditions": data.get('flush_trigger_conditions', [
                    "Trichome: ≥60 % milchig",
                    "Pistillen: ≥70 % bräunlich zurückgezogen",
                    "Drain-EC: sinkend (< 2.0 mS/cm)",
                    "pH-Stabilisierung im Drain (6.0–6.4)"
                ]),
                "flush_medium": data.get('flush_medium', 'Wasser'),
                "notes": data.get('notes', [])
            },
            "last_updated": datetime.now().isoformat()
        }
        
        return jsonify({
            "message": "Flush-Plan erfolgreich erstellt",
            "flush_plan": flush_plan
        }), 201
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Erstellen: {str(e)}"}), 500

@flush_bp.route('/status/<plant_id>')
def get_flush_status(plant_id):
    """Aktuellen Flush-Status abrufen"""
    if plant_id == "GG25L":
        # Berechne aktuellen Status basierend auf Blüte-Tag
        current_bloom_day = 41  # Aus deinen Daten
        flush_start_day = SAMPLE_FLUSH_DATA['flush_status']['flush_start_day']
        
        days_until_flush = flush_start_day - current_bloom_day
        
        if days_until_flush <= 0:
            status = "flush_active"
            message = "Flush läuft bereits"
        elif days_until_flush <= 3:
            status = "flush_imminent"
            message = f"Flush startet in {days_until_flush} Tagen"
        elif days_until_flush <= 7:
            status = "flush_preparation"
            message = f"Flush-Vorbereitung: {days_until_flush} Tage"
        else:
            status = "flush_planning"
            message = f"Flush geplant in {days_until_flush} Tagen"
        
        flush_status = {
            "plant_id": plant_id,
            "current_bloom_day": current_bloom_day,
            "flush_start_day": flush_start_day,
            "days_until_flush": days_until_flush,
            "status": status,
            "message": message,
            "recommendations": {
                "immediate_actions": [],
                "preparation_actions": [],
                "monitoring": []
            }
        }
        
        # Empfehlungen basierend auf Status
        if status == "flush_imminent":
            flush_status["recommendations"]["immediate_actions"] = [
                "Letzte Nährstoffgabe vorbereiten",
                "Drain-EC und pH überwachen",
                "Trichome-Status prüfen"
            ]
        elif status == "flush_preparation":
            flush_status["recommendations"]["preparation_actions"] = [
                "Flush-Medium vorbereiten",
                "Bewässerungsplan anpassen",
                "EC/pH-Sensoren kalibrieren"
            ]
        
        return jsonify(flush_status)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@flush_bp.route('/trigger/<plant_id>', methods=['POST'])
def trigger_flush(plant_id):
    """Flush manuell auslösen"""
    try:
        data = request.get_json()
        trigger_reason = data.get('reason', 'Manueller Trigger')
        
        # Hier würde normalerweise der Flush-Status in der Datenbank aktualisiert
        flush_trigger = {
            "plant_id": plant_id,
            "trigger_date": datetime.now().isoformat(),
            "trigger_reason": trigger_reason,
            "bloom_day": data.get('bloom_day', 41),
            "trichome_status": data.get('trichome_status', {}),
            "drain_ec": data.get('drain_ec'),
            "drain_ph": data.get('drain_ph'),
            "status": "flush_started"
        }
        
        return jsonify({
            "message": "Flush erfolgreich ausgelöst",
            "flush_trigger": flush_trigger
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Auslösen: {str(e)}"}), 500

@flush_bp.route('/monitoring/<plant_id>')
def get_flush_monitoring(plant_id):
    """Flush-Monitoring-Daten abrufen"""
    if plant_id == "GG25L":
        # Beispiel-Monitoring-Daten
        monitoring_data = {
            "plant_id": plant_id,
            "current_bloom_day": 41,
            "flush_start_day": 52,
            "monitoring_data": {
                "drain_ec_history": [
                    {"date": "2025-07-10", "ec": 2.1, "bloom_day": 38},
                    {"date": "2025-07-11", "ec": 2.0, "bloom_day": 39},
                    {"date": "2025-07-12", "ec": 1.9, "bloom_day": 40},
                    {"date": "2025-07-13", "ec": 1.8, "bloom_day": 41}
                ],
                "drain_ph_history": [
                    {"date": "2025-07-10", "ph": 6.2, "bloom_day": 38},
                    {"date": "2025-07-11", "ph": 6.3, "bloom_day": 39},
                    {"date": "2025-07-12", "ph": 6.2, "bloom_day": 40},
                    {"date": "2025-07-13", "ph": 6.1, "bloom_day": 41}
                ],
                "trichome_progress": [
                    {"date": "2025-07-10", "milky_percentage": 15, "bloom_day": 38},
                    {"date": "2025-07-13", "milky_percentage": 22.5, "bloom_day": 41}
                ]
            },
            "flush_ready_indicators": {
                "trichome_ready": False,  # < 60% milchig
                "ec_declining": True,     # EC sinkt
                "ph_stable": True,        # pH stabil
                "pistil_ready": False     # < 70% braun
            },
            "recommendations": {
                "next_ec_check": "2025-07-14",
                "next_trichome_check": "2025-07-15",
                "flush_trigger_conditions": [
                    "Trichome: ≥60 % milchig (aktuell: 22.5%)",
                    "Pistillen: ≥70 % bräunlich (noch zu prüfen)",
                    "Drain-EC: sinkend ✓ (1.8 mS/cm)",
                    "pH-Stabilisierung ✓ (6.1)"
                ]
            }
        }
        
        return jsonify(monitoring_data)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@flush_bp.route('/settings/<plant_id>', methods=['POST'])
def update_flush_settings(plant_id):
    """Flush-Einstellungen aktualisieren"""
    try:
        data = request.get_json()
        
        # Validierung
        if 'flush_start_day' in data and data['flush_start_day'] < 40:
            return jsonify({"error": "Flush-Start zu früh (mindestens BT40)"}), 400
        
        # Hier würde normalerweise die Datenbank aktualisiert
        updated_settings = {
            "plant_id": plant_id,
            "flush_start_day": data.get('flush_start_day', 52),
            "flush_duration": data.get('flush_duration', 12),
            "flush_medium": data.get('flush_medium', 'Wasser'),
            "target_ec": data.get('target_ec', 0.5),
            "updated_at": datetime.now().isoformat()
        }
        
        return jsonify({
            "message": "Flush-Einstellungen aktualisiert",
            "settings": updated_settings
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Aktualisieren: {str(e)}"}), 500

@flush_bp.route('/history/<plant_id>')
def get_flush_history(plant_id):
    """Flush-Historie abrufen"""
    if plant_id == "GG25L":
        # Beispiel-Historie
        history = [
            {
                "date": "2025-07-01",
                "bloom_day": 29,
                "action": "Flush-Plan erstellt",
                "notes": "Basierend auf Strain-Profil und Trichome-Entwicklung"
            },
            {
                "date": "2025-07-10",
                "bloom_day": 38,
                "action": "EC-Monitoring gestartet",
                "notes": "EC: 2.1 mS/cm, pH: 6.2"
            },
            {
                "date": "2025-07-13",
                "bloom_day": 41,
                "action": "Trichome-Status geprüft",
                "notes": "22.5% milchig, Flush in 11 Tagen geplant"
            }
        ]
        
        return jsonify({
            "plant_id": plant_id,
            "history": history
        })
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404 