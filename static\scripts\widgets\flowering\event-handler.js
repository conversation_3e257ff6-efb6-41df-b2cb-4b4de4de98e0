/**
 * Flowering Event Handler - Verwaltet alle Event-Listener für das Flowering Widget
 */

class FloweringEventHandler {
    constructor(widget) {
        this.widget = widget;
        this.setupEventListeners();
    }

    /**
     * Richtet alle Event-Listener ein
     */
    setupEventListeners() {
        this.setupTabNavigation();
        this.setupModalEventListeners();
        this.setupMarkerEventListeners();
        this.setupFlushTriggerEventListeners();
        this.setupObservationForm();
        this.setupTimelineEventForm();
        this.setupMarkerForm();
        this.setupGuidelinesTabListeners();
        this.setupMarkerFilterListeners();
    }

    /**
     * Tab-Navigation Event-Listener
     */
    setupTabNavigation() {
        // Tab-Navigation (nur innerhalb des Widgets)
        this.widget.element.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                await this.widget.switchTab(e.target.dataset.tab);
            });
        });

        // Trichom-Tab Event-Listener
        const trichomeTab = this.widget.getElementById('trichomeTab');
        if (trichomeTab) {
            trichomeTab.addEventListener('click', async () => {
                await this.widget.switchTab('trichome');
            });
        }
    }

    /**
     * Modal Event-Listener
     */
    setupModalEventListeners() {
        // Trichom-Beobachtungs-Modal
        const trichomeModalSave = document.getElementById('trichomeObservationModalSave');
        if (trichomeModalSave) {
            trichomeModalSave.addEventListener('click', (e) => {
                e.preventDefault();
                this.widget.trichomeManager.submitObservation();
            });
        }

        // Lösch-Bestätigungs-Modal
        const confirmDeleteBtn = document.querySelector('.btn-confirm-delete');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', async () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal'));
                if (modal) {
                    modal.hide();
                }
                
                // Löschvorgang durchführen
                const index = confirmDeleteBtn.getAttribute('data-delete-index');
                if (index !== null) {
                    await this.widget.trichomeManager.performDelete(parseInt(index));
                }
            });
        }

        // ESC-Taste zum Schließen
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) modal.hide();
                }
            }
        });
    }

    /**
     * Marker Event-Listener
     */
    setupMarkerEventListeners() {
        // Add Marker Button
        const addMarkerBtn = this.widget.getElementById('addMarkerBtn');
        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.widget.timelineManager.openMarkerModal();
            });
        }

        // Edit Marker Button
        const editMarkerBtn = this.widget.getElementById('editMarkerBtn');
        if (editMarkerBtn) {
            editMarkerBtn.addEventListener('click', (e) => {
                const markerId = e.target.dataset.markerId;
                this.widget.timelineManager.editMarker(markerId);
            });
        }

        // Delete Marker Button
        const deleteMarkerBtn = this.widget.getElementById('deleteMarkerBtn');
        if (deleteMarkerBtn) {
            deleteMarkerBtn.addEventListener('click', (e) => {
                const markerId = e.target.dataset.markerId;
                this.widget.timelineManager.deleteMarker(markerId);
            });
        }
    }

    /**
     * Flush-Trigger Event-Listener
     */
    setupFlushTriggerEventListeners() {
        // Manual Trigger Button
        const manualTriggerBtn = this.widget.getElementById('manualTriggerBtn');
        if (manualTriggerBtn) {
            manualTriggerBtn.addEventListener('click', () => {
                this.widget.triggerFlushManual();
            });
        }

        // Manual Flush Button
        const manualFlushBtn = this.widget.getElementById('manualFlushBtn');
        if (manualFlushBtn) {
            manualFlushBtn.addEventListener('click', () => {
                this.widget.triggerManualFlush();
            });
        }
    }

    /**
     * Beobachtungsformular Event-Listener
     */
    setupObservationForm() {
        const addObservationBtn = this.widget.getElementById('addObservationBtn');
        if (addObservationBtn) {
            addObservationBtn.addEventListener('click', () => {
                this.widget.trichomeManager.openObservationModal();
            });
        }
    }

    /**
     * Timeline-Event-Formular Event-Listener
     */
    setupTimelineEventForm() {
        const saveEventBtn = this.widget.getElementById('saveEventBtn');
        if (saveEventBtn) {
            saveEventBtn.addEventListener('click', () => {
                this.widget.timelineManager.saveTimelineEvent();
            });
        }
    }

    /**
     * Marker-Formular Event-Listener
     */
    setupMarkerForm() {
        const saveMarkerBtn = this.widget.getElementById('saveMarkerBtn');
        if (saveMarkerBtn) {
            saveMarkerBtn.addEventListener('click', () => {
                this.widget.timelineManager.saveMarker();
            });
        }
    }

    /**
     * Guidelines-Tab Event-Listener
     */
    setupGuidelinesTabListeners() {
        this.widget.element.querySelectorAll('.guideline-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchGuidelineTab(e.target.dataset.tab);
            });
        });
    }

    /**
     * Marker-Filter Event-Listener
     */
    setupMarkerFilterListeners() {
        // Kategorie-Filter
        const categoryFilter = this.widget.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.widget.timelineManager.filterMarkers();
            });
        }

        // Wichtigkeit-Filter
        const importanceFilter = this.widget.getElementById('importanceFilter');
        if (importanceFilter) {
            importanceFilter.addEventListener('change', () => {
                this.widget.timelineManager.filterMarkers();
            });
        }
    }

    /**
     * Guidelines-Tab wechseln
     */
    switchGuidelineTab(tabName) {
        // Finde den Container, in dem der geklickte Tab ist
        const clickedTab = event.target;
        const guidelinesContainer = clickedTab.closest('.flush-guidelines, .trichome-guidelines');
        
        if (!guidelinesContainer) return;
        
        // Alle Tabs und Panels in diesem Container deaktivieren
        guidelinesContainer.querySelectorAll('.guideline-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        guidelinesContainer.querySelectorAll('.guideline-content').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Gewählten Tab aktivieren
        clickedTab.classList.add('active');
        
        // Entsprechendes Panel aktivieren
        const panelId = `${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Content`;
        const activePanel = guidelinesContainer.querySelector(`#flush${panelId}, #trichome${panelId}`);
        
        if (activePanel) {
            activePanel.classList.add('active');
        }
    }
}
