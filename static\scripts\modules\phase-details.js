/**
 * Phase Details Module (Vereinfacht)
 * Handles basic Phase 2 information display
 */

class PhaseDetailsManager {
    constructor() {
        this.plantId = null;
        this.currentData = null;
        this.init();
    }

    init() {
        // Plant ID aus URL extrahieren
        const urlParts = window.location.pathname.split('/');
        this.plantId = urlParts[urlParts.length - 1];
        
        if (!this.plantId) {
            console.warn('Keine gültige Plant ID gefunden');
            return;
        }

        this.setupTabListeners();
        this.loadInitialData();
    }

    setupTabListeners() {
        // Tab-Event-Listener nur für Phase 2 Tabs (nicht für Dünger-Tabs)
        const tabButtons = document.querySelectorAll('#phaseDetailsTabs .nav-link');
        
        tabButtons.forEach((button, index) => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const target = button.getAttribute('data-bs-target');
                
                // Entferne aktive Klasse von allen Phase 2 Tabs
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });
                
                // Füge aktive Klasse zum geklickten Tab hinzu
                button.classList.add('active');
                button.setAttribute('aria-selected', 'true');
                
                // Lade Tab-Inhalt
                this.loadTabContent(target);
            });
        });
    }

    async loadInitialData() {
        try {
            // Vereinfachte Phasen-Details laden
            const response = await fetch(`/api/phase-details/${this.plantId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.currentData = data.details;
                // Nach dem Laden der Daten den ersten Tab initialisieren
                this.initializeFirstTab();
            } else {
                console.error('Fehler beim Laden der Phasen-Details:', data.message);
                this.showError('Fehler beim Laden der Phasen-Details: ' + data.message);
            }
        } catch (error) {
            console.error('Fehler beim Laden der Phasen-Details:', error);
            // Bei Fehlern trotzdem statische Inhalte anzeigen
            this.currentData = {
                phase_info: {
                    name: 'Aktuelle Phase',
                    description: 'Phase wird berechnet',
                    duration: 'Variabel'
                }
            };
            // Nach dem Laden der Daten den ersten Tab initialisieren
            this.initializeFirstTab();
        }
    }

    initializeFirstTab() {
        // Initialisiere den ersten Phase 2 Tab als aktiv und lade Inhalt
        const tabButtons = document.querySelectorAll('#phaseDetailsTabs .nav-link');
        if (tabButtons.length > 0) {
            tabButtons[0].classList.add('active');
            tabButtons[0].setAttribute('aria-selected', 'true');
            const firstTarget = tabButtons[0].getAttribute('data-bs-target');
            if (firstTarget) {
                // Verstecke alle Tab-Inhalte zuerst
                const allPhase2TabContents = document.querySelectorAll('#phaseDetailsTabContent .tab-pane');
                allPhase2TabContents.forEach(content => {
                    content.style.display = 'none';
                    content.classList.remove('show', 'active');
                });
                
                // Zeige den ersten Tab-Inhalt
                const firstContent = document.querySelector(firstTarget);
                if (firstContent) {
                    firstContent.style.display = 'block';
                    firstContent.classList.add('show', 'active');
                }
                
                // Lade den Inhalt des ersten Tabs
                this.loadTabContent(firstTarget);
            }
        }
    }

    async loadTabContent(targetId) {
        // Verstecke nur Phase 2 Tab-Inhalte
        const allPhase2TabContents = document.querySelectorAll('#phaseDetailsTabContent .tab-pane');
        allPhase2TabContents.forEach(content => {
            content.style.display = 'none';
            content.classList.remove('show', 'active');
        });
        
        // Zeige den gewählten Phase 2 Tab-Inhalt
        const targetContent = document.querySelector(targetId);
        if (targetContent) {
            targetContent.style.display = 'block';
            targetContent.classList.add('show', 'active');
        }
        
        switch (targetId) {
            case '#trichome':
                this.loadTrichomeContent();
                break;
            case '#environment':
                this.loadEnvironmentContent();
                break;
            case '#nutrients':
                this.loadNutrientsContent();
                break;
            case '#harvest':
                this.loadHarvestContent();
                break;
            default:
                console.warn('Unbekannter Phase 2 Tab:', targetId);
        }
    }

    loadTrichomeContent() {
        const container = document.getElementById('trichome-content');
        if (!container) {
            return;
        }
        
        if (!this.currentData) {
            container.innerHTML = '<div class="alert alert-warning">Keine Daten verfügbar</div>';
            return;
        }

        const html = `
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-microscope me-2"></i>Trichome-Status</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Entwicklungsstadium:</strong> Entwicklung läuft
                            </div>
                            <div class="mb-3">
                                <strong>Trichome-Zustand:</strong> 
                                <span class="badge bg-warning text-dark">In Entwicklung</span>
                            </div>
                            <div class="mb-3">
                                <strong>Erntebereit:</strong> 
                                <span class="badge bg-secondary">Noch nicht</span>
                            </div>
                            <div class="mb-3">
                                <strong>Beschreibung:</strong>
                                <p class="text-muted small">Trichome entwickeln sich normal</p>
                            </div>
                            <div class="mb-3">
                                <strong>Empfehlung:</strong>
                                <p class="text-muted small">Regelmäßig beobachten und Licht optimieren</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-calendar me-2"></i>Phase-Informationen</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Aktuelle Phase:</strong> ${this.currentData.phase_info?.name || 'Unbekannt'}
                            </div>
                            <div class="mb-3">
                                <strong>Beschreibung:</strong> ${this.currentData.phase_info?.description || 'Keine Beschreibung verfügbar'}
                            </div>
                            <div class="mb-3">
                                <strong>Dauer:</strong> ${this.currentData.phase_info?.duration || 'Unbekannt'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    loadEnvironmentContent() {
        const container = document.getElementById('environment-content');
        if (!container) {
            return;
        }

        const html = `
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-header bg-primary bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-thermometer-half me-2"></i>Temperatur</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Optimal:</strong> 20-28°C
                            </div>
                            <div class="mb-2">
                                <strong>Minimum:</strong> 18°C
                            </div>
                            <div class="mb-2">
                                <strong>Maximum:</strong> 30°C
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-droplet me-2"></i>Luftfeuchtigkeit</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Optimal:</strong> 40-70%
                            </div>
                            <div class="mb-2">
                                <strong>Minimum:</strong> 30%
                            </div>
                            <div class="mb-2">
                                <strong>Maximum:</strong> 80%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-sun me-2"></i>Beleuchtung</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Vegetativ:</strong> 18/6 Stunden
                            </div>
                            <div class="mb-2">
                                <strong>Blüte:</strong> 12/12 Stunden
                            </div>
                            <div class="mb-2">
                                <strong>Intensität:</strong> Anpassen je nach Phase
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card border-secondary">
                        <div class="card-header bg-secondary bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-info-circle me-2"></i>Hinweise</h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Halten Sie die Umgebungsbedingungen konstant und überwachen Sie regelmäßig.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    loadNutrientsContent() {
        const container = document.getElementById('nutrients-content');
        if (!container) {
            return;
        }

        const html = `
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-header bg-primary bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-flask me-2"></i>Primäre Nährstoffe</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-1"><span class="badge bg-primary">Stickstoff (N)</span></div>
                            <div class="mb-1"><span class="badge bg-primary">Phosphor (P)</span></div>
                            <div class="mb-1"><span class="badge bg-primary">Kalium (K)</span></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-success">
                        <div class="card-header bg-success bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-leaf me-2"></i>Sekundäre Nährstoffe</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-1"><span class="badge bg-success">Calcium (Ca)</span></div>
                            <div class="mb-1"><span class="badge bg-success">Magnesium (Mg)</span></div>
                            <div class="mb-1"><span class="badge bg-success">Schwefel (S)</span></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-warning">
                        <div class="card-header bg-warning bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-atom me-2"></i>Mikronährstoffe</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-1"><span class="badge bg-warning text-dark">Eisen (Fe)</span></div>
                            <div class="mb-1"><span class="badge bg-warning text-dark">Zink (Zn)</span></div>
                            <div class="mb-1"><span class="badge bg-warning text-dark">Mangan (Mn)</span></div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-info-circle me-2"></i>Empfehlungen</h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Stickstoff in der vegetativen Phase, Phosphor und Kalium in der Blüte erhöhen.</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    loadHarvestContent() {
        const container = document.getElementById('harvest-content');
        if (!container) {
            return;
        }

        const html = `
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-sickle me-2"></i>Ernte-Informationen</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Ernte-Zeitpunkt:</strong> Basierend auf Trichome-Entwicklung
                            </div>
                            <div class="mb-3">
                                <strong>Anzeichen:</strong> 
                                <ul class="mb-0">
                                    <li>Milchige Trichome</li>
                                    <li>Reife Blüten</li>
                                    <li>Verfärbte Blätter</li>
                                </ul>
                            </div>
                            <div class="mb-3">
                                <strong>Trichome-Zustand:</strong> 
                                <span class="badge bg-warning text-dark">In Entwicklung</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info bg-opacity-10">
                            <h6 class="mb-0"><i class="fa-solid fa-lightbulb me-2"></i>Empfehlungen</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Nach der Ernte:</strong>
                                <ul class="mb-0">
                                    <li>Langsam trocknen</li>
                                    <li>Kühl lagern</li>
                                    <li>Luftfeuchtigkeit kontrollieren</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    showError(message) {
        const containers = [
            'trichome-content',
            'environment-content', 
            'nutrients-content',
            'harvest-content'
        ];
        
        containers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `<div class="alert alert-danger">${message}</div>`;
            }
        });
    }
}

// Initialisierung
document.addEventListener('DOMContentLoaded', () => {
    new PhaseDetailsManager();
}); 