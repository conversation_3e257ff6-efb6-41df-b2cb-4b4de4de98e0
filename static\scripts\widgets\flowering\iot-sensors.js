/**
 * Flowering IoT Sensors - Verwaltet IoT-Sensor-Integration
 */

class FloweringIoTSensors {
    constructor(widget) {
        this.widget = widget;
        this.iotSensorsInitialized = false;
        this.iotStatusShown = false;
    }

    /**
     * Initialisiert IoT Sensor System
     */
    setupIoTSensors() {
        if (!window.iotSensorIntegration) {
            console.warn('🌸 Blüte-Widget: IoTSensorIntegration nicht verfügbar');
            return;
        }
        
        // IoT Sensors nur beim ersten Laden initialisieren
        if (!this.iotSensorsInitialized) {
            this.iotSensorsInitialized = true;
            
            // IoT-Systeme mit Verzögerung starten
            setTimeout(() => {
                this.createSensorNetwork();
                this.registerSensors();
                this.startRemoteMonitoring();
            }, 5000);
        }
        
        // IoT Status nur beim ersten Laden anzeigen
        if (!this.iotStatusShown) {
            this.iotStatusShown = true;
            setTimeout(() => {
                this.showIoTStatus();
            }, 6000);
        }
    }

    /**
     * Erstellt Sensor-Netzwerk
     */
    createSensorNetwork() {
        if (!window.iotSensorIntegration) return;
        
        try {
            const networkId = `network_${this.widget.currentPlantId}`;
            
            // Sensor-Netzwerk erstellen
            const network = window.iotSensorIntegration.createSensorNetwork(
                networkId,
                this.widget.currentPlantId,
                {
                    location: 'Grow Room',
                    plant_phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle'
                }
            );
            
            console.log('🌸 Blüte-Widget: IoT Sensor-Netzwerk erstellt:', network);
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Erstellen des Sensor-Netzwerks:', error);
        }
    }

    /**
     * Registriert Sensoren
     */
    registerSensors() {
        if (!window.iotSensorIntegration) return;
        
        const sensors = [
            { id: 'temp_01', type: 'temperature', location: 'canopy' },
            { id: 'hum_01', type: 'humidity', location: 'canopy' },
            { id: 'ppfd_01', type: 'ppfd', location: 'top_cola' },
            { id: 'co2_01', type: 'co2', location: 'room_center' }
        ];
        
        sensors.forEach(sensor => {
            try {
                const registeredSensor = window.iotSensorIntegration.registerSensor(
                    sensor.id,
                    sensor.type,
                    {
                        plant_id: this.widget.currentPlantId,
                        location: sensor.location,
                        calibrated: true
                    }
                );
                
                if (registeredSensor) {
                    // Sensor zum Netzwerk hinzufügen
                    const networkId = `network_${this.widget.currentPlantId}`;
                    window.iotSensorIntegration.addSensorToNetwork(networkId, sensor.id);
                    
                    console.log(`🌸 Blüte-Widget: Sensor ${sensor.id} registriert`);
                }
                
            } catch (error) {
                console.error(`🌸 Blüte-Widget: Fehler beim Registrieren von Sensor ${sensor.id}:`, error);
            }
        });
    }

    /**
     * Startet Remote-Monitoring
     */
    startRemoteMonitoring() {
        if (!window.iotSensorIntegration) return;
        
        try {
            // Automatische Updates deaktiviert - nur manuelle Updates
            
            // Hinweis anzeigen
            this.showUpdateNotice('IoT Monitoring: Automatische Updates deaktiviert. Daten werden nur bei Bedarf aktualisiert.');
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Starten des Remote-Monitorings:', error);
        }
    }

    /**
     * Zeigt Update-Hinweis an
     */
    showUpdateNotice(message) {
        const noticeElement = document.createElement('div');
        noticeElement.className = 'update-notice';
        noticeElement.textContent = message;
        noticeElement.style.cssText = `
            background: #e3f2fd; color: #1976d2; padding: 10px; border-radius: 5px; 
            margin: 10px 0; font-size: 0.9rem; border-left: 4px solid #2196f3;
        `;
        
        // Hinweis in den Widget-Container einfügen
        const container = this.widget.element.querySelector('.flowering-widget-container');
        if (container) {
            container.appendChild(noticeElement);
        }
    }

    /**
     * Sendet simulierte Sensor-Daten
     */
    async sendSimulatedSensorData() {
        if (!window.iotSensorIntegration) return;
        
        const sensorData = [
            { id: 'temp_01', value: this.generateSimulatedValue('temperature') },
            { id: 'hum_01', value: this.generateSimulatedValue('humidity') },
            { id: 'ppfd_01', value: this.generateSimulatedValue('ppfd') },
            { id: 'co2_01', value: this.generateSimulatedValue('co2') }
        ];
        
        for (const data of sensorData) {
            try {
                await window.iotSensorIntegration.sendSensorData(
                    data.id,
                    data.value,
                    new Date().toISOString()
                );
            } catch (error) {
                console.error(`🌸 Blüte-Widget: Fehler beim Senden von Sensor-Daten für ${data.id}:`, error);
            }
        }
    }

    /**
     * Generiert simulierte Werte
     */
    generateSimulatedValue(sensorType) {
        switch (sensorType) {
            case 'temperature':
                return 25 + Math.random() * 5; // 25-30°C
            case 'humidity':
                return 60 + Math.random() * 10; // 60-70%
            case 'ppfd':
                return 600 + Math.random() * 200; // 600-800 μmol/m²/s
            case 'co2':
                return 400 + Math.random() * 100; // 400-500 ppm
            default:
                return Math.random() * 100;
        }
    }

    /**
     * Zeigt IoT Status an
     */
    showIoTStatus() {
        try {
            const networkId = `network_${this.widget.currentPlantId}`;
            const networkStatus = window.iotSensorIntegration?.getNetworkStatus(networkId);
            
            if (!networkStatus) {
                console.warn('🌸 Blüte-Widget: IoT Netzwerk nicht gefunden');
                // Fallback: Demo-IoT-Status anzeigen
                this.showIoTStatusCard(this.generateDemoIoTStatus());
                return;
            }
            
            // IoT Status Card erstellen/anzeigen
            this.showIoTStatusCard(networkStatus);
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Abrufen des IoT Status:', error);
            // Fallback: Demo-IoT-Status anzeigen
            this.showIoTStatusCard(this.generateDemoIoTStatus());
        }
    }

    /**
     * Generiert Demo-IoT-Status für Anzeige
     */
    generateDemoIoTStatus() {
        return {
            network_id: `network_${this.widget.currentPlantId}`,
            status: 'active',
            sensors: [
                { id: 'temp_01', type: 'temperature', status: 'online', last_value: 26.5, unit: '°C' },
                { id: 'hum_01', type: 'humidity', status: 'online', last_value: 65, unit: '%' },
                { id: 'ppfd_01', type: 'ppfd', status: 'online', last_value: 750, unit: 'μmol/m²/s' },
                { id: 'co2_01', type: 'co2', status: 'offline', last_value: 420, unit: 'ppm' }
            ],
            last_update: new Date().toISOString(),
            data_points_today: 1440, // 24h * 60min
            uptime: '99.2%'
        };
    }

    /**
     * Zeigt IoT Status Card an
     */
    showIoTStatusCard(networkStatus) {
        // Prüfen ob bereits eine IoT Status Card existiert
        let iotCard = this.widget.element.querySelector('.iot-status-card');
        
        if (!iotCard) {
            // Neue IoT Status Card erstellen
            iotCard = document.createElement('div');
            iotCard.className = 'lighting-card iot-status-card';
            this.widget.element.appendChild(iotCard);
        }
        
        // Card-Inhalt aktualisieren
        iotCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-network-wired"></i>
                <h3>IoT Sensor-Netzwerk</h3>
                <div class="network-status">
                    <span class="status-indicator ${networkStatus.status === 'active' ? '' : 'error'}"></span>
                    <span class="status-text">${networkStatus.status === 'active' ? 'Aktiv' : 'Inaktiv'}</span>
                </div>
            </div>
            <div class="iot-content">
                <div class="network-info">
                    <div class="info-item">
                        <span class="info-label">Netzwerk-ID:</span>
                        <span class="info-value">${networkStatus.network_id}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Uptime:</span>
                        <span class="info-value">${networkStatus.uptime}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Datenpunkte heute:</span>
                        <span class="info-value">${networkStatus.data_points_today}</span>
                    </div>
                </div>
                <div class="sensors-list">
                    <h4>Sensoren</h4>
                    ${networkStatus.sensors.map(sensor => `
                        <div class="sensor-item">
                            <div class="sensor-header">
                                <div class="sensor-name">${this.getSensorTypeText(sensor.type)}</div>
                                <div class="sensor-status status-${sensor.status}">${sensor.status}</div>
                            </div>
                            <div class="sensor-value">
                                ${sensor.last_value} ${this.getSensorUnit(sensor.type)}
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="last-update">
                    Letzte Aktualisierung: ${new Date(networkStatus.last_update).toLocaleString('de-DE')}
                </div>
            </div>
        `;
        
        // IoT Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && iotCard.parentElement !== phase6Container) {
            phase6Container.appendChild(iotCard);
        }
    }

    /**
     * Hilfsfunktionen
     */
    getSensorTypeText(type) {
        const types = {
            'temperature': 'Temperatur',
            'humidity': 'Luftfeuchtigkeit',
            'ppfd': 'PPFD',
            'co2': 'CO₂'
        };
        return types[type] || type;
    }

    getSensorUnit(type) {
        const units = {
            'temperature': '°C',
            'humidity': '%',
            'ppfd': 'μmol/m²/s',
            'co2': 'ppm'
        };
        return units[type] || '';
    }
}
