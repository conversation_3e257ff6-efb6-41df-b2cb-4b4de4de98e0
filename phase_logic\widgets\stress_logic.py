"""
Stress Management Logic Module
Handles stress management and training techniques with strain-specific guidelines
"""

import json
import os
from typing import Dict, Any, List, Optional

class StressLogic:
    """Stress management and training techniques logic with strain-specific guidelines"""
    
    def __init__(self):
        # Load stress guidelines
        self.guidelines = self.load_stress_guidelines()
        
        # Phase mapping for guidelines
        self.phase_mapping = {
            'vegetative_early': 'vegetative_middle',  # Use middle guidelines for early
            'vegetative_middle': 'vegetative_middle',
            'vegetative_late': 'vegetative_middle',   # Use middle guidelines for late
            'flowering_early': 'flowering_middle',    # Use middle guidelines for early
            'flowering_middle': 'flowering_middle',
            'flowering_late': 'flowering_middle'      # Use middle guidelines for late
        }
        self.lst_techniques = [
            {
                'id': 'bending',
                'name': 'Biegen',
                'description': 'Sanftes Biegen der Hauptstängel',
                'difficulty': 'easy',
                'stress_level': 'low',
                'best_phase': 'vegetative_middle',
                'instructions': [
                    'Hauptstängel vorsichtig zur Seite biegen',
                    '<PERSON><PERSON>aht oder Schnur fixieren',
                    'Nicht zu stark biegen - Risse vermeiden'
                ]
            },
            {
                'id': 'topping',
                'name': 'Topping',
                'description': 'Entfernung der obersten Wachstumsspitze',
                'difficulty': 'medium',
                'stress_level': 'medium',
                'best_phase': 'vegetative_early',
                'instructions': [
                    'Oberste Wachstumsspitze mit sauberen Scheren entfernen',
                    'Mindestens 3-4 Nodien lassen',
                    'Nur bei gesunden Pflanzen durchführen'
                ]
            },
            {
                'id': 'fimming',
                'name': 'Fimming',
                'description': 'Teilweise Entfernung der Wachstumsspitze',
                'difficulty': 'hard',
                'stress_level': 'medium',
                'best_phase': 'vegetative_early',
                'instructions': [
                    'Nur 70-80% der Wachstumsspitze entfernen',
                    '4 neue Triebe entstehen lassen',
                    'Sehr präzise Ausführung nötig'
                ]
            },
            {
                'id': 'super_cropping',
                'name': 'Super Cropping',
                'description': 'Kontrolliertes Brechen der Stängel',
                'difficulty': 'hard',
                'stress_level': 'high',
                'best_phase': 'vegetative_middle',
                'instructions': [
                    'Stängel zwischen Daumen und Zeigefinger drücken',
                    'Nicht komplett durchbrechen',
                    'Nur bei dickeren Stängeln anwenden'
                ]
            }
        ]
        
        self.hst_techniques = [
            {
                'id': 'defoliation',
                'name': 'Defoliation',
                'description': 'Entfernung von Blättern für bessere Lichtverteilung',
                'difficulty': 'medium',
                'stress_level': 'high',
                'best_phase': 'flowering_early',
                'instructions': [
                    'Nur große Schattenblätter entfernen',
                    'Nicht mehr als 20-30% der Blätter',
                    'Nur bei gesunden Pflanzen'
                ]
            },
            {
                'id': 'lollipopping',
                'name': 'Lollipopping',
                'description': 'Entfernung unterer Triebe für bessere Energieverteilung',
                'difficulty': 'easy',
                'stress_level': 'medium',
                'best_phase': 'flowering_early',
                'instructions': [
                    'Untere 30-40% der Pflanze ausdünnen',
                    'Kleine Triebe und Blätter entfernen',
                    'Hauptblütenstände fördern'
                ]
            },
            {
                'id': 'monster_cropping',
                'name': 'Monster Cropping',
                'description': 'Stecklinge von blühenden Pflanzen nehmen',
                'difficulty': 'hard',
                'stress_level': 'very_high',
                'best_phase': 'flowering_early',
                'instructions': [
                    'Stecklinge in Woche 2-3 der Blüte nehmen',
                    'Sehr stressig für die Pflanze',
                    'Nur für erfahrene Grower'
                ]
            }
        ]
        
        self.phase_stress_management = {
            'germination': {
                'stress_level': 'none',
                'recommendations': ['Keine Stress-Techniken in dieser Phase'],
                'allowed_techniques': []
            },
            'vegetative_early': {
                'stress_level': 'low',
                'recommendations': [
                    'LST-Techniken können beginnen',
                    'Topping nach 3-4 Nodien',
                    'Sanfte Techniken bevorzugen'
                ],
                'allowed_techniques': ['bending', 'topping', 'fimming']
            },
            'vegetative_middle': {
                'stress_level': 'medium',
                'recommendations': [
                    'Alle LST-Techniken möglich',
                    'Super Cropping kann beginnen',
                    'Pflanzen beobachten und nicht überfordern'
                ],
                'allowed_techniques': ['bending', 'topping', 'fimming', 'super_cropping']
            },
            'vegetative_late': {
                'stress_level': 'medium',
                'recommendations': [
                    'LST fortsetzen',
                    'Pflanzen für Blüte vorbereiten',
                    'Keine neuen HST-Techniken mehr'
                ],
                'allowed_techniques': ['bending', 'super_cropping']
            },
            'flowering_early': {
                'stress_level': 'high',
                'recommendations': [
                    'Defoliation und Lollipopping möglich',
                    'LST reduzieren',
                    'Pflanzen nicht überfordern'
                ],
                'allowed_techniques': ['defoliation', 'lollipopping', 'bending']
            },
            'flowering_middle': {
                'stress_level': 'high',
                'recommendations': [
                    'Nur noch sanfte Techniken',
                    'Defoliation nur bei Bedarf',
                    'Pflanzen zur Ruhe kommen lassen'
                ],
                'allowed_techniques': ['defoliation']
            },
            'flowering_late': {
                'stress_level': 'none',
                'recommendations': [
                    'Keine Stress-Techniken mehr',
                    'Pflanzen zur Reifung bringen',
                    'Nur noch Beobachtung'
                ],
                'allowed_techniques': []
            },
            'flush': {
                'stress_level': 'none',
                'recommendations': [
                    'Keine Stress-Techniken',
                    'Pflanzen zur finalen Reifung bringen'
                ],
                'allowed_techniques': []
            }
        }
    
    def load_stress_guidelines(self) -> Dict[str, Any]:
        """Load stress guidelines from JSON file"""
        try:
            guidelines_path = os.path.join('static', 'data', 'stress-guidelines.json')
            if os.path.exists(guidelines_path):
                with open(guidelines_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"Warning: Stress guidelines file not found at {guidelines_path}")
                return {}
        except Exception as e:
            print(f"Error loading stress guidelines: {e}")
            return {}
    
    def get_strain_type_key(self, strain_type: str) -> str:
        """Convert strain type to guidelines key"""
        if 'auto' in strain_type.lower():
            return 'autoflower'
        return 'photoperiod'
    
    def get_phase_guidelines(self, phase: str, strain_type: str = 'photoperiodic') -> Optional[Dict[str, Any]]:
        """Get strain-specific guidelines for a phase"""
        if not self.guidelines or 'stressManagementGuidelines' not in self.guidelines:
            return None
        
        guidelines_phase = self.phase_mapping.get(phase, phase)
        strain_key = self.get_strain_type_key(strain_type)
        
        for phase_data in self.guidelines['stressManagementGuidelines']['phases']:
            if phase_data['phase'] == guidelines_phase:
                return {
                    'phase': phase_data,
                    'strain_type': strain_type,
                    'strain_key': strain_key,
                    'toleranz': phase_data['toleranz'].get(strain_key, 'mittel'),
                    'erlaubte_eingriffe': phase_data.get('erlaubteEingriffe', []),
                    'kritische_grenzwerte': phase_data.get('kritischeFaktoren', []),
                    'typische_stressarten': phase_data.get('warnzeichen', [])
                }
        
        return None
    
    def analyze_stress_conditions(self, phase: str, strain_type: str, sensor_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze current conditions for stress indicators"""
        guidelines = self.get_phase_guidelines(phase, strain_type)
        if not guidelines:
            return {'error': 'Keine Guidelines für diese Phase verfügbar'}
        
        analysis = {
            'guidelines_used': True,
            'phase': phase,
            'strain_type': strain_type,
            'guidelines': guidelines,
            'stress_indicators': [],
            'warnings': [],
            'recommendations': []
        }
        
        if sensor_data:
            # Check critical limits
            limits = guidelines['kritische_grenzwerte']
            
            if 'ppfd' in sensor_data and 'ppfd' in limits:
                if sensor_data['ppfd'] > limits['ppfd']['max']:
                    analysis['stress_indicators'].append({
                        'type': 'Lichtstress',
                        'severity': 'high',
                        'message': f"PPFD zu hoch: {sensor_data['ppfd']} > {limits['ppfd']['max']}"
                    })
            
            if 'ec' in sensor_data and 'ec' in limits:
                if sensor_data['ec'] > limits['ec']['max']:
                    analysis['stress_indicators'].append({
                        'type': 'EC-Stress',
                        'severity': 'medium',
                        'message': f"EC zu hoch: {sensor_data['ec']} > {limits['ec']['max']}"
                    })
            
            if 'vpd' in sensor_data and 'vpd' in limits:
                vpd = sensor_data['vpd']
                if vpd < limits['vpd']['min'] or vpd > limits['vpd']['max']:
                    analysis['stress_indicators'].append({
                        'type': 'VPD-Stress',
                        'severity': 'medium',
                        'message': f"VPD außerhalb Bereich: {vpd} (sollte {limits['vpd']['min']}-{limits['vpd']['max']} sein)"
                    })
            
            if 'temperature' in sensor_data and 'temp' in limits:
                if sensor_data['temperature'] > limits['temp']['max']:
                    analysis['stress_indicators'].append({
                        'type': 'Hitzestress',
                        'severity': 'high',
                        'message': f"Temperatur zu hoch: {sensor_data['temperature']}°C > {limits['temp']['max']}°C"
                    })
            
            if 'humidity' in sensor_data and 'rh' in limits:
                humidity = sensor_data['humidity']
                if 'min' in limits['rh'] and humidity < limits['rh']['min']:
                    analysis['stress_indicators'].append({
                        'type': 'Feuchtigkeitsstress',
                        'severity': 'medium',
                        'message': f"Luftfeuchte zu niedrig: {humidity}% < {limits['rh']['min']}%"
                    })
                elif 'max' in limits['rh'] and humidity > limits['rh']['max']:
                    analysis['stress_indicators'].append({
                        'type': 'Schimmelgefahr',
                        'severity': 'high',
                        'message': f"Luftfeuchte zu hoch: {humidity}% > {limits['rh']['max']}%"
                    })
        
        # Add strain-specific warnings
        toleranz = guidelines['toleranz']
        if toleranz == 'niedrig':
            analysis['warnings'].append(f"Vorsicht: {strain_type} hat niedrige Stresstoleranz in dieser Phase")
        elif toleranz == 'hoch':
            analysis['recommendations'].append(f"Gut: {strain_type} hat hohe Stresstoleranz in dieser Phase")
        
        return analysis
    
    def get_stress_guidelines_summary(self, phase: str, strain_type: str = 'photoperiodic') -> Dict[str, Any]:
        """Get a summary of stress guidelines for display"""
        guidelines = self.get_phase_guidelines(phase, strain_type)
        if not guidelines:
            return {'error': 'Keine Guidelines verfügbar'}
        
        return {
            'guidelines_used': True,
            'phase': phase,
            'strain_type': strain_type,
            'description': guidelines['phase']['description'],
            'toleranz': guidelines['toleranz'],
            'erlaubte_eingriffe': guidelines['erlaubte_eingriffe'],
            'kritische_grenzwerte': guidelines['kritische_grenzwerte'],
            'typische_stressarten': guidelines['typische_stressarten'],
            'faustregeln': self.guidelines.get('stressManagementGuidelines', {}).get('faustregeln', [])
        }
    
    def calculate_stress_management(self, phase: str, strain_type: str = 'photoperiodic', experience_level: str = 'intermediate') -> Dict[str, Any]:
        """Calculate stress management recommendations for a phase"""
        
        if phase not in self.phase_stress_management:
            return {
                'error': 'Phase nicht unterstützt',
                'phase': phase
            }
        
        phase_data = self.phase_stress_management[phase]
        
        # Adjust for strain type
        adjusted_recommendations = self.adjust_for_strain_type(phase_data['recommendations'], strain_type)
        
        # Adjust for experience level
        allowed_techniques = self.adjust_for_experience_level(phase_data['allowed_techniques'], experience_level)
        
        # Get specific techniques
        lst_techniques = [tech for tech in self.lst_techniques if tech['id'] in allowed_techniques]
        hst_techniques = [tech for tech in self.hst_techniques if tech['id'] in allowed_techniques]
        
        # Frontend-kompatible Struktur
        return {
            'phase': phase,
            'strain_type': strain_type,
            'experience_level': experience_level,
            'stress_level': phase_data['stress_level'],
            'recommendations': adjusted_recommendations,
            'allowed_techniques': allowed_techniques,
            'lst_techniques': lst_techniques,
            'hst_techniques': hst_techniques,
            'warnings': self.get_stress_warnings(phase, strain_type, experience_level),
            'available_techniques': {
                'lst': lst_techniques,
                'hst': hst_techniques
            },
            'phase_info': {
                'name': self.get_friendly_phase_name(phase),
                'stress_level': phase_data['stress_level'],
                'description': self.get_phase_stress_description(phase)
            }
        }
    
    def adjust_for_strain_type(self, recommendations: List[str], strain_type: str) -> List[str]:
        """Adjust recommendations for different strain types"""
        
        adjusted = recommendations.copy()
        
        if strain_type == 'autoflowering':
            adjusted.append('Autoflowering-Sorten sind stressempfindlicher')
            adjusted.append('Techniken vorsichtiger anwenden')
            adjusted.append('Kürzere Erholungszeiten einplanen')
        
        return adjusted
    
    def adjust_for_experience_level(self, allowed_techniques: List[str], experience_level: str) -> List[str]:
        """Adjust allowed techniques based on experience level"""
        
        if experience_level == 'beginner':
            # Only allow easy techniques
            return [tech for tech in allowed_techniques if tech in ['bending', 'topping']]
        elif experience_level == 'intermediate':
            # Allow medium difficulty techniques
            return [tech for tech in allowed_techniques if tech in ['bending', 'topping', 'fimming', 'defoliation', 'lollipopping']]
        else:  # advanced
            # Allow all techniques
            return allowed_techniques
    
    def get_lst_techniques(self) -> List[Dict[str, Any]]:
        """Get all LST techniques"""
        return self.lst_techniques
    
    def get_hst_techniques(self) -> List[Dict[str, Any]]:
        """Get all HST techniques"""
        return self.hst_techniques
    
    def get_training_timeline(self, phase: str, strain_type: str = 'photoperiodic') -> List[Dict[str, Any]]:
        """Get training timeline for a phase"""
        
        timeline = []
        
        if phase == 'vegetative_early':
            timeline.extend([
                {
                    'week': 1,
                    'techniques': ['bending'],
                    'description': 'Sanftes Biegen der Hauptstängel beginnen'
                },
                {
                    'week': 2,
                    'techniques': ['topping'],
                    'description': 'Topping nach 3-4 Nodien'
                },
                {
                    'week': 3,
                    'techniques': ['bending', 'fimming'],
                    'description': 'LST fortsetzen, Fimming möglich'
                }
            ])
        elif phase == 'vegetative_middle':
            timeline.extend([
                {
                    'week': 1,
                    'techniques': ['super_cropping'],
                    'description': 'Super Cropping für dickere Stängel'
                },
                {
                    'week': 2,
                    'techniques': ['bending'],
                    'description': 'LST fortsetzen'
                },
                {
                    'week': 3,
                    'techniques': ['bending'],
                    'description': 'Pflanzen für Blüte vorbereiten'
                }
            ])
        elif phase == 'flowering_early':
            timeline.extend([
                {
                    'week': 1,
                    'techniques': ['defoliation', 'lollipopping'],
                    'description': 'Defoliation und Lollipopping'
                },
                {
                    'week': 2,
                    'techniques': ['bending'],
                    'description': 'Sanfte LST fortsetzen'
                },
                {
                    'week': 3,
                    'techniques': [],
                    'description': 'Stress reduzieren'
                }
            ])
        
        # Adjust for strain type
        if strain_type == 'autoflowering':
            for item in timeline:
                item['description'] += ' (Vorsichtig bei Autoflowering)'
        
        return timeline
    
    def get_stress_warnings(self, phase: str, strain_type: str, experience_level: str) -> List[str]:
        """Get warnings for stress management"""
        
        warnings = []
        
        if strain_type == 'autoflowering':
            warnings.append('Autoflowering-Sorten sind stressempfindlicher')
            warnings.append('Techniken vorsichtiger anwenden')
            warnings.append('Kürzere Erholungszeiten einplanen')
        
        if experience_level == 'beginner':
            warnings.append('Nur einfache Techniken verwenden')
            warnings.append('Pflanzen genau beobachten')
            warnings.append('Bei Problemen sofort stoppen')
        
        if phase in ['flowering_middle', 'flowering_late', 'flush']:
            warnings.append('Keine neuen Stress-Techniken mehr')
            warnings.append('Pflanzen zur Ruhe kommen lassen')
        
        return warnings
    
    def get_personalized_recommendations(self, phase: str, strain_type: str, experience_level: str, plant_size: str = 'medium') -> Dict[str, Any]:
        """Get personalized stress management recommendations"""
        
        # Base recommendations
        base_management = self.calculate_stress_management(phase, strain_type, experience_level)
        
        # Adjust for plant size
        size_adjustments = self.get_size_adjustments(plant_size)
        
        # Combine recommendations
        personalized = {
            'phase': phase,
            'strain_type': strain_type,
            'experience_level': experience_level,
            'plant_size': plant_size,
            'stress_management': base_management,
            'size_adjustments': size_adjustments,
            'final_recommendations': self.combine_recommendations(base_management, size_adjustments)
        }
        
        return personalized
    
    def get_size_adjustments(self, plant_size: str) -> Dict[str, Any]:
        """Get adjustments based on plant size"""
        
        adjustments = {
            'small': {
                'description': 'Kleine Pflanzen - vorsichtige Techniken',
                'techniques': ['bending'],
                'intensity': 'low',
                'notes': ['Nur sanfte LST-Techniken', 'Pflanzen nicht überfordern']
            },
            'medium': {
                'description': 'Mittlere Pflanzen - Standard-Techniken',
                'techniques': ['bending', 'topping', 'fimming'],
                'intensity': 'medium',
                'notes': ['Alle LST-Techniken möglich', 'HST nur bei Bedarf']
            },
            'large': {
                'description': 'Große Pflanzen - intensive Techniken',
                'techniques': ['bending', 'topping', 'fimming', 'super_cropping', 'defoliation'],
                'intensity': 'high',
                'notes': ['Alle Techniken möglich', 'Defoliation empfohlen']
            }
        }
        
        return adjustments.get(plant_size, adjustments['medium'])
    
    def combine_recommendations(self, stress_management: Dict[str, Any], size_adjustments: Dict[str, Any]) -> Dict[str, Any]:
        """Combine stress management and size adjustments"""
        
        # Intersect allowed techniques
        allowed_techniques = list(set(stress_management['allowed_techniques']) & set(size_adjustments['techniques']))
        
        # Combine recommendations
        combined_recommendations = stress_management['recommendations'] + size_adjustments['notes']
        
        return {
            'allowed_techniques': allowed_techniques,
            'recommendations': combined_recommendations,
            'intensity': size_adjustments['intensity'],
            'description': size_adjustments['description']
        } 

    def get_friendly_phase_name(self, phase: str) -> str:
        """Get user-friendly phase name"""
        phase_names = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        }
        return phase_names.get(phase, phase.replace('_', ' '))
    
    def get_phase_stress_description(self, phase: str) -> str:
        """Get stress management description for phase"""
        descriptions = {
            'germination': 'Keine Stress-Techniken in dieser Phase - Pflanzen sind sehr empfindlich',
            'vegetative_early': 'Sanfte LST-Techniken können beginnen - Pflanzen sind noch jung',
            'vegetative_middle': 'Alle LST-Techniken möglich - Pflanzen sind kräftig genug',
            'vegetative_late': 'LST fortsetzen - Pflanzen für Blüte vorbereiten',
            'flowering_early': 'Defoliation und Lollipopping möglich - Blütenbildung fördern',
            'flowering_middle': 'Nur noch sanfte Techniken - Pflanzen zur Ruhe kommen lassen',
            'flowering_late': 'Keine Stress-Techniken mehr - Reifung fördern',
            'flush': 'Keine Stress-Techniken - Finale Reifung'
        }
        return descriptions.get(phase, 'Standard-Stress-Management') 