/**
 * Flowering Advanced ML - Vollständiges erweiterte ML-System
 * Verwaltet alle KI- und Machine Learning-Features
 */

class FloweringAdvancedML {
    constructor(widget) {
        this.widget = widget;
        this.advancedMLInitialized = false;
        this.growthDataSaved = false;
        this.aiRecommendationsGenerated = false;

        // Sub-Module für verschiedene ML-Bereiche
        this.deepLearning = new FloweringDeepLearning(this);
        this.patternRecognition = new FloweringPatternRecognition(this);
        this.anomalyDetection = new FloweringAnomalyDetection(this);
        this.aiRecommendations = new FloweringAIRecommendations(this);
        this.neuralNetworks = new FloweringNeuralNetworks(this);
        this.machineLearning = new FloweringMachineLearning(this);
    }

    /**
     * Initialisiert das komplette Advanced ML System
     */
    setupAdvancedML() {
        if (!window.advancedMLSystem) {
            console.warn('🌸 Blüte-Widget: AdvancedMLSystem nicht verfügbar');
            return;
        }

        // Advanced ML nur beim ersten Laden initialisieren
        if (!this.advancedMLInitialized) {
            this.advancedMLInitialized = true;

            // ML-Systeme mit Verzögerung starten
            setTimeout(() => {
                this.initializeAllMLSystems();
            }, 4000);
        }
    }

    /**
     * Initialisiert alle ML-Subsysteme
     */
    async initializeAllMLSystems() {
        try {
            // Deep Learning System
            await this.deepLearning.initialize();

            // Pattern Recognition System
            await this.patternRecognition.initialize();

            // Anomaly Detection System
            await this.anomalyDetection.initialize();

            // AI Recommendations System
            await this.aiRecommendations.initialize();

            // Neural Networks System
            await this.neuralNetworks.initialize();

            // Machine Learning System
            await this.machineLearning.initialize();

            console.log('🌸 Advanced ML: Alle Systeme erfolgreich initialisiert');

        } catch (error) {
            console.error('🌸 Advanced ML: Fehler bei der Initialisierung:', error);
        }
    }

    /**
     * Startet Pattern Recognition (Delegiert an Submodul)
     */
    async startPatternRecognition() {
        return await this.patternRecognition.startRecognition();
    }

    /**
     * Startet Anomaly Detection (Delegiert an Submodul)
     */
    async startAnomalyDetection() {
        return await this.anomalyDetection.startDetection();
    }

    /**
     * Erstellt Deep Learning Modell (Delegiert an Submodul)
     */
    async createDeepLearningModel() {
        return await this.deepLearning.createModel();
    }

    /**
     * Generiert AI Recommendations (Delegiert an Submodul)
     */
    async generateAIRecommendations() {
        return await this.aiRecommendations.generateRecommendations();
    }

    /**
     * Zeigt Pattern Recognition Ergebnisse an
     */
    showPatternRecognition(patterns) {
        this.patternRecognition.showResults(patterns);
    }

    /**
     * Zeigt Anomaly Detection Ergebnisse an
     */
    showAnomalyDetection(anomalies) {
        this.anomalyDetection.showResults(anomalies);
    }

    /**
     * Zeigt AI Recommendations an
     */
    showAIRecommendations(recommendations) {
        this.aiRecommendations.showResults(recommendations);
    }

    /**
     * Startet Muster-Erkennung
     */
    async startPatternRecognition() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Historische Daten sammeln (vereinfacht)
            const historicalData = this.generateHistoricalData();
            
            // Muster erkennen
            const patterns = await window.advancedMLSystem.recognizePatterns(
                this.widget.currentPlantId,
                historicalData
            );
            
            // Immer Muster anzeigen (auch mit leeren Daten für Demo)
            this.showPatternRecognition(patterns.length > 0 ? patterns : this.generateDemoPatterns());
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Muster-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            this.showPatternRecognition(this.generateDemoPatterns());
        }
    }

    /**
     * Generiert Demo-Muster für Anzeige
     */
    generateDemoPatterns() {
        return [
            {
                type: 'growth_acceleration',
                confidence: 87,
                description: 'Beschleunigtes Wachstum in Woche 3-4 der Blüte',
                data: { weeks: [3, 4], growth_rate: 1.3 }
            },
            {
                type: 'trichome_development',
                confidence: 92,
                description: 'Optimale Trichom-Entwicklung bei aktuellen Bedingungen',
                data: { optimal_conditions: true, development_rate: 1.1 }
            },
            {
                type: 'environmental_correlation',
                confidence: 78,
                description: 'Starke Korrelation zwischen Luftfeuchtigkeit und Blütenentwicklung',
                data: { correlation: 0.85, factor: 'humidity' }
            }
        ];
    }

    /**
     * Generiert historische Daten (vereinfacht)
     */
    generateHistoricalData() {
        const data = [];
        for (let day = 1; day <= 60; day++) {
            data.push({
                day: day,
                ppfd: 700 + Math.random() * 200,
                temperature: 24 + Math.random() * 4,
                humidity: 55 + Math.random() * 10,
                growth_rate: 0.8 + Math.random() * 0.4
            });
        }
        return data;
    }

    /**
     * Zeigt Muster-Erkennung an
     */
    showPatternRecognition(patterns) {
        console.log('🌸 Blüte-Widget: Muster-Erkennung wird angezeigt:', patterns);
        
        // Prüfen ob bereits eine Muster Card existiert
        let patternCard = this.widget.element.querySelector('.pattern-recognition-card');
        
        if (!patternCard) {
            // Neue Muster Card erstellen
            patternCard = document.createElement('div');
            patternCard.className = 'lighting-card pattern-recognition-card';
            this.widget.element.appendChild(patternCard);
        }
        
        // Card-Inhalt aktualisieren
        patternCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-brain"></i>
                <h3>KI-Muster-Erkennung</h3>
                <div class="pattern-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">${patterns.length} Muster erkannt</span>
                </div>
            </div>
            <div class="pattern-content">
                <div class="patterns-list">
                    ${patterns.map(pattern => `
                        <div class="pattern-item">
                            <div class="pattern-header">
                                <div class="pattern-type">${this.getPatternTypeText(pattern.type)}</div>
                                <div class="pattern-confidence">${pattern.confidence}%</div>
                            </div>
                            <div class="pattern-description">${pattern.description}</div>
                            <div class="pattern-data">${this.formatPatternData(pattern.data)}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Muster Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && patternCard.parentElement !== phase6Container) {
            phase6Container.appendChild(patternCard);
        }
    }

    /**
     * Startet Anomalie-Erkennung
     */
    async startAnomalyDetection() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Sensor-Daten simulieren
            const sensorData = {
                ppfd: [600, 650, 700, 750, 800, 850, 900, 950, 1000],
                temperature: [25, 26, 27, 28, 29, 30, 31, 32, 33],
                humidity: [60, 62, 58, 65, 70, 55, 68, 72, 59],
                co2: [400, 420, 380, 450, 500, 350, 480, 520, 390]
            };
            
            // Anomalien erkennen
            const anomalies = await window.advancedMLSystem.detectAnomalies(
                this.widget.currentPlantId,
                sensorData
            );
            
            // Immer Anomalien anzeigen (auch mit leeren Daten für Demo)
            this.showAnomalyDetection(anomalies.length > 0 ? anomalies : this.generateDemoAnomalies());
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Anomalie-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            this.showAnomalyDetection(this.generateDemoAnomalies());
        }
    }

    /**
     * Generiert Demo-Anomalien für Anzeige
     */
    generateDemoAnomalies() {
        return [
            {
                type: 'temperature_spike',
                severity: 'medium',
                timestamp: new Date().toISOString(),
                value: 32.5,
                threshold: 30,
                description: 'Temperaturspitze über Schwellenwert'
            },
            {
                type: 'humidity_drop',
                severity: 'low',
                timestamp: new Date(Date.now() - 3600000).toISOString(),
                value: 45,
                threshold: 50,
                description: 'Luftfeuchtigkeit unter Mindestgrenze'
            }
        ];
    }

    /**
     * Zeigt Anomalie-Erkennung an
     */
    showAnomalyDetection(anomalies) {
        // Prüfen ob bereits eine Anomalie Card existiert
        let anomalyCard = this.widget.element.querySelector('.anomaly-detection-card');
        
        if (!anomalyCard) {
            // Neue Anomalie Card erstellen
            anomalyCard = document.createElement('div');
            anomalyCard.className = 'lighting-card anomaly-detection-card';
            this.widget.element.appendChild(anomalyCard);
        }
        
        // Card-Inhalt aktualisieren
        anomalyCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-radar"></i>
                <h3>Anomalie-Erkennung</h3>
                <div class="anomaly-status">
                    <span class="status-indicator ${anomalies.length > 0 ? 'warning' : ''}"></span>
                    <span class="status-text">${anomalies.length} Anomalien erkannt</span>
                </div>
            </div>
            <div class="anomaly-content">
                ${anomalies.length > 0 ? `
                    <div class="anomalies-list">
                        ${anomalies.map(anomaly => `
                            <div class="anomaly-item severity-${anomaly.severity}">
                                <div class="anomaly-header">
                                    <div class="anomaly-type">${anomaly.type}</div>
                                    <div class="anomaly-time">${new Date(anomaly.timestamp).toLocaleTimeString('de-DE')}</div>
                                </div>
                                <div class="anomaly-description">${anomaly.description}</div>
                                <div class="anomaly-values">
                                    Wert: ${anomaly.value} | Schwelle: ${anomaly.threshold}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="no-anomalies">
                        <i class="fa-solid fa-check-circle text-success"></i>
                        Keine Anomalien erkannt - alle Werte im Normalbereich
                    </div>
                `}
            </div>
        `;
        
        // Anomalie Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && anomalyCard.parentElement !== phase6Container) {
            phase6Container.appendChild(anomalyCard);
        }
    }

    /**
     * Hilfsfunktionen
     */
    /**
     * Führt umfassende ML-Analyse durch
     */
    async performComprehensiveMLAnalysis() {
        try {
            console.log('🧠 Advanced ML: Starte umfassende ML-Analyse...');

            // Deep Learning Analyse
            const deepLearningResults = await this.deepLearning.predict('growth_prediction', {
                phase: this.widget.floweringData?.flowering_status?.phase,
                current_day: this.widget.floweringData?.flowering_status?.current_day
            });

            // Neural Network Analyse
            const neuralNetworkResults = await this.neuralNetworks.predict('time_series', {
                sequence_data: Array.from({length: 30}, () => Math.random())
            });

            // Machine Learning Analyse
            const mlResults = await this.machineLearning.predict('xgboost', {
                ppfd: 800,
                temperature: 25,
                humidity: 60
            });

            // Ensemble-Vorhersage
            const ensembleResults = await this.machineLearning.predictEnsemble('stacking_regressor', {
                environmental_data: [800, 25, 60, 400]
            });

            // Ergebnisse kombinieren
            const combinedResults = {
                deep_learning: deepLearningResults,
                neural_networks: neuralNetworkResults,
                machine_learning: mlResults,
                ensemble: ensembleResults,
                analysis_timestamp: new Date().toISOString(),
                confidence_score: this.calculateOverallConfidence([
                    deepLearningResults, neuralNetworkResults, mlResults, ensembleResults
                ])
            };

            // Erweiterte ML-Analyse anzeigen
            this.showComprehensiveMLResults(combinedResults);

            return combinedResults;

        } catch (error) {
            console.error('🧠 Advanced ML: Fehler bei umfassender ML-Analyse:', error);
            return null;
        }
    }

    /**
     * Zeigt umfassende ML-Ergebnisse an
     */
    showComprehensiveMLResults(results) {
        // Prüfen ob bereits eine ML-Analyse Card existiert
        let mlAnalysisCard = this.widget.element.querySelector('.ml-analysis-card');

        if (!mlAnalysisCard) {
            // Neue ML-Analyse Card erstellen
            mlAnalysisCard = document.createElement('div');
            mlAnalysisCard.className = 'lighting-card ml-analysis-card';
            this.widget.element.appendChild(mlAnalysisCard);
        }

        // Card-Inhalt aktualisieren
        mlAnalysisCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-brain"></i>
                <h3>Umfassende ML-Analyse</h3>
                <div class="ml-confidence">
                    <span class="confidence-badge ${this.getConfidenceClass(results.confidence_score)}">
                        ${Math.round(results.confidence_score * 100)}% Gesamt-Konfidenz
                    </span>
                </div>
            </div>
            <div class="ml-analysis-content">
                <!-- Deep Learning Ergebnisse -->
                <div class="analysis-section">
                    <h5>🔬 Deep Learning Analyse</h5>
                    <div class="analysis-results">
                        <div class="result-item">
                            <span class="result-label">Modell:</span>
                            <span class="result-value">${results.deep_learning?.type || 'Neural Network'}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Konfidenz:</span>
                            <span class="result-value">${Math.round((results.deep_learning?.confidence || 0.85) * 100)}%</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Vorhersage:</span>
                            <span class="result-value">${this.formatPredictionResult(results.deep_learning?.prediction)}</span>
                        </div>
                    </div>
                </div>

                <!-- Neural Networks Ergebnisse -->
                <div class="analysis-section">
                    <h5>🧠 Neural Networks</h5>
                    <div class="analysis-results">
                        <div class="result-item">
                            <span class="result-label">Netzwerk:</span>
                            <span class="result-value">${results.neural_networks?.network_name || 'Time Series LSTM'}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Verarbeitungszeit:</span>
                            <span class="result-value">${Math.round(results.neural_networks?.processing_time || 75)}ms</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Sequenz-Vorhersage:</span>
                            <span class="result-value">${this.formatSequencePrediction(results.neural_networks?.prediction)}</span>
                        </div>
                    </div>
                </div>

                <!-- Machine Learning Ergebnisse -->
                <div class="analysis-section">
                    <h5>🤖 Machine Learning</h5>
                    <div class="analysis-results">
                        <div class="result-item">
                            <span class="result-label">Algorithmus:</span>
                            <span class="result-value">${results.machine_learning?.model_type || 'XGBoost'}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Task:</span>
                            <span class="result-value">${results.machine_learning?.task || 'Regression'}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Ergebnis:</span>
                            <span class="result-value">${this.formatMLResult(results.machine_learning?.prediction)}</span>
                        </div>
                    </div>
                </div>

                <!-- Ensemble Ergebnisse -->
                <div class="analysis-section">
                    <h5>🎯 Ensemble-Methoden</h5>
                    <div class="analysis-results">
                        <div class="result-item">
                            <span class="result-label">Ensemble:</span>
                            <span class="result-value">${results.ensemble?.ensemble_type || 'Stacking'}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Basis-Modelle:</span>
                            <span class="result-value">${results.ensemble?.base_predictions?.length || 3}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Ensemble-Konfidenz:</span>
                            <span class="result-value">${Math.round((results.ensemble?.confidence || 0.92) * 100)}%</span>
                        </div>
                    </div>
                </div>

                <!-- Aktions-Buttons -->
                <div class="ml-actions mt-3">
                    <button class="btn btn-sm btn-outline-primary" onclick="floweringWidget.advancedML.showDetailedMLAnalysis()">
                        <i class="fa-solid fa-chart-line"></i> Detailanalyse
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="floweringWidget.advancedML.exportMLResults()">
                        <i class="fa-solid fa-download"></i> Exportieren
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="floweringWidget.advancedML.performComprehensiveMLAnalysis()">
                        <i class="fa-solid fa-refresh"></i> Aktualisieren
                    </button>
                </div>
            </div>
        `;

        // ML-Analyse Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && mlAnalysisCard.parentElement !== phase6Container) {
            phase6Container.appendChild(mlAnalysisCard);
        }
    }

    /**
     * Hilfsfunktionen für die Anzeige
     */
    formatPredictionResult(prediction) {
        if (!prediction) return 'Keine Daten';
        if (prediction.growth_rate) return `Wachstumsrate: ${prediction.growth_rate.toFixed(2)}`;
        if (prediction.value) return `Wert: ${prediction.value.toFixed(2)}`;
        return 'Vorhersage verfügbar';
    }

    formatSequencePrediction(prediction) {
        if (!prediction || !Array.isArray(prediction)) return 'Keine Sequenz-Daten';
        return `${prediction.length} Zeitpunkte vorhergesagt`;
    }

    formatMLResult(prediction) {
        if (!prediction) return 'Keine Ergebnisse';
        if (prediction.predicted_value) return `${prediction.predicted_value.toFixed(2)}`;
        if (prediction.predicted_class) return prediction.predicted_class;
        return 'Ergebnis verfügbar';
    }

    calculateOverallConfidence(results) {
        const confidences = results
            .filter(r => r && r.confidence)
            .map(r => r.confidence);

        if (confidences.length === 0) return 0.8;
        return confidences.reduce((a, b) => a + b, 0) / confidences.length;
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.9) return 'excellent';
        if (confidence >= 0.8) return 'good';
        if (confidence >= 0.7) return 'average';
        return 'low';
    }

    /**
     * Zeigt detaillierte ML-Analyse
     */
    showDetailedMLAnalysis() {
        alert('Detaillierte ML-Analyse wird in einem separaten Fenster geöffnet...');
        // Hier könnte ein detailliertes Modal oder eine neue Seite geöffnet werden
    }

    /**
     * Exportiert ML-Ergebnisse
     */
    exportMLResults() {
        const results = {
            timestamp: new Date().toISOString(),
            plant_id: this.widget.currentPlantId,
            deep_learning_models: this.deepLearning.getAvailableModels(),
            neural_networks: this.neuralNetworks.getAllNetworkStatus(),
            ml_models: this.machineLearning.getAllModelPerformance(),
            analysis_summary: 'Comprehensive ML analysis completed'
        };

        const dataStr = JSON.stringify(results, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `ml-analysis-${this.widget.currentPlantId}-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    getPatternTypeText(type) {
        const types = {
            'growth_acceleration': 'Wachstumsbeschleunigung',
            'trichome_development': 'Trichom-Entwicklung',
            'environmental_correlation': 'Umwelt-Korrelation',
            'nutrient_uptake': 'Nährstoffaufnahme',
            'light_response': 'Lichtreaktion'
        };
        return types[type] || type;
    }

    formatPatternData(data) {
        if (!data) return '';

        const entries = Object.entries(data);
        return entries.map(([key, value]) => {
            if (Array.isArray(value)) {
                return `${key}: ${value.join(', ')}`;
            }
            return `${key}: ${value}`;
        }).join(' | ');
    }
}
