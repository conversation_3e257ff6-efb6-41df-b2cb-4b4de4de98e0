/**
 * Flowering Advanced ML - Verwaltet erweiterte ML-Features
 */

class FloweringAdvancedML {
    constructor(widget) {
        this.widget = widget;
        this.advancedMLInitialized = false;
    }

    /**
     * Initialisiert Advanced ML System
     */
    setupAdvancedML() {
        if (!window.advancedMLSystem) {
            console.warn('🌸 Blüte-Widget: AdvancedMLSystem nicht verfügbar');
            return;
        }
        
        // Advanced ML nur beim ersten Laden initialisieren
        if (!this.advancedMLInitialized) {
            this.advancedMLInitialized = true;
            
            // ML-Systeme mit Verzögerung starten
            setTimeout(() => {
                this.createDeepLearningModel();
                this.startPatternRecognition();
                this.startAnomalyDetection();
            }, 4000);
        }
    }

    /**
     * Erstellt Deep Learning Modell
     */
    async createDeepLearningModel() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.widget.floweringData?.flowering_status?.current_day || 0
            };
            
            // Deep Learning Modell erstellen
            const model = await window.advancedMLSystem.createDeepLearningModel(
                this.widget.currentPlantId,
                plantData
            );
            
            console.log('🌸 Blüte-Widget: Deep Learning Modell erstellt:', model);
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Deep Learning Modell:', error);
        }
    }

    /**
     * Startet Muster-Erkennung
     */
    async startPatternRecognition() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Historische Daten sammeln (vereinfacht)
            const historicalData = this.generateHistoricalData();
            
            // Muster erkennen
            const patterns = await window.advancedMLSystem.recognizePatterns(
                this.widget.currentPlantId,
                historicalData
            );
            
            // Immer Muster anzeigen (auch mit leeren Daten für Demo)
            this.showPatternRecognition(patterns.length > 0 ? patterns : this.generateDemoPatterns());
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Muster-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            this.showPatternRecognition(this.generateDemoPatterns());
        }
    }

    /**
     * Generiert Demo-Muster für Anzeige
     */
    generateDemoPatterns() {
        return [
            {
                type: 'growth_acceleration',
                confidence: 87,
                description: 'Beschleunigtes Wachstum in Woche 3-4 der Blüte',
                data: { weeks: [3, 4], growth_rate: 1.3 }
            },
            {
                type: 'trichome_development',
                confidence: 92,
                description: 'Optimale Trichom-Entwicklung bei aktuellen Bedingungen',
                data: { optimal_conditions: true, development_rate: 1.1 }
            },
            {
                type: 'environmental_correlation',
                confidence: 78,
                description: 'Starke Korrelation zwischen Luftfeuchtigkeit und Blütenentwicklung',
                data: { correlation: 0.85, factor: 'humidity' }
            }
        ];
    }

    /**
     * Generiert historische Daten (vereinfacht)
     */
    generateHistoricalData() {
        const data = [];
        for (let day = 1; day <= 60; day++) {
            data.push({
                day: day,
                ppfd: 700 + Math.random() * 200,
                temperature: 24 + Math.random() * 4,
                humidity: 55 + Math.random() * 10,
                growth_rate: 0.8 + Math.random() * 0.4
            });
        }
        return data;
    }

    /**
     * Zeigt Muster-Erkennung an
     */
    showPatternRecognition(patterns) {
        console.log('🌸 Blüte-Widget: Muster-Erkennung wird angezeigt:', patterns);
        
        // Prüfen ob bereits eine Muster Card existiert
        let patternCard = this.widget.element.querySelector('.pattern-recognition-card');
        
        if (!patternCard) {
            // Neue Muster Card erstellen
            patternCard = document.createElement('div');
            patternCard.className = 'lighting-card pattern-recognition-card';
            this.widget.element.appendChild(patternCard);
        }
        
        // Card-Inhalt aktualisieren
        patternCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-brain"></i>
                <h3>KI-Muster-Erkennung</h3>
                <div class="pattern-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">${patterns.length} Muster erkannt</span>
                </div>
            </div>
            <div class="pattern-content">
                <div class="patterns-list">
                    ${patterns.map(pattern => `
                        <div class="pattern-item">
                            <div class="pattern-header">
                                <div class="pattern-type">${this.getPatternTypeText(pattern.type)}</div>
                                <div class="pattern-confidence">${pattern.confidence}%</div>
                            </div>
                            <div class="pattern-description">${pattern.description}</div>
                            <div class="pattern-data">${this.formatPatternData(pattern.data)}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Muster Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && patternCard.parentElement !== phase6Container) {
            phase6Container.appendChild(patternCard);
        }
    }

    /**
     * Startet Anomalie-Erkennung
     */
    async startAnomalyDetection() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Sensor-Daten simulieren
            const sensorData = {
                ppfd: [600, 650, 700, 750, 800, 850, 900, 950, 1000],
                temperature: [25, 26, 27, 28, 29, 30, 31, 32, 33],
                humidity: [60, 62, 58, 65, 70, 55, 68, 72, 59],
                co2: [400, 420, 380, 450, 500, 350, 480, 520, 390]
            };
            
            // Anomalien erkennen
            const anomalies = await window.advancedMLSystem.detectAnomalies(
                this.widget.currentPlantId,
                sensorData
            );
            
            // Immer Anomalien anzeigen (auch mit leeren Daten für Demo)
            this.showAnomalyDetection(anomalies.length > 0 ? anomalies : this.generateDemoAnomalies());
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Anomalie-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            this.showAnomalyDetection(this.generateDemoAnomalies());
        }
    }

    /**
     * Generiert Demo-Anomalien für Anzeige
     */
    generateDemoAnomalies() {
        return [
            {
                type: 'temperature_spike',
                severity: 'medium',
                timestamp: new Date().toISOString(),
                value: 32.5,
                threshold: 30,
                description: 'Temperaturspitze über Schwellenwert'
            },
            {
                type: 'humidity_drop',
                severity: 'low',
                timestamp: new Date(Date.now() - 3600000).toISOString(),
                value: 45,
                threshold: 50,
                description: 'Luftfeuchtigkeit unter Mindestgrenze'
            }
        ];
    }

    /**
     * Zeigt Anomalie-Erkennung an
     */
    showAnomalyDetection(anomalies) {
        // Prüfen ob bereits eine Anomalie Card existiert
        let anomalyCard = this.widget.element.querySelector('.anomaly-detection-card');
        
        if (!anomalyCard) {
            // Neue Anomalie Card erstellen
            anomalyCard = document.createElement('div');
            anomalyCard.className = 'lighting-card anomaly-detection-card';
            this.widget.element.appendChild(anomalyCard);
        }
        
        // Card-Inhalt aktualisieren
        anomalyCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-radar"></i>
                <h3>Anomalie-Erkennung</h3>
                <div class="anomaly-status">
                    <span class="status-indicator ${anomalies.length > 0 ? 'warning' : ''}"></span>
                    <span class="status-text">${anomalies.length} Anomalien erkannt</span>
                </div>
            </div>
            <div class="anomaly-content">
                ${anomalies.length > 0 ? `
                    <div class="anomalies-list">
                        ${anomalies.map(anomaly => `
                            <div class="anomaly-item severity-${anomaly.severity}">
                                <div class="anomaly-header">
                                    <div class="anomaly-type">${anomaly.type}</div>
                                    <div class="anomaly-time">${new Date(anomaly.timestamp).toLocaleTimeString('de-DE')}</div>
                                </div>
                                <div class="anomaly-description">${anomaly.description}</div>
                                <div class="anomaly-values">
                                    Wert: ${anomaly.value} | Schwelle: ${anomaly.threshold}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="no-anomalies">
                        <i class="fa-solid fa-check-circle text-success"></i>
                        Keine Anomalien erkannt - alle Werte im Normalbereich
                    </div>
                `}
            </div>
        `;
        
        // Anomalie Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && anomalyCard.parentElement !== phase6Container) {
            phase6Container.appendChild(anomalyCard);
        }
    }

    /**
     * Hilfsfunktionen
     */
    getPatternTypeText(type) {
        const types = {
            'growth_acceleration': 'Wachstumsbeschleunigung',
            'trichome_development': 'Trichom-Entwicklung',
            'environmental_correlation': 'Umwelt-Korrelation',
            'nutrient_uptake': 'Nährstoffaufnahme',
            'light_response': 'Lichtreaktion'
        };
        return types[type] || type;
    }

    formatPatternData(data) {
        if (!data) return '';
        
        const entries = Object.entries(data);
        return entries.map(([key, value]) => {
            if (Array.isArray(value)) {
                return `${key}: ${value.join(', ')}`;
            }
            return `${key}: ${value}`;
        }).join(' | ');
    }
}
