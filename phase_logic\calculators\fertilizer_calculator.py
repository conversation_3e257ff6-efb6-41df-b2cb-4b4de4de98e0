#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fertilizer Calculator für das Grow-Tagebuch
Berechnet Dünger-Empfehlungen für verschiedene Marken und Phasen
"""

from ..data.fertilizer_brands import FERTILIZER_BRANDS

class FertilizerCalculator:
    """Berechnet Dünger-Empfehlungen für verschiedene Marken und Phasen"""
    
    def __init__(self):
        self.fertilizer_brands = FERTILIZER_BRANDS
    
    def get_fertilizer_recommendations(self, phase, brand='biobizz'):
        """
        Gibt Dünger-Empfehlungen für eine bestimmte Phase und Marke zurück
        
        Args:
            phase (str): Aktuelle Phase (z.B. 'vegetative_early')
            brand (str): Düngermarke ('biobizz', 'canna', 'plagron')
        
        Returns:
            dict: Dünger-Empfehlungen
        """
        if brand not in self.fertilizer_brands:
            return {
                'error': f'Unbekannte Marke: {brand}',
                'available_brands': list(self.fertilizer_brands.keys())
            }
        
        brand_data = self.fertilizer_brands[brand]
        
        if phase not in brand_data['recommendations']:
            return {
                'error': f'Keine Empfehlungen für Phase: {phase}',
                'available_phases': list(brand_data['recommendations'].keys())
            }
        
        return brand_data['recommendations'][phase]
    
    def get_all_brand_recommendations(self, phase):
        """
        Gibt Dünger-Empfehlungen für alle Marken für eine bestimmte Phase zurück
        
        Args:
            phase (str): Aktuelle Phase
        
        Returns:
            dict: Dünger-Empfehlungen für alle Marken
        """
        recommendations = {}
        
        for brand in self.fertilizer_brands:
            recommendations[brand] = self.get_fertilizer_recommendations(phase, brand)
        
        return recommendations
    
    def get_fertilizer_info(self, brand, fertilizer_name):
        """
        Gibt Informationen über einen spezifischen Dünger zurück
        
        Args:
            brand (str): Düngermarke
            fertilizer_name (str): Name des Düngers
        
        Returns:
            dict: Dünger-Informationen
        """
        if brand not in self.fertilizer_brands:
            return {'error': f'Unbekannte Marke: {brand}'}
        
        brand_data = self.fertilizer_brands[brand]
        
        if fertilizer_name not in brand_data['fertilizers']:
            return {
                'error': f'Unbekannter Dünger: {fertilizer_name}',
                'available_fertilizers': list(brand_data['fertilizers'].keys())
            }
        
        return brand_data['fertilizers'][fertilizer_name]
    
    def check_fertilizer_combinations(self, brand, fertilizers):
        """
        Prüft Dünger-Kombinationen auf Konflikte
        
        Args:
            brand (str): Düngermarke
            fertilizers (list): Liste der Dünger-Namen
        
        Returns:
            dict: Kombinationsprüfung-Ergebnis
        """
        if brand not in self.fertilizer_brands:
            return {'error': f'Unbekannte Marke: {brand}'}
        
        brand_data = self.fertilizer_brands[brand]
        conflicts = []
        warnings = []
        
        for fertilizer in fertilizers:
            if fertilizer not in brand_data['fertilizers']:
                warnings.append(f'Unbekannter Dünger: {fertilizer}')
                continue
            
            fertilizer_data = brand_data['fertilizers'][fertilizer]
            
            # Prüfe Kombinationen
            for conflicting_fertilizer in fertilizer_data.get('combinations', []):
                if conflicting_fertilizer in fertilizers:
                    conflicts.append({
                        'fertilizer': fertilizer,
                        'conflicts_with': conflicting_fertilizer,
                        'note': fertilizer_data.get('combinations_note', '')
                    })
        
        return {
            'conflicts': conflicts,
            'warnings': warnings,
            'is_safe': len(conflicts) == 0,
            'recommendation': 'Kombination ist sicher' if len(conflicts) == 0 else 'Konflikte gefunden - siehe Details'
        }
    
    def get_ec_guidelines(self, phase, brand='biobizz'):
        """
        Gibt EC-Wert-Richtlinien für eine bestimmte Phase zurück
        
        Args:
            phase (str): Aktuelle Phase
            brand (str): Düngermarke
        
        Returns:
            dict: EC-Wert-Richtlinien
        """
        recommendation = self.get_fertilizer_recommendations(phase, brand)
        
        if 'error' in recommendation:
            return recommendation
        
        return {
            'phase': phase,
            'brand': brand,
            'ec_target': recommendation.get('ec_target', 'Nicht definiert'),
            'ec_range': recommendation.get('ec_range', 'Nicht definiert'),
            'notes': recommendation.get('notes', ''),
            'warnings': recommendation.get('warnings', [])
        }
    
    def get_all_ec_guidelines(self, phase):
        """
        Gibt EC-Wert-Richtlinien für alle Marken zurück
        
        Args:
            phase (str): Aktuelle Phase
        
        Returns:
            dict: EC-Wert-Richtlinien für alle Marken
        """
        guidelines = {}
        
        for brand in self.fertilizer_brands:
            guidelines[brand] = self.get_ec_guidelines(phase, brand)
        
        return guidelines
    
    def get_available_brands(self):
        """
        Gibt alle verfügbaren Düngermarken zurück
        
        Returns:
            list: Liste der verfügbaren Marken
        """
        return list(self.fertilizer_brands.keys())
    
    def get_available_phases(self, brand='biobizz'):
        """
        Gibt alle verfügbaren Phasen für eine Marke zurück
        
        Args:
            brand (str): Düngermarke
        
        Returns:
            list: Liste der verfügbaren Phasen
        """
        if brand not in self.fertilizer_brands:
            return []
        
        return list(self.fertilizer_brands[brand]['recommendations'].keys())
    
    def get_fertilizer_summary(self, brand='biobizz'):
        """
        Gibt eine Zusammenfassung aller Dünger einer Marke zurück
        
        Args:
            brand (str): Düngermarke
        
        Returns:
            dict: Dünger-Zusammenfassung
        """
        if brand not in self.fertilizer_brands:
            return {'error': f'Unbekannte Marke: {brand}'}
        
        brand_data = self.fertilizer_brands[brand]
        fertilizers = brand_data['fertilizers']
        
        summary = {
            'brand': brand,
            'total_fertilizers': len(fertilizers),
            'fertilizers': {}
        }
        
        for name, data in fertilizers.items():
            summary['fertilizers'][name] = {
                'name': data['name'],
                'description': data['description'],
                'npk': data['npk'],
                'usage': data['usage'],
                'has_combinations': len(data.get('combinations', [])) > 0
            }
        
        return summary 