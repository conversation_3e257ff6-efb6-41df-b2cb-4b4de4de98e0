/**
 * Flowering Timeline Manager - Verwaltet Timeline und Marker
 */

class FloweringTimelineManager {
    constructor(widget) {
        this.widget = widget;
        this.markers = [];
    }

    /**
     * Lädt Marker-Daten
     */
    async loadMarkers() {
        try {
            const response = await fetch(`/flowering/markers/${this.widget.currentPlantId}`);
            const data = await response.json();
            this.markers = data.markers || [];
            this.updateMarkersList();
            this.widget.uiRenderer.updateTimeline();
        } catch (error) {
            console.error('🌺 TimelineManager: Fe<PERSON> beim <PERSON>:', error);
        }
    }

    /**
     * Aktualisiert die Marker-Liste
     */
    updateMarkersList() {
        const markersList = document.getElementById('markersList');
        if (!markersList) return;

        if (this.markers.length === 0) {
            markersList.innerHTML = '<div class="no-markers">Ke<PERSON> Marker vorhanden</div>';
            return;
        }

        markersList.innerHTML = this.markers.map(marker => `
            <div class="marker-item" data-category="${marker.category}" data-importance="${marker.importance}">
                <div class="marker-header">
                    <div class="marker-title">${marker.title}</div>
                    <div class="marker-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="floweringWidget.timelineManager.editMarker('${marker.id}')">
                            <i class="fa-solid fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="floweringWidget.timelineManager.deleteMarker('${marker.id}')">
                            <i class="fa-solid fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="marker-meta">
                    <span class="marker-day">Tag ${marker.bloom_day}</span>
                    <span class="marker-date">${marker.date}</span>
                    <span class="marker-category badge bg-secondary">${marker.category}</span>
                    <span class="marker-importance badge bg-${this.getImportanceBadgeClass(marker.importance)}">${marker.importance}</span>
                </div>
                <div class="marker-description">${marker.description || ''}</div>
            </div>
        `).join('');

        // Event-Listener für die neuen Buttons hinzufügen
        this.setupMarkerActionListeners();
    }

    /**
     * Richtet Event-Listener für Marker-Aktionen ein
     */
    setupMarkerActionListeners() {
        // Event-Delegation für dynamisch erstellte Buttons
        const markersList = document.getElementById('markersList');
        if (!markersList) return;

        markersList.addEventListener('click', (e) => {
            if (e.target.closest('.btn-outline-primary')) {
                const markerId = e.target.closest('.marker-item').dataset.markerId;
                this.editMarker(markerId);
            } else if (e.target.closest('.btn-outline-danger')) {
                const markerId = e.target.closest('.marker-item').dataset.markerId;
                this.deleteMarker(markerId);
            }
        });
    }

    /**
     * Filtert Marker basierend auf Kategorie und Wichtigkeit
     */
    filterMarkers() {
        const categoryFilter = this.widget.getElementById('categoryFilter');
        const importanceFilter = this.widget.getElementById('importanceFilter');
        const markersList = document.getElementById('markersList');
        
        if (!markersList) return;
        
        const selectedCategory = categoryFilter ? categoryFilter.value : '';
        const selectedImportance = importanceFilter ? importanceFilter.value : '';
        
        // Alle Marker-Elemente durchgehen
        const markerItems = markersList.querySelectorAll('.marker-item');
        
        markerItems.forEach(markerItem => {
            const category = markerItem.dataset.category;
            const importance = markerItem.dataset.importance;
            let showMarker = true;
            
            // Kategorie-Filter
            if (selectedCategory && category !== selectedCategory) {
                showMarker = false;
            }
            
            // Wichtigkeit-Filter
            if (selectedImportance && importance !== selectedImportance) {
                showMarker = false;
            }
            
            // Marker anzeigen/verstecken
            if (showMarker) {
                markerItem.style.display = 'block';
                markerItem.classList.remove('filtered-out');
            } else {
                markerItem.style.display = 'none';
                markerItem.classList.add('filtered-out');
            }
        });
        
        // Anzahl der sichtbaren Marker anzeigen
        const visibleMarkers = markersList.querySelectorAll('.marker-item:not(.filtered-out)').length;
        const totalMarkers = markerItems.length;
        
        // Optional: "Keine Marker" Nachricht anzeigen
        const noMarkersMessage = markersList.querySelector('.no-markers-message');
        if (visibleMarkers === 0 && totalMarkers > 0) {
            if (!noMarkersMessage) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'no-markers-message';
                messageDiv.textContent = 'Keine Marker entsprechen den Filterkriterien';
                markersList.appendChild(messageDiv);
            }
        } else if (noMarkersMessage) {
            noMarkersMessage.remove();
        }
    }

    /**
     * Öffnet das Marker-Modal
     */
    openMarkerModal(markerId = null) {
        const modal = document.getElementById('markerModal');
        const title = document.getElementById('markerModalTitle');
        
        if (modal) {
            if (markerId) {
                if (title) title.textContent = 'Marker bearbeiten';
                this.fillMarkerForm(markerId);
            } else {
                if (title) title.textContent = 'Neuer Marker';
                this.clearMarkerForm();
            }
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }

    /**
     * Füllt das Marker-Formular mit Daten
     */
    fillMarkerForm(markerId) {
        const marker = this.markers.find(m => m.id === markerId);
        if (!marker) return;

        const form = document.getElementById('markerForm');
        if (!form) return;

        // Formular-Felder füllen
        const titleField = form.querySelector('[name="title"]');
        if (titleField) titleField.value = marker.title;

        const categoryField = form.querySelector('[name="category"]');
        if (categoryField) categoryField.value = marker.category;

        const importanceField = form.querySelector('[name="importance"]');
        if (importanceField) importanceField.value = marker.importance;

        const descriptionField = form.querySelector('[name="description"]');
        if (descriptionField) descriptionField.value = marker.description || '';

        const bloomDayField = form.querySelector('[name="bloom_day"]');
        if (bloomDayField) bloomDayField.value = marker.bloom_day;
    }

    /**
     * Leert das Marker-Formular
     */
    clearMarkerForm() {
        const form = document.getElementById('markerForm');
        if (form) {
            form.reset();
            
            // Aktuellen Blüte-Tag als Standard setzen
            const bloomDayField = form.querySelector('[name="bloom_day"]');
            if (bloomDayField && this.widget.floweringData) {
                bloomDayField.value = this.widget.floweringData.flowering_status.current_day || 0;
            }
        }
    }

    /**
     * Speichert einen Marker
     */
    async saveMarker() {
        const markerForm = this.widget.getElementById('markerForm');
        if (!markerForm) return;

        const formData = new FormData(markerForm);
        const markerData = {
            plant_id: this.widget.currentPlantId,
            title: formData.get('title'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            description: formData.get('description'),
            bloom_day: parseInt(formData.get('bloom_day')),
            date: new Date().toISOString().split('T')[0]
        };

        try {
            const response = await fetch('/flowering/markers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(markerData)
            });

            if (response.ok) {
                this.widget.uiRenderer.showSuccess('Marker erfolgreich gespeichert');
                await this.loadMarkers();
                
                // Modal schließen
                const modal = bootstrap.Modal.getInstance(document.getElementById('markerModal'));
                if (modal) modal.hide();
            } else {
                throw new Error('Fehler beim Speichern des Markers');
            }
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Speichern:', error);
            this.widget.uiRenderer.showError('Fehler beim Speichern des Markers');
        }
    }

    /**
     * Bearbeitet einen Marker
     */
    async editMarker(markerId) {
        this.openMarkerModal(markerId);
    }

    /**
     * Löscht einen Marker
     */
    async deleteMarker(markerId) {
        if (!confirm('Marker wirklich löschen?')) return;

        try {
            const response = await fetch(`/flowering/markers/${markerId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.widget.uiRenderer.showSuccess('Marker erfolgreich gelöscht');
                await this.loadMarkers();
            } else {
                throw new Error('Fehler beim Löschen des Markers');
            }
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Löschen:', error);
            this.widget.uiRenderer.showError('Fehler beim Löschen des Markers');
        }
    }

    /**
     * Speichert ein Timeline-Event
     */
    async saveTimelineEvent() {
        const eventForm = this.widget.getElementById('eventForm');
        if (!eventForm) return;

        const formData = new FormData(eventForm);
        const eventData = {
            plant_id: this.widget.currentPlantId,
            event_type: formData.get('event_type'),
            description: formData.get('description'),
            bloom_day: parseInt(formData.get('bloom_day')),
            date: new Date().toISOString().split('T')[0]
        };

        try {
            const response = await fetch('/flowering/timeline-events', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(eventData)
            });

            if (response.ok) {
                this.widget.uiRenderer.showSuccess('Timeline-Event erfolgreich gespeichert');
                await this.loadMarkers(); // Marker neu laden
            } else {
                throw new Error('Fehler beim Speichern des Timeline-Events');
            }
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Speichern des Timeline-Events:', error);
            this.widget.uiRenderer.showError('Fehler beim Speichern des Timeline-Events');
        }
    }

    /**
     * Hilfsfunktionen
     */
    getImportanceBadgeClass(importance) {
        const classes = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info'
        };
        return classes[importance] || 'secondary';
    }
}
