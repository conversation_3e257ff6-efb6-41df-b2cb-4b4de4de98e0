# Sortentyp-spezifische VPD-Richtlinien

## Übersicht

Das Grow-Tagebuch unterstützt jetzt sortentyp-spezifische VPD-Richtlinien (Vapor Pressure Deficit), die zwischen Autoflower- und Photoperiod-Sorten unterscheiden. Diese Funktion basiert auf wissenschaftlich fundierten Guidelines und bietet präzise Empfehlungen für optimale Transpiration und Wachstum.

## Implementierung

### 1. Backend-Logic (`phase_logic/widgets/vpd_logic.py`)

#### Erweiterte Methoden:

- **`load_guidelines_from_json(phase)`**: Lädt die neuen JSON-Guidelines für eine spezifische Phase
- **`calculate_vpd_with_guidelines()`**: Berechnet VPD mit strain-spezifischen Zielwerten
- **`get_vpd_status_with_guidelines()`**: Strain-spezifische Status-Bewertung mit Grenzwerten
- **`calculate_optimization_recommendations()`**: Detaillierte Optimierungsempfehlungen

#### Strain-Type-Erkennung:

```python
# Strain-Type aus Datenbank laden
strain_key = 'photoperiod' if strain_type == 'photoperiodic' else 'autoflower'

# Strain-spezifische VPD-Targets aus JSON
strain_guidelines = guidelines.get(strain_key, {})
vpd_target = strain_guidelines.get('vpdTarget', {})
temp_target = strain_guidelines.get('tempTargetC', {})
humidity_target = strain_guidelines.get('humidityTargetPercent', {})
```

### 2. API-Routes (`routes/widgets/vpd_routes.py`)

#### Automatische Strain-Type-Erkennung:

```python
# Strain-Type aus Datenbank laden falls nicht übergeben
if strain_type == 'photoperiodic' and plant_id:
    cursor.execute("SELECT strain_type, strain FROM plants WHERE id = ?", (plant_id,))
    row = cursor.fetchone()
    if row:
        # Prüfe auf Autoflower-Keywords
        auto_keywords = ['auto', 'autoflower', 'automatic', 'ruderalis']
        if any(keyword in strain_text.lower() for keyword in auto_keywords):
            strain_type = 'autoflowering'
```

### 3. Frontend-Widget (`static/scripts/widgets/vpd-widget.js`)

#### Strain-spezifische Anzeige:

```javascript
// Strain-Type aus DOM oder Widget holen
let strainType = 'photoperiodic';
const strainTypeElement = document.querySelector('[data-plant-strain-type]');
if (strainTypeElement) {
    strainType = strainTypeElement.getAttribute('data-plant-strain-type');
}

const isAutoflower = strainType.toLowerCase().includes('auto');
const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
```

#### Visuelle Kennzeichnung:

- **Badge**: "Sortentyp-spezifisch" für strain-spezifische Empfehlungen
- **Empfehlungstyp**: Farbkodierte Anzeige (grün für strain-spezifisch, gelb für allgemein)
- **Strain-spezifische Hinweise**: Erklärende Texte für Autoflower vs. Photoperiod

## JSON-Struktur

### VPD-Richtlinien (`static/data/vpd-guidelines.json`)

```json
{
  "vpdGuidelines": {
    "phases": [
      {
        "phase": "vegetative_middle",
        "description": "Mittlere Vegi – schnellste Phase für Strukturwachstum.",
        "leafTempOffsetC": 1.5,
        "autoflower": {
          "vpdTarget": { "min": 0.8, "max": 1.0 },
          "tempTargetC": { "min": 24, "max": 27 },
          "humidityTargetPercent": { "min": 55, "max": 65 },
          "notes": [
            "Pflanzen entwickeln viele neue Blattflächen – Transpiration steigt.",
            "Blattbewegung & Umluft wichtig zur Vermeidung von Feuchtestau."
          ]
        },
        "photoperiod": {
          "vpdTarget": { "min": 0.9, "max": 1.1 },
          "tempTargetC": { "min": 25, "max": 28 },
          "humidityTargetPercent": { "min": 50, "max": 60 },
          "notes": [
            "Ideal zur Wurzelstimulierung und internodialem Wachstum.",
            "Nicht zu trocken, sonst stockt die Entwicklung."
          ]
        }
      }
    ],
    "grenzwerte": {
      "lowVpd": {
        "threshold": 0.4,
        "risks": [
          "Schimmelgefahr durch stehende Feuchte",
          "Nährstoffaufnahme reduziert",
          "Stomata schließen sich → Wachstumsstopp"
        ]
      },
      "highVpd": {
        "threshold": 1.6,
        "risks": [
          "Blattverbrennungen, insbesondere an Rändern",
          "Erhöhter Wasserverlust → Stress",
          "Verzögerte Budreife oder Notblüte"
        ]
      }
    }
  }
}
```

## Strain-spezifische Unterschiede

### Autoflower-Sorten

- **VPD-Bereiche**: Niedrigere VPD-Werte für empfindlichere Pflanzen
- **Temperatur**: Konservativere Temperaturbereiche
- **Luftfeuchte**: Höhere Luftfeuchte für bessere Transpiration
- **Empfindlichkeit**: Höhere Empfindlichkeit gegen VPD-Schwankungen

### Photoperiodische Sorten

- **VPD-Bereiche**: Höhere VPD-Werte für robustere Pflanzen
- **Temperatur**: Breitere Temperaturbereiche möglich
- **Luftfeuchte**: Niedrigere Luftfeuchte verträglich
- **Anpassung**: Bessere Anpassung an VPD-Schwankungen

## Frontend-Features

### 1. Strain-spezifische Anzeige

- **Badge-System**: "Sortentyp-spezifisch" Kennzeichnung
- **Empfehlungstyp**: Farbkodierte Anzeige (grün/gelb)
- **Strain-spezifische Hinweise**: Erklärende Texte
- **Zielwerte**: Strain-spezifische VPD-, Temperatur- und Luftfeuchte-Ziele

### 2. Optimierungsempfehlungen

- **VPD-Anpassung**: Konkrete Handlungsempfehlungen
- **Temperatur-Anpassung**: Gradgenaue Empfehlungen
- **Luftfeuchte-Anpassung**: Prozentuale Anpassungen
- **Blatttemperatur-Offset**: Berücksichtigung der Blatttemperatur

### 3. Automatische Erkennung

- **DOM-Parsing**: Automatische Erkennung aus Pflanzendaten
- **Keyword-Matching**: Erkennung von Autoflower-Keywords
- **Fallback-System**: Allgemeine Empfehlungen als Fallback

## Technische Details

### Datenfluss

1. **Frontend**: Widget lädt Pflanzendaten und Strain-Type
2. **API-Call**: Plant ID wird an Backend gesendet
3. **Backend**: Strain-Type wird aus Datenbank geladen/validiert
4. **JSON-Loading**: Strain-spezifische VPD-Targets werden geladen
5. **Berechnung**: VPD wird mit strain-spezifischen Zielwerten verglichen
6. **Response**: Strain-spezifische Daten werden an Frontend gesendet
7. **Rendering**: Frontend zeigt strain-spezifische Anzeige

### VPD-Berechnung

```python
# Magnus-Formel für Sättigungsdampfdruck
def calculate_saturation_vapor_pressure(self, temperature: float) -> float:
    if temperature >= 0:
        return 6.112 * math.exp((17.67 * temperature) / (temperature + 243.5))
    else:
        return 6.112 * math.exp((22.46 * temperature) / (temperature + 272.62))

# VPD-Berechnung
def calculate_vpd(self, temperature: float, humidity: float) -> float:
    humidity_decimal = humidity / 100.0
    svp_hpa = self.calculate_saturation_vapor_pressure(temperature)
    avp_hpa = svp_hpa * humidity_decimal
    vpd_hpa = svp_hpa - avp_hpa
    vpd_kpa = vpd_hpa / 10.0
    return round(vpd_kpa, 2)
```

### Fallback-Mechanismus

- **Strain-Type unbekannt**: Verwendet allgemeine Empfehlungen
- **JSON nicht verfügbar**: Fallback auf ursprüngliche Berechnungslogik
- **Datenbank-Fehler**: Verwendet Standard-Werte

## Nutzer-Nutzen

### Für Autoflower-Grower

- **Präzise VPD-Empfehlungen**: Angepasst an empfindlichere Pflanzen
- **Schimmelprävention**: Niedrigere VPD-Werte reduzieren Risiko
- **Optimale Transpiration**: Wissenschaftlich fundierte Zielwerte

### Für Photoperiod-Grower

- **Flexibilität**: Höhere VPD-Werte für bessere Kontrolle
- **Phasen-Anpassung**: Strain-spezifische Werte pro Phase
- **Maximale Erträge**: Optimiert für längere Wachstumsphasen

### Allgemein

- **Wissenschaftliche Basis**: Fundiert auf bewährten Praktiken
- **Benutzerfreundlich**: Klare visuelle Kennzeichnung
- **Automatisch**: Keine manuelle Konfiguration nötig

## Wartung und Updates

### JSON-Updates

- **Neue Phasen**: Einfach in JSON-Datei hinzufügen
- **Zielwert-Anpassungen**: Wissenschaftliche Updates möglich
- **Strain-Types**: Erweiterung um weitere Sortentypen möglich

### Code-Wartung

- **Modular**: Separate Logic-Module für einfache Updates
- **Dokumentiert**: Klare Struktur und Kommentare
- **Testbar**: Einzelne Komponenten testbar

## Zusammenfassung

Die sortentyp-spezifischen VPD-Richtlinien bieten:

✅ **Wissenschaftlich fundierte VPD-Empfehlungen** für Autoflower und Photoperiod-Sorten  
✅ **Automatische Erkennung** des Sortentyps aus Pflanzendaten  
✅ **Visuelle Kennzeichnung** mit Badges und Tooltips  
✅ **Detaillierte Optimierungsempfehlungen** für VPD, Temperatur und Luftfeuchte  
✅ **Grenzwert-System** mit Risiko-Bewertung  
✅ **Fallback-System** für unbekannte Sortentypen  
✅ **Modulare Architektur** für einfache Wartung und Erweiterung  

Das System verbessert die VPD-Optimierung und reduziert das Risiko von Wachstumsproblemen durch falsche Transpirationsbedingungen, besonders bei Autoflower-Sorten. 