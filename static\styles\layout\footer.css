/* ===== FOOTER LAYOUT ===== */
footer, .footer {
  background: var(--background) !important;
  color: var(--text) !important;
  border-top: 1.5px solid var(--card-border) !important;
}

footer, .footer, .footer * {
  color: var(--text) !important;
  opacity: 0.95;
}

footer a, .footer a {
  color: var(--accent) !important;
  text-decoration: underline;
}

footer .text-muted, .footer .text-muted {
  color: var(--text) !important;
  opacity: 0.7;
}

/* Dark Mode Footer */
[data-theme="dark"] footer,
[data-theme="dark"] .footer {
  background-color: var(--bs-dark);
  border-top: 1.5px solid var(--card-border) !important;
}

/* Sticky Footer Setup */
html {
  height: 100%;
}

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

footer,
.footer {
  margin-top: auto;
} 