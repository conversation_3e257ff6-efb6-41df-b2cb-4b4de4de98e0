/**
 * 🤖 KI-basiertes Beleuchtungsempfehlungssystem
 * 
 * Analysiert Pflanzenwachstum und Beleuchtungsdaten
 * Generiert intelligente Empfehlungen für optimale Ergebnisse
 */

class AILightingAdvisor {
    constructor() {
        this.recommendationHistory = new Map();
        this.plantGrowthPatterns = new Map();
        this.optimizationRules = this.initializeOptimizationRules();
        
        console.log('🤖 AI Lighting Advisor: Initialisiert');
    }
    
    /**
     * Optimierungsregeln initialisieren
     */
    initializeOptimizationRules() {
        return {
            // PPFD-Optimierung basierend auf Phase und Strain
            ppfd_optimization: {
                'vegetative_early': {
                    'photoperiodic': { min: 250, optimal: 350, max: 450 },
                    'autoflowering': { min: 250, optimal: 350, max: 450 }
                },
                'vegetative_middle': {
                    'photoperiodic': { min: 400, optimal: 500, max: 600 },
                    'autoflowering': { min: 400, optimal: 500, max: 600 }
                },
                'vegetative_late': {
                    'photoperiodic': { min: 500, optimal: 600, max: 700 },
                    'autoflowering': { min: 500, optimal: 600, max: 700 }
                },
                'flowering_early': {
                    'photoperiodic': { min: 600, optimal: 700, max: 800 },
                    'autoflowering': { min: 600, optimal: 700, max: 800 }
                },
                'flowering_middle': {
                    'photoperiodic': { min: 700, optimal: 800, max: 900 },
                    'autoflowering': { min: 700, optimal: 800, max: 900 }
                },
                'flowering_late': {
                    'photoperiodic': { min: 600, optimal: 700, max: 800 },
                    'autoflowering': { min: 600, optimal: 700, max: 800 }
                },
                'flush': {
                    'photoperiodic': { min: 450, optimal: 600, max: 750 },
                    'autoflowering': { min: 450, optimal: 600, max: 750 }
                }
            },
            
            // Photoperiode-Optimierung
            photoperiod_optimization: {
                'vegetative_early': { 'photoperiodic': 18, 'autoflowering': 18 },
                'vegetative_middle': { 'photoperiodic': 18, 'autoflowering': 18 },
                'vegetative_late': { 'photoperiodic': 18, 'autoflowering': 18 },
                'flowering_early': { 'photoperiodic': 12, 'autoflowering': 18 },
                'flowering_middle': { 'photoperiodic': 12, 'autoflowering': 18 },
                'flowering_late': { 'photoperiodic': 12, 'autoflowering': 18 },
                'flush': { 'photoperiodic': 10, 'autoflowering': 18 }
            },
            
            // Farbtemperatur-Optimierung
            color_temp_optimization: {
                'vegetative_early': { min: 4000, optimal: 5000, max: 6500 },
                'vegetative_middle': { min: 4000, optimal: 4500, max: 6000 },
                'vegetative_late': { min: 3500, optimal: 4000, max: 5500 },
                'flowering_early': { min: 3000, optimal: 3500, max: 4500 },
                'flowering_middle': { min: 2700, optimal: 3000, max: 4000 },
                'flowering_late': { min: 2700, optimal: 3000, max: 4000 },
                'flush': { min: 2700, optimal: 3000, max: 3500 }
            }
        };
    }
    
    /**
     * Intelligente Beleuchtungsempfehlungen generieren
     */
    async generateRecommendations(plantId, plantData, lightingData, energyData) {
        try {
            const analysis = this.analyzePlantGrowth(plantId, plantData, lightingData, energyData);
            const recommendations = this.createRecommendations(analysis);
            const confidence = this.calculateConfidence(analysis);
            
            const result = {
                plantId,
                timestamp: new Date().toISOString(),
                analysis,
                recommendations,
                confidence,
                priority: this.calculatePriority(recommendations, confidence)
            };
            
            // Empfehlungen speichern
            this.saveRecommendation(plantId, result);
            
            console.log('🤖 AI Lighting Advisor: Empfehlungen generiert für Pflanze', plantId);
            
            return result;
            
        } catch (error) {
            console.error('🤖 AI Lighting Advisor: Fehler beim Generieren von Empfehlungen:', error);
            return null;
        }
    }
    
    /**
     * Pflanzenwachstum analysieren
     */
    analyzePlantGrowth(plantId, plantData, lightingData, energyData) {
        const analysis = {
            currentPhase: plantData?.phase || 'unknown',
            strainType: plantData?.strain_type || 'photoperiodic',
            currentDay: plantData?.current_day || 0,
            phaseProgress: this.calculatePhaseProgress(plantData),
            lightingEfficiency: this.analyzeLightingEfficiency(lightingData, energyData),
            growthTrends: this.analyzeGrowthTrends(plantId),
            stressIndicators: this.detectStressIndicators(plantData, lightingData),
            optimizationOpportunities: this.identifyOptimizationOpportunities(plantData, lightingData)
        };
        
        return analysis;
    }
    
    /**
     * Phasenfortschritt berechnen
     */
    calculatePhaseProgress(plantData) {
        if (!plantData?.phase_start_day || !plantData?.current_day) {
            return 0;
        }
        
        const phaseDurations = {
            'vegetative_early': 14,
            'vegetative_middle': 21,
            'vegetative_late': 14,
            'flowering_early': 21,
            'flowering_middle': 28,
            'flowering_late': 21,
            'flush': 14
        };
        
        const currentPhase = plantData.phase;
        const phaseDuration = phaseDurations[currentPhase] || 21;
        const daysInPhase = plantData.current_day - plantData.phase_start_day;
        
        return Math.min(100, Math.max(0, (daysInPhase / phaseDuration) * 100));
    }
    
    /**
     * Beleuchtungseffizienz analysieren
     */
    analyzeLightingEfficiency(lightingData, energyData) {
        if (!lightingData?.current || !energyData) {
            return { score: 0, issues: ['Keine Daten verfügbar'] };
        }
        
        const current = lightingData.current;
        const ppfd = current.ppfd_calculated || 0;
        const lampPower = current.lamp_power_w || 600;
        const ppfdPerWatt = lampPower > 0 ? ppfd / lampPower : 0;
        
        const issues = [];
        let score = 100;
        
        // PPFD/Watt Effizienz bewerten
        if (ppfdPerWatt < 0.8) {
            issues.push('Niedrige Beleuchtungseffizienz (PPFD/Watt < 0.8)');
            score -= 30;
        } else if (ppfdPerWatt < 1.0) {
            issues.push('Verbesserungswürdige Beleuchtungseffizienz');
            score -= 15;
        }
        
        // Lampenabstand optimieren
        const distance = current.lamp_distance_cm || 60;
        if (distance > 80) {
            issues.push('Lampenabstand könnte reduziert werden');
            score -= 10;
        } else if (distance < 40) {
            issues.push('Lampenabstand könnte erhöht werden (Stress-Risiko)');
            score -= 20;
        }
        
        // Beleuchtungsstunden optimieren
        const hours = current.light_hours || 12;
        const phase = lightingData.recommendations?.phase || 'flowering_middle';
        const recommendedHours = this.optimizationRules.photoperiod_optimization[phase]?.[lightingData.strain_type] || 12;
        
        if (Math.abs(hours - recommendedHours) > 2) {
            issues.push(`Beleuchtungsstunden sollten auf ${recommendedHours}h angepasst werden`);
            score -= 15;
        }
        
        return {
            score: Math.max(0, score),
            ppfdPerWatt,
            issues
        };
    }
    
    /**
     * Wachstumstrends analysieren
     */
    analyzeGrowthTrends(plantId) {
        const history = this.plantGrowthPatterns.get(plantId) || [];
        const recentData = history.slice(-7); // Letzte 7 Einträge
        
        if (recentData.length < 3) {
            return { trend: 'unknown', confidence: 0 };
        }
        
        // PPFD-Trend analysieren
        const ppfdTrend = this.calculateTrend(recentData.map(d => d.ppfd));
        const efficiencyTrend = this.calculateTrend(recentData.map(d => d.efficiency));
        
        let overallTrend = 'stable';
        let confidence = 0.5;
        
        if (ppfdTrend > 0.1 && efficiencyTrend > 0.05) {
            overallTrend = 'improving';
            confidence = 0.8;
        } else if (ppfdTrend < -0.1 || efficiencyTrend < -0.05) {
            overallTrend = 'declining';
            confidence = 0.7;
        }
        
        return { trend: overallTrend, confidence, ppfdTrend, efficiencyTrend };
    }
    
    /**
     * Trend berechnen
     */
    calculateTrend(values) {
        if (values.length < 2) return 0;
        
        const n = values.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = values.reduce((sum, val) => sum + val, 0);
        const sumXY = values.reduce((sum, val, index) => sum + (index * val), 0);
        const sumX2 = values.reduce((sum, val, index) => sum + (index * index), 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return slope;
    }
    
    /**
     * Stress-Indikatoren erkennen
     */
    detectStressIndicators(plantData, lightingData) {
        const indicators = [];
        
        if (!lightingData?.current) return indicators;
        
        const current = lightingData.current;
        const ppfd = current.ppfd_calculated || 0;
        const phase = plantData?.phase || 'flowering_middle';
        
        // PPFD-basierte Stress-Indikatoren
        const phaseRules = this.optimizationRules.ppfd_optimization[phase];
        if (phaseRules) {
            const strainType = plantData?.strain_type || 'photoperiodic';
            const rules = phaseRules[strainType];
            
            if (rules) {
                if (ppfd > rules.max) {
                    indicators.push({
                        type: 'ppfd_too_high',
                        severity: 'high',
                        message: `PPFD (${ppfd}) ist zu hoch für ${phase}`
                    });
                } else if (ppfd < rules.min) {
                    indicators.push({
                        type: 'ppfd_too_low',
                        severity: 'medium',
                        message: `PPFD (${ppfd}) ist zu niedrig für ${phase}`
                    });
                }
            }
        }
        
        // Beleuchtungsstunden-basierte Stress-Indikatoren
        const hours = current.light_hours || 12;
        const recommendedHours = this.optimizationRules.photoperiod_optimization[phase]?.[plantData?.strain_type] || 12;
        
        if (Math.abs(hours - recommendedHours) > 4) {
            indicators.push({
                type: 'photoperiod_mismatch',
                severity: 'medium',
                message: `Beleuchtungsstunden (${hours}h) weichen stark von Empfehlung (${recommendedHours}h) ab`
            });
        }
        
        return indicators;
    }
    
    /**
     * Optimierungsmöglichkeiten identifizieren
     */
    identifyOptimizationOpportunities(plantData, lightingData) {
        const opportunities = [];
        
        if (!lightingData?.current) return opportunities;
        
        const current = lightingData.current;
        const phase = plantData?.phase || 'flowering_middle';
        const strainType = plantData?.strain_type || 'photoperiodic';
        
        // PPFD-Optimierung
        const ppfdRules = this.optimizationRules.ppfd_optimization[phase]?.[strainType];
        if (ppfdRules) {
            const currentPPFD = current.ppfd_calculated || 0;
            const optimalPPFD = ppfdRules.optimal;
            
            if (Math.abs(currentPPFD - optimalPPFD) > 50) {
                opportunities.push({
                    type: 'ppfd_optimization',
                    priority: 'high',
                    current: currentPPFD,
                    target: optimalPPFD,
                    improvement: Math.abs(currentPPFD - optimalPPFD)
                });
            }
        }
        
        // Farbtemperatur-Optimierung
        const tempRules = this.optimizationRules.color_temp_optimization[phase];
        if (tempRules) {
            const currentTemp = current.color_temperature_k || 3000;
            const optimalTemp = tempRules.optimal;
            
            if (Math.abs(currentTemp - optimalTemp) > 500) {
                opportunities.push({
                    type: 'color_temp_optimization',
                    priority: 'medium',
                    current: currentTemp,
                    target: optimalTemp,
                    improvement: Math.abs(currentTemp - optimalTemp)
                });
            }
        }
        
        return opportunities;
    }
    
    /**
     * Empfehlungen erstellen
     */
    createRecommendations(analysis) {
        const recommendations = [];
        
        // PPFD-Empfehlungen
        if (analysis.optimizationOpportunities.some(opp => opp.type === 'ppfd_optimization')) {
            const ppfdOpp = analysis.optimizationOpportunities.find(opp => opp.type === 'ppfd_optimization');
            recommendations.push({
                type: 'ppfd_adjustment',
                priority: 'high',
                title: 'PPFD optimieren',
                description: `PPFD von ${ppfdOpp.current} auf ${ppfdOpp.target} μmol/m²/s anpassen`,
                action: `Lampenabstand oder Leistung anpassen`,
                expectedImprovement: 'Bessere Wachstumsrate und Blütenbildung'
            });
        }
        
        // Photoperiode-Empfehlungen
        if (analysis.stressIndicators.some(ind => ind.type === 'photoperiod_mismatch')) {
            const phase = analysis.currentPhase;
            const strainType = analysis.strainType;
            const recommendedHours = this.optimizationRules.photoperiod_optimization[phase]?.[strainType];
            
            recommendations.push({
                type: 'photoperiod_adjustment',
                priority: 'medium',
                title: 'Photoperiode anpassen',
                description: `Beleuchtungsstunden auf ${recommendedHours}h für ${phase} anpassen`,
                action: 'Timer-Einstellungen überprüfen',
                expectedImprovement: 'Optimale Blüteninduktion und Entwicklung'
            });
        }
        
        // Effizienz-Empfehlungen
        if (analysis.lightingEfficiency.score < 70) {
            recommendations.push({
                type: 'efficiency_improvement',
                priority: 'medium',
                title: 'Beleuchtungseffizienz verbessern',
                description: analysis.lightingEfficiency.issues.join(', '),
                action: 'Lampenabstand optimieren oder effizientere Lampen verwenden',
                expectedImprovement: 'Reduzierte Energiekosten bei gleicher Leistung'
            });
        }
        
        // Farbtemperatur-Empfehlungen
        if (analysis.optimizationOpportunities.some(opp => opp.type === 'color_temp_optimization')) {
            const tempOpp = analysis.optimizationOpportunities.find(opp => opp.type === 'color_temp_optimization');
            recommendations.push({
                type: 'color_temp_adjustment',
                priority: 'low',
                title: 'Farbtemperatur optimieren',
                description: `Farbtemperatur von ${tempOpp.current}K auf ${tempOpp.target}K anpassen`,
                action: 'Spektrum-Einstellungen überprüfen',
                expectedImprovement: 'Bessere Pflanzenentwicklung und Qualität'
            });
        }
        
        return recommendations;
    }
    
    /**
     * Konfidenz der Empfehlungen berechnen
     */
    calculateConfidence(analysis) {
        let confidence = 0.5; // Basis-Konfidenz
        
        // Höhere Konfidenz bei klaren Stress-Indikatoren
        if (analysis.stressIndicators.length > 0) {
            confidence += 0.2;
        }
        
        // Höhere Konfidenz bei klaren Optimierungsmöglichkeiten
        if (analysis.optimizationOpportunities.length > 0) {
            confidence += 0.15;
        }
        
        // Höhere Konfidenz bei guter Datenqualität
        if (analysis.lightingEfficiency.score > 0) {
            confidence += 0.1;
        }
        
        // Höhere Konfidenz bei stabilen Wachstumstrends
        if (analysis.growthTrends.confidence > 0.7) {
            confidence += 0.05;
        }
        
        return Math.min(1.0, confidence);
    }
    
    /**
     * Priorität der Empfehlungen berechnen
     */
    calculatePriority(recommendations, confidence) {
        if (recommendations.length === 0) return 'none';
        
        const highPriorityCount = recommendations.filter(rec => rec.priority === 'high').length;
        const mediumPriorityCount = recommendations.filter(rec => rec.priority === 'medium').length;
        
        if (highPriorityCount > 0) return 'high';
        if (mediumPriorityCount > 0) return 'medium';
        return 'low';
    }
    
    /**
     * Empfehlung speichern
     */
    saveRecommendation(plantId, recommendation) {
        const history = this.recommendationHistory.get(plantId) || [];
        history.push(recommendation);
        
        // Nur die letzten 20 Empfehlungen behalten
        if (history.length > 20) {
            history.splice(0, history.length - 20);
        }
        
        this.recommendationHistory.set(plantId, history);
        
        // In localStorage speichern
        localStorage.setItem(`ai_recommendations_${plantId}`, JSON.stringify(history));
    }
    
    /**
     * Empfehlungsverlauf abrufen
     */
    getRecommendationHistory(plantId) {
        return this.recommendationHistory.get(plantId) || [];
    }
    
    /**
     * Aktuelle Empfehlung abrufen
     */
    getCurrentRecommendation(plantId) {
        const history = this.getRecommendationHistory(plantId);
        return history.length > 0 ? history[history.length - 1] : null;
    }
    
    /**
     * Wachstumsdaten für Trend-Analyse speichern
     */
    saveGrowthData(plantId, data) {
        const history = this.plantGrowthPatterns.get(plantId) || [];
        history.push({
            ...data,
            timestamp: new Date().toISOString()
        });
        
        // Nur die letzten 30 Einträge behalten
        if (history.length > 30) {
            history.splice(0, history.length - 30);
        }
        
        this.plantGrowthPatterns.set(plantId, history);
        
        // In localStorage speichern
        localStorage.setItem(`growth_patterns_${plantId}`, JSON.stringify(history));
    }
    
    /**
     * Debug-Informationen ausgeben
     */
    debug() {
        console.log('🤖 AI Lighting Advisor Debug Info:');
        console.log('Recommendation History:', this.recommendationHistory.size);
        console.log('Growth Patterns:', this.plantGrowthPatterns.size);
        console.log('Optimization Rules:', Object.keys(this.optimizationRules));
    }
}

// Globale Instanz erstellen
window.aiLightingAdvisor = new AILightingAdvisor();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AILightingAdvisor;
} 