/**
 * Phase-Checklisten Widget
 * Ermöglicht das Erstellen und Verwalten von Checklisten für jede Phase
 */

class PhaseChecklistWidget {
    constructor(containerId, plantId) {
        this.containerId = containerId;
        this.plantId = plantId;
        this.currentPhase = null;
        this.checklist = [];
        this.init();
    }
    
    init() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            console.error(`PhaseChecklistWidget: Container ${this.containerId} nicht gefunden`);
            return;
        }
        
        this.render();
    }
    
    setCurrentPhase(phaseData) {
        this.currentPhase = phaseData;
        this.loadChecklist();
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="card phase-checklist-card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        Phase-Checkliste
                    </h5>
                    <button 
                        type="button" 
                        class="btn btn-sm btn-outline-light" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="left"
                        title="Aufgaben-Checkliste für jede Wachstumsphase. Erstelle eigene Aufgaben oder lade Vorlagen. Hilft dabei, keine wichtigen Schritte zu vergessen und den Fortschritt systematisch zu verfolgen."
                    >
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div id="phase-checklist-content">
                        <div class="text-center">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">Lade...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Tooltip initialisieren
        const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    async loadChecklist() {
        if (!this.currentPhase) return;
        
        const phaseName = this.currentPhase.sub_phase;
        
        try {
            const response = await fetch(`/api/phase-checklist/${this.plantId}?phase_name=${phaseName}`);
            const data = await response.json();
            
            if (data.success) {
                this.checklist = data.checklist;
                this.renderChecklist();
            }
        } catch (error) {
            console.error('Fehler beim Laden der Checklist:', error);
            this.showError('Fehler beim Laden der Checklist');
        }
    }
    
    renderChecklist() {
        const contentDiv = document.getElementById('phase-checklist-content');
        if (!contentDiv) return;
        
        if (!this.currentPhase) {
            contentDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Wähle eine Phase aus, um die Checkliste zu sehen
                </div>
            `;
            return;
        }
        
        // Verwende die korrekten Datenfelder
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        const phaseDisplayName = this.getPhaseDisplayName(phaseName);
        const completedCount = this.checklist.filter(item => item.is_completed).length;
        const totalCount = this.checklist.length;
        const stageDay = this.currentPhase.sub_stage_day || '?';
        const stageDuration = this.currentPhase.sub_stage_duration || '?';
        
        contentDiv.innerHTML = `
            <div class="mb-3">
                <h6 class="text-success">
                    <i class="fas fa-leaf me-2"></i>
                    ${phaseDisplayName}
                </h6>
                <small class="text-muted">
                    Tag ${stageDay} von ${stageDuration}
                </small>
                <div class="progress mt-2" style="height: 8px;">
                    <div class="progress-bar bg-success" 
                         role="progressbar" 
                         style="width: ${totalCount > 0 ? (completedCount / totalCount * 100) : 0}%">
                    </div>
                </div>
                <small class="text-muted">
                    ${completedCount} von ${totalCount} Aufgaben erledigt
                </small>
            </div>
            
            <div id="checklist-items">
                ${this.renderChecklistItems()}
            </div>
            
            <div class="mt-3">
                <div class="input-group">
                    <input 
                        type="text" 
                        id="new-checklist-item" 
                        class="form-control form-control-sm" 
                        placeholder="Neue Aufgabe hinzufügen..."
                        onkeypress="phaseChecklistWidget.handleKeyPress(event)"
                    >
                    <button 
                        class="btn btn-outline-success btn-sm" 
                        type="button"
                        onclick="phaseChecklistWidget.addItem()"
                    >
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <div class="mt-3">
                <button 
                    type="button" 
                    class="btn btn-outline-primary btn-sm"
                    onclick="phaseChecklistWidget.loadTemplate()"
                >
                    <i class="fas fa-magic me-1"></i>
                    Vorlage laden
                </button>
                
                ${this.checklist.length > 0 ? `
                    <button 
                        type="button" 
                        class="btn btn-outline-warning btn-sm ms-2"
                        onclick="phaseChecklistWidget.clearAll()"
                    >
                        <i class="fas fa-trash me-1"></i>
                        Alle löschen
                    </button>
                ` : ''}
            </div>
        `;
    }
    
    renderChecklistItems() {
        if (this.checklist.length === 0) {
            return `
                <div class="alert alert-light">
                    <i class="fas fa-lightbulb me-2"></i>
                    Noch keine Aufgaben vorhanden. Füge manuell hinzu oder lade eine Vorlage.
                </div>
            `;
        }
        
        return this.checklist.map(item => `
            <div class="form-check checklist-item ${item.is_completed ? 'completed' : ''}" data-item-id="${item.id}">
                <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="check-${item.id}"
                    ${item.is_completed ? 'checked' : ''}
                    onchange="phaseChecklistWidget.toggleItem('${item.id}')"
                >
                <label class="form-check-label ${item.is_completed ? 'text-muted text-decoration-line-through' : ''}" for="check-${item.id}">
                    ${item.checklist_item}
                </label>
                <button 
                    type="button" 
                    class="btn btn-outline-danger btn-sm float-end"
                    onclick="phaseChecklistWidget.deleteItem('${item.id}')"
                    title="Aufgabe löschen"
                >
                    <i class="fas fa-times"></i>
                </button>
                ${item.is_completed ? `
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle me-1"></i>
                        Erledigt am ${this.formatDate(item.completed_at)}
                    </small>
                ` : ''}
            </div>
        `).join('');
    }
    
    async addItem() {
        if (!this.currentPhase) return;
        
        const input = document.getElementById('new-checklist-item');
        const itemText = input.value.trim();
        
        if (!itemText) {
            this.showError('Bitte gib eine Aufgabe ein');
            return;
        }
        
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        
        try {
            const response = await fetch(`/api/phase-checklist/${this.plantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phase_name: phaseName,
                    item_text: itemText
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                input.value = '';
                this.showSuccess('Aufgabe erfolgreich hinzugefügt');
                await this.loadChecklist();
            } else {
                this.showError(data.message || 'Fehler beim Hinzufügen');
            }
        } catch (error) {
            console.error('Fehler beim Hinzufügen der Aufgabe:', error);
            this.showError('Fehler beim Hinzufügen der Aufgabe');
        }
    }
    
    async toggleItem(itemId) {
        try {
            const response = await fetch(`/api/phase-checklist/toggle/${itemId}`, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                await this.loadChecklist();
            } else {
                this.showError(data.message || 'Fehler beim Ändern des Status');
            }
        } catch (error) {
            console.error('Fehler beim Umschalten der Aufgabe:', error);
            this.showError('Fehler beim Umschalten der Aufgabe');
        }
    }
    
    async deleteItem(itemId) {
        if (!confirm('Möchtest du diese Aufgabe wirklich löschen?')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/phase-checklist/delete/${itemId}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Aufgabe erfolgreich gelöscht');
                await this.loadChecklist();
            } else {
                this.showError(data.message || 'Fehler beim Löschen');
            }
        } catch (error) {
            console.error('Fehler beim Löschen der Aufgabe:', error);
            this.showError('Fehler beim Löschen der Aufgabe');
        }
    }
    
    async loadTemplate() {
        if (!this.currentPhase) return;
        
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        const template = this.getChecklistTemplate(phaseName);
        
        if (template.length === 0) {
            this.showError('Keine Vorlage für diese Phase verfügbar');
            return;
        }
        
        // Alle Vorlagen-Items hinzufügen
        for (const itemText of template) {
            try {
                const response = await fetch(`/api/phase-checklist/${this.plantId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phase_name: phaseName,
                        item_text: itemText
                    })
                });
                
                const data = await response.json();
                if (!data.success) {
                    console.error('Fehler beim Hinzufügen der Vorlage:', data.message);
                }
            } catch (error) {
                console.error('Fehler beim Hinzufügen der Vorlage:', error);
            }
        }
        
        this.showSuccess('Vorlage erfolgreich geladen');
        await this.loadChecklist();
    }
    
    async clearAll() {
        if (!confirm('Möchtest du wirklich alle Aufgaben löschen?')) {
            return;
        }
        
        // Alle Items löschen
        for (const item of this.checklist) {
            try {
                await fetch(`/api/phase-checklist/delete/${item.id}`, {
                    method: 'DELETE'
                });
            } catch (error) {
                console.error('Fehler beim Löschen der Aufgabe:', error);
            }
        }
        
        this.showSuccess('Alle Aufgaben gelöscht');
        await this.loadChecklist();
    }
    
    handleKeyPress(event) {
        if (event.key === 'Enter') {
            this.addItem();
        }
    }
    
    getPhaseDisplayName(phaseName) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            // Alternative Namen aus der Datenbank
            'Keimung': 'Keimung',
            'Frühe Wachstumsphase': 'Frühe Wachstumsphase',
            'Mittlere Wachstumsphase': 'Mittlere Wachstumsphase',
            'Späte Wachstumsphase': 'Späte Wachstumsphase',
            'Frühe Blüte': 'Frühe Blüte',
            'Mittlere Blüte': 'Mittlere Blüte',
            'Späte Blüte': 'Späte Blüte'
        };
        return phaseNames[phaseName] || phaseName;
    }
    
    getChecklistTemplate(phaseName) {
        const templates = {
            'germination': [
                'Samen in feuchtes Substrat legen',
                'Temperatur auf 22-26°C einstellen',
                'Luftfeuchtigkeit auf 70-80% halten',
                'Substrat feucht aber nicht nass halten',
                'Keimung nach 3-7 Tagen überprüfen'
            ],
            'vegetative_early': [
                'Lichtzyklus auf 18/6 einstellen',
                'VPD auf 0.8-1.2 kPa optimieren',
                'Bio Grow Dünger einschleichen (1ml/L)',
                'Wurzelbildung mit Root Juice fördern',
                'Pflanzenabstand überprüfen',
                'Lüftung für frische Luft sicherstellen'
            ],
            'vegetative_middle': [
                'Bio Grow auf 3-4ml/L erhöhen',
                'Top Max für Blüte-Vorbereitung hinzufügen',
                'LST-Training beginnen',
                'VPD auf 1.0-1.4 kPa optimieren',
                'Pflanzenhöhe und -breite kontrollieren',
                'Heaven für bessere Nährstoffaufnahme verwenden'
            ],
            'vegetative_late': [
                'Bio Bloom einschleichen (1ml/L)',
                'Top Max auf 2ml/L erhöhen',
                'LST-Training fortsetzen',
                'VPD auf 1.2-1.6 kPa optimieren',
                'Pflanzenform für Blüte vorbereiten',
                'Lichtabstand an Pflanzenhöhe anpassen'
            ],
            'flowering_early': [
                'Lichtzyklus auf 12/12 umstellen (nur bei photoperiodic)',
                'Bio Bloom auf 2-3ml/L erhöhen',
                'Bio Grow auf 2ml/L reduzieren',
                'Top Max auf 3ml/L erhöhen',
                'VPD auf 1.4-1.8 kPa optimieren',
                'LST-Training beenden',
                'Blütenbildung beobachten'
            ],
            'flowering_middle': [
                'Bio Bloom auf 4ml/L erhöhen',
                'Bio Grow auf 1ml/L reduzieren',
                'Top Max auf 4ml/L erhöhen',
                'VPD auf 1.6-2.0 kPa optimieren',
                'Blütenentwicklung dokumentieren',
                'Trichome-Entwicklung überprüfen',
                'Lüftung für Geruchskontrolle verstärken'
            ],
            'flowering_late': [
                'Bio Bloom auf 2ml/L reduzieren',
                'Top Max auf 2ml/L reduzieren',
                'VPD auf 1.4-1.6 kPa optimieren',
                'Trichome-Farbe und -Reife prüfen',
                'Ernte-Zeitpunkt bestimmen',
                'Flush-Vorbereitung beginnen'
            ],
            // Alternative Namen aus der Datenbank
            'Keimung': [
                'Samen in feuchtes Substrat legen',
                'Temperatur auf 22-26°C einstellen',
                'Luftfeuchtigkeit auf 70-80% halten',
                'Substrat feucht aber nicht nass halten',
                'Keimung nach 3-7 Tagen überprüfen'
            ],
            'Frühe Wachstumsphase': [
                'Lichtzyklus auf 18/6 einstellen',
                'VPD auf 0.8-1.2 kPa optimieren',
                'Bio Grow Dünger einschleichen (1ml/L)',
                'Wurzelbildung mit Root Juice fördern',
                'Pflanzenabstand überprüfen',
                'Lüftung für frische Luft sicherstellen'
            ],
            'Mittlere Wachstumsphase': [
                'Bio Grow auf 3-4ml/L erhöhen',
                'Top Max für Blüte-Vorbereitung hinzufügen',
                'LST-Training beginnen',
                'VPD auf 1.0-1.4 kPa optimieren',
                'Pflanzenhöhe und -breite kontrollieren',
                'Heaven für bessere Nährstoffaufnahme verwenden'
            ],
            'Späte Wachstumsphase': [
                'Bio Bloom einschleichen (1ml/L)',
                'Top Max auf 2ml/L erhöhen',
                'LST-Training fortsetzen',
                'VPD auf 1.2-1.6 kPa optimieren',
                'Pflanzenform für Blüte vorbereiten',
                'Lichtabstand an Pflanzenhöhe anpassen'
            ],
            'Frühe Blüte': [
                'Lichtzyklus auf 12/12 umstellen (nur bei photoperiodic)',
                'Bio Bloom auf 2-3ml/L erhöhen',
                'Bio Grow auf 2ml/L reduzieren',
                'Top Max auf 3ml/L erhöhen',
                'VPD auf 1.4-1.8 kPa optimieren',
                'LST-Training beenden',
                'Blütenbildung beobachten'
            ],
            'Mittlere Blüte': [
                'Bio Bloom auf 4ml/L erhöhen',
                'Bio Grow auf 1ml/L reduzieren',
                'Top Max auf 4ml/L erhöhen',
                'VPD auf 1.6-2.0 kPa optimieren',
                'Blütenentwicklung dokumentieren',
                'Trichome-Entwicklung überprüfen',
                'Lüftung für Geruchskontrolle verstärken'
            ],
            'Späte Blüte': [
                'Bio Bloom auf 2ml/L reduzieren',
                'Top Max auf 2ml/L reduzieren',
                'VPD auf 1.4-1.6 kPa optimieren',
                'Trichome-Farbe und -Reife prüfen',
                'Ernte-Zeitpunkt bestimmen',
                'Flush-Vorbereitung beginnen'
            ]
        };
        return templates[phaseName] || [];
    }
    
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const contentDiv = document.getElementById('phase-checklist-content');
        if (contentDiv) {
            contentDiv.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    }
    
    showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const contentDiv = document.getElementById('phase-checklist-content');
        if (contentDiv) {
            contentDiv.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }
}

// Globale Instanz für einfachen Zugriff
let phaseChecklistWidget = null;

// Initialisierungsfunktion
function initPhaseChecklistWidget(containerId, plantId) {
    phaseChecklistWidget = new PhaseChecklistWidget(containerId, plantId);
    return phaseChecklistWidget;
} 