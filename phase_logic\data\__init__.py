#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Daten-Module für die Phasenlogik
"""

from .fertilizer_brands import FERTILIZER_BRANDS
from .phase_definitions import PHASE_SUBSTAGES, PHASE_FERTILIZER_RECOMMENDATIONS
from .phase_details import (
    TRICHOME_DEVELOPMENT, 
    ENVIRONMENT_CONDITIONS, 
    EXTENDED_NUTRIENTS, 
    HARVEST_REASONS,
    get_trichome_development,
    get_environment_conditions,
    get_extended_nutrients,
    get_harvest_reason,
    calculate_bloom_week,
    get_complete_phase_details
)

__all__ = [
    'FERTILIZER_BRANDS', 
    'PHASE_SUBSTAGES', 
    'PHASE_FERTILIZER_RECOMMENDATIONS',
    'TRICHOME_DEVELOPMENT',
    'ENVIRONMENT_CONDITIONS',
    'EXTENDED_NUTRIENTS',
    'HARVEST_REASONS',
    'get_trichome_development',
    'get_environment_conditions',
    'get_extended_nutrients',
    'get_harvest_reason',
    'calculate_bloom_week',
    'get_complete_phase_details'
] 