/* ===== FEATURE FLAGS COMPONENT STYLES ===== */

.feature-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.feature-disabled::after {
  content: " (Deaktiviert)";
  font-style: italic;
  color: #6c757d;
}

/* Feature-specific styles */
[data-feature="measurements"] .feature-content {
  border-left: 3px solid var(--info);
  padding-left: 1rem;
}

[data-feature="watering"] .feature-content {
  border-left: 3px solid var(--success);
  padding-left: 1rem;
}

[data-feature="fertilizer"] .feature-content {
  border-left: 3px solid var(--warning);
  padding-left: 1rem;
}

[data-feature="sensors"] .feature-content {
  border-left: 3px solid var(--accent);
  padding-left: 1rem;
}

[data-feature="analysis"] .feature-content {
  border-left: 3px solid var(--secondary);
  padding-left: 1rem;
}

[data-feature="chatgpt"] .feature-content {
  border-left: 3px solid var(--danger);
  padding-left: 1rem;
} 