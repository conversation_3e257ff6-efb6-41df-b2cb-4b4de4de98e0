#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trichome-Analyse-Routes für praktisches Trichome-Tracking
Basierend auf echten Daten von Blüte-Tag 41
"""

from flask import Blueprint, request, jsonify, render_template
from datetime import datetime, timedelta
import json
import os

trichome_bp = Blueprint('trichome', __name__, url_prefix='/trichome')

# Beispiel-Daten basierend auf deinen JSON-Daten
SAMPLE_TRICHOME_DATA = {
    "plant_id": "GG25L",
    "observation_date": "2025-07-13",
    "bloom_day": 41,
    "observed_zones": [
        {
            "zone": "Top-Cola",
            "clear": 70,
            "milky": 30,
            "amber": 0,
            "notes": "Einzelne milchige Trichome sichtbar, klare überwiegen."
        },
        {
            "zone": "Seitentrieb",
            "clear": 85,
            "milky": 15,
            "amber": 0,
            "notes": "Nur ganz vereinzelt milchig. Reifeprozess startet."
        }
    ],
    "tools_used": "USB-Mikroskop 100x mit LED",
    "recommended_next_observation": "2025-07-15",
    "visual_log_path": "/images/observation_2025-07-13/"
}

@trichome_bp.route('/')
def trichome_dashboard():
    """Hauptseite für Trichome-Analyse"""
    return render_template('widgets/trichome-widget.html')

@trichome_bp.route('/observation/<plant_id>')
def get_trichome_observation(plant_id):
    """Trichome-Beobachtung für eine Pflanze abrufen"""
    # Hier würde normalerweise die Datenbank abgefragt werden
    # Für jetzt verwenden wir die Beispieldaten
    if plant_id == "GG25L":
        return jsonify(SAMPLE_TRICHOME_DATA)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@trichome_bp.route('/observation/<plant_id>', methods=['POST'])
def add_trichome_observation(plant_id):
    """Neue Trichome-Beobachtung hinzufügen"""
    try:
        data = request.get_json()
        
        # Validierung der Daten
        required_fields = ['observation_date', 'bloom_day', 'observed_zones']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Feld '{field}' fehlt"}), 400
        
        # Hier würde normalerweise in die Datenbank gespeichert werden
        observation = {
            "plant_id": plant_id,
            "observation_date": data['observation_date'],
            "bloom_day": data['bloom_day'],
            "observed_zones": data['observed_zones'],
            "tools_used": data.get('tools_used', 'Unbekannt'),
            "recommended_next_observation": data.get('recommended_next_observation'),
            "visual_log_path": data.get('visual_log_path', ''),
            "created_at": datetime.now().isoformat()
        }
        
        # Empfehlung für nächste Beobachtung berechnen
        next_obs_date = datetime.strptime(data['observation_date'], '%Y-%m-%d') + timedelta(days=2)
        observation['recommended_next_observation'] = next_obs_date.strftime('%Y-%m-%d')
        
        return jsonify({
            "message": "Trichome-Beobachtung erfolgreich hinzugefügt",
            "observation": observation
        }), 201
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Hinzufügen: {str(e)}"}), 500

@trichome_bp.route('/analysis/<plant_id>')
def get_trichome_analysis(plant_id):
    """Trichome-Analyse und Empfehlungen abrufen"""
    if plant_id == "GG25L":
        # Berechne Durchschnittswerte aus den Zonen
        zones = SAMPLE_TRICHOME_DATA['observed_zones']
        total_clear = sum(zone['clear'] for zone in zones)
        total_milky = sum(zone['milky'] for zone in zones)
        total_amber = sum(zone['amber'] for zone in zones)
        
        avg_clear = total_clear / len(zones)
        avg_milky = total_milky / len(zones)
        avg_amber = total_amber / len(zones)
        
        # Bestimme Reifestatus
        if avg_amber > 30:
            maturity_status = "reif"
            harvest_recommendation = "Ernte optimal"
        elif avg_milky > 60:
            maturity_status = "fast_reif"
            harvest_recommendation = "Ernte in 3-5 Tagen"
        elif avg_milky > 30:
            maturity_status = "reifend"
            harvest_recommendation = "Weiter beobachten, Flush vorbereiten"
        else:
            maturity_status = "früh"
            harvest_recommendation = "Noch zu früh für Ernte"
        
        analysis = {
            "plant_id": plant_id,
            "current_status": {
                "clear_percentage": round(avg_clear, 1),
                "milky_percentage": round(avg_milky, 1),
                "amber_percentage": round(avg_amber, 1),
                "maturity_status": maturity_status
            },
            "recommendations": {
                "harvest": harvest_recommendation,
                "next_observation": "2025-07-15",
                "flush_ready": avg_milky > 30,
                "target_ratios": {
                    "balanced": {"milky": 60, "amber": 20},
                    "energetic": {"milky": 80, "amber": 10},
                    "relaxed": {"milky": 40, "amber": 40}
                }
            },
            "progress_tracking": {
                "days_since_last_observation": 0,
                "trichome_development_rate": "normal",
                "estimated_days_to_harvest": 24 if avg_milky < 50 else 10
            }
        }
        
        return jsonify(analysis)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@trichome_bp.route('/target/<plant_id>', methods=['POST'])
def set_harvest_target(plant_id):
    """Zielwirkung für Ernte setzen"""
    try:
        data = request.get_json()
        target_effect = data.get('target_effect', 'balanced')
        
        # Zielwerte basierend auf gewünschter Wirkung
        target_values = {
            "balanced": {"milky": 60, "amber": 20},
            "energetic": {"milky": 80, "amber": 10},
            "relaxed": {"milky": 40, "amber": 40}
        }
        
        target = target_values.get(target_effect, target_values["balanced"])
        
        return jsonify({
            "message": f"Zielwirkung auf '{target_effect}' gesetzt",
            "target_effect": target_effect,
            "target_trichomes": target,
            "plant_id": plant_id
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Setzen des Ziels: {str(e)}"}), 500

@trichome_bp.route('/history/<plant_id>')
def get_trichome_history(plant_id):
    """Verlauf der Trichome-Entwicklung abrufen"""
    # Hier würde normalerweise die Datenbank abgefragt werden
    # Für jetzt verwenden wir Beispieldaten
    if plant_id == "GG25L":
        history = [
            {
                "date": "2025-07-10",
                "bloom_day": 38,
                "avg_milky": 15,
                "avg_amber": 0,
                "notes": "Erste milchige Trichome sichtbar"
            },
            {
                "date": "2025-07-13",
                "bloom_day": 41,
                "avg_milky": 22.5,
                "avg_amber": 0,
                "notes": "Reifeprozess beschleunigt sich"
            }
        ]
        return jsonify({"plant_id": plant_id, "history": history})
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404 