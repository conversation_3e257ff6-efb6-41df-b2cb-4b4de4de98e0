/**
 * Delete Modal Module
 * Handles delete confirmation modal functionality
 */

class DeleteModal {
    constructor() {
        this.modal = document.getElementById('delete-modal');
        this.message = document.getElementById('delete-message');
        this.confirmBtn = document.getElementById('confirm-delete');
        
        this.init();
    }
    
    init() {
        if (this.modal && this.message && this.confirmBtn) {
            this.setupModal();
        }
    }
    
    setupModal() {
        // Reset modal when hidden
        this.modal.addEventListener('hidden.bs.modal', () => {
            this.reset();
        });
    }
    
    open(plantId, plantName) {
        if (!this.modal || !this.message || !this.confirmBtn) {
            console.error('Delete modal elements not found');
            return;
        }
        
        this.message.textContent = `Möchtest du die Pflanze "${plantName}" wirklich löschen? Alle zugehörigen Einträge werden ebenfalls gelöscht.`;
        
        this.confirmBtn.onclick = () => {
            this.submitDelete(plantId);
        };
        
        const bootstrapModal = new bootstrap.Modal(this.modal);
        bootstrapModal.show();
    }
    
    submitDelete(plantId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/plant/${plantId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
    
    reset() {
        if (this.confirmBtn) {
            this.confirmBtn.onclick = null;
        }
    }
}

// Export for use in other modules
window.DeleteModal = DeleteModal; 