"""
Watering Logic Module
Handles watering plan calculations and recommendations
"""

from typing import Dict, Any, List

class WateringLogic:
    """Watering plan calculation and optimization logic"""
    
    def __init__(self):
        self.substrate_absorption = {
            'coco': 0.8,
            'soil': 0.6,
            'hydro': 1.0,
            'perlite': 0.3,
            'vermiculite': 0.7
        }
        
        self.phase_watering_factors = {
            'germination': 0.3,
            'vegetative_early': 0.5,
            'vegetative_middle': 0.7,
            'vegetative_late': 0.8,
            'flowering_early': 0.9,
            'flowering_middle': 1.0,
            'flowering_late': 0.8,
            'flush': 0.6
        }
        
        self.strain_type_factors = {
            'photoperiodic': 1.0,
            'autoflowering': 0.8
        }
    
    def calculate_watering_plan(self, phase: str, pot_size: float, substrate: str = 'coco', strain_type: str = 'photoperiodic') -> Dict[str, Any]:
        """Calculate watering plan for a specific phase and setup"""
        
        # Get base factors
        substrate_factor = self.substrate_absorption.get(substrate, 0.8)
        phase_factor = self.phase_watering_factors.get(phase, 0.7)
        strain_factor = self.strain_type_factors.get(strain_type, 1.0)
        
        # Calculate base watering amount (ml per liter of pot size)
        # Realistische Werte basierend auf praktischer Erfahrung
        # Typische Bewässerung: 300-400ml pro Liter Substrat
        base_amount_per_liter = 350  # ml per liter (realistischer Wert)
        
        # Calculate total watering amount
        # Berücksichtige Substrat-Absorption und Phase-Faktoren
        total_amount = pot_size * base_amount_per_liter * substrate_factor * phase_factor * strain_factor
        
        # Calculate frequency based on phase and substrate
        frequency = self.calculate_frequency(phase, substrate, strain_type)
        
        # Generate schedule
        schedule = self.generate_schedule(phase, frequency, total_amount)
        
        # Frontend-kompatible Struktur
        return {
            'phase': phase,
            'substrate': substrate,
            'pot_size_l': pot_size,
            'amount_ml_per_liter': base_amount_per_liter,
            'total_amount_ml': round(total_amount, 0),
            'frequency': frequency,
            'schedule': schedule,
            'recommendations': {
                'substrate_factor': substrate_factor,
                'phase_factor': phase_factor,
                'strain_factor': strain_factor,
                'note': self.get_phase_watering_note(phase),
                'tips': self.get_watering_tips(phase, substrate)
            }
        }
    
    def calculate_frequency(self, phase: str, substrate: str, strain_type: str) -> str:
        """Calculate watering frequency based on phase and substrate"""
        
        if substrate == 'hydro':
            return 'kontinuierlich'
        
        base_frequencies = {
            'germination': 'alle 2-3 Tage',
            'vegetative_early': 'alle 2-3 Tage',
            'vegetative_middle': 'alle 1-2 Tage',
            'vegetative_late': 'täglich',
            'flowering_early': 'täglich',
            'flowering_middle': 'täglich',
            'flowering_late': 'alle 1-2 Tage',
            'flush': 'alle 2-3 Tage'
        }
        
        frequency = base_frequencies.get(phase, 'alle 2-3 Tage')
        
        # Adjust for strain type
        if strain_type == 'autoflowering':
            if 'täglich' in frequency:
                frequency = 'alle 1-2 Tage'
            elif 'alle 1-2 Tage' in frequency:
                frequency = 'alle 2-3 Tage'
        
        return frequency
    
    def generate_schedule(self, phase: str, frequency: str, amount: float) -> List[Dict[str, Any]]:
        """Generate detailed watering schedule"""
        
        schedule = []
        
        if frequency == 'kontinuierlich':
            schedule.append({
                'day': 'kontinuierlich',
                'amount': amount,
                'description': 'Kontinuierliche Bewässerung (Hydroponik)',
                'notes': 'EC und pH regelmäßig kontrollieren'
            })
        elif frequency == 'täglich':
            for i in range(7):
                schedule.append({
                    'day': f'Tag {i+1}',
                    'amount': amount,
                    'description': 'Tägliche Bewässerung',
                    'notes': 'Substrat sollte leicht feucht bleiben'
                })
        elif frequency == 'alle 1-2 Tage':
            for i in range(7):
                if i % 2 == 0:
                    schedule.append({
                        'day': f'Tag {i+1}',
                        'amount': amount,
                        'description': 'Bewässerung',
                        'notes': 'Substrat zwischen Bewässerungen leicht trocknen lassen'
                    })
                else:
                    schedule.append({
                        'day': f'Tag {i+1}',
                        'amount': 0,
                        'description': 'Keine Bewässerung',
                        'notes': 'Substrat trocknen lassen'
                    })
        else:  # alle 2-3 Tage
            for i in range(7):
                if i % 3 == 0:
                    schedule.append({
                        'day': f'Tag {i+1}',
                        'amount': amount,
                        'description': 'Bewässerung',
                        'notes': 'Substrat sollte zwischen Bewässerungen trocknen'
                    })
                else:
                    schedule.append({
                        'day': f'Tag {i+1}',
                        'amount': 0,
                        'description': 'Keine Bewässerung',
                        'notes': 'Substrat trocknen lassen'
                    })
        
        return schedule
    
    def calculate_plant_watering_schedule(self, plant: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate watering schedule for a specific plant"""
        
        # Extract plant data
        pot_size = float(plant.get('pot_size', 11.0))
        substrate = plant.get('substrate', 'coco')
        strain_type = plant.get('strain_type', 'photoperiodic')
        
        # Get current phase (simplified - in practice this would come from phase logic)
        current_phase = 'vegetative_middle'  # Default
        
        # Calculate watering plan
        plan = self.calculate_watering_plan(current_phase, pot_size, substrate, strain_type)
        
        return {
            'plant_id': plant.get('id'),
            'plant_name': plant.get('plant_name'),
            'current_phase': current_phase,
            'watering_plan': plan,
            'last_watering': None,  # Would come from database
            'next_watering': None   # Would be calculated
        }
    
    def get_available_substrates(self) -> List[Dict[str, Any]]:
        """Get list of available substrates with descriptions"""
        return [
            {
                'id': 'coco',
                'name': 'Kokosfaser',
                'description': 'Hochwertiges Substrat mit guter Drainage',
                'absorption_factor': self.substrate_absorption['coco']
            },
            {
                'id': 'soil',
                'name': 'Erde',
                'description': 'Traditionelles Substrat mit Nährstoffen',
                'absorption_factor': self.substrate_absorption['soil']
            },
            {
                'id': 'hydro',
                'name': 'Hydroponik',
                'description': 'Wasserbasierte Kultivierung',
                'absorption_factor': self.substrate_absorption['hydro']
            },
            {
                'id': 'perlite',
                'name': 'Perlite',
                'description': 'Leichtes Substrat mit sehr guter Drainage',
                'absorption_factor': self.substrate_absorption['perlite']
            },
            {
                'id': 'vermiculite',
                'name': 'Vermiculite',
                'description': 'Feuchtigkeitsspeicherndes Substrat',
                'absorption_factor': self.substrate_absorption['vermiculite']
            }
        ]
    
    def get_substrate_recommendations(self, substrate: str) -> Dict[str, Any]:
        """Get specific recommendations for a substrate"""
        recommendations = {
            'coco': {
                'description': 'Kokosfaser ist ein ausgezeichnetes Substrat für Anfänger und Fortgeschrittene',
                'advantages': ['Gute Drainage', 'Wiederverwendbar', 'pH-neutral'],
                'disadvantages': ['Kann teuer sein', 'Benötigt regelmäßige Bewässerung'],
                'tips': [
                    'EC zwischen 1.2-1.8 halten',
                    'pH zwischen 5.8-6.2',
                    'Niemals komplett trocknen lassen'
                ]
            },
            'soil': {
                'description': 'Erde ist das traditionellste Substrat für Cannabis',
                'advantages': ['Einfach zu handhaben', 'Enthält Nährstoffe', 'Verzeiht Fehler'],
                'disadvantages': ['Schwerer', 'Kann Schädlinge enthalten', 'Weniger Kontrolle'],
                'tips': [
                    'Qualitätserde verwenden',
                    'pH zwischen 6.0-7.0',
                    'Nicht überwässern'
                ]
            },
            'hydro': {
                'description': 'Hydroponik bietet maximale Kontrolle über Nährstoffe',
                'advantages': ['Schnelleres Wachstum', 'Maximale Kontrolle', 'Höhere Erträge'],
                'disadvantages': ['Komplex', 'Teuer', 'Fehleranfällig'],
                'tips': [
                    'EC und pH täglich kontrollieren',
                    'Wassertemperatur überwachen',
                    'Backup-Systeme haben'
                ]
            }
        }
        
        return recommendations.get(substrate, {
            'description': 'Substrat nicht gefunden',
            'advantages': [],
            'disadvantages': [],
            'tips': []
        }) 

    def get_phase_watering_note(self, phase: str) -> str:
        """Get watering note for specific phase"""
        notes = {
            'germination': 'Sehr vorsichtig bewässern - Substrat nur leicht feucht halten',
            'vegetative_early': 'Moderate Bewässerung für gesundes Wurzelwachstum',
            'vegetative_middle': 'Regelmäßige Bewässerung für kräftiges Wachstum',
            'vegetative_late': 'Häufige Bewässerung für maximale Entwicklung',
            'flowering_early': 'Erhöhte Bewässerung für Blütenbildung',
            'flowering_middle': 'Optimale Bewässerung für Blütenentwicklung',
            'flowering_late': 'Reduzierte Bewässerung für Reifung',
            'flush': 'Minimale Bewässerung für finale Reifung'
        }
        return notes.get(phase, 'Standard-Bewässerung')
    
    def get_watering_tips(self, phase: str, substrate: str) -> List[str]:
        """Get watering tips for phase and substrate"""
        tips = []
        
        # Phase-spezifische Tipps
        if phase in ['germination', 'vegetative_early']:
            tips.append('Substrat nicht zu nass halten')
            tips.append('Langsam bewässern')
        
        if phase in ['vegetative_middle', 'vegetative_late']:
            tips.append('Regelmäßig bewässern')
            tips.append('Substrat zwischen Bewässerungen leicht trocknen lassen')
        
        if phase in ['flowering_early', 'flowering_middle']:
            tips.append('Häufiger bewässern')
            tips.append('EC und pH kontrollieren')
        
        if phase in ['flowering_late', 'flush']:
            tips.append('Bewässerung reduzieren')
            tips.append('Substrat trockener halten')
        
        # Substrat-spezifische Tipps
        if substrate == 'coco':
            tips.append('Coco niemals komplett trocknen lassen')
            tips.append('EC zwischen 1.2-1.8 halten')
        elif substrate == 'soil':
            tips.append('Finger-Test: 2cm tief fühlen')
            tips.append('Nicht überwässern')
        elif substrate == 'hydro':
            tips.append('Wassertemperatur überwachen')
            tips.append('EC und pH täglich kontrollieren')
        
        return tips 

    def load_guidelines_from_json(self, phase: str) -> Dict[str, Any]:
        """Load watering guidelines from JSON file for specific phase"""
        try:
            import json
            import os
            
            # Path to guidelines JSON file
            guidelines_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'static', 'data', 'watering-guidelines.json'
            )
            
            if not os.path.exists(guidelines_path):
                return None
            
            with open(guidelines_path, 'r', encoding='utf-8') as f:
                guidelines_data = json.load(f)
            
            # Find matching phase
            guidelines = guidelines_data.get('wateringGuidelines', {})
            phases = guidelines.get('phases', [])
            
            matching_phase = None
            for phase_data in phases:
                if phase_data.get('phase') == phase:
                    matching_phase = phase_data
                    break
            
            if not matching_phase:
                return None
            
            return {
                'phase': phase,
                'guidelines': matching_phase,
                'faustregeln': guidelines.get('faustregeln', []),
                'topfGroessenBeispiel': guidelines.get('topfGroessenBeispiel', []),
                'einflussfaktoren': guidelines.get('einflussfaktoren', []),
                'meta': guidelines.get('meta', {})
            }
            
        except Exception as e:
            print(f"Fehler beim Laden der Watering Guidelines: {e}")
            return None

    def calculate_watering_with_guidelines(self, phase: str, pot_size: float, substrate: str = 'coco', strain_type: str = 'photoperiodic') -> Dict[str, Any]:
        """Calculate watering plan using JSON guidelines if available"""
        
        print(f"WateringLogic: calculate_watering_with_guidelines called with phase={phase}, pot_size={pot_size}, substrate={substrate}, strain_type={strain_type}")
        
        # Try to load guidelines first
        guidelines_data = self.load_guidelines_from_json(phase)
        print(f"WateringLogic: Guidelines data loaded: {guidelines_data is not None}")
        
        if guidelines_data:
            # Use guidelines data
            guidelines = guidelines_data['guidelines']
            print(f"WateringLogic: Guidelines found: {guidelines}")
            
            # Get strain type key for guidelines
            strain_key = 'photoperiod' if strain_type == 'photoperiodic' else 'autoflower'
            print(f"WateringLogic: Using strain key: {strain_key}")
            
            # Get strain-specific guidelines
            strain_guidelines = guidelines.get(strain_key, {})
            print(f"WateringLogic: Strain guidelines: {strain_guidelines}")
            
            if not strain_guidelines:
                print(f"WateringLogic: No strain guidelines found for {strain_key}")
                return self.calculate_watering_plan(phase, pot_size, substrate, strain_type)
            
            # Parse watering volume (e.g., "5–10 % des Topfvolumens")
            watering_volume = strain_guidelines.get('wateringVolume', '')
            print(f"WateringLogic: Watering volume: {watering_volume}")
            
            # Extract percentage range
            min_factor = 0.05  # Default 5%
            max_factor = 0.10  # Default 10%
            is_percent = False
            
            if watering_volume:
                import re
                percentage_match = re.search(r'(\d+(?:[–-]\d+)?)\s*% des Topfvolumens', watering_volume)
                if percentage_match:
                    is_percent = True
                    percentage_str = percentage_match.group(1)
                    if '–' in percentage_str or '-' in percentage_str:
                        parts = percentage_str.replace('–', '-').split('-')
                        if len(parts) == 2:
                            min_factor = float(parts[0]) / 100
                            max_factor = float(parts[1]) / 100
                    else:
                        single_factor = float(percentage_str) / 100
                        min_factor = single_factor * 0.8
                        max_factor = single_factor * 1.2
                else:
                    # Fallback: ml/l oder absolute Werte, Substratfaktor verwenden
                    is_percent = False
            
            print(f"WateringLogic: Parsed factors - min={min_factor}, max={max_factor}, is_percent={is_percent}")
            
            # Calculate watering amounts based on factors
            if is_percent:
                min_amount_ml = pot_size * 1000 * min_factor
                max_amount_ml = pot_size * 1000 * max_factor
                avg_amount_ml = (min_amount_ml + max_amount_ml) / 2
                adjusted_amount_ml = avg_amount_ml  # Kein Substratfaktor!
                substrate_factor = 1.0
            else:
                # Fallback: Substratfaktor verwenden
                substrate_factor = self.substrate_absorption.get(substrate, 0.8)
                min_amount_ml = pot_size * 1000 * min_factor * substrate_factor
                max_amount_ml = pot_size * 1000 * max_factor * substrate_factor
                avg_amount_ml = (min_amount_ml + max_amount_ml) / 2
                adjusted_amount_ml = avg_amount_ml
            
            print(f"WateringLogic: Calculated amounts - min={min_amount_ml}, max={max_amount_ml}, avg={avg_amount_ml}, adjusted={adjusted_amount_ml}")
            
            # Get frequency from guidelines
            frequency = strain_guidelines.get('wateringFrequency', self.calculate_frequency(phase, substrate, strain_type))
            schedule = self.generate_schedule(phase, frequency, adjusted_amount_ml)
            
            # Get drain expected from guidelines
            drain_expected = strain_guidelines.get('drainExpected', True)
            
            result = {
                'phase': phase,
                'substrate': substrate,
                'pot_size_l': pot_size,
                'amount_ml_per_liter': round(adjusted_amount_ml / pot_size, 1),
                'total_amount_ml': round(adjusted_amount_ml, 0),
                'min_amount_ml': round(min_amount_ml, 0),
                'max_amount_ml': round(max_amount_ml, 0),
                'watering_factor_min': min_factor,
                'watering_factor_max': max_factor,
                'frequency': frequency,
                'schedule': schedule,
                'guidelines_used': True,
                'strain_specific': True,
                'drain_expected': drain_expected,
                'recommendations': {
                    'substrate_factor': substrate_factor,
                    'strain_factor': 1.0,  # Already included in JSON factors
                    'note': guidelines.get('description', self.get_phase_watering_note(phase)),
                    'tips': strain_guidelines.get('notes', self.get_watering_tips(phase, substrate)),
                    'guidelines': guidelines_data
                }
            }
            
            print(f"WateringLogic: Returning result: {result}")
            return result
        else:
            # Fallback to original calculation
            print(f"WateringLogic: No guidelines found, using fallback calculation")
            return self.calculate_watering_plan(phase, pot_size, substrate, strain_type) 