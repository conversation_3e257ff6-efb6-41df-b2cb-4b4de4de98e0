/**
 * Flush & Harvest Widget
 * Verwaltet Flush-Prozesse und Ernte-Vorbereitung
 */
class FlushWidget {
    constructor(container) {
        this.container = container;
        this.plantId = container.dataset.plantId;
        this.currentFlush = null;
        this.flushHistory = [];
        this.harvestChecklist = [];
        
        this.init();
    }

    init() {
        this.loadFlushStatus();
        this.loadFlushHistory();
        this.loadHarvestChecklist();
        this.loadGuidelines();
        this.bindEvents();
        this.setupDateInputs();
    }

    bindEvents() {
        // Flush-Steuerung
        const startFlushBtn = this.container.querySelector('#startFlushBtn');
        const completeFlushBtn = this.container.querySelector('#completeFlushBtn');
        const harvestBtn = this.container.querySelector('#harvestBtn');

        if (startFlushBtn) {
            startFlushBtn.addEventListener('click', () => this.startFlush());
        }
        if (completeFlushBtn) {
            completeFlushBtn.addEventListener('click', () => this.completeFlush());
        }
        if (harvestBtn) {
            harvestBtn.addEventListener('click', () => this.startHarvest());
        }

        // Guidelines Modal
        const guidelinesBtn = this.container.querySelector('[data-bs-target="#flushGuidelinesModal"]');
        if (guidelinesBtn) {
            guidelinesBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.loadGuidelines();
            });
        }

        // Harvest Checklist
        this.container.addEventListener('change', (e) => {
            if (e.target.matches('.harvest-checklist-item')) {
                this.updateChecklistItem(e.target);
            }
        });
    }

    setupDateInputs() {
        const startDateInput = this.container.querySelector('#flushStartDate');
        if (startDateInput) {
            // Setze Standard-Datum auf heute
            const today = new Date().toISOString().split('T')[0];
            startDateInput.value = today;
            startDateInput.min = today;
        }
    }

    async loadFlushStatus() {
        try {
            const response = await fetch(`/api/flush/status/${this.plantId}`);
            if (!response.ok) throw new Error('Fehler beim Laden des Flush-Status');
            
            const data = await response.json();
            this.currentFlush = data.current_flush;
            this.updateFlushStatusDisplay(data);
            this.updateFlushControls(data);
            
            if (this.currentFlush) {
                this.updateFlushProgress();
            }
        } catch (error) {
            console.error('Fehler beim Laden des Flush-Status:', error);
            this.showError('Fehler beim Laden des Flush-Status');
        }
    }

    async loadFlushHistory() {
        try {
            const response = await fetch(`/api/flush/history/${this.plantId}`);
            if (!response.ok) throw new Error('Fehler beim Laden der Flush-Historie');
            
            const data = await response.json();
            this.flushHistory = data.history || [];
            this.updateFlushHistoryDisplay();
        } catch (error) {
            console.error('Fehler beim Laden der Flush-Historie:', error);
        }
    }

    async loadHarvestChecklist() {
        try {
            const response = await fetch(`/api/flush/harvest-checklist/${this.plantId}`);
            if (!response.ok) throw new Error('Fehler beim Laden der Ernte-Checkliste');
            
            const data = await response.json();
            this.harvestChecklist = data.checklist || [];
            this.updateHarvestChecklistDisplay();
        } catch (error) {
            console.error('Fehler beim Laden der Ernte-Checkliste:', error);
        }
    }

    async loadGuidelines() {
        try {
            const response = await fetch('/api/flush/guidelines');
            if (!response.ok) throw new Error('Fehler beim Laden der Guidelines');
            
            const data = await response.json();
            this.updateGuidelinesDisplay(data);
        } catch (error) {
            console.error('Fehler beim Laden der Guidelines:', error);
            this.showGuidelinesError();
        }
    }

    updateFlushStatusDisplay(data) {
        const statusDisplay = this.container.querySelector('#flushStatusDisplay');
        if (!statusDisplay) return;

        let statusHtml = '';
        let alertClass = 'alert-info';

        if (data.current_flush) {
            const flush = data.current_flush;
            const daysRemaining = flush.days_remaining;
            
            if (daysRemaining > 0) {
                statusHtml = `
                    <i class="fa-solid fa-tint me-2"></i>
                    <strong>Flush läuft:</strong> Tag ${flush.current_day} von ${flush.duration} 
                    (${daysRemaining} Tage verbleibend)
                `;
                alertClass = 'alert-warning';
            } else {
                statusHtml = `
                    <i class="fa-solid fa-check-circle me-2"></i>
                    <strong>Flush abgeschlossen:</strong> Bereit für die Ernte!
                `;
                alertClass = 'alert-success';
            }
        } else if (data.recommended_flush) {
            statusHtml = `
                <i class="fa-solid fa-info-circle me-2"></i>
                <strong>Flush empfohlen:</strong> ${data.recommended_flush.reason}
            `;
            alertClass = 'alert-info';
        } else {
            statusHtml = `
                <i class="fa-solid fa-clock me-2"></i>
                <strong>Kein Flush aktiv:</strong> Flush kann gestartet werden, wenn bereit
            `;
            alertClass = 'alert-info';
        }

        statusDisplay.className = `alert ${alertClass}`;
        statusDisplay.innerHTML = statusHtml;
    }

    updateFlushControls(data) {
        const startBtn = this.container.querySelector('#startFlushBtn');
        const completeBtn = this.container.querySelector('#completeFlushBtn');
        const harvestBtn = this.container.querySelector('#harvestBtn');
        const progressDiv = this.container.querySelector('.flush-progress');

        if (data.current_flush) {
            if (startBtn) startBtn.style.display = 'none';
            if (completeBtn) completeBtn.style.display = 'inline-block';
            if (progressDiv) progressDiv.style.display = 'block';
            
            if (data.current_flush.days_remaining <= 0) {
                if (harvestBtn) harvestBtn.style.display = 'inline-block';
                if (completeBtn) completeBtn.style.display = 'none';
            }
        } else {
            if (startBtn) startBtn.style.display = 'inline-block';
            if (completeBtn) completeBtn.style.display = 'none';
            if (harvestBtn) harvestBtn.style.display = 'none';
            if (progressDiv) progressDiv.style.display = 'none';
        }
    }

    updateFlushProgress() {
        if (!this.currentFlush) return;

        const progressBar = this.container.querySelector('#flushProgressBar');
        const progressText = this.container.querySelector('#flushProgressText');
        
        if (progressBar && progressText) {
            const progress = (this.currentFlush.current_day / this.currentFlush.duration) * 100;
            progressBar.style.width = `${Math.min(progress, 100)}%`;
            progressText.textContent = `Tag ${this.currentFlush.current_day} von ${this.currentFlush.duration}`;
            
            if (progress >= 100) {
                progressBar.className = 'progress-bar bg-success';
            }
        }
    }

    updateFlushHistoryDisplay() {
        const tbody = this.container.querySelector('#flushHistoryBody');
        if (!tbody) return;

        if (this.flushHistory.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted">
                        <i class="fa-solid fa-info-circle me-2"></i>Keine Flush-Historie verfügbar
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.flushHistory.map(flush => `
            <tr>
                <td>${this.formatDate(flush.start_date)}</td>
                <td>${flush.end_date ? this.formatDate(flush.end_date) : '-'}</td>
                <td>${flush.duration} Tage</td>
                <td>
                    <span class="badge ${this.getStatusBadgeClass(flush.status)}">
                        ${this.getStatusText(flush.status)}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    updateHarvestChecklistDisplay() {
        const checklistContainer = this.container.querySelector('#harvestChecklist');
        if (!checklistContainer) return;

        if (this.harvestChecklist.length === 0) {
            checklistContainer.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fa-solid fa-info-circle me-2"></i>Keine Checkliste verfügbar
                </div>
            `;
            return;
        }

        checklistContainer.innerHTML = this.harvestChecklist.map(item => `
            <div class="list-group-item">
                <div class="form-check">
                    <input class="form-check-input harvest-checklist-item" 
                           type="checkbox" 
                           id="checklist_${item.id}" 
                           data-item-id="${item.id}"
                           ${item.completed ? 'checked' : ''}>
                    <label class="form-check-label ${item.completed ? 'completed' : ''}" 
                           for="checklist_${item.id}">
                        ${item.description}
                    </label>
                </div>
            </div>
        `).join('');
    }

    updateGuidelinesDisplay(data) {
        const content = document.querySelector('#flushGuidelinesContent');
        if (!content) return;

        // Verwende die korrekte Datenstruktur aus der API
        const guidelines = data.guidelines || data;
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fa-solid fa-tint me-2"></i>Flush-Guidelines</h6>
                    <ul class="list-unstyled">
                        ${(guidelines.methoden || []).map(methode => `
                            <li class="mb-2">
                                <i class="fa-solid fa-check text-success me-2"></i>
                                ${methode}
                            </li>
                        `).join('')}
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>Fehlerquellen</h6>
                    <ul class="list-unstyled">
                        ${(guidelines.fehlerquellen || []).map(fehler => `
                            <li class="mb-2">
                                <i class="fa-solid fa-times text-danger me-2"></i>
                                ${fehler}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
            <div class="mt-3">
                <h6><i class="fa-solid fa-lightbulb me-2"></i>Startbedingungen</h6>
                <div class="alert alert-info">
                    <strong>Autoflower:</strong> ${(guidelines.startbedingungen?.autoflower?.indikatoren || []).join(', ')}
                    <br><strong>Photoperiod:</strong> ${(guidelines.startbedingungen?.photoperiod?.indikatoren || []).join(', ')}
                </div>
            </div>
        `;
    }

    async startFlush() {
        const startDate = this.container.querySelector('#flushStartDate').value;
        const duration = this.container.querySelector('#flushDuration').value;

        if (!startDate || !duration) {
            this.showError('Bitte alle Felder ausfüllen');
            return;
        }

        try {
            const response = await fetch('/api/flush/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plant_id: this.plantId,
                    start_date: startDate,
                    duration: parseInt(duration)
                })
            });

            if (!response.ok) throw new Error('Fehler beim Starten des Flush');

            const data = await response.json();
            this.showSuccess('Flush erfolgreich gestartet!');
            this.loadFlushStatus();
            this.loadFlushHistory();
        } catch (error) {
            console.error('Fehler beim Starten des Flush:', error);
            this.showError('Fehler beim Starten des Flush');
        }
    }

    async completeFlush() {
        if (!confirm('Flush wirklich beenden? Dies kann nicht rückgängig gemacht werden.')) {
            return;
        }

        try {
            const response = await fetch('/api/flush/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plant_id: this.plantId
                })
            });

            if (!response.ok) throw new Error('Fehler beim Beenden des Flush');

            const data = await response.json();
            this.showSuccess('Flush erfolgreich beendet!');
            this.loadFlushStatus();
            this.loadFlushHistory();
        } catch (error) {
            console.error('Fehler beim Beenden des Flush:', error);
            this.showError('Fehler beim Beenden des Flush');
        }
    }

    async startHarvest() {
        if (!confirm('Ernte wirklich starten? Dies markiert das Ende des Anbaus.')) {
            return;
        }

        try {
            const response = await fetch('/api/flush/harvest', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plant_id: this.plantId
                })
            });

            if (!response.ok) throw new Error('Fehler beim Starten der Ernte');

            const data = await response.json();
            this.showSuccess('Ernte erfolgreich gestartet!');
            this.loadFlushStatus();
            this.loadHarvestChecklist();
        } catch (error) {
            console.error('Fehler beim Starten der Ernte:', error);
            this.showError('Fehler beim Starten der Ernte');
        }
    }

    async updateChecklistItem(checkbox) {
        const itemId = checkbox.dataset.itemId;
        const completed = checkbox.checked;

        try {
            const response = await fetch('/api/flush/checklist/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plant_id: this.plantId,
                    item_id: itemId,
                    completed: completed
                })
            });

            if (!response.ok) throw new Error('Fehler beim Aktualisieren der Checkliste');

            const label = checkbox.nextElementSibling;
            if (completed) {
                label.classList.add('completed');
            } else {
                label.classList.remove('completed');
            }
        } catch (error) {
            console.error('Fehler beim Aktualisieren der Checkliste:', error);
            checkbox.checked = !completed; // Revert checkbox
            this.showError('Fehler beim Aktualisieren der Checkliste');
        }
    }

    // Utility Methods
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE');
    }

    getStatusBadgeClass(status) {
        switch (status) {
            case 'active': return 'bg-warning';
            case 'completed': return 'bg-success';
            case 'cancelled': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'active': return 'Aktiv';
            case 'completed': return 'Abgeschlossen';
            case 'cancelled': return 'Abgebrochen';
            default: return 'Unbekannt';
        }
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        this.container.insertBefore(alertDiv, this.container.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    showGuidelinesError() {
        const content = document.querySelector('#flushGuidelinesContent');
        if (content) {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa-solid fa-exclamation-triangle me-2"></i>
                    Fehler beim Laden der Guidelines. Bitte versuchen Sie es später erneut.
                </div>
            `;
        }
    }
}

// Widget Registration
document.addEventListener('DOMContentLoaded', function() {
    const flushWidgets = document.querySelectorAll('.flush-widget');
    flushWidgets.forEach(widget => {
        new FlushWidget(widget);
    });
}); 
window.FlushWidget = FlushWidget; 