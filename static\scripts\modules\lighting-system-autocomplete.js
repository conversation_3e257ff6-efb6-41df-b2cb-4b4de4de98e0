/**
 * Lighting System Autocomplete Module
 * Enhanced autocomplete functionality for lighting system input
 */

class LightingSystemAutocomplete {
    constructor() {
        this.init();
    }
    
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupAutocomplete();
        });
    }
    
    setupAutocomplete() {
        const lightingInput = document.getElementById('plant-lighting');
        if (!lightingInput) return;
        
        // Add input event listener for real-time suggestions
        lightingInput.addEventListener('input', (e) => {
            this.showSuggestions(e.target.value);
        });
        
        // Add focus event to show all suggestions
        lightingInput.addEventListener('focus', (e) => {
            this.showSuggestions(e.target.value);
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.lighting-system-container')) {
                this.hideSuggestions();
            }
        });
    }
    
    showSuggestions(inputValue) {
        const lightingInput = document.getElementById('plant-lighting');
        const suggestionsContainer = this.getOrCreateSuggestionsContainer();
        const container = lightingInput.closest('.lighting-system-container');
        
        // Get saved values from datalist
        const datalist = document.getElementById('lighting-system-suggestions');
        const savedValues = Array.from(datalist.options).map(option => option.value);
        
        // Filter suggestions based on input
        const filteredSuggestions = savedValues.filter(value => 
            value.toLowerCase().includes(inputValue.toLowerCase())
        );
        
        // Show suggestions
        if (filteredSuggestions.length > 0 || inputValue.trim() !== '') {
            suggestionsContainer.innerHTML = '';
            
            // Add filtered saved values
            filteredSuggestions.forEach(suggestion => {
                const suggestionElement = document.createElement('div');
                suggestionElement.className = 'suggestion-item';
                suggestionElement.textContent = suggestion;
                suggestionElement.addEventListener('click', () => {
                    lightingInput.value = suggestion;
                    this.hideSuggestions();
                });
                suggestionsContainer.appendChild(suggestionElement);
            });
            
            // Add current input as "new value" if it's not empty and not in saved values
            if (inputValue.trim() !== '' && !savedValues.includes(inputValue.trim())) {
                const newValueElement = document.createElement('div');
                newValueElement.className = 'suggestion-item new-value';
                newValueElement.innerHTML = `<i class="fa-solid fa-plus-circle"></i> "${inputValue}" (neu)`;
                newValueElement.addEventListener('click', () => {
                    lightingInput.value = inputValue.trim();
                    this.hideSuggestions();
                });
                suggestionsContainer.appendChild(newValueElement);
            }
            
            suggestionsContainer.style.display = 'block';
            if (container) {
                container.classList.add('active');
            }
        } else {
            this.hideSuggestions();
        }
    }
    
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('lighting-system-suggestions-container');
        const lightingInput = document.getElementById('plant-lighting');
        const container = lightingInput?.closest('.lighting-system-container');
        
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
        
        if (container) {
            container.classList.remove('active');
        }
    }
    
    getOrCreateSuggestionsContainer() {
        let container = document.getElementById('lighting-system-suggestions-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'lighting-system-suggestions-container';
            container.className = 'suggestions-container';
            
            const lightingInput = document.getElementById('plant-lighting');
            const parentContainer = lightingInput.closest('.mb-3');
            
            if (parentContainer) {
                parentContainer.classList.add('lighting-system-container');
                parentContainer.appendChild(container);
            }
        }
        
        return container;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new LightingSystemAutocomplete();
});

// Export for use in other modules
window.LightingSystemAutocomplete = LightingSystemAutocomplete; 