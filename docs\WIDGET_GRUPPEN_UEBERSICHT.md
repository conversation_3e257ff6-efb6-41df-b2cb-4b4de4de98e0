# 🎛️ Widget-Gruppen Übersicht - Grow-Tagebuch

**Erstellt:** 13.07.2025  
**Status:** Konzeptphase  
**Zweck:** Zentrale Übersicht aller Widget-Gruppen und ihrer Module

---

## 📋 **Übersicht der Widget-Gruppen**

Das Grow-Tagebuch ist in **5 Haupt-Widget-Gruppen** organisiert, die thematisch zusammengefasste Funktionalitäten bieten:

| Widget-Gruppe | Enthaltene Module | Status |
|---------------|-------------------|--------|
| **🌱 Vegetation-Widget** | Substrat, Gießen, Klima, Training, Nährstoffe | ✅ Konzept vorhanden |
| **🌸 Blüte-Widget** | Trichome, Flush, Blütezeit | ✅ Implementiert |
| **🧬 Genetik/Strain-Widget** | <PERSON><PERSON>, Genetik-Datenbank | ✅ Konzept vorhanden |
| **🐛 Schädlings-Widget** | Pest & Disease Management | ✅ Konzept vorhanden |
| **🌾 Post-Harvest Widget** | Trocknung, Curing, Langzeit-Handling | ⏳ Geplant |

---

## 🌱 **1. Vegetations-Management Widget**

### **Hauptfunktion:**
Umfassendes Management der Wachstumsphase mit Substratanalyse, Bewässerung, Klimamanagement, Nährstoff-Tracking und Trainingszeitpunkten.

### **Widget-ID:** `vegetationManagementWidget`

### **Tab-Struktur:**
1. **📊 Übersicht** - Phasen-Status, Wachstumsdaten, Empfehlungen
2. **💧 Gießen & Klima** - Bewässerungsplan, VPD-Optimierung, Klima-Daten
3. **🌿 Nährstoffe** - EC/pH-Tracking, Dünger-Empfehlungen, Drain-Analyse
4. **✂️ Training** - Trainingszeitpunkte, Eingriffs-Empfehlungen, Stress-Management
5. **🔍 Früh-Indikatoren** - Wachstumsanalyse, Stress-Erkennung, Prognosen

### **Enthaltene Module:**

#### **A. Substratdatenbank & Gießverhalten**
- **Datei:** `_konzepte/SUBSTRATDATENBANK_GIEßVERHALTEN_MODUL_KONZEPT.md`
- **API:** `GET /api/substrates`, `POST /api/substrate/set`
- **Features:**
  - Substrat-spezifische Gießfrequenzen
  - pH/EC-Zielbereiche je Substrat
  - Drain-Ziele und Empfehlungen
  - Topfgewicht-Tracking

#### **B. Bewässerungs-Analyse & Planung**
- **Datei:** `_konzepte/BEWAESSERUNGS_WIDGET_KONZEPT.md`
- **API:** `POST /api/watering/analyse`, `GET /api/watering/recommendation`
- **Features:**
  - Phase-spezifische Gießmengen
  - Strain-spezifische Frequenzen
  - EC/pH-basierte Berechnungen
  - Drainage-Analyse

#### **C. Klimamanagement (VPD/Temperatur/Luftfeuchte)**
- **Datei:** `_konzepte/KLIMAMANAGEMENT_MODUL_KONZEPT.md`
- **API:** `GET /api/climate`, `POST /api/climate/update`
- **Features:**
  - Echtzeit-VPD-Berechnung
  - Strain-spezifische Zielwerte
  - Optimierungsempfehlungen
  - Sensor-Integration

#### **D. Nährstoff-Guidelines & EC/pH-Tracking**
- **Datei:** `_reports/NAEHRSTOFF_WIDGET_IMPLEMENTATION.md`
- **API:** `POST /api/nutrients/analyse`, `GET /api/nutrients/recommendations`
- **Features:**
  - Phase-spezifische EC/pH-Ziele
  - Strain-spezifische Anpassungen
  - Automatische Diagnose
  - Korrekturstrategien

#### **E. Stress-Management & Trainingszeitpunkt**
- **Datei:** `_konzepte/STRESS_MANAGEMENT_WIDGET_KONZEPT.md`
- **API:** `POST /api/stress/analyse`, `GET /api/stress/recommendations`
- **Features:**
  - Echtzeit-Stressbewertung
  - Strain- & Phasenanpassung
  - Eingriffs-Analyse
  - Warnsystem

#### **F. Trainingszeitpunkt- & Eingriffs-Widget**
- **Datei:** `_konzepte/TRAININGS_WIDGET_KONZEPT.md`
- **API:** `POST /api/training/analyse`, `GET /api/training/allowed-methods`
- **Features:**
  - Optimale Trainingszeitpunkte
  - Strain-spezifische Methoden
  - Stress-Level-Integration
  - Verlauf-Tracking

### **Guidelines-Integration:**
- `static/data/stress-guidelines.json`
- `static/data/klimamanagement-guidelines.json`
- `static/data/substrates.json` (geplant)
- `static/data/training-guidelines.json` (geplant)

---

## 🌸 **2. Blüte-Management Widget**

### **Hauptfunktion:**
Integriertes Blüte-Management-System für Trichome-Analyse, Flush-Trigger und Blüte-Zeitmanagement.

### **Widget-ID:** `blueteManagementWidget`

### **Tab-Struktur:**
1. **📊 Überblick** - Blüte-Status, Zeitlinie, Empfehlungen
2. **📍 Marker** - Event-Marker mit Filter (Kategorie, Wichtigkeit)
3. **🔬 Trichome** - Trichome-Analyse, Guidelines, Empfehlungen
4. **💡 Beleuchtung** - Blüte-spezifische Beleuchtungssteuerung
5. **🚰 Flush-Trigger** - Automatische & manuelle Flush-Auslösung
6. **⏰ Prognose** - Erntezeit-Prognose mit Countdown

### **Enthaltene Module:**

#### **A. Trichome-Analyse**
- **Status:** ✅ Implementiert
- **Features:**
  - Persistente Speicherung von Trichome-Beobachtungen
  - Automatische Auswertung und Reife-Bewertung
  - Strain-spezifische Zielwerte

#### **B. Flush & Erntefenster**
- **Status:** ✅ Implementiert
- **Features:**
  - Automatische Flush-Auslösung
  - Manuelle Auslösung mit Begründung
  - Status-Tracking mit Countdown

#### **C. Blüte-Zeitmanagement**
- **Status:** ✅ Implementiert
- **Features:**
  - Phasen-Tracking
  - Event-Marker-System
  - Zeitlinie-Integration

#### **D. Beleuchtungs-Management** ⭐ NEU
- **Status:** ✅ Implementiert (13.07.2025)
- **Features:**
  - PPFD/DLI-Berechnung für Blütephasen
  - Strain-spezifische Beleuchtungsrichtlinien
  - Einstellbare Lampenparameter
  - Automatische Photoperiode-Anpassung
  - Farbtemperatur-Optimierung für Blütenbildung

### **Guidelines-Integration:**
- `static/data/bluete-management-guidelines.json`
- `static/data/bluete-zeitmanagement-guidelines.json`

---

## 🧬 **3. Genetik & Strain-Analyse Widget**

### **Hauptfunktion:**
Strain-spezifische Analyse, Genetik-Vergleich und historische Grow-Vergleiche.

### **Widget-ID:** `strainAnalysisWidget`

### **Tab-Struktur:**
1. **🧬 Strain-Profil** - Genetik-Daten, Eigenschaften, Toleranzen
2. **📊 Vergleich** - Strain-Vergleiche, Performance-Analyse
3. **📈 Historie** - Historische Grow-Daten, Erfolgsfaktoren
4. **🎯 Empfehlungen** - Strain-spezifische Guidelines

### **Enthaltene Module:**

#### **A. Strain-Verhaltensdatenbank**
- **Datei:** `_konzepte/STRAIN_VERHALTENSDATENBANK_WIDGET_KONZEPT.md`
- **API:** `GET /api/strains`, `GET /api/strain/:id`
- **Features:**
  - Strain-spezifische Profile
  - Toleranz-Datenbank
  - Wachstumsprofile
  - Sensorische Eigenschaften

#### **B. Historische Grow-Vergleiche**
- **Status:** ⏳ Geplant
- **Features:**
  - Performance-Vergleiche
  - Erfolgsfaktor-Analyse
  - Strain-Optimierung

### **Guidelines-Integration:**
- `static/data/strain-profiles.json` (geplant)
- `static/data/genetics-database.json` (geplant)

---

## 🐛 **4. Schädlings- & Krankheitsanalyse Widget**

### **Hauptfunktion:**
Intelligentes Diagnose-System für Pflanzenprobleme und -krankheiten mit Bildanalyse.

### **Widget-ID:** `pestDiseaseAnalyzerWidget`

### **Tab-Struktur:**
1. **🔍 Symptom-Analyse** - Symptom-Auswahl, Bild-Upload
2. **🐛 Schädlings-DB** - Schädlings-Datenbank, Erkennung
3. **🦠 Krankheitsbilder** - Krankheits-Datenbank, Diagnose
4. **💊 Behandlungen** - Gegenmaßnahmen, Prophylaxe
5. **📊 Verlauf** - Behandlungsverlauf, Erfolgs-Tracking

### **Enthaltene Module:**

#### **A. Schädlingserkennung**
- **Datei:** `_konzepte/SCHAEDLINGS_KRANKHEITS_WIDGET_KONZEPT.md`
- **API:** `POST /api/diagnosis/analyse`, `GET /api/diagnosis/treatment`
- **Features:**
  - Symptom-basierte Diagnose
  - Bildanalyse (optional)
  - Wahrscheinlichkeitsbewertung
  - Gegenmaßnahmen

#### **B. Krankheitsbilder**
- **Status:** ✅ Konzept vorhanden
- **Features:**
  - Pilz- & Bakterienprofile
  - Nährstoffmängel-Erkennung
  - Behandlungsstrategien

#### **C. Prophylaxe & Gegenmaßnahmen**
- **Status:** ✅ Konzept vorhanden
- **Features:**
  - Vorbeugestrategien
  - Behandlungsprotokolle
  - Erfolgs-Tracking

### **Guidelines-Integration:**
- `static/data/symptom-profiles.json` (geplant)
- `static/data/pest-database.json` (geplant)
- `static/data/treatment-protocols.json` (geplant)

---

## 🌾 **5. Post-Harvest Management Widget**

### **Hauptfunktion:**
Umfassendes Management der Trocknung, Curing und Langzeit-Lagerung.

### **Widget-ID:** `postHarvestManagementWidget`

### **Tab-Struktur:**
1. **🌡️ Trocknung** - Trocknungsbedingungen, Verlauf, Empfehlungen
2. **🫙 Curing** - Curing-Protokolle, Feuchtigkeits-Tracking
3. **📦 Endlagerung** - Langzeit-Lagerung, Bedingungen
4. **📊 Statistiken** - Post-Harvest-Daten, Qualitäts-Tracking

### **Enthaltene Module:**

#### **A. Trocknung & Curing**
- **Status:** ⏳ Geplant
- **Features:**
  - Trocknungsbedingungen-Tracking
  - Curing-Protokolle
  - Feuchtigkeits-Monitoring
  - Qualitäts-Kontrolle

#### **B. Post-Harvest Guidelines**
- **Status:** ⏳ Geplant
- **Features:**
  - Strain-spezifische Protokolle
  - Optimale Bedingungen
  - Qualitäts-Erhaltung

### **Guidelines-Integration:**
- `static/data/post-harvest-guidelines.json` (geplant)
- `static/data/curing-protocols.json` (geplant)

---

## 🎛️ **Dashboard-Integration**

### **Zentrale Dashboard-Ansicht**
- **Datei:** `_konzepte/ZENTRALES_GROW_DASHBOARD_MODUL_KONZEPT.md`
- **Funktion:** Übergeordnetes Widget, das alle Phasen modular zusammenführt
- **Features:**
  - Schnellzugriff auf alle Widget-Gruppen
  - Live-Status aller Pflanzen
  - Warnungen und Empfehlungen
  - Drag & Drop Widget-Manager

### **Widget-Manager-System**
- **Datei:** `static/scripts/core/widget-manager.js`
- **Features:**
  - Modulare Widget-Aktivierung
  - Layout-Persistierung
  - Konfigurations-Management

---

## 📊 **Implementierungs-Status**

| Widget-Gruppe | Konzept | Guidelines | Frontend | Backend | Status |
|---------------|---------|------------|----------|---------|--------|
| **Vegetation-Widget** | ✅ | ✅ | ⏳ | ⏳ | In Entwicklung |
| **Blüte-Widget** | ✅ | ✅ | ✅ | ✅ | ✅ Implementiert |
| **Genetik-Widget** | ✅ | ⏳ | ⏳ | ⏳ | Konzept vorhanden |
| **Schädlings-Widget** | ✅ | ⏳ | ⏳ | ⏳ | Konzept vorhanden |
| **Post-Harvest-Widget** | ⏳ | ⏳ | ⏳ | ⏳ | Geplant |

---

## 🚀 **Nächste Schritte**

### **Phase 1: Vegetation-Widget (Priorität 1)**
1. **Substratdatenbank** implementieren
2. **Bewässerungs-Widget** erweitern
3. **Klimamanagement** integrieren
4. **Nährstoff-Tracking** vervollständigen
5. **Stress-Management** implementieren

### **Phase 2: Genetik-Widget (Priorität 2)**
1. **Strain-Datenbank** aufbauen
2. **Vergleichs-Funktionen** entwickeln
3. **Historie-Integration** implementieren

### **Phase 3: Schädlings-Widget (Priorität 3)**
1. **Symptom-Datenbank** erstellen
2. **Diagnose-Engine** entwickeln
3. **Behandlungs-Protokolle** implementieren

### **Phase 4: Post-Harvest-Widget (Priorität 4)**
1. **Trocknungs-Protokolle** definieren
2. **Curing-System** entwickeln
3. **Lagerungs-Management** implementieren

---

## 📋 **Technische Anforderungen**

### **Backend-API-Struktur:**
```python
# Widget-Gruppen API-Endpunkte
GET /api/widgets/vegetation/status/<plant_id>
GET /api/widgets/bluete/status/<plant_id>
GET /api/widgets/genetics/status/<plant_id>
GET /api/widgets/pests/status/<plant_id>
GET /api/widgets/post-harvest/status/<plant_id>
```

### **Frontend-Widget-Struktur:**
```javascript
// Widget-Gruppen Manager
window.VegetationWidget = new VegetationManagementWidget();
window.BlueteWidget = new BlueteManagementWidget();
window.GeneticsWidget = new StrainAnalysisWidget();
window.PestsWidget = new PestDiseaseAnalyzerWidget();
window.PostHarvestWidget = new PostHarvestManagementWidget();
```

### **Guidelines-Integration:**
- **JSON-basierte Guidelines** für alle Widget-Gruppen
- **Strain-spezifische Anpassungen** automatisch
- **Phase-spezifische Empfehlungen** dynamisch
- **Modulare Erweiterbarkeit** für neue Guidelines

---

**Diese Widget-Gruppen-Struktur bietet eine übersichtliche und benutzerfreundliche Organisation aller Grow-Management-Funktionen.** 