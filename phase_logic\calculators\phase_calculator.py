#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase Calculator für das Grow-Tagebuch
Berechnet die aktuellen Phasen einer Pflanze automatisch
"""

from datetime import datetime, timedelta
from ..data.phase_definitions import PHASE_SUBSTAGES
from ..data.vpd_guidelines import calculate_vpd, get_vpd_status, get_vpd_recommendations

class PhaseCalculator:
    """Berechnet die aktuellen Phasen einer Pflanze automatisch"""
    
    def __init__(self):
        self.phase_substages = PHASE_SUBSTAGES
    
    def calculate_current_phase(self, start_date, flowering_date=None, current_date=None, strain_type='photoperiodic'):
        """
        Berechnet die aktuelle Phase einer Pflanze
        
        Args:
            start_date (str): Startdatum im Format 'YYYY-MM-DD'
            flowering_date (str, optional): Blütedatum im Format 'YYYY-MM-DD'
            current_date (str, optional): Aktuelles Datum im Format 'YYYY-MM-DD'
            strain_type (str): Sortentyp ('autoflowering' oder 'photoperiodic')
        
        Returns:
            dict: Aktuelle Phasen-Informationen
        """
        if current_date is None:
            current_date = datetime.now().strftime('%Y-%m-%d')
        
        start = datetime.strptime(start_date, '%Y-%m-%d')
        current = datetime.strptime(current_date, '%Y-%m-%d')
        
        # Tage seit Start
        days_since_start = (current - start).days
        
        if days_since_start < 0:
            return {
                'error': 'Startdatum liegt in der Zukunft',
                'days_since_start': days_since_start
            }
        
        # Autoflowering vs Photoperiodic Logik
        if strain_type == 'autoflowering':
            return self._calculate_autoflowering_phase(days_since_start)
        else:
            return self._calculate_photoperiodic_phase(start_date, flowering_date, current_date, days_since_start)
    
    def _calculate_autoflowering_phase(self, days_since_start):
        """
        Berechnet die Phase für Autoflowering-Sorten
        
        Autoflowering-Sorten haben typischerweise:
        - Keimung: 0-7 Tage
        - Vegetativ: 7-21 Tage (2 Wochen)
        - Blüte: 21-70 Tage (7 Wochen)
        - Gesamtdauer: ~10-12 Wochen
        """
        # Keimung (0-7 Tage)
        if days_since_start <= 7:
            return self._get_germination_phase(days_since_start, is_autoflowering=True)
        
        # Vegetative Phase (7-21 Tage)
        if days_since_start <= 21:
            return self._get_vegetative_phase(days_since_start - 7, is_autoflowering=True)
        
        # Blüte-Phase (21+ Tage)
        flowering_days = days_since_start - 21
        return self._get_flowering_phase(flowering_days, is_autoflowering=True)
    
    def _calculate_photoperiodic_phase(self, start_date, flowering_date, current_date, days_since_start):
        """
        Berechnet die Phase für photoperiodische Sorten (Standard-Logik)
        """
        # Keimung (0-7 Tage)
        if days_since_start <= 7:
            return self._get_germination_phase(days_since_start)
        
        # Vegetative Phase (8+ Tage, bis Blüte beginnt)
        if flowering_date is None:
            # Standard: 6 Wochen vegetative Phase
            veg_end_day = 42  # 6 Wochen * 7 Tage
            if days_since_start <= veg_end_day:
                return self._get_vegetative_phase(days_since_start - 7)
            else:
                # Blüte ohne explizites Datum
                flowering_start_day = veg_end_day
                flowering_days = days_since_start - flowering_start_day
                return self._get_flowering_phase(flowering_days)
        else:
            # Mit explizitem Blütedatum
            flowering = datetime.strptime(flowering_date, '%Y-%m-%d')
            start = datetime.strptime(start_date, '%Y-%m-%d')
            flowering_start_day = (flowering - start).days
            
            if days_since_start < flowering_start_day:
                # Noch in vegetativer Phase
                return self._get_vegetative_phase(days_since_start - 7)
            else:
                # In Blüte-Phase
                flowering_days = days_since_start - flowering_start_day
                return self._get_flowering_phase(flowering_days)
    
    def _get_germination_phase(self, days, is_autoflowering=False):
        """Berechnet die Keimungsphase"""
        days_to_next = max(0, 7 - days)
        
        if is_autoflowering:
            next_phase = 'Vegetative Phase (Auto)'
            light_hours = {'min': 18, 'max': 20}  # Autoflowering: 18/6 oder 20/4
        else:
            next_phase = 'Vegetative Phase - Frühe Phase'
            light_hours = {'min': 18, 'max': 20}  # Photoperiodic: 18/6
        
        return {
            'main_phase': 'germination',
            'sub_phase': 'germination',
            'phase_name': 'Keimung',
            'days_in_phase': days,
            'total_days': days,
            'description': 'Erste Entwicklung, Wurzelbildung',
            'vpd_range': '0.4 – 0.8 kPa',
            'optimal_vpd': '0.6 kPa',
            'optimal_conditions': {
                'temperature': {'min': 22, 'max': 26},
                'humidity': {'min': 70, 'max': 80},
                'light_hours': light_hours
            },
            'next_phase': next_phase,
            'days_to_next': days_to_next,
            'is_autoflowering': is_autoflowering
        }
    
    def _get_vegetative_phase(self, days, is_autoflowering=False):
        """Berechnet die vegetative Phase"""
        if is_autoflowering:
            # Autoflowering: Kürzere vegetative Phase (2 Wochen)
            if days <= 7:
                sub_phase = 'early'
                phase_name = 'Vegetative Phase (Auto) - Frühe Phase'
                days_to_next = max(0, 7 - days)
                next_phase = 'Vegetative Phase (Auto) - Späte Phase'
            else:
                sub_phase = 'late'
                phase_name = 'Vegetative Phase (Auto) - Späte Phase'
                days_to_next = max(0, 14 - days)
                next_phase = 'Blüte (Auto) - Frühe Blüte'
            
            # Autoflowering-spezifische Bedingungen
            optimal_conditions = {
                'temperature': {'min': 22, 'max': 28},
                'humidity': {'min': 60, 'max': 70},
                'light_hours': {'min': 18, 'max': 20}  # 18/6 oder 20/4
            }
        else:
            # Photoperiodic: Standard vegetative Phase (3 Wochen)
            if days <= 7:
                sub_phase = 'early'
                phase_name = 'Vegetative Phase - Frühe Phase'
                days_to_next = max(0, 7 - days)
                next_phase = 'Vegetative Phase - Mittlere Phase'
            elif days <= 14:
                sub_phase = 'middle'
                phase_name = 'Vegetative Phase - Mittlere Phase'
                days_to_next = max(0, 14 - days)
                next_phase = 'Vegetative Phase - Späte Phase'
            else:
                sub_phase = 'late'
                phase_name = 'Vegetative Phase - Späte Phase'
                days_to_next = max(0, 21 - days)
                next_phase = 'Blüte - Frühe Blüte'
            
            substage_info = self.phase_substages['vegetative'][sub_phase]
            optimal_conditions = substage_info['optimal_conditions']
        
        return {
            'main_phase': 'vegetative',
            'sub_phase': f'vegetative_{sub_phase}',
            'phase_name': phase_name,
            'days_in_phase': days,
            'total_days': days + 7,  # +7 für Keimung
            'description': 'Vegetatives Wachstum' + (' (Autoflowering)' if is_autoflowering else ''),
            'vpd_range': '0.8 – 1.2 kPa',
            'optimal_vpd': '1.0 kPa',
            'optimal_conditions': optimal_conditions,
            'next_phase': next_phase,
            'days_to_next': days_to_next,
            'is_autoflowering': is_autoflowering
        }
    
    def _get_flowering_phase(self, days, is_autoflowering=False):
        """Berechnet die Blüte-Phase"""
        if is_autoflowering:
            # Autoflowering: Kürzere Blüte-Phase (7 Wochen)
            if days <= 14:
                sub_phase = 'early'
                phase_name = 'Blüte (Auto) - Frühe Blüte'
                days_to_next = max(0, 14 - days)
                next_phase = 'Blüte (Auto) - Mittlere Blüte'
            elif days <= 35:
                sub_phase = 'middle'
                phase_name = 'Blüte (Auto) - Mittlere Blüte'
                days_to_next = max(0, 35 - days)
                next_phase = 'Blüte (Auto) - Späte Blüte'
            else:
                sub_phase = 'late'
                phase_name = 'Blüte (Auto) - Späte Blüte'
                days_to_next = max(0, 49 - days)  # 7 Wochen Blüte
                next_phase = 'Ernte'
            
            # Autoflowering-spezifische Bedingungen
            optimal_conditions = {
                'temperature': {'min': 20, 'max': 26},
                'humidity': {'min': 45, 'max': 55},
                'light_hours': {'min': 18, 'max': 20}  # 18/6 oder 20/4
            }
        else:
            # Photoperiodic: Standard Blüte-Phase (9 Wochen)
            if days <= 14:
                sub_phase = 'early'
                phase_name = 'Blüte - Frühe Blüte'
                days_to_next = max(0, 14 - days)
                next_phase = 'Blüte - Mittlere Blüte'
            elif days <= 35:
                sub_phase = 'middle'
                phase_name = 'Blüte - Mittlere Blüte'
                days_to_next = max(0, 35 - days)
                next_phase = 'Blüte - Späte Blüte'
            else:
                sub_phase = 'late'
                phase_name = 'Blüte - Späte Blüte'
                days_to_next = max(0, 63 - days)  # 9 Wochen Blüte
                next_phase = 'Ernte'
            
            substage_info = self.phase_substages['flowering'][sub_phase]
            optimal_conditions = substage_info['optimal_conditions']
        
        return {
            'main_phase': 'flowering',
            'sub_phase': f'flowering_{sub_phase}',
            'phase_name': phase_name,
            'days_in_phase': days,
            'total_days': days,  # Tage in Blüte
            'description': 'Blütenbildung' + (' (Autoflowering)' if is_autoflowering else ''),
            'vpd_range': '1.0 – 1.4 kPa',
            'optimal_vpd': '1.2 kPa',
            'optimal_conditions': optimal_conditions,
            'next_phase': next_phase,
            'days_to_next': days_to_next,
            'is_autoflowering': is_autoflowering
        }
    
    def get_phase_timeline(self, start_date, flowering_date=None, total_weeks=16):
        """
        Erstellt eine Timeline aller Phasen
        
        Args:
            start_date (str): Startdatum
            flowering_date (str, optional): Blütedatum
            total_weeks (int): Gesamtdauer in Wochen
        
        Returns:
            list: Timeline mit allen Phasen
        """
        timeline = []
        start = datetime.strptime(start_date, '%Y-%m-%d')
        
        # Keimung (Woche 1)
        timeline.append({
            'week': 1,
            'start_date': start.strftime('%Y-%m-%d'),
            'end_date': (start + timedelta(days=6)).strftime('%Y-%m-%d'),
            'phase': 'germination',
            'phase_name': 'Keimung',
            'description': 'Erste Entwicklung, Wurzelbildung'
        })
        
        # Vegetative Phase
        veg_start = start + timedelta(days=7)
        if flowering_date:
            flowering = datetime.strptime(flowering_date, '%Y-%m-%d')
            veg_weeks = (flowering - veg_start).days // 7
        else:
            veg_weeks = 6  # Standard: 6 Wochen
        
        for week in range(2, 2 + veg_weeks):
            week_start = veg_start + timedelta(days=(week-2)*7)
            week_end = week_start + timedelta(days=6)
            
            if week <= 3:
                sub_phase = 'early'
                phase_name = 'Vegetative Phase - Frühe Phase'
            elif week <= 4:
                sub_phase = 'middle'
                phase_name = 'Vegetative Phase - Mittlere Phase'
            else:
                sub_phase = 'late'
                phase_name = 'Vegetative Phase - Späte Phase'
            
            timeline.append({
                'week': week,
                'start_date': week_start.strftime('%Y-%m-%d'),
                'end_date': week_end.strftime('%Y-%m-%d'),
                'phase': f'vegetative_{sub_phase}',
                'phase_name': phase_name,
                'description': self.phase_substages['vegetative'][sub_phase]['description']
            })
        
        # Blüte-Phase
        flower_start = veg_start + timedelta(weeks=veg_weeks)
        remaining_weeks = total_weeks - veg_weeks - 1  # -1 für Keimung
        
        for week in range(2 + veg_weeks, 2 + veg_weeks + remaining_weeks):
            week_start = flower_start + timedelta(days=(week-2-veg_weeks)*7)
            week_end = week_start + timedelta(days=6)
            
            if week <= 2 + veg_weeks + 2:
                sub_phase = 'early'
                phase_name = 'Blüte - Frühe Blüte'
            elif week <= 2 + veg_weeks + 5:
                sub_phase = 'middle'
                phase_name = 'Blüte - Mittlere Blüte'
            else:
                sub_phase = 'late'
                phase_name = 'Blüte - Späte Blüte'
            
            timeline.append({
                'week': week,
                'start_date': week_start.strftime('%Y-%m-%d'),
                'end_date': week_end.strftime('%Y-%m-%d'),
                'phase': f'flowering_{sub_phase}',
                'phase_name': phase_name,
                'description': self.phase_substages['flowering'][sub_phase]['description']
            })
        
        return timeline
    
    def calculate_vpd_for_phase(self, temperature, humidity, phase):
        """
        Berechnet VPD für eine bestimmte Phase
        
        Args:
            temperature (float): Temperatur in Celsius
            humidity (float): Luftfeuchtigkeit in Prozent
            phase (str): Aktuelle Phase
        
        Returns:
            dict: VPD-Informationen
        """
        vpd = calculate_vpd(temperature, humidity)
        status = get_vpd_status(vpd, phase)
        recommendations = get_vpd_recommendations(phase)
        
        return {
            'vpd': vpd,
            'status': status,
            'recommendations': recommendations
        } 