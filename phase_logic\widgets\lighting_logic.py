"""
Lighting Logic Module
Handles lighting plan calculations and recommendations
"""

from typing import Dict, Any, List

class LightingLogic:
    """Lighting plan calculation and optimization logic"""
    
    def __init__(self):
        self.phase_lighting = {
            'germination': {
                'photoperiod': '18/6',
                'intensity': 'low',
                'description': 'Sanftes Licht für Keimung'
            },
            'vegetative_early': {
                'photoperiod': '18/6',
                'intensity': 'medium',
                'description': 'Moderate Beleuchtung für frühes Wachstum'
            },
            'vegetative_middle': {
                'photoperiod': '18/6',
                'intensity': 'high',
                'description': 'Starke Beleuchtung für kräftiges Wachstum'
            },
            'vegetative_late': {
                'photoperiod': '18/6',
                'intensity': 'high',
                'description': 'Maximale Beleuchtung vor Blüte'
            },
            'flowering_early': {
                'photoperiod': '12/12',
                'intensity': 'high',
                'description': 'Blütenbildung mit starker Beleuchtung'
            },
            'flowering_middle': {
                'photoperiod': '12/12',
                'intensity': 'high',
                'description': 'Optimale Beleuchtung für Blütenentwicklung'
            },
            'flowering_late': {
                'photoperiod': '12/12',
                'intensity': 'medium',
                'description': 'Reduzierte Beleuchtung für Reifung'
            },
            'flush': {
                'photoperiod': '12/12',
                'intensity': 'low',
                'description': 'Minimale Beleuchtung für finale Reifung'
            }
        }
        
        self.light_types = {
            'led': {
                'name': 'LED',
                'efficiency': 'high',
                'heat_output': 'low',
                'lifespan': 'long',
                'cost': 'medium'
            },
            'hps': {
                'name': 'HPS (High Pressure Sodium)',
                'efficiency': 'medium',
                'heat_output': 'high',
                'lifespan': 'medium',
                'cost': 'low'
            },
            'mh': {
                'name': 'MH (Metal Halide)',
                'efficiency': 'medium',
                'heat_output': 'high',
                'lifespan': 'medium',
                'cost': 'low'
            },
            'cmh': {
                'name': 'CMH (Ceramic Metal Halide)',
                'efficiency': 'high',
                'heat_output': 'medium',
                'lifespan': 'long',
                'cost': 'high'
            }
        }
    
    def calculate_lighting_plan(self, phase: str, strain_type: str = 'photoperiodic', light_type: str = 'led', 
                               lamp_power_w: float = 240.0, lamp_distance_cm: float = 25.0, 
                               light_hours: int = 18, ppfd_measured: float = None,
                               color_temperature_k: int = 3500, power_percentage: int = 100,
                               additional_wavelengths: str = '') -> Dict[str, Any]:
        """Calculate lighting plan for a specific phase and setup"""
        
        if phase not in self.phase_lighting:
            return {
                'error': 'Phase nicht unterstützt',
                'phase': phase
            }
        
        # Get base lighting data for phase
        phase_data = self.phase_lighting[phase]
        
        # Adjust for strain type
        adjusted_data = self.adjust_for_strain_type(phase_data, strain_type)
        
        # Add light type information
        light_info = self.light_types.get(light_type, self.light_types['led'])
        
        # Berechne tatsächliche Leistung basierend auf Prozent
        actual_power_w = lamp_power_w * (power_percentage / 100.0)
        
        # Calculate actual PPFD based on lamp parameters (mit tatsächlicher Leistung)
        calculated_ppfd = self.calculate_ppfd(actual_power_w, lamp_distance_cm, light_type, color_temperature_k, additional_wavelengths)
        
        # Use measured PPFD if provided, otherwise use calculated
        actual_ppfd = ppfd_measured if ppfd_measured is not None else calculated_ppfd
        
        # Generate schedule
        schedule = self.generate_lighting_schedule(phase, adjusted_data, strain_type)
        
        # Frontend-kompatible Struktur
        return {
            'phase': phase,
            'strain_type': strain_type,
            'light_type': light_type,
            'photoperiod': adjusted_data['photoperiod'],
            'intensity': adjusted_data['intensity'],
            'description': adjusted_data['description'],
            'light_info': light_info,
            'schedule': schedule,
            'current': {
                'lamp_power_w': lamp_power_w,
                'lamp_distance_cm': lamp_distance_cm,
                'light_hours': light_hours,
                'ppfd_calculated': calculated_ppfd,
                'ppfd_measured': ppfd_measured,
                'ppfd_actual': actual_ppfd,
                'color_temperature_k': color_temperature_k,
                'power_percentage': power_percentage,
                'additional_wavelengths': additional_wavelengths,
                'actual_power_w': actual_power_w
            },
            'recommendations': {
                'ppfd_target': self.get_ppfd_target(phase),
                'ppfd_range': self.get_ppfd_range(phase),
                'light_hours': self.get_light_hours(phase, strain_type),
                'intensity': adjusted_data['intensity'],
                'distance': self.get_light_distance(phase, light_type),
                'note': self.get_phase_lighting_note(phase, strain_type)
            }
        }
    
    def adjust_for_strain_type(self, phase_data: Dict[str, Any], strain_type: str) -> Dict[str, Any]:
        """Adjust lighting data for different strain types"""
        
        adjusted_data = phase_data.copy()
        
        if strain_type == 'autoflowering':
            # Autoflowering strains don't need photoperiod changes
            if '12/12' in adjusted_data['photoperiod']:
                adjusted_data['photoperiod'] = '18/6'
                adjusted_data['description'] += ' (Autoflowering - konstante Beleuchtung)'
        
        return adjusted_data
    
    def generate_lighting_schedule(self, phase: str, phase_data: Dict[str, Any], strain_type: str) -> List[Dict[str, Any]]:
        """Generate detailed lighting schedule"""
        
        schedule = []
        photoperiod = phase_data['photoperiod']
        hours_on, hours_off = map(int, photoperiod.split('/'))
        
        # Generate 7-day schedule
        for day in range(1, 8):
            schedule.append({
                'day': f'Tag {day}',
                'hours_on': hours_on,
                'hours_off': hours_off,
                'photoperiod': photoperiod,
                'intensity': phase_data['intensity'],
                'description': phase_data['description'],
                'notes': self.get_schedule_notes(phase, strain_type)
            })
        
        return schedule
    
    def get_schedule_notes(self, phase: str, strain_type: str) -> str:
        """Get specific notes for lighting schedule"""
        
        notes = {
            'germination': 'Sanftes Licht, keine direkte Sonneneinstrahlung',
            'vegetative_early': 'Langsam Intensität erhöhen',
            'vegetative_middle': 'Optimale Wachstumsbedingungen',
            'vegetative_late': 'Pflanzen für Blüte vorbereiten',
            'flowering_early': 'Lichtzyklus auf 12/12 umstellen',
            'flowering_middle': 'Maximale Blütenentwicklung',
            'flowering_late': 'Langsam Intensität reduzieren',
            'flush': 'Minimale Beleuchtung für finale Reifung'
        }
        
        base_note = notes.get(phase, 'Standard-Beleuchtung')
        
        if strain_type == 'autoflowering':
            base_note += ' (Autoflowering: konstante Beleuchtung)'
        
        return base_note
    
    def calculate_plant_lighting_schedule(self, plant: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate lighting schedule for a specific plant"""
        
        # Extract plant data
        strain_type = plant.get('strain_type', 'photoperiodic')
        light_type = plant.get('lighting_system', 'led')
        
        # Get current phase (simplified - in practice this would come from phase logic)
        current_phase = 'vegetative_middle'  # Default
        
        # Calculate lighting plan
        plan = self.calculate_lighting_plan(current_phase, strain_type, light_type)
        
        return {
            'plant_id': plant.get('id'),
            'plant_name': plant.get('plant_name'),
            'current_phase': current_phase,
            'lighting_plan': plan,
            'last_light_change': None,  # Would come from database
            'next_light_change': None   # Would be calculated
        }
    
    def get_available_light_types(self) -> List[Dict[str, Any]]:
        """Get list of available light types with descriptions"""
        return [
            {
                'id': 'led',
                'name': 'LED',
                'description': 'Energieeffiziente Beleuchtung mit niedriger Wärmeentwicklung',
                'efficiency': self.light_types['led']['efficiency'],
                'heat_output': self.light_types['led']['heat_output'],
                'lifespan': self.light_types['led']['lifespan'],
                'cost': self.light_types['led']['cost']
            },
            {
                'id': 'hps',
                'name': 'HPS (High Pressure Sodium)',
                'description': 'Traditionelle Beleuchtung mit hoher Wärmeentwicklung',
                'efficiency': self.light_types['hps']['efficiency'],
                'heat_output': self.light_types['hps']['heat_output'],
                'lifespan': self.light_types['hps']['lifespan'],
                'cost': self.light_types['hps']['cost']
            },
            {
                'id': 'mh',
                'name': 'MH (Metal Halide)',
                'description': 'Ideale Beleuchtung für vegetative Phase',
                'efficiency': self.light_types['mh']['efficiency'],
                'heat_output': self.light_types['mh']['heat_output'],
                'lifespan': self.light_types['mh']['lifespan'],
                'cost': self.light_types['mh']['cost']
            },
            {
                'id': 'cmh',
                'name': 'CMH (Ceramic Metal Halide)',
                'description': 'Hochwertige Beleuchtung mit ausgezeichnetem Spektrum',
                'efficiency': self.light_types['cmh']['efficiency'],
                'heat_output': self.light_types['cmh']['heat_output'],
                'lifespan': self.light_types['cmh']['lifespan'],
                'cost': self.light_types['cmh']['cost']
            }
        ]
    
    def get_strain_lighting_recommendations(self, strain_type: str) -> Dict[str, Any]:
        """Get lighting recommendations based on strain type"""
        
        if strain_type == 'autoflowering':
            return {
                'photoperiod': '18/6 oder 20/4',
                'note': 'Autoflowering-Sorten benötigen keine Lichtzyklus-Änderung',
                'advantages': ['Einfacher zu handhaben', 'Konsistente Beleuchtung'],
                'tips': [
                    'Konstante 18/6 oder 20/4 Beleuchtung verwenden',
                    'Keine Lichtzyklus-Änderung nötig',
                    'Intensität kann während der Blüte reduziert werden'
                ]
            }
        else:  # photoperiodic
            return {
                'photoperiod': '18/6 → 12/12',
                'note': 'Lichtzyklus muss für Blüte geändert werden',
                'advantages': ['Mehr Kontrolle', 'Längere vegetative Phase möglich'],
                'tips': [
                    '18/6 für vegetative Phase',
                    '12/12 für Blütephase',
                    'Langsam umstellen für bessere Ergebnisse'
                ]
            }
    
    def get_lighting_recommendations(self, phase: str, strain_type: str, light_type: str) -> Dict[str, Any]:
        """Get comprehensive lighting recommendations"""
        
        recommendations = {
            'general': [
                'Lichtabstand regelmäßig kontrollieren',
                'Temperatur unter der Lampe überwachen',
                'Lampen regelmäßig reinigen'
            ],
            'phase_specific': self.get_phase_specific_recommendations(phase),
            'strain_specific': self.get_strain_lighting_recommendations(strain_type),
            'light_type_specific': self.get_light_type_recommendations(light_type)
        }
        
        return recommendations
    
    def get_phase_specific_recommendations(self, phase: str) -> List[str]:
        """Get phase-specific lighting recommendations"""
        
        phase_recommendations = {
            'germination': [
                'Sehr sanftes Licht verwenden',
                'Direkte Sonneneinstrahlung vermeiden',
                'Lichtabstand: 60-80cm'
            ],
            'vegetative_early': [
                'Langsam Intensität erhöhen',
                'Lichtabstand: 40-60cm',
                '18/6 Beleuchtung beibehalten'
            ],
            'vegetative_middle': [
                'Volle Intensität für optimales Wachstum',
                'Lichtabstand: 30-50cm',
                'Temperatur kontrollieren'
            ],
            'vegetative_late': [
                'Pflanzen für Blüte vorbereiten',
                'Lichtabstand: 30-50cm',
                'Nächste Phase planen'
            ],
            'flowering_early': [
                'Lichtzyklus auf 12/12 umstellen (Autoflowers: 18/6 beibehalten)',
                'Lichtabstand: 30-50cm',
                'Blütenbildung fördern'
            ],
            'flowering_middle': [
                'Optimale Beleuchtung für Blütenentwicklung',
                'Lichtabstand: 30-50cm',
                'Temperatur und Luftfeuchte kontrollieren'
            ],
            'flowering_late': [
                'Langsam Intensität reduzieren',
                'Lichtabstand: 40-60cm',
                'Reifung fördern'
            ],
            'flush': [
                'Minimale Beleuchtung',
                'Lichtabstand: 50-70cm',
                'Finale Reifung'
            ]
        }
        
        return phase_recommendations.get(phase, ['Standard-Beleuchtung verwenden'])
    
    def get_light_type_recommendations(self, light_type: str) -> List[str]:
        """Get light type specific recommendations"""
        
        light_recommendations = {
            'led': [
                'Energieeffizient und kühl',
                'Lange Lebensdauer',
                'Vollspektrum-LEDs bevorzugen'
            ],
            'hps': [
                'Hohe Wärmeentwicklung beachten',
                'Gute für Blütephase',
                'Regelmäßige Wartung nötig'
            ],
            'mh': [
                'Ideal für vegetative Phase',
                'Blau-spektrum betont',
                'Nicht für Blütephase verwenden'
            ],
            'cmh': [
                'Ausgezeichnetes Spektrum',
                'Gute für alle Phasen',
                'Höhere Anschaffungskosten'
            ]
        }
        
        return light_recommendations.get(light_type, ['Standard-Empfehlungen befolgen']) 

    def get_ppfd_target(self, phase: str) -> str:
        """Get PPFD target for phase"""
        ppfd_targets = {
            'germination': '100-200',
            'vegetative_early': '300-400',
            'vegetative_middle': '400-600',
            'vegetative_late': '500-700',
            'flowering_early': '600-800',
            'flowering_middle': '700-900',
            'flowering_late': '500-700',
            'flush': '300-500'
        }
        return ppfd_targets.get(phase, '400-600')
    
    def get_ppfd_range(self, phase: str) -> str:
        """Get PPFD range for phase"""
        ppfd_ranges = {
            'germination': '50-300',
            'vegetative_early': '200-500',
            'vegetative_middle': '300-700',
            'vegetative_late': '400-800',
            'flowering_early': '500-900',
            'flowering_middle': '600-1000',
            'flowering_late': '400-800',
            'flush': '200-600'
        }
        return ppfd_ranges.get(phase, '300-700')
    
    def get_light_hours(self, phase: str, strain_type: str) -> int:
        """Get light hours for phase and strain type"""
        if strain_type == 'autoflowering':
            return 18  # Autoflowering: konstante Beleuchtung
        
        # Photoperiodic strains
        if phase.startswith('vegetative'):
            return 18
        elif phase.startswith('flowering') or phase == 'flush':
            return 12
        else:
            return 18
    
    def get_light_distance(self, phase: str, light_type: str) -> str:
        """Get recommended light distance"""
        if phase == 'germination':
            return '60-80cm'
        elif phase in ['vegetative_early', 'flowering_late', 'flush']:
            return '40-60cm'
        else:
            return '30-50cm'
    
    def calculate_ppfd(self, lamp_power_w: float, lamp_distance_cm: float, light_type: str = 'led', color_temperature_k: int = 3500, additional_wavelengths: str = '') -> float:
        """Calculate PPFD based on lamp power, distance, type, color temperature and additional wavelengths"""
        
        try:
            # Efficiency factors for different light types (μmol/J)
            efficiency_factors = {
                'led': 2.0,      # Modern LED efficiency
                'hps': 1.4,      # HPS efficiency
                'mh': 1.2,       # MH efficiency
                'cmh': 1.6       # CMH efficiency
            }
            
            efficiency = efficiency_factors.get(light_type, 2.0)
            
            # Farbtemperatur-Bonus (wärmere Temperaturen sind besser für Pflanzen)
            color_temp_bonus = 1.0
            if 3000 <= color_temperature_k <= 4000:
                color_temp_bonus = 1.1  # Optimal für Blüte
            elif 4000 <= color_temperature_k <= 5000:
                color_temp_bonus = 1.05  # Gut für Wachstum
            elif 5000 <= color_temperature_k <= 6500:
                color_temp_bonus = 1.0   # Neutral
            elif color_temperature_k > 6500:
                color_temp_bonus = 0.95  # Kühler, weniger effizient für Pflanzen
            
            # Zusatz-Wellenlängen-Bonus
            wavelength_bonus = 1.0
            if additional_wavelengths:
                # HyperRed (660nm) - sehr effizient für Blüte
                if '660' in additional_wavelengths or 'hyperred' in additional_wavelengths.lower():
                    wavelength_bonus += 0.15
                # FarRed (730nm) - gut für Blüteinduktion
                if '730' in additional_wavelengths or 'farred' in additional_wavelengths.lower():
                    wavelength_bonus += 0.1
                # UV-LEDs - gut für Terpenproduktion
                if 'uv' in additional_wavelengths.lower() or '380' in additional_wavelengths or '400' in additional_wavelengths:
                    wavelength_bonus += 0.05
                # IR-LEDs - gut für Wärme
                if 'ir' in additional_wavelengths.lower() or '850' in additional_wavelengths:
                    wavelength_bonus += 0.03
            
            # Convert distance from cm to m
            distance_m = lamp_distance_cm / 100.0
            
            # Calculate PPFD using inverse square law with bonuses
            # PPFD = (Power * Efficiency * ColorTempBonus * WavelengthBonus) / (4 * π * distance²)
            import math
            ppfd = (lamp_power_w * efficiency * color_temp_bonus * wavelength_bonus) / (4 * math.pi * distance_m * distance_m)
            
            return round(ppfd, 0)
        except Exception as e:
            print(f"PPFD calculation error: {e}")
            return 400.0  # Fallback value
    
    def get_phase_lighting_note(self, phase: str, strain_type: str) -> str:
        """Get lighting note for specific phase"""
        notes = {
            'germination': 'Sanftes Licht für optimale Keimung',
            'vegetative_early': 'Moderate Beleuchtung für gesundes Wachstum',
            'vegetative_middle': 'Starke Beleuchtung für kräftiges Wachstum',
            'vegetative_late': 'Maximale Beleuchtung vor Blüte',
            'flowering_early': 'Starke Beleuchtung für Blütenbildung',
            'flowering_middle': 'Optimale Beleuchtung für Blütenentwicklung',
            'flowering_late': 'Reduzierte Beleuchtung für Reifung',
            'flush': 'Minimale Beleuchtung für finale Reifung'
        }
        
        base_note = notes.get(phase, 'Standard-Beleuchtung')
        
        if strain_type == 'autoflowering':
            base_note += ' (Autoflowering: konstante Beleuchtung)'
        
        return base_note

    def load_guidelines_from_json(self, phase: str) -> Dict[str, Any]:
        """Load lighting guidelines from JSON file for specific phase"""
        try:
            import json
            import os
            
            # Path to guidelines JSON file
            guidelines_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'static', 'data', 'lighting-guidelines.json'
            )
            
            if not os.path.exists(guidelines_path):
                return None
            
            with open(guidelines_path, 'r', encoding='utf-8') as f:
                guidelines_data = json.load(f)
            
            # Find matching phase
            guidelines = guidelines_data.get('lightingGuidelines', {})
            phases = guidelines.get('phases', [])
            
            matching_phase = None
            for phase_data in phases:
                if phase_data.get('phase') == phase:
                    matching_phase = phase_data
                    break
            
            if not matching_phase:
                return None
            
            return {
                'phase': phase,
                'guidelines': matching_phase,
                'tent_examples': guidelines.get('tentExamples', []),
                'meta': guidelines.get('meta', {})
            }
            
        except Exception as e:
            print(f"Fehler beim Laden der Lighting Guidelines: {e}")
            return None

    def calculate_lighting_with_guidelines(self, phase: str, strain_type: str = 'photoperiodic', light_type: str = 'led', 
                                         lamp_power_w: float = 240.0, lamp_distance_cm: float = 25.0, 
                                         light_hours: int = 18, ppfd_measured: float = None,
                                         color_temperature_k: int = 3500, power_percentage: int = 100,
                                         additional_wavelengths: str = '') -> Dict[str, Any]:
        """Calculate lighting plan using JSON guidelines if available"""
        
        # Try to load guidelines first
        guidelines_data = self.load_guidelines_from_json(phase)
        
        if guidelines_data:
            # Use guidelines data
            guidelines = guidelines_data['guidelines']
            
            # Get strain type key for guidelines
            strain_key = 'photoperiod' if strain_type == 'photoperiodic' else 'autoflower'
            
            # Get PPFD range from guidelines based on strain type
            ppfd_range = guidelines.get('ppfd', {})
            if isinstance(ppfd_range, dict) and strain_key in ppfd_range:
                # New format with strain-specific PPFD
                strain_ppfd = ppfd_range[strain_key]
                min_ppfd = strain_ppfd.get('min', 0)
                max_ppfd = strain_ppfd.get('max', 0)
            else:
                # Fallback to old format
                min_ppfd = ppfd_range.get('min', 0)
                max_ppfd = ppfd_range.get('max', 0)
            
            target_ppfd = (min_ppfd + max_ppfd) / 2
            
            # Get DLI range from guidelines based on strain type
            dli_range = guidelines.get('dli', {})
            if isinstance(dli_range, dict) and strain_key in dli_range:
                # New format with strain-specific DLI
                strain_dli = dli_range[strain_key]
                min_dli = strain_dli.get('min', 0)
                max_dli = strain_dli.get('max', 0)
            else:
                # Fallback to old format
                min_dli = dli_range.get('min', 0)
                max_dli = dli_range.get('max', 0)
            
            # Get light hours from guidelines
            light_hours_guideline = guidelines.get('lightHours', 18)
            if isinstance(light_hours_guideline, dict):
                # Handle different light hours for photoperiod vs autoflower
                light_hours_guideline = light_hours_guideline.get(strain_key, 18)
            
            # Calculate actual PPFD based on lamp parameters
            actual_power_w = lamp_power_w * (power_percentage / 100.0)
            calculated_ppfd = self.calculate_ppfd(actual_power_w, lamp_distance_cm, light_type, color_temperature_k, additional_wavelengths)
            
            # Use measured PPFD if provided, otherwise use calculated
            actual_ppfd = ppfd_measured if ppfd_measured is not None else calculated_ppfd
            
            # Calculate DLI
            dli = actual_ppfd * light_hours_guideline * 0.0036
            
            # Generate schedule
            schedule = self.generate_lighting_schedule(phase, {
                'photoperiod': f'{light_hours_guideline}/{24-light_hours_guideline}',
                'intensity': 'high' if target_ppfd > 600 else 'medium' if target_ppfd > 300 else 'low',
                'description': guidelines.get('description', '')
            }, strain_type)
            
            return {
                'phase': phase,
                'strain_type': strain_type,
                'light_type': light_type,
                'photoperiod': f'{light_hours_guideline}/{24-light_hours_guideline}',
                'intensity': 'high' if target_ppfd > 600 else 'medium' if target_ppfd > 300 else 'low',
                'description': guidelines.get('description', ''),
                'schedule': schedule,
                'guidelines_used': True,
                'current': {
                    'lamp_power_w': lamp_power_w,
                    'lamp_distance_cm': lamp_distance_cm,
                    'light_hours': light_hours_guideline,
                    'ppfd_calculated': calculated_ppfd,
                    'ppfd_measured': ppfd_measured,
                    'ppfd_actual': actual_ppfd,
                    'color_temperature_k': color_temperature_k,
                    'power_percentage': power_percentage,
                    'additional_wavelengths': additional_wavelengths,
                    'actual_power_w': actual_power_w,
                    'dli': round(dli, 2)
                },
                'recommendations': {
                    'ppfd_target': f"{target_ppfd:.0f} µmol/m²/s",
                    'ppfd_range': f"{min_ppfd}-{max_ppfd} µmol/m²/s",
                    'dli_range': f"{min_dli}-{max_dli} mol/m²/Tag",
                    'light_hours': light_hours_guideline,
                    'intensity': 'high' if target_ppfd > 600 else 'medium' if target_ppfd > 300 else 'low',
                    'distance': self.get_light_distance(phase, light_type),
                    'note': self.get_phase_lighting_note(phase, strain_type),
                    'guidelines': guidelines_data,
                    'strain_specific': True
                }
            }
        else:
            # Fallback to original calculation
            return self.calculate_lighting_plan(phase, strain_type, light_type, 
                                              lamp_power_w, lamp_distance_cm, light_hours, 
                                              ppfd_measured, color_temperature_k, power_percentage, additional_wavelengths) 