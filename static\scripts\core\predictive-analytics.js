/**
 * 🔮 Predictive Analytics System
 * 
 * Vorhersagende Analysen für Pflanzenwachstum und Problemerkennung
 * Nutzt historische Daten und Machine Learning für Prognosen
 */

class PredictiveAnalytics {
    constructor() {
        this.growthModels = new Map();
        this.problemPredictors = new Map();
        this.harvestPredictors = new Map();
        this.optimizationModels = new Map();
        this.historicalData = new Map();
        
        // Modell-Parameter
        this.modelConfig = {
            minDataPoints: 10,
            predictionHorizon: 7, // 7 Tage Vorhersage
            confidenceThreshold: 0.7,
            updateInterval: 24 * 60 * 60 * 1000 // 24 Stunden
        };
        
        console.log('🔮 PredictiveAnalytics: Initialisiert');
    }
    
    /**
     * Wachstumsprognose generieren
     */
    async generateGrowthPrediction(plantId, plantData, lightingData, daysAhead = 7) {
        try {
            const historicalData = this.getHistoricalData(plantId);
            
            if (historicalData.length < this.modelConfig.minDataPoints) {
                return {
                    available: false,
                    message: `Mindestens ${this.modelConfig.minDataPoints} Datenpunkte erforderlich`
                };
            }
            
            // Wachstumsmodell trainieren
            const growthModel = this.trainGrowthModel(historicalData, plantData);
            
            // Prognose generieren
            const prediction = this.generatePrediction(growthModel, daysAhead);
            
            // Konfidenz berechnen
            const confidence = this.calculatePredictionConfidence(growthModel, historicalData);
            
            // Prognose speichern
            this.growthModels.set(plantId, {
                model: growthModel,
                prediction: prediction,
                confidence: confidence,
                lastUpdated: new Date().toISOString()
            });
            
            console.log('🔮 PredictiveAnalytics: Wachstumsprognose generiert für Pflanze', plantId);
            
            return {
                available: true,
                prediction: prediction,
                confidence: confidence,
                model: growthModel
            };
            
        } catch (error) {
            console.error('🔮 PredictiveAnalytics: Fehler bei Wachstumsprognose:', error);
            return {
                available: false,
                message: 'Fehler bei der Prognose-Generierung'
            };
        }
    }
    
    /**
     * Problemerkennung und -vorhersage
     */
    async predictProblems(plantId, plantData, lightingData, environmentalData) {
        try {
            const problems = [];
            const historicalData = this.getHistoricalData(plantId);
            
            // PPFD-basierte Probleme
            const ppfdProblems = this.analyzePPFDIssues(lightingData, plantData);
            problems.push(...ppfdProblems);
            
            // Photoperiode-Probleme
            const photoperiodProblems = this.analyzePhotoperiodIssues(lightingData, plantData);
            problems.push(...photoperiodProblems);
            
            // Umwelt-Probleme
            const environmentalProblems = this.analyzeEnvironmentalIssues(environmentalData, plantData);
            problems.push(...environmentalProblems);
            
            // Wachstums-Probleme (basierend auf historischen Daten)
            const growthProblems = this.analyzeGrowthIssues(historicalData, plantData);
            problems.push(...growthProblems);
            
            // Probleme nach Wahrscheinlichkeit sortieren
            problems.sort((a, b) => b.probability - a.probability);
            
            // Nur Probleme mit hoher Wahrscheinlichkeit zurückgeben
            const highProbabilityProblems = problems.filter(p => p.probability > 0.6);
            
            // Problempredictor speichern
            this.problemPredictors.set(plantId, {
                problems: highProbabilityProblems,
                timestamp: new Date().toISOString()
            });
            
            return highProbabilityProblems;
            
        } catch (error) {
            console.error('🔮 PredictiveAnalytics: Fehler bei Problemerkennung:', error);
            return [];
        }
    }
    
    /**
     * Ernte-Prognose generieren
     */
    async generateHarvestPrediction(plantId, plantData, trichomeData) {
        try {
            const historicalData = this.getHistoricalData(plantId);
            
            if (historicalData.length < this.modelConfig.minDataPoints) {
                return {
                    available: false,
                    message: 'Unzureichende Daten für Ernte-Prognose'
                };
            }
            
            // Ernte-Modell trainieren
            const harvestModel = this.trainHarvestModel(historicalData, plantData, trichomeData);
            
            // Ernte-Datum vorhersagen
            const harvestPrediction = this.predictHarvestDate(harvestModel, plantData);
            
            // Ernte-Fenster berechnen
            const harvestWindow = this.calculateHarvestWindow(harvestPrediction, plantData);
            
            // Konfidenz berechnen
            const confidence = this.calculateHarvestConfidence(harvestModel, historicalData);
            
            // Ernte-Predictor speichern
            this.harvestPredictors.set(plantId, {
                model: harvestModel,
                prediction: harvestPrediction,
                window: harvestWindow,
                confidence: confidence,
                lastUpdated: new Date().toISOString()
            });
            
            return {
                available: true,
                harvestDate: harvestPrediction,
                harvestWindow: harvestWindow,
                confidence: confidence,
                model: harvestModel
            };
            
        } catch (error) {
            console.error('🔮 PredictiveAnalytics: Fehler bei Ernte-Prognose:', error);
            return {
                available: false,
                message: 'Fehler bei der Ernte-Prognose'
            };
        }
    }
    
    /**
     * Optimierungs-Vorschläge generieren
     */
    async generateOptimizationSuggestions(plantId, plantData, lightingData, performanceData) {
        try {
            const suggestions = [];
            
            // PPFD-Optimierung
            const ppfdSuggestions = this.analyzePPFDOptimization(lightingData, plantData);
            suggestions.push(...ppfdSuggestions);
            
            // Energie-Optimierung
            const energySuggestions = this.analyzeEnergyOptimization(lightingData, performanceData);
            suggestions.push(...energySuggestions);
            
            // Zeitplan-Optimierung
            const scheduleSuggestions = this.analyzeScheduleOptimization(lightingData, plantData);
            suggestions.push(...scheduleSuggestions);
            
            // Spektrum-Optimierung
            const spectrumSuggestions = this.analyzeSpectrumOptimization(lightingData, plantData);
            suggestions.push(...spectrumSuggestions);
            
            // Vorschläge nach Impact sortieren
            suggestions.sort((a, b) => b.expectedImpact - a.expectedImpact);
            
            // Optimierungs-Modell speichern
            this.optimizationModels.set(plantId, {
                suggestions: suggestions,
                timestamp: new Date().toISOString()
            });
            
            return suggestions;
            
        } catch (error) {
            console.error('🔮 PredictiveAnalytics: Fehler bei Optimierungs-Vorschlägen:', error);
            return [];
        }
    }
    
    /**
     * Historische Daten abrufen
     */
    getHistoricalData(plantId) {
        const data = this.historicalData.get(plantId) || [];
        
        // Daten aus localStorage laden falls nicht im Speicher
        if (data.length === 0) {
            const storedData = localStorage.getItem(`analytics_data_${plantId}`);
            if (storedData) {
                const parsedData = JSON.parse(storedData);
                this.historicalData.set(plantId, parsedData);
                return parsedData;
            }
        }
        
        return data;
    }
    
    /**
     * Neue Daten hinzufügen
     */
    addDataPoint(plantId, dataPoint) {
        const historicalData = this.getHistoricalData(plantId);
        
        // Neuen Datenpunkt hinzufügen
        historicalData.push({
            ...dataPoint,
            timestamp: new Date().toISOString()
        });
        
        // Nur die letzten 100 Datenpunkte behalten
        if (historicalData.length > 100) {
            historicalData.splice(0, historicalData.length - 100);
        }
        
        this.historicalData.set(plantId, historicalData);
        
        // In localStorage speichern
        localStorage.setItem(`analytics_data_${plantId}`, JSON.stringify(historicalData));
        
        console.log(`🔮 PredictiveAnalytics: Neuer Datenpunkt für Pflanze ${plantId} hinzugefügt`);
    }
    
    /**
     * Wachstumsmodell trainieren
     */
    trainGrowthModel(historicalData, plantData) {
        // Lineares Wachstumsmodell (vereinfacht)
        const growthData = historicalData.map(entry => ({
            day: entry.day || 0,
            ppfd: entry.ppfd || 0,
            height: entry.height || 0,
            phase: entry.phase || 'unknown'
        }));
        
        // Wachstumsrate berechnen
        const growthRates = [];
        for (let i = 1; i < growthData.length; i++) {
            const rate = (growthData[i].height - growthData[i-1].height) / 
                        (growthData[i].day - growthData[i-1].day);
            growthRates.push(rate);
        }
        
        const avgGrowthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
        
        // PPFD-Effizienz berechnen
        const ppfdEfficiency = growthData.map(entry => entry.height / Math.max(entry.ppfd, 1));
        const avgPPFDEfficiency = ppfdEfficiency.reduce((sum, eff) => sum + eff, 0) / ppfdEfficiency.length;
        
        return {
            type: 'linear_growth',
            avgGrowthRate: avgGrowthRate,
            avgPPFDEfficiency: avgPPFDEfficiency,
            phase: plantData.phase,
            strainType: plantData.strain_type,
            lastDataPoint: growthData[growthData.length - 1]
        };
    }
    
    /**
     * Prognose generieren
     */
    generatePrediction(model, daysAhead) {
        const lastDataPoint = model.lastDataPoint;
        const predictions = [];
        
        for (let day = 1; day <= daysAhead; day++) {
            const predictedDay = lastDataPoint.day + day;
            const predictedHeight = lastDataPoint.height + (model.avgGrowthRate * day);
            const predictedPPFD = this.predictOptimalPPFD(predictedDay, model.phase);
            
            predictions.push({
                day: predictedDay,
                height: Math.max(0, predictedHeight),
                ppfd: predictedPPFD,
                phase: this.predictPhase(predictedDay, model.phase),
                confidence: this.calculateDayConfidence(day, model)
            });
        }
        
        return predictions;
    }
    
    /**
     * Optimalen PPFD für Tag vorhersagen
     */
    predictOptimalPPFD(day, currentPhase) {
        const phasePPFD = {
            'vegetative_early': 350,
            'vegetative_middle': 500,
            'vegetative_late': 600,
            'flowering_early': 700,
            'flowering_middle': 800,
            'flowering_late': 700,
            'flush': 600
        };
        
        return phasePPFD[currentPhase] || 500;
    }
    
    /**
     * Phase für Tag vorhersagen
     */
    predictPhase(day, currentPhase) {
        const phaseTransitions = {
            'vegetative_early': 14,
            'vegetative_middle': 35,
            'vegetative_late': 49,
            'flowering_early': 70,
            'flowering_middle': 98,
            'flowering_late': 119,
            'flush': 133
        };
        
        // Nächste Phase bestimmen
        for (const [phase, transitionDay] of Object.entries(phaseTransitions)) {
            if (day <= transitionDay) {
                return phase;
            }
        }
        
        return 'flush';
    }
    
    /**
     * Konfidenz für Tag berechnen
     */
    calculateDayConfidence(day, model) {
        // Konfidenz nimmt mit der Zeit ab
        const baseConfidence = 0.9;
        const decayRate = 0.05; // 5% Abnahme pro Tag
        
        return Math.max(0.3, baseConfidence - (day * decayRate));
    }
    
    /**
     * Prognose-Konfidenz berechnen
     */
    calculatePredictionConfidence(model, historicalData) {
        // Konfidenz basierend auf Datenqualität und Modell-Stabilität
        let confidence = 0.5;
        
        // Mehr Daten = höhere Konfidenz
        if (historicalData.length >= 20) confidence += 0.2;
        else if (historicalData.length >= 10) confidence += 0.1;
        
        // Stabile Wachstumsrate = höhere Konfidenz
        const growthRates = historicalData.slice(-5).map(entry => entry.growth_rate || 0);
        const rateVariance = this.calculateVariance(growthRates);
        if (rateVariance < 0.1) confidence += 0.2;
        else if (rateVariance < 0.3) confidence += 0.1;
        
        return Math.min(1.0, confidence);
    }
    
    /**
     * PPFD-Probleme analysieren
     */
    analyzePPFDIssues(lightingData, plantData) {
        const problems = [];
        const currentPPFD = lightingData?.current?.ppfd_calculated || 0;
        const phase = plantData?.phase || 'flowering_middle';
        
        // PPFD-Bereiche für verschiedene Phasen
        const phasePPFDRanges = {
            'vegetative_early': { min: 250, optimal: 350, max: 450 },
            'vegetative_middle': { min: 400, optimal: 500, max: 600 },
            'vegetative_late': { min: 500, optimal: 600, max: 700 },
            'flowering_early': { min: 600, optimal: 700, max: 800 },
            'flowering_middle': { min: 700, optimal: 800, max: 900 },
            'flowering_late': { min: 600, optimal: 700, max: 800 },
            'flush': { min: 450, optimal: 600, max: 750 }
        };
        
        const range = phasePPFDRanges[phase];
        if (!range) return problems;
        
        // PPFD zu niedrig
        if (currentPPFD < range.min) {
            problems.push({
                type: 'ppfd_too_low',
                severity: 'medium',
                probability: 0.8,
                message: `PPFD (${currentPPFD}) ist zu niedrig für ${phase}`,
                recommendation: `PPFD auf ${range.optimal} μmol/m²/s erhöhen`,
                expectedImpact: 'Reduziertes Wachstum und Blütenbildung'
            });
        }
        
        // PPFD zu hoch
        if (currentPPFD > range.max) {
            problems.push({
                type: 'ppfd_too_high',
                severity: 'high',
                probability: 0.9,
                message: `PPFD (${currentPPFD}) ist zu hoch für ${phase}`,
                recommendation: `PPFD auf ${range.optimal} μmol/m²/s reduzieren`,
                expectedImpact: 'Stress und mögliche Schäden'
            });
        }
        
        return problems;
    }
    
    /**
     * Photoperiode-Probleme analysieren
     */
    analyzePhotoperiodIssues(lightingData, plantData) {
        const problems = [];
        const currentHours = lightingData?.current?.light_hours || 12;
        const phase = plantData?.phase || 'flowering_middle';
        const strainType = plantData?.strain_type || 'photoperiodic';
        
        // Empfohlene Photoperiode
        const recommendedHours = {
            'vegetative_early': { 'photoperiodic': 18, 'autoflowering': 18 },
            'vegetative_middle': { 'photoperiodic': 18, 'autoflowering': 18 },
            'vegetative_late': { 'photoperiodic': 18, 'autoflowering': 18 },
            'flowering_early': { 'photoperiodic': 12, 'autoflowering': 18 },
            'flowering_middle': { 'photoperiodic': 12, 'autoflowering': 18 },
            'flowering_late': { 'photoperiodic': 12, 'autoflowering': 18 },
            'flush': { 'photoperiodic': 10, 'autoflowering': 18 }
        };
        
        const recommended = recommendedHours[phase]?.[strainType];
        if (!recommended) return problems;
        
        // Photoperiode-Probleme
        if (Math.abs(currentHours - recommended) > 2) {
            problems.push({
                type: 'photoperiod_mismatch',
                severity: 'medium',
                probability: 0.7,
                message: `Photoperiode (${currentHours}h) weicht von Empfehlung (${recommended}h) ab`,
                recommendation: `Photoperiode auf ${recommended}h anpassen`,
                expectedImpact: 'Suboptimale Blüteninduktion'
            });
        }
        
        return problems;
    }
    
    /**
     * Umwelt-Probleme analysieren
     */
    analyzeEnvironmentalIssues(environmentalData, plantData) {
        const problems = [];
        
        if (!environmentalData) return problems;
        
        const { temperature, humidity, co2 } = environmentalData;
        
        // Temperatur-Probleme
        if (temperature > 30) {
            problems.push({
                type: 'temperature_too_high',
                severity: 'high',
                probability: 0.9,
                message: `Temperatur (${temperature}°C) ist zu hoch`,
                recommendation: 'Temperatur auf 20-28°C reduzieren',
                expectedImpact: 'Stress und reduzierte Photosynthese'
            });
        } else if (temperature < 18) {
            problems.push({
                type: 'temperature_too_low',
                severity: 'medium',
                probability: 0.7,
                message: `Temperatur (${temperature}°C) ist zu niedrig`,
                recommendation: 'Temperatur auf 20-28°C erhöhen',
                expectedImpact: 'Langsames Wachstum'
            });
        }
        
        // Luftfeuchtigkeit-Probleme
        if (humidity > 80) {
            problems.push({
                type: 'humidity_too_high',
                severity: 'high',
                probability: 0.8,
                message: `Luftfeuchtigkeit (${humidity}%) ist zu hoch`,
                recommendation: 'Luftfeuchtigkeit auf 40-70% reduzieren',
                expectedImpact: 'Schimmel-Risiko'
            });
        } else if (humidity < 40) {
            problems.push({
                type: 'humidity_too_low',
                severity: 'medium',
                probability: 0.6,
                message: `Luftfeuchtigkeit (${humidity}%) ist zu niedrig`,
                recommendation: 'Luftfeuchtigkeit auf 40-70% erhöhen',
                expectedImpact: 'Trockenstress'
            });
        }
        
        return problems;
    }
    
    /**
     * Wachstums-Probleme analysieren
     */
    analyzeGrowthIssues(historicalData, plantData) {
        const problems = [];
        
        if (historicalData.length < 5) return problems;
        
        // Wachstumsrate analysieren
        const recentData = historicalData.slice(-5);
        const growthRates = [];
        
        for (let i = 1; i < recentData.length; i++) {
            const rate = (recentData[i].height - recentData[i-1].height) / 
                        (recentData[i].day - recentData[i-1].day);
            growthRates.push(rate);
        }
        
        const avgGrowthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
        
        // Erwartete Wachstumsrate für Phase
        const expectedRates = {
            'vegetative_early': 2.0,
            'vegetative_middle': 3.0,
            'vegetative_late': 2.5,
            'flowering_early': 1.5,
            'flowering_middle': 1.0,
            'flowering_late': 0.5,
            'flush': 0.2
        };
        
        const expectedRate = expectedRates[plantData.phase] || 1.0;
        
        // Wachstumsprobleme
        if (avgGrowthRate < expectedRate * 0.5) {
            problems.push({
                type: 'slow_growth',
                severity: 'medium',
                probability: 0.7,
                message: `Wachstumsrate (${avgGrowthRate.toFixed(1)}cm/Tag) ist zu niedrig`,
                recommendation: 'PPFD und Nährstoffe überprüfen',
                expectedImpact: 'Verzögertes Wachstum'
            });
        }
        
        return problems;
    }
    
    /**
     * Ernte-Modell trainieren
     */
    trainHarvestModel(historicalData, plantData, trichomeData) {
        // Vereinfachtes Ernte-Modell basierend auf Trichom-Daten
        const model = {
            type: 'trichome_based',
            strainType: plantData.strain_type,
            phase: plantData.phase,
            currentDay: plantData.current_day || 0
        };
        
        // Trichom-basierte Ernte-Logik
        if (trichomeData) {
            const { clear, milky, amber } = trichomeData;
            const total = clear + milky + amber;
            
            if (total > 0) {
                model.clearPercentage = (clear / total) * 100;
                model.milkyPercentage = (milky / total) * 100;
                model.amberPercentage = (amber / total) * 100;
            }
        }
        
        return model;
    }
    
    /**
     * Ernte-Datum vorhersagen
     */
    predictHarvestDate(model, plantData) {
        const currentDay = plantData.current_day || 0;
        
        // Basis-Ernte-Zeit für verschiedene Phasen
        const baseHarvestTimes = {
            'vegetative_early': 70,
            'vegetative_middle': 70,
            'vegetative_late': 70,
            'flowering_early': 85,
            'flowering_middle': 100,
            'flowering_late': 115,
            'flush': 130
        };
        
        let baseHarvestDay = baseHarvestTimes[plantData.phase] || 100;
        
        // Trichom-basierte Anpassungen
        if (model.amberPercentage > 30) {
            // Viele bernsteinfarbene Trichome - bald erntebereit
            baseHarvestDay = Math.min(baseHarvestDay, currentDay + 7);
        } else if (model.milkyPercentage > 70) {
            // Viele milchige Trichome - noch 2-3 Wochen
            baseHarvestDay = currentDay + 14;
        } else if (model.clearPercentage > 70) {
            // Viele klare Trichome - noch 4-6 Wochen
            baseHarvestDay = currentDay + 35;
        }
        
        return Math.max(currentDay, baseHarvestDay);
    }
    
    /**
     * Ernte-Fenster berechnen
     */
    calculateHarvestWindow(harvestDay, plantData) {
        const earlyHarvest = harvestDay - 5;
        const optimalHarvest = harvestDay;
        const lateHarvest = harvestDay + 5;
        
        return {
            early: Math.max(plantData.current_day || 0, earlyHarvest),
            optimal: optimalHarvest,
            late: lateHarvest,
            description: `Optimaler Ernte-Zeitraum: Tag ${earlyHarvest}-${lateHarvest}`
        };
    }
    
    /**
     * Ernte-Konfidenz berechnen
     */
    calculateHarvestConfidence(model, historicalData) {
        let confidence = 0.5;
        
        // Trichom-Daten vorhanden = höhere Konfidenz
        if (model.clearPercentage !== undefined) {
            confidence += 0.3;
        }
        
        // Mehr historische Daten = höhere Konfidenz
        if (historicalData.length >= 20) {
            confidence += 0.2;
        }
        
        return Math.min(1.0, confidence);
    }
    
    /**
     * PPFD-Optimierung analysieren
     */
    analyzePPFDOptimization(lightingData, plantData) {
        const suggestions = [];
        const currentPPFD = lightingData?.current?.ppfd_calculated || 0;
        const lampPower = lightingData?.current?.lamp_power_w || 600;
        const ppfdPerWatt = lampPower > 0 ? currentPPFD / lampPower : 0;
        
        // PPFD/Watt Optimierung
        if (ppfdPerWatt < 0.8) {
            suggestions.push({
                type: 'ppfd_efficiency',
                priority: 'high',
                expectedImpact: 0.8,
                message: 'PPFD/Watt-Effizienz verbessern',
                recommendation: 'Lampenabstand optimieren oder effizientere Lampen verwenden',
                expectedSavings: '20-30% Energieeinsparung'
            });
        }
        
        return suggestions;
    }
    
    /**
     * Energie-Optimierung analysieren
     */
    analyzeEnergyOptimization(lightingData, performanceData) {
        const suggestions = [];
        
        // Energiespar-Vorschläge
        suggestions.push({
            type: 'energy_saving',
            priority: 'medium',
            expectedImpact: 0.6,
            message: 'Energiespar-Modus aktivieren',
            recommendation: 'PPFD um 15% reduzieren bei minimalem Wachstumsverlust',
            expectedSavings: '15-20% Energieeinsparung'
        });
        
        return suggestions;
    }
    
    /**
     * Zeitplan-Optimierung analysieren
     */
    analyzeScheduleOptimization(lightingData, plantData) {
        const suggestions = [];
        const currentHours = lightingData?.current?.light_hours || 12;
        
        // Zeitplan-Optimierung
        if (plantData.phase === 'flowering_middle' && currentHours > 12) {
            suggestions.push({
                type: 'schedule_optimization',
                priority: 'medium',
                expectedImpact: 0.5,
                message: 'Photoperiode für Blüte optimieren',
                recommendation: 'Beleuchtungsstunden auf 12h reduzieren',
                expectedImprovement: 'Bessere Blüteninduktion'
            });
        }
        
        return suggestions;
    }
    
    /**
     * Spektrum-Optimierung analysieren
     */
    analyzeSpectrumOptimization(lightingData, plantData) {
        const suggestions = [];
        const currentTemp = lightingData?.current?.color_temperature_k || 3000;
        
        // Spektrum-Optimierung
        const optimalTemps = {
            'vegetative_early': 5000,
            'vegetative_middle': 4500,
            'vegetative_late': 4000,
            'flowering_early': 3500,
            'flowering_middle': 3000,
            'flowering_late': 3000,
            'flush': 2700
        };
        
        const optimalTemp = optimalTemps[plantData.phase];
        if (optimalTemp && Math.abs(currentTemp - optimalTemp) > 500) {
            suggestions.push({
                type: 'spectrum_optimization',
                priority: 'low',
                expectedImpact: 0.3,
                message: 'Farbtemperatur optimieren',
                recommendation: `Farbtemperatur auf ${optimalTemp}K anpassen`,
                expectedImprovement: 'Bessere Pflanzenentwicklung'
            });
        }
        
        return suggestions;
    }
    
    /**
     * Varianz berechnen
     */
    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }
    
    /**
     * Debug-Informationen ausgeben
     */
    debug() {
        console.log('🔮 PredictiveAnalytics Debug Info:');
        console.log('Growth Models:', this.growthModels.size);
        console.log('Problem Predictors:', this.problemPredictors.size);
        console.log('Harvest Predictors:', this.harvestPredictors.size);
        console.log('Optimization Models:', this.optimizationModels.size);
        console.log('Historical Data:', this.historicalData.size);
    }
}

// Globale Instanz erstellen
window.predictiveAnalytics = new PredictiveAnalytics();

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PredictiveAnalytics;
} 