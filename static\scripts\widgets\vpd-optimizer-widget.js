// vpd-optimizer-widget.js
// Neues VPD-Analyse- & Optimierungs-Widget (Grundgerüst)

class VPDOptimizerWidget {
    constructor(containerId = 'vpd-optimizer-box', options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.currentPhase = options.currentPhase || 'flowering_middle';
        this.strainType = options.strainType || 'photoperiodic';
        this.temperature = 25;
        this.humidity = 50;
        this.leafTemperature = null;
        if (this.container) {
            this.render();
            this.setupEventListeners();
        }
    }

    render() {
        this.container.innerHTML = `
            <div class="vpd-optimizer-widget-card">
                <div class="vpd-optimizer-header mb-2">
                    <h5><i class="fa-solid fa-droplet me-2"></i>VPD-Analyse & Optimierung</h5>
                </div>
                <div class="vpd-optimizer-inputs mb-3">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <label for="vpd-temp-input">Lufttemperatur (°C)</label>
                            <input type="number" id="vpd-temp-input" class="form-control" value="${this.temperature}" min="10" max="40" step="0.1">
                        </div>
                        <div class="col-md-3">
                            <label for="vpd-humidity-input">Luftfeuchte (%)</label>
                            <input type="number" id="vpd-humidity-input" class="form-control" value="${this.humidity}" min="10" max="100" step="1">
                        </div>
                        <div class="col-md-3">
                            <label for="vpd-leaf-temp-input">Blatttemperatur (optional)</label>
                            <input type="number" id="vpd-leaf-temp-input" class="form-control" value="${this.leafTemperature ?? ''}" min="10" max="45" step="0.1">
                        </div>
                        <div class="col-md-3">
                            <label for="vpd-phase-input">Phase</label>
                            <select id="vpd-phase-input" class="form-select">
                                <option value="germination">Keimung</option>
                                <option value="vegetative_early">Frühe Vegi</option>
                                <option value="vegetative_middle">Mittlere Vegi</option>
                                <option value="vegetative_late">Späte Vegi</option>
                                <option value="flowering_early">Frühe Blüte</option>
                                <option value="flowering_middle">Mittlere Blüte</option>
                                <option value="flowering_late">Späte Blüte</option>
                                <option value="flush">Flush</option>
                            </select>
                        </div>
                    </div>
                    <div class="row g-2 mt-2">
                        <div class="col-md-3">
                            <label for="vpd-strain-input">Strain-Typ</label>
                            <select id="vpd-strain-input" class="form-select">
                                <option value="photoperiodic">Photoperiodisch</option>
                                <option value="autoflowering">Autoflower</option>
                            </select>
                        </div>
                        <div class="col-md-3 align-self-end">
                            <button id="vpd-analyse-btn" class="btn btn-primary w-100">
                                <i class="fa fa-calculator"></i> Analyse starten
                            </button>
                        </div>
                    </div>
                </div>
                <div id="vpd-optimizer-result"></div>
                <div class="mt-3 text-end">
                    <button id="vpd-history-btn" class="btn btn-outline-secondary btn-sm">
                        <i class="fa fa-chart-line"></i> Verlauf anzeigen
                    </button>
                </div>
            </div>
            <div class="modal fade" id="vpd-history-modal" tabindex="-1" aria-labelledby="vpdHistoryModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="vpdHistoryModalLabel"><i class="fa fa-chart-line me-2"></i>VPD-Verlauf</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                        </div>
                        <div class="modal-body">
                            <canvas id="vpd-history-chart" height="120"></canvas>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        // Setze aktuelle Auswahl
        document.getElementById('vpd-phase-input').value = this.currentPhase;
        document.getElementById('vpd-strain-input').value = this.strainType;
    }

    setupEventListeners() {
        this.container.addEventListener('input', (e) => {
            if (e.target.id === 'vpd-temp-input') this.temperature = parseFloat(e.target.value) || 25;
            if (e.target.id === 'vpd-humidity-input') this.humidity = parseInt(e.target.value) || 50;
            if (e.target.id === 'vpd-leaf-temp-input') this.leafTemperature = e.target.value ? parseFloat(e.target.value) : null;
            if (e.target.id === 'vpd-phase-input') this.currentPhase = e.target.value;
            if (e.target.id === 'vpd-strain-input') this.strainType = e.target.value;
        });
        this.container.addEventListener('click', (e) => {
            if (e.target.id === 'vpd-analyse-btn') {
                this.runAnalysis();
            }
            if (e.target.id === 'vpd-history-btn') {
                this.showHistoryModal();
            }
        });
    }

    async runAnalysis() {
        const resultDiv = this.container.querySelector('#vpd-optimizer-result');
        resultDiv.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Analyse läuft...</div>';
        try {
            const payload = {
                phase: this.currentPhase,
                strain_type: this.strainType,
                temperature: this.temperature,
                humidity: this.humidity
            };
            if (this.leafTemperature !== null) payload.leaf_temperature = this.leafTemperature;
            const response = await fetch('/api/vpd/analyse', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!response.ok) throw new Error('Analyse fehlgeschlagen');
            const data = await response.json();
            this.renderResult(data.analysis, resultDiv);
        } catch (err) {
            resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Fehler: ${err.message}</div>`;
        }
    }

    renderResult(analysis, resultDiv) {
        if (!analysis) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Keine Analyse-Daten verfügbar.</div>';
            return;
        }
        const status = analysis.status;
        const color = status.status === 'optimal' ? 'success' : status.status === 'low' ? 'warning' : status.status === 'high' ? 'danger' : 'info';
        resultDiv.innerHTML = `
            <div class="vpd-optimizer-result-card">
                <div class="alert alert-${color}">
                    <strong>VPD: ${analysis.vpd} kPa</strong> <br>
                    <span>${status.message}</span>
                </div>
                <div class="mb-2">
                    <b>Zielbereich:</b> ${analysis.target_range.min} – ${analysis.target_range.max} kPa
                    ${analysis.leaf_temp_offset !== null ? `<br><b>Blatttemperatur-Offset:</b> ${analysis.leaf_temp_offset > 0 ? '+' : ''}${analysis.leaf_temp_offset}°C` : ''}
                </div>
                <div class="mb-2">
                    <b>Empfehlungen:</b>
                    <ul>
                        ${analysis.recommendations.map(r => `<li>${r}</li>`).join('')}
                    </ul>
                </div>
                ${analysis.warnings && analysis.warnings.length > 0 ? `<div class="alert alert-danger"><b>Warnungen:</b><ul>${analysis.warnings.map(w => `<li>${w}</li>`).join('')}</ul></div>` : ''}
            </div>
        `;
    }

    async showHistoryModal() {
        // Bootstrap Modal öffnen
        const modal = new bootstrap.Modal(document.getElementById('vpd-history-modal'));
        modal.show();
        // Daten laden und Chart rendern
        await this.loadAndRenderHistoryChart();
    }

    async loadAndRenderHistoryChart() {
        const chartCanvas = document.getElementById('vpd-history-chart');
        if (!chartCanvas) return;
        // Lade Verlauf (plant_id=1 als Beispiel)
        let history = [];
        try {
            const response = await fetch('/api/vpd/history/1');
            if (!response.ok) throw new Error('Verlauf konnte nicht geladen werden');
            const data = await response.json();
            history = data.history || [];
        } catch (err) {
            chartCanvas.parentElement.innerHTML = `<div class="alert alert-danger">Fehler beim Laden des Verlaufs: ${err.message}</div>`;
            return;
        }
        if (history.length === 0) {
            chartCanvas.parentElement.innerHTML = '<div class="alert alert-warning">Keine Verlaufsdaten vorhanden.</div>';
            return;
        }
        // Daten für Chart.js aufbereiten
        const labels = history.map(e => new Date(e.entry_date).toLocaleString('de-DE'));
        const vpdData = history.map(e => e.vpd_value);
        const tempData = history.map(e => e.temperature);
        const rhData = history.map(e => e.humidity);
        // Chart.js initialisieren
        if (this._historyChart) {
            this._historyChart.destroy();
        }
        this._historyChart = new Chart(chartCanvas, {
            type: 'line',
            data: {
                labels: labels.reverse(),
                datasets: [
                    {
                        label: 'VPD (kPa)',
                        data: vpdData.reverse(),
                        borderColor: 'rgba(0,123,255,1)',
                        backgroundColor: 'rgba(0,123,255,0.1)',
                        yAxisID: 'y',
                        tension: 0.2
                    },
                    {
                        label: 'Temperatur (°C)',
                        data: tempData.reverse(),
                        borderColor: 'rgba(220,53,69,1)',
                        backgroundColor: 'rgba(220,53,69,0.1)',
                        yAxisID: 'y1',
                        tension: 0.2
                    },
                    {
                        label: 'Luftfeuchte (%)',
                        data: rhData.reverse(),
                        borderColor: 'rgba(40,167,69,1)',
                        backgroundColor: 'rgba(40,167,69,0.1)',
                        yAxisID: 'y2',
                        tension: 0.2
                    }
                ]
            },
            options: {
                responsive: true,
                interaction: { mode: 'index', intersect: false },
                stacked: false,
                plugins: {
                    legend: { position: 'top' },
                    title: { display: true, text: 'VPD-Verlauf & Umgebungswerte' }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: 'VPD (kPa)' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: { drawOnChartArea: false },
                        title: { display: true, text: 'Temperatur (°C)' }
                    },
                    y2: {
                        type: 'linear',
                        display: false,
                        position: 'right',
                        grid: { drawOnChartArea: false },
                        title: { display: true, text: 'Luftfeuchte (%)' }
                    }
                }
            }
        });
    }
}

// Global verfügbar machen
window.VPDOptimizerWidget = VPDOptimizerWidget; 