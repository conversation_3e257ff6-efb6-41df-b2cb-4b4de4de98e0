/* ===== BEM METHODOLOGY CSS ===== */
/* Block Element Modifier (BEM) Methodologie für modulare CSS-Struktur */

/* ===== PLANT CARD BLOCK ===== */
.plant-card {
    /* Block styles */
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.plant-card__header {
    /* Element styles */
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.plant-card__title {
    /* Element styles */
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.plant-card__subtitle {
    /* Element styles */
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.plant-card__body {
    /* Element styles */
    padding: 1rem;
}

.plant-card__footer {
    /* Element styles */
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.plant-card--featured {
    /* Modifier styles */
    border: 2px solid #ffd700;
    transform: scale(1.02);
}

.plant-card--completed {
    /* Modifier styles */
    opacity: 0.7;
    filter: grayscale(0.3);
}

.plant-card--active {
    /* Modifier styles */
    border-left: 4px solid #28a745;
}

/* ===== PHASE INFO BLOCK ===== */
.phase-info {
    /* Block styles */
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.phase-info__header {
    /* Element styles */
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.phase-info__icon {
    /* Element styles */
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.phase-info__title {
    /* Element styles */
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.phase-info__content {
    /* Element styles */
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.phase-info__item {
    /* Element styles */
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.phase-info__label {
    /* Element styles */
    font-weight: 500;
    margin-right: 0.5rem;
    color: #6c757d;
}

.phase-info__value {
    /* Element styles */
    font-weight: 600;
    color: #212529;
}

.phase-info--current {
    /* Modifier styles */
    border-left: 4px solid #007bff;
}

.phase-info--completed {
    /* Modifier styles */
    border-left: 4px solid #28a745;
}

.phase-info--upcoming {
    /* Modifier styles */
    border-left: 4px solid #ffc107;
}

/* ===== WIDGET CONTAINER BLOCK ===== */
.widget-container {
    /* Block styles */
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.widget-container__header {
    /* Element styles */
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.widget-container__title {
    /* Element styles */
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.widget-container__icon {
    /* Element styles */
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.widget-container__body {
    /* Element styles */
    padding: 1rem;
}

.widget-container__footer {
    /* Element styles */
    padding: 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.widget-container--collapsed {
    /* Modifier styles */
    max-height: 60px;
    overflow: hidden;
}

.widget-container--expanded {
    /* Modifier styles */
    max-height: none;
}

/* ===== STATUS BADGE BLOCK ===== */
.status-badge {
    /* Block styles */
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.status-badge__icon {
    /* Element styles */
    margin-right: 0.25rem;
    font-size: 0.875rem;
}

.status-badge__text {
    /* Element styles */
    font-weight: 500;
}

.status-badge--active {
    /* Modifier styles */
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge--completed {
    /* Modifier styles */
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-badge--warning {
    /* Modifier styles */
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge--danger {
    /* Modifier styles */
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .phase-info__content {
        grid-template-columns: 1fr;
    }
    
    .plant-card__header {
        padding: 0.75rem;
    }
    
    .plant-card__body {
        padding: 0.75rem;
    }
    
    .widget-container__header {
        padding: 0.75rem;
    }
    
    .widget-container__body {
        padding: 0.75rem;
    }
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .plant-card {
    background-color: #2d3748;
    color: #e2e8f0;
}

[data-theme="dark"] .plant-card__footer {
    background-color: #4a5568;
    border-top-color: #718096;
}

[data-theme="dark"] .phase-info {
    background-color: #2d3748;
    color: #e2e8f0;
}

[data-theme="dark"] .phase-info__item {
    background-color: #4a5568;
}

[data-theme="dark"] .widget-container {
    background-color: #2d3748;
    color: #e2e8f0;
}

[data-theme="dark"] .widget-container__header {
    background-color: #4a5568;
    border-bottom-color: #718096;
}

[data-theme="dark"] .widget-container__footer {
    background-color: #4a5568;
    border-top-color: #718096;
} 