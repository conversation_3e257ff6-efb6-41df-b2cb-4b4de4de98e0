/**
 * Phase-Notizen Widget
 * Ermöglicht das Erstellen und Bearbeiten von Notizen für jede Phase
 */

class PhaseNotesWidget {
    constructor(containerId, plantId) {
        this.containerId = containerId;
        this.plantId = plantId;
        this.currentPhase = null;
        this.notes = {};
        this.init();
    }
    
    init() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            console.error(`PhaseNotesWidget: Container ${this.containerId} nicht gefunden`);
            return;
        }
        
        this.render();
        this.loadNotes();
    }
    
    setCurrentPhase(phaseData) {
        this.currentPhase = phaseData;
        this.render();
        this.loadCurrentPhaseNote();
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="card phase-notes-card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        Phase-Notizen
                    </h5>
                    <button 
                        type="button" 
                        class="btn btn-sm btn-outline-light" 
                        data-bs-toggle="tooltip" 
                        data-bs-placement="left"
                        title="Persönliche Notizen für jede Wachstumsphase. Dokumentiere Beobachtungen, Probleme und Erfolge. Hilft bei der Optimierung zukünftiger Grows und der Nachverfolgung von Phasen-spezifischen Erfahrungen."
                    >
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div id="phase-notes-content">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Lade...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Tooltip initialisieren
        const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    async loadNotes() {
        try {
            const response = await fetch(`/api/phase-notes/${this.plantId}`);
            const data = await response.json();
            
            if (data.success) {
                this.notes = {};
                data.notes.forEach(note => {
                    this.notes[note.phase_name] = note;
                });
                this.renderNotes();
            }
        } catch (error) {
            console.error('Fehler beim Laden der Phase-Notizen:', error);
            this.showError('Fehler beim Laden der Notizen');
        }
    }
    
    async loadCurrentPhaseNote() {
        if (!this.currentPhase) return;
        
        const phaseName = this.currentPhase.sub_phase;
        try {
            const response = await fetch(`/api/phase-notes/${this.plantId}?phase_name=${phaseName}`);
            const data = await response.json();
            
            if (data.success && data.note) {
                this.notes[phaseName] = data.note;
            }
            this.renderNotes();
        } catch (error) {
            console.error('Fehler beim Laden der aktuellen Phase-Notiz:', error);
        }
    }
    
    renderNotes() {
        const contentDiv = document.getElementById('phase-notes-content');
        if (!contentDiv) return;
        
        if (!this.currentPhase) {
            contentDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Wähle eine Phase aus, um Notizen zu erstellen
                </div>
            `;
            return;
        }
        
        // Verwende die korrekten Datenfelder
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        const phaseDisplayName = this.getPhaseDisplayName(phaseName);
        const currentNote = this.notes[phaseName];
        const stageDay = this.currentPhase.sub_stage_day || '?';
        const stageDuration = this.currentPhase.sub_stage_duration || '?';
        
        contentDiv.innerHTML = `
            <div class="mb-3">
                <h6 class="text-primary">
                    <i class="fas fa-leaf me-2"></i>
                    ${phaseDisplayName}
                </h6>
                <small class="text-muted">
                    Tag ${stageDay} von ${stageDuration}
                </small>
            </div>
            
            <div class="mb-3">
                <label for="phase-note-text" class="form-label">Notizen für diese Phase:</label>
                <textarea 
                    id="phase-note-text" 
                    class="form-control" 
                    rows="4" 
                    placeholder="${this.getNoteTemplate(phaseName)}"
                >${currentNote ? currentNote.note_text : ''}</textarea>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <button 
                    type="button" 
                    class="btn btn-primary btn-sm"
                    onclick="phaseNotesWidget.saveNote()"
                >
                    <i class="fas fa-save me-1"></i>
                    Speichern
                </button>
                
                ${currentNote ? `
                    <button 
                        type="button" 
                        class="btn btn-outline-danger btn-sm"
                        onclick="phaseNotesWidget.deleteNote()"
                    >
                        <i class="fas fa-trash me-1"></i>
                        Löschen
                    </button>
                ` : ''}
            </div>
            
            ${currentNote ? `
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Zuletzt bearbeitet: ${this.formatDate(currentNote.updated_at)}
                    </small>
                </div>
            ` : ''}
        `;
    }
    
    async saveNote() {
        if (!this.currentPhase) return;
        
        const noteText = document.getElementById('phase-note-text').value.trim();
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        
        try {
            const response = await fetch(`/api/phase-notes/${this.plantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    phase_name: phaseName,
                    note_text: noteText
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Notiz erfolgreich gespeichert');
                await this.loadNotes(); // Neu laden um aktualisierte Daten zu bekommen
            } else {
                this.showError(data.message || 'Fehler beim Speichern');
            }
        } catch (error) {
            console.error('Fehler beim Speichern der Notiz:', error);
            this.showError('Fehler beim Speichern der Notiz');
        }
    }
    
    async deleteNote() {
        if (!this.currentPhase) return;
        
        if (!confirm('Möchtest du diese Phase-Notiz wirklich löschen?')) {
            return;
        }
        
        const phaseName = this.currentPhase.sub_phase || this.currentPhase.sub_stage;
        
        try {
            const response = await fetch(`/api/phase-notes/${this.plantId}/${phaseName}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Notiz erfolgreich gelöscht');
                delete this.notes[phaseName];
                this.renderNotes();
            } else {
                this.showError(data.message || 'Fehler beim Löschen');
            }
        } catch (error) {
            console.error('Fehler beim Löschen der Notiz:', error);
            this.showError('Fehler beim Löschen der Notiz');
        }
    }
    
    getPhaseDisplayName(phaseName) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            // Alternative Namen aus der Datenbank
            'Keimung': 'Keimung',
            'Frühe Wachstumsphase': 'Frühe Wachstumsphase',
            'Mittlere Wachstumsphase': 'Mittlere Wachstumsphase',
            'Späte Wachstumsphase': 'Späte Wachstumsphase',
            'Frühe Blüte': 'Frühe Blüte',
            'Mittlere Blüte': 'Mittlere Blüte',
            'Späte Blüte': 'Späte Blüte'
        };
        return phaseNames[phaseName] || phaseName;
    }
    
    getNoteTemplate(phaseName) {
        const templates = {
            'germination': 'Keimung: Samen zeigen sich nach X Tagen. Substrat-Feuchtigkeit: [ ]. Temperatur: [ ]°C. Besonderheiten: [ ]',
            'vegetative_early': 'Frühe Wachstumsphase: Pflanzenhöhe: [ ]cm. Neue Blätter: [ ]. Dünger-Reaktion: [ ]. Besonderheiten: [ ]',
            'vegetative_middle': 'Mittlere Wachstumsphase: Pflanzenhöhe: [ ]cm. LST-Training: [ ]. Dünger-Dosierung: [ ]. Besonderheiten: [ ]',
            'vegetative_late': 'Späte Wachstumsphase: Pflanzenform: [ ]. Blüte-Vorbereitung: [ ]. Dünger-Umstellung: [ ]. Besonderheiten: [ ]',
            'flowering_early': 'Frühe Blüte: Blütenbildung: [ ]. Lichtzyklus-Umstellung: [ ]. Dünger-Anpassung: [ ]. Besonderheiten: [ ]',
            'flowering_middle': 'Mittlere Blüte: Blütenentwicklung: [ ]. Trichome: [ ]. Geruch: [ ]. Besonderheiten: [ ]',
            'flowering_late': 'Späte Blüte: Trichome-Farbe: [ ]. Ernte-Vorbereitung: [ ]. Flush: [ ]. Besonderheiten: [ ]',
            // Alternative Namen aus der Datenbank
            'Keimung': 'Keimung: Samen zeigen sich nach X Tagen. Substrat-Feuchtigkeit: [ ]. Temperatur: [ ]°C. Besonderheiten: [ ]',
            'Frühe Wachstumsphase': 'Frühe Wachstumsphase: Pflanzenhöhe: [ ]cm. Neue Blätter: [ ]. Dünger-Reaktion: [ ]. Besonderheiten: [ ]',
            'Mittlere Wachstumsphase': 'Mittlere Wachstumsphase: Pflanzenhöhe: [ ]cm. LST-Training: [ ]. Dünger-Dosierung: [ ]. Besonderheiten: [ ]',
            'Späte Wachstumsphase': 'Späte Wachstumsphase: Pflanzenform: [ ]. Blüte-Vorbereitung: [ ]. Dünger-Umstellung: [ ]. Besonderheiten: [ ]',
            'Frühe Blüte': 'Frühe Blüte: Blütenbildung: [ ]. Lichtzyklus-Umstellung: [ ]. Dünger-Anpassung: [ ]. Besonderheiten: [ ]',
            'Mittlere Blüte': 'Mittlere Blüte: Blütenentwicklung: [ ]. Trichome: [ ]. Geruch: [ ]. Besonderheiten: [ ]',
            'Späte Blüte': 'Späte Blüte: Trichome-Farbe: [ ]. Ernte-Vorbereitung: [ ]. Flush: [ ]. Besonderheiten: [ ]'
        };
        return templates[phaseName] || `${phaseName}: [Notizen hier eingeben...]`;
    }
    
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    showSuccess(message) {
        // Erfolgs-Benachrichtigung anzeigen
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const contentDiv = document.getElementById('phase-notes-content');
        if (contentDiv) {
            contentDiv.appendChild(alertDiv);
            
            // Auto-Entfernung nach 3 Sekunden
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    }
    
    showError(message) {
        // Fehler-Benachrichtigung anzeigen
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const contentDiv = document.getElementById('phase-notes-content');
        if (contentDiv) {
            contentDiv.appendChild(alertDiv);
            
            // Auto-Entfernung nach 5 Sekunden
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }
}

// Globale Instanz für einfachen Zugriff
let phaseNotesWidget = null;

// Initialisierungsfunktion
function initPhaseNotesWidget(containerId, plantId) {
    phaseNotesWidget = new PhaseNotesWidget(containerId, plantId);
    return phaseNotesWidget;
} 