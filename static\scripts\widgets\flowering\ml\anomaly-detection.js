/**
 * Flowering Anomaly Detection - Spezialisiertes Anomalie-Erkennungssystem
 */

class FloweringAnomalyDetection {
    constructor(advancedML) {
        this.advancedML = advancedML;
        this.widget = advancedML.widget;
        this.detectedAnomalies = [];
        this.detectionAlgorithms = new Map();
        this.thresholds = new Map();
        this.baselineData = new Map();
    }

    /**
     * Initialisiert das Anomaly Detection System
     */
    async initialize() {
        try {
            console.log('🚨 Anomaly Detection: Initialisierung gestartet...');
            
            // Erkennungsalgorithmen initialisieren
            this.initializeDetectionAlgorithms();
            
            // Schwellenwerte definieren
            this.defineThresholds();
            
            // Baseline-Daten etablieren
            await this.establishBaseline();
            
            console.log('🚨 Anomaly Detection: System erfolgreich initialisiert');
            
        } catch (error) {
            console.error('🚨 Anomaly Detection: Fehler bei der Initialisierung:', error);
        }
    }

    /**
     * Startet die Anomalie-Erkennung
     */
    async startDetection() {
        if (!window.advancedMLSystem) return [];
        
        try {
            // Aktuelle Sensor-Daten sammeln
            const sensorData = this.collectSensorData();
            
            // Verschiedene Anomalie-Erkennungsalgorithmen anwenden
            const anomalies = await this.detectAllAnomalies(sensorData);
            
            // Anomalien klassifizieren und bewerten
            const classifiedAnomalies = this.classifyAnomalies(anomalies);
            
            // Ergebnisse anzeigen
            this.showResults(classifiedAnomalies);
            
            return classifiedAnomalies;
            
        } catch (error) {
            console.error('🚨 Anomaly Detection: Fehler bei der Anomalie-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            const demoAnomalies = this.generateDemoAnomalies();
            this.showResults(demoAnomalies);
            return demoAnomalies;
        }
    }

    /**
     * Sammelt aktuelle Sensor-Daten
     */
    collectSensorData() {
        return {
            ppfd: this.generateSensorValues('ppfd', 600, 1000, 50),
            temperature: this.generateSensorValues('temperature', 20, 35, 10),
            humidity: this.generateSensorValues('humidity', 40, 80, 15),
            co2: this.generateSensorValues('co2', 300, 1500, 20),
            ph: this.generateSensorValues('ph', 5.5, 7.0, 10),
            ec: this.generateSensorValues('ec', 0.8, 2.5, 12),
            growth_rate: this.generateSensorValues('growth_rate', 0.5, 2.0, 8),
            leaf_temperature: this.generateSensorValues('leaf_temperature', 18, 32, 10)
        };
    }

    /**
     * Generiert realistische Sensor-Werte mit gelegentlichen Anomalien
     */
    generateSensorValues(type, min, max, count) {
        const values = [];
        const baseValue = min + (max - min) * 0.6; // Normalwert bei 60% des Bereichs
        
        for (let i = 0; i < count; i++) {
            let value;
            
            // 10% Chance auf Anomalie
            if (Math.random() < 0.1) {
                // Anomalie: Wert außerhalb des normalen Bereichs
                if (Math.random() < 0.5) {
                    value = min - (max - min) * 0.2; // Zu niedrig
                } else {
                    value = max + (max - min) * 0.2; // Zu hoch
                }
            } else {
                // Normalwert mit kleiner Variation
                value = baseValue + (Math.random() - 0.5) * (max - min) * 0.3;
            }
            
            values.push({
                value: Math.max(0, value), // Keine negativen Werte
                timestamp: new Date(Date.now() - (count - i) * 60000).toISOString(),
                quality: 0.9 + Math.random() * 0.1
            });
        }
        
        return values;
    }

    /**
     * Erkennt alle Arten von Anomalien
     */
    async detectAllAnomalies(sensorData) {
        const allAnomalies = [];
        
        // Statistische Anomalien
        const statisticalAnomalies = await this.detectStatisticalAnomalies(sensorData);
        allAnomalies.push(...statisticalAnomalies);
        
        // Zeitreihen-Anomalien
        const timeSeriesAnomalies = await this.detectTimeSeriesAnomalies(sensorData);
        allAnomalies.push(...timeSeriesAnomalies);
        
        // Korrelations-Anomalien
        const correlationAnomalies = await this.detectCorrelationAnomalies(sensorData);
        allAnomalies.push(...correlationAnomalies);
        
        // Schwellenwert-Anomalien
        const thresholdAnomalies = await this.detectThresholdAnomalies(sensorData);
        allAnomalies.push(...thresholdAnomalies);
        
        // Muster-basierte Anomalien
        const patternAnomalies = await this.detectPatternAnomalies(sensorData);
        allAnomalies.push(...patternAnomalies);
        
        return allAnomalies;
    }

    /**
     * Erkennt statistische Anomalien
     */
    async detectStatisticalAnomalies(sensorData) {
        const anomalies = [];
        
        Object.entries(sensorData).forEach(([sensorType, values]) => {
            const numericValues = values.map(v => v.value);
            const mean = numericValues.reduce((a, b) => a + b, 0) / numericValues.length;
            const stdDev = Math.sqrt(numericValues.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / numericValues.length);
            
            values.forEach((reading, index) => {
                const zScore = Math.abs((reading.value - mean) / stdDev);
                
                if (zScore > 2.5) { // 2.5 Standardabweichungen
                    anomalies.push({
                        type: 'statistical_outlier',
                        sensor: sensorType,
                        value: reading.value,
                        expected: mean,
                        deviation: zScore,
                        severity: zScore > 3 ? 'high' : 'medium',
                        timestamp: reading.timestamp,
                        description: `${sensorType} Wert ${reading.value.toFixed(2)} ist ${zScore.toFixed(1)} Standardabweichungen vom Mittelwert entfernt`,
                        confidence: Math.min(0.95, 0.6 + (zScore - 2.5) * 0.1)
                    });
                }
            });
        });
        
        return anomalies;
    }

    /**
     * Erkennt Zeitreihen-Anomalien
     */
    async detectTimeSeriesAnomalies(sensorData) {
        const anomalies = [];
        
        Object.entries(sensorData).forEach(([sensorType, values]) => {
            if (values.length < 3) return;
            
            for (let i = 1; i < values.length - 1; i++) {
                const prev = values[i - 1].value;
                const current = values[i].value;
                const next = values[i + 1].value;
                
                // Plötzliche Sprünge erkennen
                const jumpToPrev = Math.abs(current - prev);
                const jumpToNext = Math.abs(next - current);
                const avgJump = (jumpToPrev + jumpToNext) / 2;
                
                const threshold = this.getJumpThreshold(sensorType);
                
                if (avgJump > threshold) {
                    anomalies.push({
                        type: 'sudden_jump',
                        sensor: sensorType,
                        value: current,
                        jump_size: avgJump,
                        severity: avgJump > threshold * 2 ? 'high' : 'medium',
                        timestamp: values[i].timestamp,
                        description: `Plötzlicher Sprung in ${sensorType}: ${avgJump.toFixed(2)} Einheiten`,
                        confidence: Math.min(0.9, 0.7 + (avgJump / threshold - 1) * 0.2)
                    });
                }
            }
        });
        
        return anomalies;
    }

    /**
     * Erkennt Korrelations-Anomalien
     */
    async detectCorrelationAnomalies(sensorData) {
        const anomalies = [];
        
        // Temperatur-Luftfeuchtigkeit Korrelation
        if (sensorData.temperature && sensorData.humidity) {
            const tempHumidityAnomaly = this.checkTemperatureHumidityCorrelation(
                sensorData.temperature, 
                sensorData.humidity
            );
            if (tempHumidityAnomaly) anomalies.push(tempHumidityAnomaly);
        }
        
        // PPFD-Blatttemperatur Korrelation
        if (sensorData.ppfd && sensorData.leaf_temperature) {
            const ppfdTempAnomaly = this.checkPPFDTemperatureCorrelation(
                sensorData.ppfd, 
                sensorData.leaf_temperature
            );
            if (ppfdTempAnomaly) anomalies.push(ppfdTempAnomaly);
        }
        
        return anomalies;
    }

    /**
     * Erkennt Schwellenwert-Anomalien
     */
    async detectThresholdAnomalies(sensorData) {
        const anomalies = [];
        
        Object.entries(sensorData).forEach(([sensorType, values]) => {
            const thresholds = this.thresholds.get(sensorType);
            if (!thresholds) return;
            
            values.forEach(reading => {
                if (reading.value < thresholds.min) {
                    anomalies.push({
                        type: 'threshold_violation',
                        sensor: sensorType,
                        value: reading.value,
                        threshold: thresholds.min,
                        violation_type: 'below_minimum',
                        severity: 'high',
                        timestamp: reading.timestamp,
                        description: `${sensorType} unter Mindestwert: ${reading.value.toFixed(2)} < ${thresholds.min}`,
                        confidence: 0.95
                    });
                } else if (reading.value > thresholds.max) {
                    anomalies.push({
                        type: 'threshold_violation',
                        sensor: sensorType,
                        value: reading.value,
                        threshold: thresholds.max,
                        violation_type: 'above_maximum',
                        severity: 'high',
                        timestamp: reading.timestamp,
                        description: `${sensorType} über Maximalwert: ${reading.value.toFixed(2)} > ${thresholds.max}`,
                        confidence: 0.95
                    });
                }
            });
        });
        
        return anomalies;
    }

    /**
     * Erkennt Muster-basierte Anomalien
     */
    async detectPatternAnomalies(sensorData) {
        const anomalies = [];
        
        // Zyklische Muster-Verletzungen
        Object.entries(sensorData).forEach(([sensorType, values]) => {
            const patternViolation = this.detectPatternViolation(sensorType, values);
            if (patternViolation) anomalies.push(patternViolation);
        });
        
        return anomalies;
    }

    /**
     * Klassifiziert und bewertet Anomalien
     */
    classifyAnomalies(anomalies) {
        return anomalies
            .filter(anomaly => anomaly.confidence > 0.6) // Mindest-Konfidenz
            .map(anomaly => ({
                ...anomaly,
                priority: this.calculatePriority(anomaly),
                impact: this.assessImpact(anomaly),
                recommendations: this.generateRecommendations(anomaly)
            }))
            .sort((a, b) => {
                // Sortiere nach Schweregrad und Konfidenz
                const severityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                const aSeverity = severityOrder[a.severity] || 1;
                const bSeverity = severityOrder[b.severity] || 1;
                
                if (aSeverity !== bSeverity) return bSeverity - aSeverity;
                return b.confidence - a.confidence;
            });
    }

    /**
     * Zeigt Anomaly Detection Ergebnisse an
     */
    showResults(anomalies) {
        // Prüfen ob bereits eine Anomalie Card existiert
        let anomalyCard = this.widget.element.querySelector('.anomaly-detection-card');
        
        if (!anomalyCard) {
            // Neue Anomalie Card erstellen
            anomalyCard = document.createElement('div');
            anomalyCard.className = 'lighting-card anomaly-detection-card';
            this.widget.element.appendChild(anomalyCard);
        }
        
        // Card-Inhalt aktualisieren
        anomalyCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-radar"></i>
                <h3>Anomalie-Erkennung</h3>
                <div class="anomaly-status">
                    <span class="status-indicator ${anomalies.length > 0 ? 'warning' : ''}"></span>
                    <span class="status-text">${anomalies.length} Anomalien erkannt</span>
                </div>
            </div>
            <div class="anomaly-content">
                <div class="anomaly-notice alert alert-info mb-3">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    <strong>Anomalie-System:</strong> ${this.getAvailableAlgorithms().length} Erkennungsalgorithmen aktiv.
                </div>
                
                ${anomalies.length > 0 ? `
                    <div class="anomalies-list">
                        ${anomalies.map(anomaly => `
                            <div class="anomaly-item severity-${anomaly.severity}">
                                <div class="anomaly-header">
                                    <div class="anomaly-type">${this.getAnomalyTypeText(anomaly.type)}</div>
                                    <div class="anomaly-severity">${this.getSeverityText(anomaly.severity)}</div>
                                    <div class="anomaly-time">${new Date(anomaly.timestamp).toLocaleTimeString('de-DE')}</div>
                                </div>
                                <div class="anomaly-description">${anomaly.description}</div>
                                <div class="anomaly-details">
                                    <span class="sensor-info"><strong>Sensor:</strong> ${this.getSensorTypeText(anomaly.sensor)}</span>
                                    <span class="confidence-info"><strong>Konfidenz:</strong> ${Math.round(anomaly.confidence * 100)}%</span>
                                    ${anomaly.impact ? `<span class="impact-info"><strong>Auswirkung:</strong> ${anomaly.impact}</span>` : ''}
                                </div>
                                ${anomaly.recommendations ? `
                                    <div class="anomaly-recommendations">
                                        <strong>Empfehlungen:</strong>
                                        <ul>
                                            ${anomaly.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="no-anomalies">
                        <i class="fa-solid fa-check-circle text-success"></i>
                        <p>Keine Anomalien erkannt - alle Werte im Normalbereich</p>
                        <small>Letzte Überprüfung: ${new Date().toLocaleTimeString('de-DE')}</small>
                    </div>
                `}
            </div>
        `;
        
        // Anomalie Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && anomalyCard.parentElement !== phase6Container) {
            phase6Container.appendChild(anomalyCard);
        }
        
        // Erkannte Anomalien speichern
        this.detectedAnomalies = anomalies;
    }

    /**
     * Hilfsfunktionen
     */
    initializeDetectionAlgorithms() {
        this.detectionAlgorithms.set('statistical', { name: 'Statistische Analyse', accuracy: 0.85 });
        this.detectionAlgorithms.set('time_series', { name: 'Zeitreihen-Analyse', accuracy: 0.82 });
        this.detectionAlgorithms.set('correlation', { name: 'Korrelations-Analyse', accuracy: 0.88 });
        this.detectionAlgorithms.set('threshold', { name: 'Schwellenwert-Analyse', accuracy: 0.95 });
        this.detectionAlgorithms.set('pattern', { name: 'Muster-Analyse', accuracy: 0.78 });
        this.detectionAlgorithms.set('isolation_forest', { name: 'Isolation Forest', accuracy: 0.87 });
        this.detectionAlgorithms.set('one_class_svm', { name: 'One-Class SVM', accuracy: 0.83 });
    }

    defineThresholds() {
        this.thresholds.set('ppfd', { min: 400, max: 1200, optimal: [600, 900] });
        this.thresholds.set('temperature', { min: 18, max: 32, optimal: [22, 26] });
        this.thresholds.set('humidity', { min: 40, max: 80, optimal: [50, 70] });
        this.thresholds.set('co2', { min: 300, max: 1500, optimal: [800, 1200] });
        this.thresholds.set('ph', { min: 5.5, max: 7.0, optimal: [6.0, 6.5] });
        this.thresholds.set('ec', { min: 0.8, max: 2.5, optimal: [1.2, 1.8] });
    }

    async establishBaseline() {
        // Etabliere Baseline-Werte für normale Bedingungen
        this.baselineData.set('normal_variance', 0.1);
        this.baselineData.set('correlation_threshold', 0.7);
        this.baselineData.set('pattern_deviation_threshold', 0.2);
    }

    getJumpThreshold(sensorType) {
        const thresholds = {
            'ppfd': 100,
            'temperature': 3,
            'humidity': 10,
            'co2': 200,
            'ph': 0.5,
            'ec': 0.3
        };
        return thresholds[sensorType] || 10;
    }

    checkTemperatureHumidityCorrelation(tempValues, humidityValues) {
        // Vereinfachte Korrelationsprüfung
        if (tempValues.length !== humidityValues.length) return null;
        
        const avgTemp = tempValues.reduce((sum, v) => sum + v.value, 0) / tempValues.length;
        const avgHumidity = humidityValues.reduce((sum, v) => sum + v.value, 0) / humidityValues.length;
        
        // Erwartete inverse Korrelation: hohe Temperatur = niedrige Luftfeuchtigkeit
        if (avgTemp > 28 && avgHumidity > 70) {
            return {
                type: 'correlation_anomaly',
                sensor: 'temperature_humidity',
                description: 'Unerwartete Temperatur-Luftfeuchtigkeit Kombination',
                severity: 'medium',
                timestamp: new Date().toISOString(),
                confidence: 0.8
            };
        }
        return null;
    }

    checkPPFDTemperatureCorrelation(ppfdValues, tempValues) {
        // Vereinfachte PPFD-Temperatur Korrelationsprüfung
        const avgPPFD = ppfdValues.reduce((sum, v) => sum + v.value, 0) / ppfdValues.length;
        const avgTemp = tempValues.reduce((sum, v) => sum + v.value, 0) / tempValues.length;
        
        // Hohe PPFD sollte zu höherer Blatttemperatur führen
        if (avgPPFD > 800 && avgTemp < 22) {
            return {
                type: 'correlation_anomaly',
                sensor: 'ppfd_temperature',
                description: 'Niedrige Blatttemperatur trotz hoher PPFD',
                severity: 'medium',
                timestamp: new Date().toISOString(),
                confidence: 0.75
            };
        }
        return null;
    }

    detectPatternViolation(sensorType, values) {
        // Vereinfachte Muster-Verletzungserkennung
        if (values.length < 5) return null;
        
        const recentValues = values.slice(-5).map(v => v.value);
        const variance = this.calculateVariance(recentValues);
        const expectedVariance = this.baselineData.get('normal_variance') || 0.1;
        
        if (variance > expectedVariance * 5) {
            return {
                type: 'pattern_violation',
                sensor: sensorType,
                description: `Ungewöhnliche Variabilität in ${sensorType}`,
                severity: 'medium',
                timestamp: new Date().toISOString(),
                confidence: 0.7
            };
        }
        return null;
    }

    calculateVariance(values) {
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        return values.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / values.length;
    }

    calculatePriority(anomaly) {
        const severityWeight = { 'high': 3, 'medium': 2, 'low': 1 };
        const confidenceWeight = anomaly.confidence;
        return (severityWeight[anomaly.severity] || 1) * confidenceWeight;
    }

    assessImpact(anomaly) {
        const impacts = {
            'threshold_violation': 'Direkter Einfluss auf Pflanzenwachstum',
            'statistical_outlier': 'Mögliche Sensor- oder Umweltprobleme',
            'sudden_jump': 'Systeminstabilität oder Sensordefekt',
            'correlation_anomaly': 'Unerwartete Umweltbedingungen',
            'pattern_violation': 'Abweichung von normalen Mustern'
        };
        return impacts[anomaly.type] || 'Unbekannte Auswirkung';
    }

    generateRecommendations(anomaly) {
        const recommendations = {
            'threshold_violation': [
                'Sofortige Überprüfung der Systemeinstellungen',
                'Anpassung der Umweltparameter',
                'Monitoring verstärken'
            ],
            'statistical_outlier': [
                'Sensor-Kalibrierung prüfen',
                'Mehrere Messungen zur Bestätigung',
                'Umgebungsbedingungen überprüfen'
            ],
            'sudden_jump': [
                'Hardware-Diagnose durchführen',
                'Verkabelung und Verbindungen prüfen',
                'Backup-Sensoren aktivieren'
            ],
            'correlation_anomaly': [
                'Klimasteuerung überprüfen',
                'Ventilation und Luftzirkulation prüfen',
                'Umweltbedingungen anpassen'
            ],
            'pattern_violation': [
                'Langzeit-Trends analysieren',
                'Systemstabilität überprüfen',
                'Präventive Wartung durchführen'
            ]
        };
        return recommendations[anomaly.type] || ['Weitere Analyse erforderlich'];
    }

    generateDemoAnomalies() {
        return [
            {
                type: 'threshold_violation',
                sensor: 'temperature',
                value: 34.5,
                threshold: 32,
                violation_type: 'above_maximum',
                severity: 'high',
                timestamp: new Date().toISOString(),
                description: 'Temperatur über Maximalwert: 34.5°C > 32°C',
                confidence: 0.95,
                impact: 'Hitzestress für die Pflanze',
                recommendations: ['Sofortige Kühlung aktivieren', 'Ventilation erhöhen', 'Beleuchtung reduzieren']
            },
            {
                type: 'statistical_outlier',
                sensor: 'humidity',
                value: 25,
                expected: 60,
                deviation: 3.2,
                severity: 'medium',
                timestamp: new Date(Date.now() - 300000).toISOString(),
                description: 'Luftfeuchtigkeit 25% ist 3.2 Standardabweichungen vom Mittelwert entfernt',
                confidence: 0.87,
                impact: 'Mögliche Austrocknung',
                recommendations: ['Luftbefeuchter aktivieren', 'Bewässerung prüfen', 'Sensor kalibrieren']
            }
        ];
    }

    getAnomalyTypeText(type) {
        const types = {
            'threshold_violation': 'Schwellenwert-Verletzung',
            'statistical_outlier': 'Statistischer Ausreißer',
            'sudden_jump': 'Plötzlicher Sprung',
            'correlation_anomaly': 'Korrelations-Anomalie',
            'pattern_violation': 'Muster-Verletzung'
        };
        return types[type] || type;
    }

    getSeverityText(severity) {
        const severities = {
            'high': 'Hoch',
            'medium': 'Mittel',
            'low': 'Niedrig'
        };
        return severities[severity] || severity;
    }

    getSensorTypeText(sensor) {
        const sensors = {
            'temperature': 'Temperatur',
            'humidity': 'Luftfeuchtigkeit',
            'ppfd': 'PPFD',
            'co2': 'CO₂',
            'ph': 'pH-Wert',
            'ec': 'EC-Wert',
            'growth_rate': 'Wachstumsrate',
            'leaf_temperature': 'Blatttemperatur'
        };
        return sensors[sensor] || sensor;
    }

    getAvailableAlgorithms() {
        return Array.from(this.detectionAlgorithms.keys());
    }
}
