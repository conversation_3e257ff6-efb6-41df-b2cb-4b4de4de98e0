#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blüte-Zeitmanagement-Routes für praktisches Zeitachsen-Tracking
Basierend auf echten Daten von Blüte-Tag 41
Erweitert um Marker-Verwaltung, <PERSON><PERSON><PERSON>-<PERSON>gger-Logik und Trichom-Integration
"""

from flask import Blueprint, request, jsonify, render_template
from datetime import datetime, timedelta
import json
from database_basic import GrowDiaryBasicDB

flowering_bp = Blueprint('flowering', __name__, url_prefix='/flowering')

# Datenbank-Instanz
db = GrowDiaryBasicDB()

# Trichom-Zielwerte für Flush-Trigger
TRICHOME_TARGETS = {
    "flush_start": {
        "milky_percent": 70,
        "amber_percent": 20,
        "clear_percent": 10
    },
    "flush_intensify": {
        "milky_percent": 90,
        "amber_percent": 10,
        "clear_percent": 0
    },
    "harvest_ready": {
        "milky_percent": 80,
        "amber_percent": 20,
        "clear_percent": 0
    }
}

# Beispiel-Daten basierend auf deinen JSON-Daten
SAMPLE_FLOWERING_DATA = {
    "plant_id": "GG25L",
    "bloom_start_date": "2025-06-02",
    "current_bloom_day": 41,
    "strain_profile": {
        "name": "White Widow XXL Auto",
        "flowering_duration_days": [60, 75],
        "flush_recommendation": "14 Tage vor Ernte",
        "stretch_estimate_days": [7, 25]
    },
    "phases": {
        "preflower": {"start_day": 0, "end_day": 14},
        "stretch": {"start_day": 7, "end_day": 25},
        "flowering_middle": {"start_day": 21, "end_day": 56},
        "flowering_late": {"start_day": 49, "end_day": 75},
        "flush": {"start_day": 61, "end_day": 75}
    },
    "current_phase": "flowering_middle",
    "progress_percentage": 62.5,
    "estimated_flush_start": 61,
    "estimated_harvest_window": [65, 72],
    "markers": [
        {
            "id": 1,
            "plant_id": "GG25L",
            "event_type": "stretch_completed",
            "event_name": "Stretch beendet",
            "bloom_day": 25,
            "date": "2025-06-27",
            "notes": "Wachstum stabilisiert",
            "category": "growth",
            "importance": "high",
            "auto_calculated": False,
            "created_at": "2025-06-27T15:30:00Z"
        },
        {
            "id": 2,
            "plant_id": "GG25L",
            "event_type": "trichome_milky",
            "event_name": "Erste milchige Trichome",
            "bloom_day": 48,
            "date": "2025-07-20",
            "notes": "Erste milchige Trichome an Top Buds",
            "category": "maturity",
            "importance": "high",
            "auto_calculated": False,
            "created_at": "2025-07-20T10:45:00Z"
        }
    ],
    "flush_status": {
        "triggered": False,
        "reason": None,
        "start_recommended": 61,
        "flush_duration_days": 12,
        "flush_day_today": 0,
        "flush_target_day": 73,
        "status_message": "Flush noch nicht empfohlen"
    },
    "harvest_prediction": {
        "early": [60, 64],
        "optimal": [65, 72],
        "late": [73, 78],
        "current_message": "Flush vorbereiten ab Tag 61. Ernte wahrscheinlich an Tag 68–71."
    }
}

# Marker-Kategorien und Event-Typen
MARKER_CATEGORIES = {
    "growth": ["stretch_completed", "height_stable", "bud_formation"],
    "maturity": ["trichome_milky", "trichome_amber", "pistils_retracted", "first_trichomes"],
    "flush": ["flush_started", "flush_completed", "nutrient_stopped"],
    "stress": ["flowering_delay", "nutrient_burn", "light_stress", "temperature_stress"]
}

EVENT_NAMES = {
    "stretch_completed": "Stretch beendet",
    "trichome_milky": "Milchige Trichome",
    "trichome_amber": "Bernstein Trichome",
    "pistils_retracted": "Pistillen zurückgezogen",
    "first_trichomes": "Erste Trichome",
    "flush_started": "Flush gestartet",
    "flush_completed": "Flush abgeschlossen",
    "nutrient_stopped": "Nährstoffe gestoppt",
    "flowering_delay": "Blühverzögerung",
    "nutrient_burn": "Nährstoffverbrennung",
    "light_stress": "Lichtstress",
    "temperature_stress": "Temperaturstress"
}

@flowering_bp.route('/')
def flowering_dashboard():
    """Hauptseite für Blüte-Zeitmanagement"""
    return render_template('widgets/flowering-widget.html')

@flowering_bp.route('/status/<plant_id>')
def get_flowering_status(plant_id):
    """Blüte-Status für eine Pflanze abrufen"""
    print(f"🌺 FloweringRoutes: Status-Anfrage für Plant ID: {plant_id}")
    
    # Akzeptiere sowohl GG25L als auch die echte Plant ID
    if plant_id in ["GG25L", "ebc30eef-80fe-4709-930d-91e919fe8082"]:
        # Pflanze aus der Datenbank laden
        print(f"🌺 FloweringRoutes: Lade Pflanze aus Datenbank...")
        plant = db.get_plant_by_id(plant_id)
        print(f"🌺 FloweringRoutes: Pflanze geladen: {plant}")
        if not plant:
            return jsonify({"error": "Pflanze nicht gefunden"}), 404
        
        # Strain-Name aus der Datenbank verwenden
        strain_name = plant.get('strain', 'Sorte nicht angegeben')
        
        # Strain-Profile mit echten Daten erstellen
        strain_profile = {
            "name": strain_name,
            "strain": strain_name,  # Zusätzlich für Frontend-Kompatibilität
            "flowering_duration_days": SAMPLE_FLOWERING_DATA["strain_profile"]["flowering_duration_days"],
            "flush_recommendation": SAMPLE_FLOWERING_DATA["strain_profile"]["flush_recommendation"],
            "stretch_estimate_days": SAMPLE_FLOWERING_DATA["strain_profile"]["stretch_estimate_days"]
        }
        
        # Echte Blüte-Tage berechnen (wie in der "Wichtige Phase-Angaben" Card)
        current_day = 0
        bloom_start_date = None
        if plant.get('flower_start_date'):
            try:
                bloom_start = datetime.strptime(plant['flower_start_date'], '%Y-%m-%d').date()
                today = datetime.now().date()
                current_day = max(0, (today - bloom_start).days)
                bloom_start_date = plant['flower_start_date']
            except (ValueError, TypeError):
                current_day = 0
                bloom_start_date = None
        
        # Blütefortschritt berechnen (wie in der "Wichtige Phase-Angaben" Card)
        # Annahme: 9 Wochen Blüte = 63 Tage
        total_blüte_weeks = 9
        bloom_progress_percentage = min(100, round((current_day / (total_blüte_weeks * 7)) * 100, 1)) if current_day > 0 else 0
        
        # Phase basierend auf echten Tagen bestimmen
        if current_day < 14:
            current_phase = "preflower"
        elif current_day < 25:
            current_phase = "stretch"
        elif current_day < 42:
            current_phase = "flowering_middle"
        elif current_day < 75:
            current_phase = "flowering_late"
        else:
            current_phase = "flush"
        
        # Grow-Fortschritt berechnen (basierend auf Gesamtdauer)
        grow_progress_percentage = 0
        if plant.get('start_date'):
            try:
                grow_start = datetime.strptime(plant['start_date'], '%Y-%m-%d').date()
                today = datetime.now().date()
                grow_days = max(0, (today - grow_start).days)
                # Annahme: Gesamtdauer Grow = 100 Tage, falls kein Enddatum
                grow_total_days = 100
                grow_progress_percentage = min(100, round((grow_days / grow_total_days) * 100, 1))
            except (ValueError, TypeError):
                grow_progress_percentage = 0

        # Flush-Start berechnen (14 Tage vor Ernte)
        flowering_range = strain_profile["flowering_duration_days"]
        estimated_flush_start = max(0, flowering_range[1] - 14)
        estimated_harvest_window = [flowering_range[1] - 7, flowering_range[1] + 7]
        
        # Stretch-Status basierend auf echten Daten
        stretch_completed = current_day >= 25
        stretch_duration = min(18, current_day - 7) if current_day > 7 else 0

        # Konsistente Fortschrittswerte
        progress_percentage = bloom_progress_percentage  # Für Kompatibilität

        # Transformiere die Daten für das Frontend
        flowering_status = {
            "plant_id": plant_id,
            "name": plant.get('plant_name', 'Unbenannt'),  # Echter Pflanzenname
            "flowering_status": {
                "phase": current_phase,
                "current_day": current_day,
                "bloom_start_date": bloom_start_date,
                "estimated_flush_start": estimated_flush_start,
                "estimated_harvest_window": estimated_harvest_window,
                "stretch_completed": stretch_completed,
                "stretch_duration": stretch_duration,
                "progress_percentage": bloom_progress_percentage,
                "grow_progress_percentage": grow_progress_percentage,
                "bloom_progress_percentage": bloom_progress_percentage
            },
            "strain_profile": strain_profile,
            "phases": SAMPLE_FLOWERING_DATA["phases"],
            "markers": [],  # Wird später mit echten Daten gefüllt
            "flush_status": {
                "triggered": current_day >= estimated_flush_start,
                "reason": "Automatisch basierend auf Blüte-Tagen" if current_day >= estimated_flush_start else None,
                "start_recommended": estimated_flush_start,
                "flush_duration_days": 14,
                "flush_day_today": max(0, current_day - estimated_flush_start) if current_day >= estimated_flush_start else 0,
                "flush_target_day": flowering_range[1],
                "status_message": f"Flush empfohlen ab Tag {estimated_flush_start}" if current_day >= estimated_flush_start else f"Flush noch nicht empfohlen (ab Tag {estimated_flush_start})"
            },
            "recommendation": {
                "flush_start": estimated_flush_start,
                "flush_duration": 14,
                "harvest_optimal": estimated_harvest_window,
                "harvest_early": [max(0, flowering_range[1] - 14), flowering_range[1] - 7],
                "harvest_late": [flowering_range[1] + 7, flowering_range[1] + 14],
                "message": f"Flush vorbereiten ab Tag {estimated_flush_start}. Ernte wahrscheinlich an Tag {estimated_harvest_window[0]}-{estimated_harvest_window[1]}.",
                "urgency": "high" if current_day >= estimated_flush_start else "medium"
            }
        }
        print(f"🌺 FloweringRoutes: Sende Response: {flowering_status}")
        return jsonify(flowering_status)
    else:
        print(f"🌺 FloweringRoutes: Plant ID nicht unterstützt: {plant_id}")
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@flowering_bp.route('/status/<plant_id>', methods=['POST'])
def update_flowering_status(plant_id):
    """Blüte-Status aktualisieren"""
    try:
        data = request.get_json()
        
        # Validierung der Daten
        required_fields = ['current_day', 'bloom_start_date']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Feld '{field}' fehlt"}), 400
        
        # Status aktualisieren
        updated_status = {
            "plant_id": plant_id,
            "flowering_status": {
                "phase": data.get('phase', 'flowering_middle'),
                "current_day": data['current_day'],
                "bloom_start_date": data['bloom_start_date'],
                "estimated_flush_start": data.get('estimated_flush_start', 52),
                "estimated_harvest_window": data.get('estimated_harvest_window', [65, 70]),
                "stretch_completed": data.get('stretch_completed', True),
                "stretch_duration": data.get('stretch_duration', 23),
                "progress_percentage": data.get('progress_percentage', 62.5)
            },
            "last_updated": datetime.now().isoformat()
        }
        
        return jsonify({
            "message": "Blüte-Status erfolgreich aktualisiert",
            "status": updated_status
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Aktualisieren: {str(e)}"}), 500

@flowering_bp.route('/timeline/<plant_id>')
def get_flowering_timeline(plant_id):
    """Blüte-Zeitlinie abrufen"""
    if plant_id == "GG25L":
        # Berechne Zeitlinie basierend auf aktuellen Daten
        current_day = SAMPLE_FLOWERING_DATA['current_bloom_day']
        bloom_start = datetime.strptime(SAMPLE_FLOWERING_DATA['bloom_start_date'], '%Y-%m-%d')
        
        timeline = {
            "plant_id": plant_id,
            "bloom_start_date": SAMPLE_FLOWERING_DATA['bloom_start_date'],
            "current_day": current_day,
            "events": [
                {
                    "day": 1,
                    "date": "2025-06-02",
                    "event": "Blüte gestartet",
                    "category": "milestone",
                    "description": "12/12 Lichtzyklus aktiviert"
                },
                {
                    "day": 7,
                    "date": "2025-06-08",
                    "event": "Erste Blüten sichtbar",
                    "category": "development",
                    "description": "Kleine Blütenknospen erscheinen"
                },
                {
                    "day": 14,
                    "date": "2025-06-15",
                    "event": "Stretch-Phase",
                    "category": "growth",
                    "description": "Pflanzen wachsen schnell in die Höhe"
                },
                {
                    "day": 25,
                    "date": "2025-06-27",
                    "event": "Stretch beendet",
                    "category": "growth",
                    "description": "Höhe seit BT+25 konstant"
                },
                {
                    "day": 35,
                    "date": "2025-07-07",
                    "event": "Erste Trichome",
                    "category": "development",
                    "description": "Erste milchige Trichome sichtbar"
                },
                {
                    "day": current_day,
                    "date": datetime.now().strftime('%Y-%m-%d'),
                    "event": "Aktueller Status",
                    "category": "current",
                    "description": f"Blüte-Tag {current_day}, 62.5% Fortschritt"
                },
                {
                    "day": 52,
                    "date": (bloom_start + timedelta(days=51)).strftime('%Y-%m-%d'),
                    "event": "Flush-Start geplant",
                    "category": "planned",
                    "description": "Voraussichtlicher Flush-Beginn"
                },
                {
                    "day": 65,
                    "date": (bloom_start + timedelta(days=64)).strftime('%Y-%m-%d'),
                    "event": "Ernte optimal",
                    "category": "planned",
                    "description": "Optimaler Erntezeitpunkt"
                }
            ],
            "phases": [
                {
                    "name": "Frühe Blüte",
                    "start_day": 1,
                    "end_day": 14,
                    "description": "Blütenbildung beginnt"
                },
                {
                    "name": "Stretch-Phase",
                    "start_day": 14,
                    "end_day": 25,
                    "description": "Schnelles Höhenwachstum"
                },
                {
                    "name": "Hauptblüte",
                    "start_day": 25,
                    "end_day": 50,
                    "description": "Intensive Blütenentwicklung"
                },
                {
                    "name": "Reifephase",
                    "start_day": 50,
                    "end_day": 70,
                    "description": "Trichome-Reifung und Flush"
                }
            ]
        }
        
        return jsonify(timeline)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

# Marker-Verwaltung API
@flowering_bp.route('/markers/<plant_id>')
def get_markers(plant_id):
    """Alle Marker für eine Pflanze abrufen"""
    try:
        # Pflanze aus der Datenbank laden
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"error": "Pflanze nicht gefunden"}), 404
        
        # Echte Marker aus der Datenbank laden
        markers = db.get_flowering_markers(plant_id)
        
        # Aktuellen Blüte-Tag berechnen
        current_day = 0
        if plant.get('flower_start_date'):
            try:
                bloom_start = datetime.strptime(plant['flower_start_date'], '%Y-%m-%d').date()
                today = datetime.now().date()
                current_day = max(0, (today - bloom_start).days)
            except (ValueError, TypeError):
                current_day = 0
        
        print(f"🌺 FloweringRoutes: Lade Marker für Plant {plant_id}, gefunden: {len(markers)} Marker")
        
        return jsonify({
            "plant_id": plant_id,
            "markers": markers,
            "categories": MARKER_CATEGORIES,
            "event_names": EVENT_NAMES,
            "current_day": current_day
        })
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Laden der Marker: {e}")
        return jsonify({"error": f"Fehler beim Laden der Marker: {str(e)}"}), 500

@flowering_bp.route('/marker/<plant_id>', methods=['POST'])
def add_marker(plant_id):
    """Neuen Marker hinzufügen"""
    try:
        data = request.get_json()
        print(f"🌺 FloweringRoutes: Neuer Marker für Plant {plant_id}: {data}")
        
        # Validierung
        required_fields = ['event_type', 'bloom_day', 'event_name']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Feld '{field}' fehlt"}), 400
        
        # Pflanze aus Datenbank laden
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"error": "Pflanze nicht gefunden"}), 404
        
        # Datum berechnen basierend auf Blüte-Start und Blüte-Tag
        marker_date = data.get('date')
        if not marker_date:
            # Fallback: Datum aus Blüte-Start berechnen
            if plant.get('flower_start_date'):
                try:
                    bloom_start = datetime.strptime(plant['flower_start_date'], '%Y-%m-%d')
                    marker_date = (bloom_start + timedelta(days=data['bloom_day'] - 1)).strftime('%Y-%m-%d')
                except (ValueError, TypeError):
                    marker_date = datetime.now().strftime('%Y-%m-%d')
            else:
                marker_date = datetime.now().strftime('%Y-%m-%d')
        
        # Marker in Datenbank speichern
        marker_data = {
            "plant_id": plant_id,
            "event_type": data['event_type'],
            "event_name": data['event_name'],
            "bloom_day": data['bloom_day'],
            "date": marker_date,
            "notes": data.get('notes', ''),
            "category": data.get('category', get_category_for_event(data['event_type'])),
            "importance": data.get('importance', 'medium'),
            "auto_calculated": data.get('auto_calculated', False)
        }
        
        marker_id = db.create_flowering_marker(plant_id, marker_data)
        if not marker_id:
            return jsonify({"error": "Fehler beim Speichern in Datenbank"}), 500
        
        # Marker mit ID zurückgeben
        marker_data['id'] = marker_id
        marker_data['created_at'] = datetime.now().isoformat()
        
        print(f"🌺 FloweringRoutes: Marker erfolgreich gespeichert mit ID: {marker_id}")
        
        return jsonify({
            "message": "Marker erfolgreich hinzugefügt",
            "marker": marker_data
        }), 201
        
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Hinzufügen des Markers: {e}")
        return jsonify({"error": f"Fehler beim Hinzufügen: {str(e)}"}), 500

@flowering_bp.route('/marker/<plant_id>/<int:marker_id>', methods=['PUT'])
def update_marker(plant_id, marker_id):
    """Marker bearbeiten"""
    try:
        data = request.get_json()
        print(f"🌺 FloweringRoutes: Update Marker {marker_id} für Plant {plant_id}: {data}")
        
        # Marker aus Datenbank laden
        marker = db.get_flowering_marker(marker_id)
        if not marker or marker['plant_id'] != plant_id:
            return jsonify({"error": "Marker nicht gefunden"}), 404
        
        # Marker aktualisieren
        update_data = {}
        for key, value in data.items():
            if key in ['event_type', 'event_name', 'notes', 'category', 'importance', 'bloom_day']:
                update_data[key] = value
        
        success = db.update_flowering_marker(marker_id, update_data)
        if not success:
            return jsonify({"error": "Fehler beim Aktualisieren in Datenbank"}), 500
        
        # Aktualisierten Marker zurückgeben
        updated_marker = db.get_flowering_marker(marker_id)
        
        return jsonify({
            "message": "Marker erfolgreich aktualisiert",
            "marker": updated_marker
        })
        
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Aktualisieren des Markers: {e}")
        return jsonify({"error": f"Fehler beim Aktualisieren: {str(e)}"}), 500

@flowering_bp.route('/marker/<plant_id>/<int:marker_id>', methods=['DELETE'])
def delete_marker(plant_id, marker_id):
    """Marker löschen"""
    try:
        print(f"🌺 FloweringRoutes: Lösche Marker {marker_id} für Plant {plant_id}")
        
        # Marker aus Datenbank laden
        marker = db.get_flowering_marker(marker_id)
        if not marker or marker['plant_id'] != plant_id:
            return jsonify({"error": "Marker nicht gefunden"}), 404
        
        # Marker löschen
        success = db.delete_flowering_marker(marker_id)
        if not success:
            return jsonify({"error": "Fehler beim Löschen aus Datenbank"}), 500
        
        print(f"🌺 FloweringRoutes: Marker {marker_id} erfolgreich gelöscht")
        
        return jsonify({
            "message": "Marker erfolgreich gelöscht",
            "marker": marker
        })
        
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Löschen des Markers: {e}")
        return jsonify({"error": f"Fehler beim Löschen: {str(e)}"}), 500

# Flush-Trigger-Logik
@flowering_bp.route('/flush-trigger/<plant_id>')
def get_flush_trigger_status(plant_id):
    """Flush-Trigger-Status abrufen"""
    try:
        # Lade Pflanze aus Datenbank
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"error": "Pflanze nicht gefunden"}), 404
        
        # Aktuellen Status berechnen
        check_and_update_flush_trigger(plant_id)
        
        # Lade Guidelines
        guidelines = load_flush_harvest_guidelines()
        flush_guidelines = guidelines.get('flush', {})
        
        # Bestimme Strain-Typ für Guidelines-Filterung
        strain_type = plant.get('strain_type', 'photoperiod').lower()
        if 'auto' in strain_type:
            strain_type = 'autoflower'
        else:
            strain_type = 'photoperiod'
        
        # Filtere Startbedingungen nach Strain-Typ
        startbedingungen = flush_guidelines.get('startbedingungen', {})
        filtered_startbedingungen = {}
        
        if strain_type in startbedingungen:
            filtered_startbedingungen[strain_type] = startbedingungen[strain_type]
        
        # Berechne konsistenten Flush-Status basierend auf echten Daten
        bloom_start_date = plant.get('flower_start_date')
        if not bloom_start_date:
            return jsonify({"error": "Blüte-Start-Datum nicht gefunden"}), 400
        
        bloom_start = datetime.strptime(bloom_start_date, '%Y-%m-%d')
        current_day = (datetime.now() - bloom_start).days
        
        # Strain-Profile für Flush-Berechnung
        strain_profile = {
            "name": plant.get('strain', 'Unbekannt'),
            "strain": plant.get('strain', 'Unbekannt'),
            "flowering_duration_days": [60, 75],  # Standard für Autoflower
            "flush_recommendation": "14 Tage vor Ernte",
            "stretch_estimate_days": [7, 25]
        }
        
        # Flush-Start berechnen (14 Tage vor Ernte)
        flowering_range = strain_profile["flowering_duration_days"]
        estimated_flush_start = max(0, flowering_range[1] - 14)
        
        # Konsistenter Flush-Status
        flush_status = {
            "triggered": current_day >= estimated_flush_start,
            "reason": "Automatisch basierend auf Blüte-Tagen" if current_day >= estimated_flush_start else None,
            "start_recommended": estimated_flush_start,
            "flush_duration_days": 14,
            "flush_day_today": max(0, current_day - estimated_flush_start) if current_day >= estimated_flush_start else 0,
            "flush_target_day": flowering_range[1],
            "status_message": f"Flush empfohlen ab Tag {estimated_flush_start}" if current_day >= estimated_flush_start else f"Flush noch nicht empfohlen (ab Tag {estimated_flush_start})"
        }
        
        response_data = {
            "plant_id": plant_id,
            "plant_name": plant.get('plant_name', ''),
            "strain_type": strain_type,
            "flowering_status": {
                "current_day": current_day,
                "bloom_start_date": bloom_start_date
            },
            "flush_status": flush_status,
            "trigger_conditions": get_trigger_conditions(plant_id),
            "flush_guidelines": {
                "startbedingungen": filtered_startbedingungen,
                "methoden": flush_guidelines.get('methoden', []),
                "fehlerquellen": flush_guidelines.get('fehlerquellen', []),
                "faustregeln": guidelines.get('faustregeln', [])  # Auf oberster Ebene
            }
        }
        
        print(f"🌺 FloweringRoutes: Sende flush_guidelines für {strain_type} mit {len(filtered_startbedingungen.get(strain_type, {}).get('indikatoren', []))} Indikatoren")
        print(f"🌺 FloweringRoutes: flush_guidelines keys: {list(response_data['flush_guidelines'].keys())}")
        
        return jsonify(response_data)
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Laden der Flush-Trigger-Daten: {e}")
        return jsonify({"error": f"Fehler beim Laden der Daten: {str(e)}"}), 500

@flowering_bp.route('/flush-trigger/<plant_id>', methods=['POST'])
def trigger_flush_manual(plant_id):
    """Flush manuell auslösen"""
    try:
        data = request.get_json()
        
        # Manueller Flush-Trigger
        manual_override = {
            "manual_flush_override": True,
            "override_reason": data.get('reason', 'Manueller Trigger'),
            "flush_start_day": data.get('flush_start_day', SAMPLE_FLOWERING_DATA['current_bloom_day'])
        }
        
        # Flush-Status aktualisieren
        SAMPLE_FLOWERING_DATA['flush_status'].update({
            "triggered": True,
            "reason": f"Manuell: {manual_override['override_reason']}",
            "start_recommended": manual_override['flush_start_day'],
            "status_message": f"Flush manuell ausgelöst: {manual_override['override_reason']}"
        })
        
        return jsonify({
            "message": "Flush erfolgreich manuell ausgelöst",
            "flush_status": SAMPLE_FLOWERING_DATA['flush_status'],
            "manual_override": manual_override
        })
        
    except Exception as e:
        return jsonify({"error": f"Fehler beim Auslösen: {str(e)}"}), 500

@flowering_bp.route('/prediction/<plant_id>')
def get_flowering_prediction(plant_id):
    """Blüte-Prognose abrufen"""
    try:
        # Lade Pflanze aus Datenbank
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({"error": "Pflanze nicht gefunden"}), 404
        
        # Berechne aktuellen Tag basierend auf echten Daten
        bloom_start_date = plant.get('flower_start_date')
        if not bloom_start_date:
            return jsonify({"error": "Blüte-Start-Datum nicht gefunden"}), 400
        
        bloom_start = datetime.strptime(bloom_start_date, '%Y-%m-%d')
        current_day = (datetime.now() - bloom_start).days
        
        # Strain-Profile für Berechnung
        strain_profile = {
            "name": plant.get('strain', 'Unbekannt'),
            "strain": plant.get('strain', 'Unbekannt'),
            "flowering_duration_days": [60, 75],  # Standard für Autoflower
            "flush_recommendation": "14 Tage vor Ernte",
            "stretch_estimate_days": [7, 25]
        }
        
        # Flush-Start berechnen (14 Tage vor Ernte)
        flowering_range = strain_profile["flowering_duration_days"]
        estimated_flush_start = max(0, flowering_range[1] - 14)
        estimated_harvest_window = [flowering_range[1] - 7, flowering_range[1] + 7]
        
        # Berechne Prognosen
        days_until_flush = estimated_flush_start - current_day
        days_until_harvest = estimated_harvest_window[0] - current_day
        
        prediction = {
            "plant_id": plant_id,
            "current_day": current_day,
            "strain_name": strain_profile['name'],
            "predictions": {
                "flush_start": {
                    "day": estimated_flush_start,
                    "days_remaining": days_until_flush,
                    "date": (datetime.now() + timedelta(days=days_until_flush)).strftime('%Y-%m-%d'),
                    "confidence": "high" if days_until_flush > 0 else "current"
                },
                "harvest_optimal": {
                    "day": estimated_harvest_window[0],
                    "days_remaining": days_until_harvest,
                    "date": (datetime.now() + timedelta(days=days_until_harvest)).strftime('%Y-%m-%d'),
                    "confidence": "high"
                },
                "harvest_window": {
                    "early": {
                        "day": estimated_harvest_window[0],
                        "days_remaining": estimated_harvest_window[0] - current_day,
                        "date": (datetime.now() + timedelta(days=estimated_harvest_window[0] - current_day)).strftime('%Y-%m-%d')
                    },
                    "late": {
                        "day": estimated_harvest_window[1],
                        "days_remaining": estimated_harvest_window[1] - current_day,
                        "date": (datetime.now() + timedelta(days=estimated_harvest_window[1] - current_day)).strftime('%Y-%m-%d')
                    }
                }
            },
            "recommendations": {
                "immediate": [
                    "Trichome alle 2-3 Tage kontrollieren",
                    "EC/pH-Werte überwachen",
                    "Flush-Vorbereitung starten"
                ],
                "next_week": [
                    "Flush-Medium vorbereiten",
                    "Ernte-Equipment bereitstellen",
                    "Trocknungsraum vorbereiten"
                ],
                "next_month": [
                    "Ernte durchführen",
                    "Trocknung starten",
                    "Curing vorbereiten"
                ]
            },
            "risk_factors": {
                "high": [],
                "medium": [
                    "Trichome-Entwicklung langsamer als erwartet"
                ],
                "low": [
                    "Wetteränderungen könnten Klima beeinflussen"
                ]
            }
        }
        
        return jsonify(prediction)
        
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Laden der Prognose: {e}")
        return jsonify({"error": f"Fehler beim Laden der Prognose: {str(e)}"}), 500

@flowering_bp.route('/progress/<plant_id>')
def get_flowering_progress(plant_id):
    """Blüte-Fortschritt abrufen"""
    if plant_id == "GG25L":
        current_day = SAMPLE_FLOWERING_DATA['current_bloom_day']
        strain_profile = SAMPLE_FLOWERING_DATA['strain_profile']
        
        # Berechne Fortschritt
        total_flowering_days = strain_profile['flowering_duration_days'][1]  # 75 Tage
        progress_percentage = (current_day / total_flowering_days) * 100
        
        # Bestimme Phase
        if current_day <= 14:
            phase = "Frühe Blüte"
            phase_progress = (current_day / 14) * 100
        elif current_day <= 25:
            phase = "Stretch-Phase"
            phase_progress = ((current_day - 14) / 11) * 100
        elif current_day <= 50:
            phase = "Hauptblüte"
            phase_progress = ((current_day - 25) / 25) * 100
        else:
            phase = "Reifephase"
            phase_progress = ((current_day - 50) / 25) * 100
        
        progress = {
            "plant_id": plant_id,
            "current_day": current_day,
            "total_flowering_days": total_flowering_days,
            "overall_progress": round(progress_percentage, 1),
            "current_phase": phase,
            "phase_progress": round(phase_progress, 1),
            "milestones": {
                "stretch_completed": current_day > 25,  # Stretch endet bei Tag 25
                "first_trichomes": current_day >= 35,
                "flush_ready": current_day >= 45,
                "harvest_ready": current_day >= 60
            },
            "next_milestones": [
                {
                    "name": "Flush-Start",
                    "day": SAMPLE_FLOWERING_DATA['estimated_flush_start'],
                    "days_remaining": SAMPLE_FLOWERING_DATA['estimated_flush_start'] - current_day,
                    "description": "Nährstoffzufuhr stoppen"
                },
                {
                    "name": "Ernte optimal",
                    "day": SAMPLE_FLOWERING_DATA['harvest_prediction']['optimal'][0],
                    "days_remaining": SAMPLE_FLOWERING_DATA['harvest_prediction']['optimal'][0] - current_day,
                    "description": "Beste Qualität"
                }
            ]
        }
        
        return jsonify(progress)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

@flowering_bp.route('/history/<plant_id>')
def get_flowering_history(plant_id):
    """Blüte-Historie abrufen"""
    if plant_id == "GG25L":
        # Simulierte Historie
        history = {
            "plant_id": plant_id,
            "history": [
                {
                    "date": "2025-06-02",
                    "day": 1,
                    "event": "Blüte gestartet",
                    "phase": "preflower",
                    "notes": "12/12 Lichtzyklus aktiviert"
                },
                {
                    "date": "2025-06-09",
                    "day": 7,
                    "event": "Stretch beginnt",
                    "phase": "stretch",
                    "notes": "Wachstum beschleunigt sich"
                },
                {
                    "date": "2025-06-27",
                    "day": 25,
                    "event": "Stretch beendet",
                    "phase": "flowering_middle",
                    "notes": "Wachstum stabilisiert, Blütenbildung intensiviert"
                },
                {
                    "date": "2025-07-13",
                    "day": 41,
                    "event": "Aktueller Stand",
                    "phase": "flowering_middle",
                    "notes": "Blütenbildung läuft optimal"
                }
            ]
        }
        return jsonify(history)
    else:
        return jsonify({"error": "Pflanze nicht gefunden"}), 404

# ===== TRICHOM-INTEGRATION ROUTES =====

@flowering_bp.route('/trichome-status/<plant_id>')
def get_trichome_status(plant_id):
    """Gibt den aktuellen Trichom-Status und alle Beobachtungen zurück"""
    trichome_entries = db.get_trichome_entries(plant_id)
    if not trichome_entries:
        return jsonify({"has_data": False, "message": "Keine Trichom-Beobachtungen vorhanden"})
    latest = sorted(trichome_entries, key=lambda x: x['date'])[-1]
    status = calculate_trichome_status(latest)
    status['observations'] = trichome_entries
    return jsonify(status)

@flowering_bp.route('/trichome-observation/<plant_id>', methods=['POST'])
def save_trichome_observation(plant_id):
    """Neue Trichom-Beobachtung speichern (persistente DB)"""
    try:
        data = request.get_json()
        required_fields = ['date', 'bloom_day', 'location', 'clear_percentage', 'milky_percentage', 'amber_percentage']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Feld '{field}' fehlt"}), 400
        entry_id = db.add_trichome_entry(plant_id, data)
        observation = db.get_trichome_entry(entry_id)
        return jsonify({
            "success": True,
            "message": "Trichom-Beobachtung erfolgreich gespeichert",
            "observation": observation
        })
    except Exception as e:
        return jsonify({"error": f"Fehler beim Speichern: {str(e)}"}), 500

@flowering_bp.route('/trichome-observation/<plant_id>', methods=['PUT'])
def update_trichome_observation(plant_id):
    """Trichom-Beobachtung bearbeiten (persistente DB)"""
    try:
        data = request.get_json()
        entry_id = data.get('id')
        if not entry_id:
            return jsonify({"error": "ID der Beobachtung fehlt"}), 400
        db.update_trichome_entry(entry_id, data)
        observation = db.get_trichome_entry(entry_id)
        return jsonify({
            "success": True,
            "message": "Trichom-Beobachtung erfolgreich bearbeitet",
            "observation": observation
        })
    except Exception as e:
        return jsonify({"error": f"Fehler beim Bearbeiten: {str(e)}"}), 500

@flowering_bp.route('/trichome-observation/<plant_id>', methods=['DELETE'])
def delete_trichome_observation(plant_id):
    """Trichom-Beobachtung löschen (persistente DB)"""
    try:
        entry_id = request.args.get('id', type=int)
        if not entry_id:
            return jsonify({"error": "ID der Beobachtung fehlt"}), 400
        db.delete_trichome_entry(entry_id)
        return jsonify({
            "success": True,
            "message": "Trichom-Beobachtung erfolgreich gelöscht",
            "deleted_observation_id": entry_id
        })
    except Exception as e:
        return jsonify({"error": f"Fehler beim Löschen: {str(e)}"}), 500

@flowering_bp.route('/trichome-trigger/<plant_id>')
def check_trichome_flush_trigger(plant_id):
    """Prüft ob Trichom-Status Flush-Trigger auslöst (DB)"""
    try:
        trichome_entries = db.get_trichome_entries(plant_id)
        if trichome_entries:
            latest_entry = sorted(trichome_entries, key=lambda x: x['date'])[-1]
            milky = latest_entry['milky_percentage']
            amber = latest_entry['amber_percentage']
            clear = latest_entry['clear_percentage']
            
            # Erweiterte Flush-Trigger-Bedingungen basierend auf Trichom-Entwicklung
            triggered = False
            trigger_reason = ""
            trigger_type = "none"
            
            # Bedingung 1: Optimaler Flush-Start (70-80% milchig, 10-20% bernstein)
            if milky >= 70 and amber >= 10 and amber <= 25:
                triggered = True
                trigger_type = "optimal"
                trigger_reason = f"Optimaler Flush-Start: {milky}% milchig, {amber}% bernstein"
            
            # Bedingung 2: Später Flush-Start (hohe Bernstein-Werte)
            elif amber >= 25:
                triggered = True
                trigger_type = "late"
                trigger_reason = f"Später Flush-Start: {amber}% bernstein Trichome"
            
            # Bedingung 3: Früher Flush-Start (sehr hohe milchige Werte)
            elif milky >= 85:
                triggered = True
                trigger_type = "early"
                trigger_reason = f"Früher Flush-Start: {milky}% milchige Trichome"
            
            # Bedingung 4: Erntebereit (sehr hohe Bernstein-Werte)
            elif amber >= 30:
                triggered = True
                trigger_type = "harvest"
                trigger_reason = f"Erntebereit: {amber}% bernstein Trichome"
            
            # Berechne Empfehlung basierend auf Trigger-Typ
            if triggered:
                if trigger_type == "optimal":
                    recommendation = "Flush für 14 Tage starten"
                    urgency = "high"
                    action = "flush_start"
                elif trigger_type == "late":
                    recommendation = "Sofortiger Flush für 7-10 Tage"
                    urgency = "critical"
                    action = "flush_urgent"
                elif trigger_type == "early":
                    recommendation = "Flush für 10-12 Tage starten"
                    urgency = "medium"
                    action = "flush_start"
                elif trigger_type == "harvest":
                    recommendation = "Ernte vorbereiten - Flush optional"
                    urgency = "critical"
                    action = "harvest_prepare"
            else:
                # Berechne Fortschritt zum nächsten Trigger
                progress_to_flush = 0
                if milky >= 50:
                    progress_to_flush = min(100, ((milky - 50) * 5 + amber * 3))
                else:
                    progress_to_flush = min(50, milky * 1.5)
                
                recommendation = f"Flush in {max(0, 70 - milky)}% milchigen Trichomen"
                urgency = "low"
                action = "monitor"
                trigger_reason = f"Fortschritt zum Flush: {round(progress_to_flush, 1)}%"
            
            return jsonify({
                "plant_id": plant_id,
                "trigger_status": {
                    "triggered": triggered,
                    "trigger_type": trigger_type,
                    "reason": trigger_reason,
                    "recommendation": recommendation,
                    "urgency": urgency,
                    "action": action,
                    "message": f"{milky}% milchige, {amber}% bernstein Trichome"
                },
                "trichome_data": {
                    "date": latest_entry['date'],
                    "bloom_day": latest_entry['bloom_day'],
                    "clear_percent": clear,
                    "milky_percent": milky,
                    "amber_percent": amber,
                    "location": latest_entry.get('location', ''),
                    "notes": latest_entry.get('notes', '')
                }
            })
        else:
            return jsonify({
                "plant_id": plant_id,
                "trigger_status": {
                    "triggered": False,
                    "trigger_type": "none",
                    "reason": "Keine Trichom-Daten verfügbar",
                    "recommendation": "Trichom-Analyse durchführen",
                    "urgency": "low",
                    "action": "monitor"
                }
            })
    except Exception as e:
        return jsonify({"error": f"Fehler bei Trichom-Trigger-Prüfung: {str(e)}"}), 500

@flowering_bp.route('/trichome-recommendation/<plant_id>')
def get_trichome_recommendation(plant_id):
    """Gibt Trichom-basierte Empfehlungen für Blüte-Management (DB)"""
    try:
        trichome_entries = db.get_trichome_entries(plant_id)
        if trichome_entries:
            latest_entry = sorted(trichome_entries, key=lambda x: x['date'])[-1]
            milky = latest_entry['milky_percentage']
            amber = latest_entry['amber_percentage']
            clear = latest_entry['clear_percentage']
            
            # Erweiterte Empfehlung basierend auf Trichom-Verhältnis und Trend
            if amber >= 30:
                recommendation_type = "harvest_recommended"
                message = "Trichome zeigen optimale Erntebereitschaft"
                action = "Ernte vorbereiten"
                urgency = "critical"
                details = f"{amber}% bernstein Trichome - optimal für Ernte"
                next_check = "Sofort"
                tips = [
                    "Ernte in den nächsten 3-5 Tagen durchführen",
                    "Trocknung vorbereiten",
                    "Curing-Plan erstellen"
                ]
            elif amber >= 20 and milky >= 60:
                recommendation_type = "harvest_approaching"
                message = "Ernte nähert sich - Trichome entwickeln sich optimal"
                action = "Ernte vorbereiten"
                urgency = "high"
                details = f"{amber}% bernstein, {milky}% milchige Trichome - Ernte in 5-10 Tagen"
                next_check = "2-3 Tage"
                tips = [
                    "Trichome täglich kontrollieren",
                    "Ernte-Zeitplan erstellen",
                    "Trocknungsraum vorbereiten"
                ]
            elif milky >= 70 and amber >= 10:
                recommendation_type = "flush_recommended"
                message = "Trichome zeigen optimale Flush-Bereitschaft"
                action = "Flush für 14 Tage starten"
                urgency = "high"
                details = f"{milky}% milchige, {amber}% bernstein Trichome - perfekt für Flush-Start"
                next_check = "Sofort"
                tips = [
                    "Nährstoffe sofort stoppen",
                    "Nur Wasser für 14 Tage",
                    "Trichome weiter beobachten"
                ]
            elif milky >= 50 and amber >= 5:
                recommendation_type = "flush_approaching"
                message = "Flush nähert sich - Trichome entwickeln sich gut"
                action = "Flush vorbereiten"
                urgency = "medium"
                details = f"{milky}% milchige, {amber}% bernstein Trichome - Flush in 5-10 Tagen"
                next_check = "3-5 Tage"
                tips = [
                    "Nährstoff-Dosierung reduzieren",
                    "Trichome alle 2-3 Tage kontrollieren",
                    "Flush-Plan erstellen"
                ]
            elif milky >= 30:
                recommendation_type = "monitoring_recommended"
                message = "Trichome entwickeln sich gut"
                action = "Weiter beobachten"
                urgency = "medium"
                details = f"{milky}% milchige Trichome - Entwicklung läuft gut"
                next_check = "5-7 Tage"
                tips = [
                    "Nährstoffe normal weitergeben",
                    "Trichome wöchentlich kontrollieren",
                    "Entwicklung dokumentieren"
                ]
            else:
                recommendation_type = "early_development"
                message = "Frühe Trichom-Entwicklung"
                action = "Geduldig warten"
                urgency = "low"
                details = f"{milky}% milchige Trichome - noch in früher Entwicklung"
                next_check = "7-10 Tage"
                tips = [
                    "Nährstoffe normal weitergeben",
                    "Pflanzen nicht stressen",
                    "Regelmäßige Kontrollen"
                ]
            
            return jsonify({
                "plant_id": plant_id,
                "recommendation": {
                    "type": recommendation_type,
                    "message": message,
                    "action": action,
                    "urgency": urgency,
                    "details": details,
                    "next_check": next_check,
                    "tips": tips
                },
                "trichome_data": {
                    "date": latest_entry['date'],
                    "bloom_day": latest_entry['bloom_day'],
                    "clear_percent": clear,
                    "milky_percent": milky,
                    "amber_percent": amber,
                    "location": latest_entry.get('location', ''),
                    "notes": latest_entry.get('notes', '')
                },
                "observations_count": len(trichome_entries)
            })
        else:
            return jsonify({
                "plant_id": plant_id,
                "recommendation": {
                    "type": "none",
                    "message": "Keine Trichom-Daten verfügbar",
                    "action": "Trichom-Analyse durchführen",
                    "urgency": "low"
                }
            })
    except Exception as e:
        return jsonify({"error": f"Fehler bei Trichom-Empfehlung: {str(e)}"}), 500

@flowering_bp.route('/trichome-progress/<plant_id>')
def get_trichome_progress(plant_id):
    """Gibt den Fortschritt der Trichom-Entwicklung zurück (DB)"""
    try:
        trichome_entries = db.get_trichome_entries(plant_id)
        if trichome_entries:
            progress = calculate_trichome_progress(trichome_entries)
            return jsonify(progress)
        else:
            return jsonify({"has_data": False, "message": "Keine Trichom-Beobachtungen vorhanden"})
    except Exception as e:
        return jsonify({"error": f"Fehler beim Berechnen des Trichom-Fortschritts: {str(e)}"}), 500

# ===== TRICHOM-HELPER FUNCTIONS =====

def calculate_trichome_status(trichome_entry):
    """Berechnet den aktuellen Trichom-Status und Empfehlungen"""
    clear = trichome_entry['clear_percentage']
    milky = trichome_entry['milky_percentage']
    amber = trichome_entry['amber_percentage']
    
    # Validiere dass Summe = 100%
    total = clear + milky + amber
    if total != 100:
        # Normalisiere auf 100%
        factor = 100 / total
        clear = int(clear * factor)
        milky = int(milky * factor)
        amber = int(amber * factor)
    
    # Bestimme Reifegrad
    maturity_level = "early"
    if milky >= 70 and amber >= 20:
        maturity_level = "flush_ready"
    elif milky >= 90 and amber >= 10:
        maturity_level = "harvest_ready"
    elif milky >= 50:
        maturity_level = "developing"
    
    # Berechne Fortschritt zu Flush-Ziel
    flush_progress = min(100, (milky / TRICHOME_TARGETS["flush_start"]["milky_percent"]) * 100)
    
    return {
        "has_data": True,
        "maturity_level": maturity_level,
        "clear_percent": clear,
        "milky_percent": milky,
        "amber_percent": amber,
        "flush_progress": flush_progress,
        "recommendation": get_trichome_recommendation_text(maturity_level, milky, amber),
        "urgency": get_trichome_urgency(maturity_level)
    }

def check_trichome_flush_conditions(trichome_entry):
    """Prüft ob Trichom-Status Flush-Trigger auslöst"""
    clear = trichome_entry['clear_percentage']
    milky = trichome_entry['milky_percentage']
    amber = trichome_entry['amber_percentage']  # Korrigiert von 'amber_percent'
    
    # Flush-Start-Bedingung
    if milky >= TRICHOME_TARGETS["flush_start"]["milky_percent"] and amber >= TRICHOME_TARGETS["flush_start"]["amber_percent"]:
        return {
            "triggered": True,
            "trigger_type": "flush_start",
            "reason": f"Trichomstatus erreicht: {milky}% milchig, {amber}% bernstein",
            "message": "Flush kann gestartet werden!",
            "urgency": "medium",
            "recommended_action": "Flush-Phase starten"
        }
    
    # Flush-Intensivierung
    elif milky >= TRICHOME_TARGETS["flush_intensify"]["milky_percent"] and amber >= TRICHOME_TARGETS["flush_intensify"]["amber_percent"]:
        return {
            "triggered": True,
            "trigger_type": "flush_intensify",
            "reason": f"Hohe Reife: {milky}% milchig, {amber}% bernstein",
            "message": "Flush intensivieren oder Ernte planen!",
            "urgency": "high",
            "recommended_action": "Flush intensivieren"
        }
    
    # Ernte-Bereitschaft
    elif milky >= TRICHOME_TARGETS["harvest_ready"]["milky_percent"] and amber >= TRICHOME_TARGETS["harvest_ready"]["amber_percent"]:
        return {
            "triggered": True,
            "trigger_type": "harvest_ready",
            "reason": f"Optimaler Erntezeitpunkt: {milky}% milchig, {amber}% bernstein",
            "message": "Pflanze ist erntebereit!",
            "urgency": "high",
            "recommended_action": "Ernte vorbereiten"
        }
    
    # Kein Trigger
    else:
        return {
            "triggered": False,
            "trigger_type": None,
            "reason": f"Trichomstatus: {milky}% milchig, {amber}% bernstein - noch nicht reif genug",
            "message": "Weiter beobachten",
            "urgency": "low",
            "recommended_action": "Trichome weiter beobachten"
        }

def generate_trichome_recommendation(trichome_entry):
    """Generiert Trichom-basierte Empfehlungen"""
    clear = trichome_entry['clear_percentage']
    milky = trichome_entry['milky_percentage']
    amber = trichome_entry['amber_percentage']  # Korrigiert von 'amber_percent'
    bloom_day = trichome_entry['bloom_day']
    
    # Bestimme Empfehlungstyp
    if milky >= 90 and amber >= 10:
        return {
            "type": "harvest_prepare",
            "message": "Pflanze ist erntebereit!",
            "action": "Ernte vorbereiten",
            "urgency": "high",
            "details": f"Trichomstatus: {milky}% milchig, {amber}% bernstein"
        }
    elif milky >= 70 and amber >= 20:
        return {
            "type": "flush_start",
            "message": "Flush-Phase kann gestartet werden",
            "action": "Flush starten",
            "urgency": "medium",
            "details": f"Trichomstatus: {milky}% milchig, {amber}% bernstein"
        }
    elif milky >= 50:
        return {
            "type": "continue_monitoring",
            "message": "Trichome entwickeln sich gut",
            "action": "Weiter beobachten",
            "urgency": "low",
            "details": f"Trichomstatus: {milky}% milchig, {amber}% bernstein"
        }
    else:
        return {
            "type": "early_stage",
            "message": "Trichome noch in früher Entwicklung",
            "action": "Geduldig warten",
            "urgency": "low",
            "details": f"Trichomstatus: {milky}% milchig, {amber}% bernstein"
        }

def calculate_trichome_progress(trichome_entries):
    """Berechnet Trichom-Fortschritt über Zeit"""
    if not trichome_entries:
        return {
            "has_data": False,
            "message": "Keine Trichom-Beobachtungen vorhanden"
        }
    
    # Sortiere nach Datum
    sorted_entries = sorted(trichome_entries, key=lambda x: x['date'])
    latest_entry = sorted_entries[-1]
    
    # Berechne aktuelle Werte
    clear = latest_entry['clear_percentage']
    milky = latest_entry['milky_percentage']
    amber = latest_entry['amber_percentage']
    
    # Berechne Entwicklungsfortschritt (0-100%)
    development_progress = 0
    if milky >= 80 and amber >= 20:
        development_progress = 100  # Optimal reif
    elif milky >= 70 and amber >= 10:
        development_progress = 85 + (amber - 10) * 1.5  # Flush-bereit
    elif milky >= 50:
        development_progress = 60 + (milky - 50) * 1.25  # Gut entwickelt
    elif milky >= 30:
        development_progress = 30 + (milky - 30) * 1.5  # Entwickelnd
    else:
        development_progress = milky * 1.0  # Frühe Entwicklung
    
    # Berechne Reifungsfortschritt (0-100%)
    maturity_progress = min(100, amber * 3.33)  # 30% Bernstein = 100% reif
    
    # Berechne Flush-Fortschritt (0-100%)
    flush_progress = 0
    if milky >= 70 and amber >= 10:
        flush_progress = min(100, ((milky - 70) * 2 + (amber - 10) * 3))
    elif milky >= 50:
        flush_progress = min(50, (milky - 50) * 2.5)
    else:
        flush_progress = min(25, milky * 0.5)
    
    # Berechne Trends basierend auf mehreren Beobachtungen
    trend_data = {
        "milky_trend": "stabil",
        "amber_trend": "stabil",
        "development_speed": "normal",
        "predicted_harvest_days": None
    }
    
    if len(sorted_entries) >= 2:
        # Berechne Änderungsraten
        first_obs = sorted_entries[0]
        last_obs = sorted_entries[-1]
        
        try:
            days_between = (datetime.strptime(last_obs['date'], '%Y-%m-%d') - 
                           datetime.strptime(first_obs['date'], '%Y-%m-%d')).days
            
            if days_between > 0:
                milky_change = last_obs['milky_percentage'] - first_obs['milky_percentage']
                amber_change = last_obs['amber_percentage'] - first_obs['amber_percentage']
                
                milky_per_day = milky_change / days_between
                amber_per_day = amber_change / days_between
                
                # Trend-Bewertung
                if milky_per_day > 3:
                    trend_data["milky_trend"] = "schnell steigend"
                elif milky_per_day > 1:
                    trend_data["milky_trend"] = "steigend"
                elif milky_per_day < -1:
                    trend_data["milky_trend"] = "fallend"
                
                if amber_per_day > 2:
                    trend_data["amber_trend"] = "schnell steigend"
                elif amber_per_day > 0.5:
                    trend_data["amber_trend"] = "steigend"
                elif amber_per_day < -0.5:
                    trend_data["amber_trend"] = "fallend"
                
                # Entwicklungsgeschwindigkeit
                if amber_per_day > 1.5:
                    trend_data["development_speed"] = "schnell"
                elif amber_per_day > 0.5:
                    trend_data["development_speed"] = "normal"
                else:
                    trend_data["development_speed"] = "langsam"
                
                # Vorhersage Ernte-Tage
                if amber_per_day > 0:
                    days_to_harvest = (30 - last_obs['amber_percentage']) / amber_per_day
                    if days_to_harvest > 0 and days_to_harvest < 30:
                        trend_data["predicted_harvest_days"] = round(days_to_harvest)
        except:
            pass  # Falls Datum-Parsing fehlschlägt
    
    # Erstelle Fortschritts-Phasen
    phases = {
        "early_development": {
            "name": "Frühe Entwicklung",
            "target_milky": 30,
            "current_progress": min(100, (milky / 30) * 100),
            "completed": milky >= 30
        },
        "mid_development": {
            "name": "Mittlere Entwicklung",
            "target_milky": 50,
            "current_progress": min(100, max(0, ((milky - 30) / 20) * 100)),
            "completed": milky >= 50
        },
        "advanced_development": {
            "name": "Fortgeschrittene Entwicklung",
            "target_milky": 70,
            "current_progress": min(100, max(0, ((milky - 50) / 20) * 100)),
            "completed": milky >= 70
        },
        "flush_ready": {
            "name": "Flush-Bereit",
            "target_amber": 10,
            "current_progress": min(100, (amber / 10) * 100),
            "completed": amber >= 10
        },
        "harvest_ready": {
            "name": "Ernte-Bereit",
            "target_amber": 20,
            "current_progress": min(100, (amber / 20) * 100),
            "completed": amber >= 20
        }
    }
    
    return {
        "has_data": True,
        "plant_id": latest_entry['plant_id'],
        "progress": {
            "development_progress": round(development_progress, 1),
            "maturity_progress": round(maturity_progress, 1),
            "flush_progress": round(flush_progress, 1),
            "overall_progress": round((development_progress + maturity_progress + flush_progress) / 3, 1)
        },
        "current_values": {
            "milky_percentage": milky,
            "amber_percentage": amber,
            "clear_percentage": clear,
            "date": latest_entry['date'],
            "bloom_day": latest_entry['bloom_day']
        },
        "trend": trend_data,
        "phases": phases,
        "observations_count": len(trichome_entries),
        "latest_observation": {
            "date": latest_entry['date'],
            "bloom_day": latest_entry['bloom_day'],
            "location": latest_entry.get('location', ''),
            "notes": latest_entry.get('notes', '')
        }
    }

def get_trichome_recommendation_text(maturity_level, milky, amber):
    """Gibt Text-Empfehlung basierend auf Reifegrad"""
    if maturity_level == "harvest_ready":
        return f"Erntebereit! {milky}% milchig, {amber}% bernstein"
    elif maturity_level == "flush_ready":
        return f"Flush kann starten: {milky}% milchig, {amber}% bernstein"
    elif maturity_level == "developing":
        return f"Entwicklung läuft: {milky}% milchig, {amber}% bernstein"
    else:
        return f"Frühe Phase: {milky}% milchig, {amber}% bernstein"

def get_trichome_urgency(maturity_level):
    """Gibt Dringlichkeit basierend auf Reifegrad"""
    if maturity_level == "harvest_ready":
        return "high"
    elif maturity_level == "flush_ready":
        return "medium"
    else:
        return "low" 

# Hilfsfunktionen
def get_category_for_event(event_type):
    """Kategorie für Event-Typ bestimmen"""
    for category, events in MARKER_CATEGORIES.items():
        if event_type in events:
            return category
    return "custom"

def check_and_update_flush_trigger(plant_id):
    """Flush-Trigger prüfen und aktualisieren"""
    if plant_id not in ["GG25L", "ebc30eef-80fe-4709-930d-91e919fe8082"]:
        return
    
    current_day = SAMPLE_FLOWERING_DATA['current_bloom_day']
    flowering_range = SAMPLE_FLOWERING_DATA['strain_profile']['flowering_duration_days']
    
    # Trigger-Bedingungen prüfen
    triggered, reason = check_flush_trigger_conditions(current_day, flowering_range)
    
    if triggered:
        SAMPLE_FLOWERING_DATA['flush_status'].update({
            "triggered": True,
            "reason": reason,
            "start_recommended": current_day,
            "status_message": f"Flush empfohlen: {reason}"
        })
    else:
        SAMPLE_FLOWERING_DATA['flush_status'].update({
            "triggered": False,
            "reason": None,
            "status_message": "Flush noch nicht empfohlen"
        })

def check_flush_trigger_conditions(current_day, flowering_range):
    """Flush-Trigger-Bedingungen prüfen"""
    # Bedingung 1: Tagefortschritt (≥85% der Breeder-Zeit)
    if current_day >= 0.85 * flowering_range[1]:
        return True, f"Blütefortschritt >85% (Tag {current_day}/{flowering_range[1]})"
    
    # Bedingung 2: Trichome-Status (aus Markern)
    trichome_markers = [m for m in SAMPLE_FLOWERING_DATA['markers'] 
                       if m['event_type'] in ['trichome_milky', 'trichome_amber']]
    
    if trichome_markers:
        latest_trichome = max(trichome_markers, key=lambda x: x['bloom_day'])
        if latest_trichome['event_type'] == 'trichome_milky' and latest_trichome['bloom_day'] >= 45:
            return True, f"Milchige Trichome seit Tag {latest_trichome['bloom_day']}"
    
    # Bedingung 3: Pistillen-Status
    pistil_markers = [m for m in SAMPLE_FLOWERING_DATA['markers'] 
                     if m['event_type'] == 'pistils_retracted']
    if pistil_markers:
        return True, "Pistillen zurückgezogen"
    
    return False, None

def load_flush_harvest_guidelines():
    """Lädt die Flush-Harvest-Guidelines aus der JSON-Datei"""
    try:
        with open('static/data/flush-harvest-guidelines.json', 'r', encoding='utf-8') as f:
            guidelines = json.load(f)
        print(f"🌺 FloweringRoutes: Guidelines erfolgreich geladen: {len(guidelines.get('flushHarvestGuidelines', {}).get('flush', {}).get('startbedingungen', {}))} Startbedingungen")
        return guidelines.get('flushHarvestGuidelines', {})
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Laden der Guidelines: {e}")
        return {}

def get_trigger_conditions(plant_id):
    """Gibt die Trigger-Bedingungen für eine Pflanze zurück"""
    # Lade Guidelines
    guidelines = load_flush_harvest_guidelines()
    flush_guidelines = guidelines.get('flush', {})
    
    # Standard-Bedingungen
    trigger_conditions = {
        "bloom_day_range": [50, 70],
        "trichome_milky_min": 70,
        "trichome_amber_min": 20,
        "auto_trigger": True,
        "guidelines": {
            "startbedingungen": flush_guidelines.get('startbedingungen', {}),
            "methoden": flush_guidelines.get('methoden', []),
            "fehlerquellen": flush_guidelines.get('fehlerquellen', []),
            "faustregeln": flush_guidelines.get('faustregeln', [])
        }
    }
    
    return trigger_conditions

def load_trichome_guidelines():
    """Lädt die Trichome-Guidelines aus der JSON-Datei"""
    try:
        with open('static/data/bluete-zeitmanagement-guidelines.json', 'r', encoding='utf-8') as f:
            guidelines = json.load(f)
        print(f"🌺 FloweringRoutes: Trichome-Guidelines erfolgreich geladen")
        return guidelines.get('floweringManagementGuidelines', {})
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Laden der Trichome-Guidelines: {e}")
        return {}

@flowering_bp.route('/trichome-guidelines/<plant_id>')
def get_trichome_guidelines(plant_id):
    """Gibt Trichome-spezifische Guidelines für eine Pflanze zurück"""
    try:
        # Pflanze aus Datenbank laden
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return jsonify({'error': 'Pflanze nicht gefunden'}), 404
        
        # Strain-Typ bestimmen
        strain_type = plant.get('strain_type', 'photoperiod').lower()
        if 'auto' in strain_type:
            strain_type = 'autoflower'
        else:
            strain_type = 'photoperiod'
        
        # Trichome-Guidelines laden
        guidelines = load_trichome_guidelines()
        
        # Guidelines für Strain-Typ filtern
        filtered_guidelines = {
            "eventMarker": guidelines.get('eventMarker', []),
            "harvestEmpfehlungen": guidelines.get('harvestEmpfehlungen', {}),
            "faustregeln": guidelines.get('faustregeln', []),
            "strainAbhaengigkeit": guidelines.get('strainAbhaengigkeit', {}),
            "strain_type": strain_type
        }
        
        print(f"🌺 FloweringRoutes: Sende trichome_guidelines für {strain_type}")
        
        return jsonify({
            "plant_id": plant_id,
            "plant_name": plant.get('plant_name', ''),
            "strain_type": strain_type,
            "trichome_guidelines": filtered_guidelines
        })
        
    except Exception as e:
        print(f"🌺 FloweringRoutes: Fehler beim Laden der Trichome-Guidelines: {e}")
        return jsonify({'error': 'Fehler beim Laden der Guidelines'}), 500 