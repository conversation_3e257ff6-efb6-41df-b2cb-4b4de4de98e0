<!-- Modularisiertes Flowering Widget -->
<div class="flowering-widget-container">
    <!-- Header mit Pflanze-Info -->
    <div class="widget-header">
        <div class="plant-info">
            <h1>🌺 Blüte-Zeitmanagement (Modular)</h1>
            <div class="plant-details">
                <span class="plant-id" data-plant-id="{{ plant.id }}">{{ plant.id }}</span>
                <span class="strain-name" id="strainName">{{ plant.strain or 'Sorte nicht angegeben' }}</span>
            </div>
        </div>
        <div class="current-status">
            <div class="bloom-day">
                <span class="day-number" id="currentDay">41</span>
                <span class="day-label">Blüte-Tag</span>
            </div>
            <div class="progress-circle">
                <div class="progress-ring">
                    <svg width="60" height="60">
                        <circle class="progress-ring-bg" cx="30" cy="30" r="25"></circle>
                        <circle class="progress-ring-circle" id="progressCircle" cx="30" cy="30" r="25"></circle>
                    </svg>
                    <div class="progress-text" id="progressText">62.5%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab-Navigation -->
    <div class="tab-navigation">
        <button class="tab-btn active" data-tab="overview">Übersicht</button>
        <button class="tab-btn" data-tab="trichome">Trichome</button>
        <button class="tab-btn" data-tab="timeline">Timeline</button>
        <button class="tab-btn" data-tab="lighting">Beleuchtung</button>
        <button class="tab-btn" data-tab="flush-trigger">Flush-Trigger</button>
        <button class="tab-btn" data-tab="prediction">Prognose</button>
    </div>

    <!-- Tab-Inhalte -->
    <div class="tab-content">
        <!-- Übersicht Tab -->
        <div class="tab-pane active" id="overview">
            <div class="overview-grid">
                <!-- Phase-Card -->
                <div class="phase-card">
                    <h3>Aktuelle Phase</h3>
                    <div class="phase-info">
                        <div class="phase-name" id="phaseDescription">Mittlere Blüte</div>
                        <div class="phase-percentage" id="phasePercentage">(Grow: 85.2% | Blüte: 62.5%)</div>
                    </div>
                    <div class="phase-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="phaseProgress" style="width: 62.5%"></div>
                        </div>
                        <div class="progress-text" id="phaseProgressText">62.5%</div>
                    </div>
                </div>

                <!-- Flush-Card -->
                <div class="flush-card" id="flushCard">
                    <h3>Flush-Status</h3>
                    <div class="flush-status" id="flushStatus">Inaktiv</div>
                </div>

                <!-- Ernte-Prognose -->
                <div class="harvest-card">
                    <h3>Ernte-Prognose</h3>
                    <div class="harvest-options">
                        <div class="harvest-option">
                            <span class="harvest-label">Früh:</span>
                            <span class="harvest-value" id="harvestEarly">Tag 60-65</span>
                        </div>
                        <div class="harvest-option">
                            <span class="harvest-label">Optimal:</span>
                            <span class="harvest-value" id="harvestOptimal">Tag 70-75</span>
                        </div>
                        <div class="harvest-option">
                            <span class="harvest-label">Spät:</span>
                            <span class="harvest-value" id="harvestLate">Tag 80-85</span>
                        </div>
                    </div>
                </div>

                <!-- Meilensteine -->
                <div class="milestones-card">
                    <h3>Kommende Meilensteine</h3>
                    <div class="milestones-list" id="milestonesList">
                        <!-- Wird dynamisch gefüllt -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Trichome Tab -->
        <div class="tab-pane" id="trichome">
            <div class="trichome-content">
                <!-- Trichom-Status -->
                <div class="trichome-status-card">
                    <h3>Trichom-Status</h3>
                    <div class="trichome-badge" id="trichomeBadge">30% / 50% / 20%</div>
                    <div class="trichome-segments">
                        <div class="segment clear">
                            <div class="segment-label">Klar</div>
                            <div class="segment-bar">
                                <div class="segment-fill" id="clearTrichomeBar" style="width: 30%"></div>
                            </div>
                            <div class="segment-value" id="clearTrichomeValue">30%</div>
                        </div>
                        <div class="segment milky">
                            <div class="segment-label">Milchig</div>
                            <div class="segment-bar">
                                <div class="segment-fill" id="milkyTrichomeBar" style="width: 50%"></div>
                            </div>
                            <div class="segment-value" id="milkyTrichomeValue">50%</div>
                        </div>
                        <div class="segment amber">
                            <div class="segment-label">Bernstein</div>
                            <div class="segment-bar">
                                <div class="segment-fill" id="amberTrichomeBar" style="width: 20%"></div>
                            </div>
                            <div class="segment-value" id="amberTrichomeValue">20%</div>
                        </div>
                    </div>
                    <div class="last-update">
                        Letzte Aktualisierung: <span id="lastTrichomeUpdate">Nie</span>
                    </div>
                </div>

                <!-- Beobachtungen -->
                <div class="observations-card">
                    <div class="card-header">
                        <h3>Beobachtungen</h3>
                        <button class="btn btn-primary" id="addObservationBtn">Neue Beobachtung</button>
                    </div>
                    <div class="observations-list" id="observationsList">
                        <!-- Wird dynamisch gefüllt -->
                    </div>
                </div>

                <!-- Flush-Alert -->
                <div class="alert alert-warning" id="flushAlert" style="display: none;">
                    <i class="fa-solid fa-exclamation-triangle"></i>
                    <strong>Flush empfohlen!</strong> 
                    Die Trichome zeigen optimale Reife für den Flush-Start.
                </div>
            </div>
        </div>

        <!-- Timeline Tab -->
        <div class="tab-pane" id="timeline">
            <div class="timeline-content">
                <!-- Timeline-Übersicht -->
                <div class="timeline-overview">
                    <h3>Blüte-Timeline</h3>
                    <div class="timeline-events" id="timelineContent">
                        <!-- Wird dynamisch gefüllt -->
                    </div>
                </div>

                <!-- Marker-Verwaltung -->
                <div class="markers-management">
                    <div class="card-header">
                        <h3>Marker</h3>
                        <button class="btn btn-primary" id="addMarkerBtn">Neuer Marker</button>
                    </div>
                    
                    <!-- Filter -->
                    <div class="marker-filters">
                        <select id="categoryFilter" class="form-select">
                            <option value="">Alle Kategorien</option>
                            <option value="trichome">Trichome</option>
                            <option value="growth">Wachstum</option>
                            <option value="problem">Problem</option>
                            <option value="milestone">Meilenstein</option>
                        </select>
                        <select id="importanceFilter" class="form-select">
                            <option value="">Alle Wichtigkeiten</option>
                            <option value="high">Hoch</option>
                            <option value="medium">Mittel</option>
                            <option value="low">Niedrig</option>
                        </select>
                    </div>

                    <!-- Marker-Liste -->
                    <div class="markers-list" id="markersList">
                        <!-- Wird dynamisch gefüllt -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Beleuchtung Tab -->
        <div class="tab-pane" id="lighting">
            <div class="lighting-content">
                <!-- Beleuchtungs-Übersicht -->
                <div class="lighting-overview">
                    <h3>Beleuchtungs-Status</h3>
                    <div class="lighting-status">
                        <span class="status-indicator" id="flowering-lighting-status"></span>
                        <span class="status-text" id="flowering-lighting-status-text">Optimal</span>
                    </div>
                </div>

                <!-- Beleuchtungs-Einstellungen -->
                <div class="lighting-settings">
                    <div class="setting-group">
                        <label for="flowering-lamp-power">Lampenleistung (W)</label>
                        <input type="number" id="flowering-lamp-power" class="form-control" value="600">
                    </div>
                    <div class="setting-group">
                        <label for="flowering-lamp-distance">Lampenabstand (cm)</label>
                        <input type="number" id="flowering-lamp-distance" class="form-control" value="50">
                    </div>
                    <div class="setting-group">
                        <label for="flowering-photoperiod">Photoperiode (h)</label>
                        <input type="number" id="flowering-photoperiod" class="form-control" value="12">
                    </div>
                </div>

                <!-- PPFD-Anzeige -->
                <div class="ppfd-display">
                    <h4>PPFD</h4>
                    <div class="ppfd-value" id="flowering-ppfd-value">800 μmol/m²/s</div>
                    <div class="ppfd-recommendation" id="flowering-ppfd-recommendation">Empfohlen: 750 μmol/m²/s</div>
                </div>

                <!-- Empfehlungen -->
                <div class="lighting-recommendations">
                    <h4>Empfehlungen</h4>
                    <div class="recommendations-list" id="flowering-lighting-recommendations">
                        <!-- Wird dynamisch gefüllt -->
                    </div>
                </div>

                <!-- Energieverbrauch -->
                <div class="energy-consumption">
                    <h4>Energieverbrauch</h4>
                    <div class="energy-metrics">
                        <div class="metric">
                            <span class="metric-label">Täglich:</span>
                            <span class="metric-value" id="flowering-daily-consumption">7.2 kWh</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Monatlich:</span>
                            <span class="metric-value" id="flowering-monthly-cost">64.80 €</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Effizienz:</span>
                            <span class="metric-value" id="flowering-efficiency">1.33 μmol/J</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weitere Tabs... -->
        <div class="tab-pane" id="flush-trigger">
            <div class="flush-trigger-content">
                <h3>Flush-Trigger</h3>
                <p>Flush-Trigger Inhalt wird hier angezeigt...</p>
            </div>
        </div>

        <div class="tab-pane" id="prediction">
            <div class="prediction-content">
                <h3>Prognosen</h3>
                <p>Prognose-Inhalt wird hier angezeigt...</p>
            </div>
        </div>
    </div>

    <!-- Phase 6 Features Container -->
    <div id="phase6Features" class="phase6-features">
        <!-- Advanced Features werden hier dynamisch eingefügt -->
    </div>
</div>

<!-- Modals -->
<!-- Trichom-Beobachtungs-Modal -->
<div class="modal fade" id="trichomeObservationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Neue Trichom-Beobachtung</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="trichomeObservationForm">
                    <div class="mb-3">
                        <label for="clear_percentage" class="form-label">Klare Trichome (%)</label>
                        <input type="number" class="form-control" id="clear_percentage" name="clear_percentage" min="0" max="100" required>
                    </div>
                    <div class="mb-3">
                        <label for="milky_percentage" class="form-label">Milchige Trichome (%)</label>
                        <input type="number" class="form-control" id="milky_percentage" name="milky_percentage" min="0" max="100" required>
                    </div>
                    <div class="mb-3">
                        <label for="amber_percentage" class="form-label">Bernstein Trichome (%)</label>
                        <input type="number" class="form-control" id="amber_percentage" name="amber_percentage" min="0" max="100" required>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notizen</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
                <button type="button" class="btn btn-primary" id="trichomeObservationModalSave">Speichern</button>
            </div>
        </div>
    </div>
</div>

<!-- Marker-Modal -->
<div class="modal fade" id="markerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="markerModalTitle">Neuer Marker</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="markerForm">
                    <div class="mb-3">
                        <label for="marker_title" class="form-label">Titel</label>
                        <input type="text" class="form-control" id="marker_title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="marker_category" class="form-label">Kategorie</label>
                        <select class="form-select" id="marker_category" name="category" required>
                            <option value="trichome">Trichome</option>
                            <option value="growth">Wachstum</option>
                            <option value="problem">Problem</option>
                            <option value="milestone">Meilenstein</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="marker_importance" class="form-label">Wichtigkeit</label>
                        <select class="form-select" id="marker_importance" name="importance" required>
                            <option value="high">Hoch</option>
                            <option value="medium">Mittel</option>
                            <option value="low">Niedrig</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="marker_bloom_day" class="form-label">Blüte-Tag</label>
                        <input type="number" class="form-control" id="marker_bloom_day" name="bloom_day" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="marker_description" class="form-label">Beschreibung</label>
                        <textarea class="form-control" id="marker_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
                <button type="button" class="btn btn-primary" id="saveMarkerBtn">Speichern</button>
            </div>
        </div>
    </div>
</div>

<!-- Skripte -->
<script src="/static/scripts/widgets/flowering/index.js"></script>
<script>
// Warte auf Module und initialisiere dann das Widget
window.addEventListener('floweringModulesReady', function() {
    console.log('🌺 Flowering Widget: Module bereit, initialisiere Widget...');
    
    // Kurze Verzögerung um sicherzustellen, dass DOM bereit ist
    setTimeout(() => {
        if (typeof FloweringWidget !== 'undefined') {
            window.floweringWidget = new FloweringWidget();
            console.log('🌺 Flowering Widget: Erfolgreich initialisiert');
        } else {
            console.error('🌺 Flowering Widget: FloweringWidget Klasse nicht gefunden');
        }
    }, 100);
});
</script>
