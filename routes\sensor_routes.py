"""
Sensor Routes
Handles sensor integration APIs
"""

from flask import Blueprint, request, jsonify
from phase_logic.sensors.govee_integration import govee_integration
import json

sensor_bp = Blueprint('sensor', __name__, url_prefix='/api/sensor')

@sensor_bp.route('/test', methods=['GET'])
def test_sensor_routes():
    """Test route to verify sensor blueprint is working"""
    return jsonify({
        'success': True,
        'message': 'Sensor routes are working!',
        'blueprint': 'sensor',
        'url_prefix': '/api/sensor'
    })

@sensor_bp.route('/govee/setup', methods=['POST'])
def setup_govee():
    """Setup Govee API key"""
    try:
        data = request.get_json()
        
        if not data or 'api_key' not in data:
            return jsonify({
                'success': False,
                'message': 'API-Key ist erforderlich'
            }), 400
        
        api_key = data['api_key']
        success = govee_integration.setup_api_key(api_key)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Govee API erfolgreich eingerichtet',
                'status': govee_integration.get_connection_status()
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Govee API Setup fehlgeschlagen - API-Key überprüfen'
            }), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Govee Setup: {str(e)}'
        }), 500

@sensor_bp.route('/govee/status', methods=['GET'])
def get_govee_status():
    """Get Govee connection status"""
    try:
        status = govee_integration.get_connection_status()
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Abrufen des Status: {str(e)}'
        }), 500

@sensor_bp.route('/govee/devices', methods=['GET'])
def get_govee_devices():
    """Get all available Govee devices"""
    try:
        if not govee_integration.is_connected():
            return jsonify({
                'success': False,
                'message': 'Govee API nicht verbunden - Setup erforderlich'
            }), 400
        
        devices = govee_integration.get_devices()
        
        # Filter for hygrometers only
        hygrometers = [device for device in devices if device['type'] == 'hygrometer']
        
        return jsonify({
            'success': True,
            'devices': devices,
            'hygrometers': hygrometers,
            'count': len(devices),
            'hygrometer_count': len(hygrometers)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Abrufen der Geräte: {str(e)}'
        }), 500

@sensor_bp.route('/govee/reading/<device_id>', methods=['GET'])
def get_govee_reading(device_id):
    """Get current reading from a specific device"""
    try:
        if not govee_integration.is_connected():
            return jsonify({
                'success': False,
                'message': 'Govee API nicht verbunden'
            }), 400
        
        # Get model from query parameter
        model = request.args.get('model')
        if not model:
            return jsonify({
                'success': False,
                'message': 'Model-Parameter erforderlich'
            }), 400
        
        # Get current reading
        reading = govee_integration.get_hygrometer_data(device_id, model)
        
        if reading:
            return jsonify({
                'success': True,
                'reading': reading
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Keine Daten vom Gerät verfügbar'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Abrufen der Daten: {str(e)}'
        }), 500

@sensor_bp.route('/govee/last-reading/<device_id>', methods=['GET'])
def get_last_govee_reading(device_id):
    """Get last known reading from a device"""
    try:
        reading = govee_integration.get_last_reading(device_id)
        
        if reading:
            return jsonify({
                'success': True,
                'reading': reading
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Keine gespeicherten Daten verfügbar'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Abrufen der letzten Daten: {str(e)}'
        }), 500

@sensor_bp.route('/govee/test', methods=['POST'])
def test_govee_connection():
    """Test Govee API connection"""
    try:
        data = request.get_json()
        
        if not data or 'api_key' not in data:
            return jsonify({
                'success': False,
                'message': 'API-Key ist erforderlich'
            }), 400
        
        api_key = data['api_key']
        success = govee_integration.setup_api_key(api_key)
        
        if success:
            # Try to get devices as additional test
            devices = govee_integration.get_devices()
            
            return jsonify({
                'success': True,
                'message': 'Govee API Verbindung erfolgreich',
                'devices_found': len(devices),
                'hygrometers_found': len([d for d in devices if d['type'] == 'hygrometer'])
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Govee API Verbindung fehlgeschlagen'
            }), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Testen der Verbindung: {str(e)}'
        }), 500 