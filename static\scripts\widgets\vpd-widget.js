/**
 * VPD Widget Module
 * Handles VPD (Vapor Pressure Deficit) optimization functionality
 */

class VPDWidget {
    constructor(containerId = 'vpd-optimization-box', options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        // Stelle sicher, dass currentPhase ein String ist
        let phase = options.currentPhase || window.currentPhaseKey || 'flowering_middle';
        if (typeof phase === 'object' && phase !== null) {
            phase = 'flowering_late'; // Fallback wenn es ein Objekt ist
        }
        this.currentPhase = phase;
        this.temperature = 25;
        this.humidity = 50;
        this.leafTemperature = null; // NEU: Blatttemperatur
        this.vpdChart = null; // NEU: Chart-Referenz für Cleanup
        
        // NEU: Govee Sensor Integration
        this.goveeEnabled = false;
        this.goveeDevice = null;
        this.goveeAutoUpdate = false;
        this.goveeUpdateInterval = null;
        
        if (this.container) {
            this.init();
        } else {
            console.warn(`VPD Widget: Container ${containerId} nicht gefunden`);
        }
    }

    init() {

        this.loadVPDOptimization();
        this.setupEventListeners();
    }

    // Hilfsfunktion für benutzerfreundliche Phase-Namen
    getFriendlyPhaseName(phaseKey) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phaseKey] || phaseKey.replace('_', ' ');
    }

    // Event-Listener für Widget-Interaktionen
    setupEventListeners() {
        // Event-Delegation für dynamisch erstellte Elemente
        this.container.addEventListener('click', (e) => {
            if (e.target.id === 'vpd-calc-btn') {
                this.calculateVPD();
            }
            // NEU: Lösch-Button für VPD-Einträge
            if (e.target.classList.contains('vpd-delete-entry-btn')) {
                const entryId = e.target.getAttribute('data-entry-id');
                if (entryId) {
                    this.deleteVPDEntry(entryId);
                }
            }
            // NEU: Erweiterte Analyse Button
            if (e.target.id === 'vpd-advanced-analysis-btn') {
                this.performAdvancedAnalysis();
            }
            // NEU: Govee Sensor Setup Button
            if (e.target.id === 'vpd-govee-setup-btn') {
                this.setupGoveeIntegration();
            }
        });

        // Input-Event-Listener für Live-Updates
        this.container.addEventListener('input', (e) => {
            if (e.target.id === 'temperature-input' || e.target.id === 'humidity-input' || e.target.id === 'leaf-temperature-input') { // NEU
                this.updateInputValues();
            }
        });
    }

    // Aktuelle Input-Werte aktualisieren
    updateInputValues() {
        const tempInput = document.getElementById('temperature-input');
        const humidityInput = document.getElementById('humidity-input');
        const leafTempInput = document.getElementById('leaf-temperature-input'); // NEU
        
        if (tempInput) this.temperature = parseFloat(tempInput.value) || 25;
        if (humidityInput) this.humidity = parseInt(humidityInput.value) || 50;
        if (leafTempInput) {
            const val = parseFloat(leafTempInput.value);
            this.leafTemperature = isNaN(val) ? null : val;
        }
    }

    // VPD-Berechnung auslösen
    async calculateVPD() {
        this.updateInputValues();
        await this.loadVPDOptimization();
    }

    // NEU: Erweiterte VPD-Analyse durchführen
    async performAdvancedAnalysis() {
        this.updateInputValues();
        
        // Plant ID aus DOM holen
        let plantId = null;
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            plantId = plantIdElement.getAttribute('data-plant-id');
        } else {
            const urlParams = new URLSearchParams(window.location.search);
            plantId = urlParams.get('plant_id');
        }
        
        // Strain-Type aus DOM holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        // Loading-Indikator anzeigen
        this.showAdvancedAnalysisModal('Lade erweiterte VPD-Analyse...');
        
        try {
            const response = await fetch('/api/vpd/analysis/comprehensive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    temperature: this.temperature,
                    humidity: this.humidity,
                    leaf_temperature: this.leafTemperature,
                    phase: this.currentPhase,
                    strain_type: strainType,
                    plant_id: plantId
                })
            });
            
            if (!response.ok) {
                throw new Error('Erweiterte VPD-Analyse konnte nicht geladen werden');
            }
            
            const data = await response.json();
            this.renderAdvancedAnalysis(data.analysis);
            
        } catch (error) {
            console.error('Fehler bei der erweiterten VPD-Analyse:', error);
            this.showAdvancedAnalysisModal(`Fehler: ${error.message}`);
        }
    }

    // NEU: Erweiterte Analyse-Modal anzeigen
    showAdvancedAnalysisModal(content) {
        // Prüfen ob Modal bereits existiert
        let modal = document.getElementById('vpd-advanced-analysis-modal');
        if (modal) {
            modal.remove();
        }

        const modalHtml = `
            <div class="modal fade" id="vpd-advanced-analysis-modal" tabindex="-1" aria-labelledby="vpd-advanced-analysis-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered" style="max-width: 800px;">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="vpd-advanced-analysis-modal-label">
                                <i class="fas fa-chart-line me-2"></i>Erweiterte VPD-Analyse
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="vpd-advanced-analysis-content" style="max-height: 70vh; overflow-y: auto;">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">${content}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Modal anzeigen
        const modalElement = document.getElementById('vpd-advanced-analysis-modal');
        const bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.show();

        // Modal nach dem Schließen entfernen
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });
    }

    // NEU: Erweiterte Analyse rendern
    renderAdvancedAnalysis(analysis) {
        const content = document.getElementById('vpd-advanced-analysis-content');
        if (!content) return;

        const html = `
            <div class="vpd-advanced-analysis">
                <!-- Übersicht -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Aktuelle Werte</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <strong>VPD:</strong> ${analysis.current.vpd} kPa<br>
                                        <strong>Temperatur:</strong> ${analysis.current.temperature}°C<br>
                                        <strong>Luftfeuchte:</strong> ${analysis.current.humidity}%
                                    </div>
                                    <div class="col-6">
                                        <strong>Status:</strong> <span class="badge bg-${this.getStatusColor(analysis.current.status.status)}">${analysis.current.status.message}</span><br>
                                        <strong>Abweichung:</strong> ${analysis.current.status.deviation_percent}%<br>
                                        <strong>Optimal:</strong> ${analysis.current.status.optimal_value} kPa
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Optimierungs-Score</h6>
                            </div>
                            <div class="card-body text-center">
                                <h2 class="text-${this.getScoreColor(analysis.optimization_score.total_score)}">${analysis.optimization_score.total_score}</h2>
                                <h4 class="text-${this.getScoreColor(analysis.optimization_score.total_score)}">${analysis.optimization_score.grade}</h4>
                                <p class="mb-0">Verbesserungspotential: ${analysis.optimization_score.improvement_potential}%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Problemanalyse -->
                ${this.renderProblemAnalysis(analysis.problem_analysis)}

                <!-- KI-Empfehlungen -->
                ${this.renderAIRecommendations(analysis.ai_recommendations)}

                <!-- Effizienz-Metriken -->
                ${this.renderEfficiencyMetrics(analysis.efficiency_metrics)}

                <!-- Risiko-Bewertung -->
                ${this.renderRiskAssessment(analysis.risk_assessment)}

                <!-- Trend-Analyse -->
                ${this.renderTrendAnalysis(analysis.trend_analysis)}

                <!-- Historische Erkenntnisse -->
                ${analysis.historical_insights ? this.renderHistoricalInsights(analysis.historical_insights) : ''}
            </div>
        `;

        content.innerHTML = html;
    }

    // NEU: Status-Farbe
    getStatusColor(status) {
        const colors = {
            'optimal': 'success',
            'good': 'info',
            'high': 'warning',
            'low': 'warning',
            'critical': 'danger'
        };
        return colors[status] || 'secondary';
    }

    // NEU: Score-Farbe
    getScoreColor(score) {
        if (score >= 80) return 'success';
        if (score >= 60) return 'warning';
        return 'danger';
    }

    // NEU: Problemanalyse rendern
    renderProblemAnalysis(problemAnalysis) {
        if (!problemAnalysis.problems || problemAnalysis.problems.length === 0) {
            return `
                <div class="card border-success mb-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Problemanalyse</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0 text-success"><i class="fas fa-thumbs-up me-2"></i>Keine Probleme erkannt - VPD ist optimal!</p>
                    </div>
                </div>
            `;
        }

        const problemsHtml = problemAnalysis.problems.map(problem => `
            <div class="alert alert-${problem.severity === 'high' ? 'danger' : 'warning'} mb-3">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>${problem.description}</h6>
                <div class="row">
                    <div class="col-md-4">
                        <strong>Ursachen:</strong>
                        <ul class="mb-0">
                            ${problem.causes.map(cause => `<li>${cause}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <strong>Symptome:</strong>
                        <ul class="mb-0">
                            ${problem.symptoms.map(symptom => `<li>${symptom}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <strong>Sofortmaßnahmen:</strong>
                        <ul class="mb-0">
                            ${problem.immediate_actions.map(action => `<li>${action}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="card border-warning mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Problemanalyse (${problemAnalysis.problem_count} Probleme)</h6>
                </div>
                <div class="card-body">
                    ${problemsHtml}
                </div>
            </div>
        `;
    }

    // NEU: KI-Empfehlungen rendern
    renderAIRecommendations(aiRecommendations) {
        const priorityActions = aiRecommendations.priority_actions.map(action => 
            `<li class="list-group-item list-group-item-danger"><i class="fas fa-exclamation-circle me-2"></i>${action}</li>`
        ).join('');

        const gradualAdjustments = aiRecommendations.gradual_adjustments.map(adjustment => 
            `<li class="list-group-item list-group-item-warning"><i class="fas fa-arrow-up me-2"></i>${adjustment}</li>`
        ).join('');

        const preventionTips = aiRecommendations.prevention_tips.map(tip => 
            `<li class="list-group-item list-group-item-info"><i class="fas fa-shield-alt me-2"></i>${tip}</li>`
        ).join('');

        const efficiencyImprovements = aiRecommendations.efficiency_improvements.map(improvement => 
            `<li class="list-group-item list-group-item-success"><i class="fas fa-rocket me-2"></i>${improvement}</li>`
        ).join('');

        return `
            <div class="card border-primary mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-robot me-2"></i>KI-basierte Empfehlungen</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Prioritäts-Aktionen</h6>
                            <ul class="list-group list-group-flush mb-3">
                                ${priorityActions || '<li class="list-group-item text-success">Keine kritischen Aktionen erforderlich</li>'}
                            </ul>
                            
                            <h6 class="text-warning"><i class="fas fa-arrow-up me-2"></i>Graduelle Anpassungen</h6>
                            <ul class="list-group list-group-flush mb-3">
                                ${gradualAdjustments || '<li class="list-group-item text-success">Keine Anpassungen erforderlich</li>'}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info"><i class="fas fa-shield-alt me-2"></i>Präventions-Tipps</h6>
                            <ul class="list-group list-group-flush mb-3">
                                ${preventionTips}
                            </ul>
                            
                            <h6 class="text-success"><i class="fas fa-rocket me-2"></i>Effizienz-Verbesserungen</h6>
                            <ul class="list-group list-group-flush">
                                ${efficiencyImprovements}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // NEU: Effizienz-Metriken rendern
    renderEfficiencyMetrics(efficiencyMetrics) {
        return `
            <div class="card border-info mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Effizienz-Metriken</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h4 class="text-${this.getScoreColor(efficiencyMetrics.efficiency_score)}">${efficiencyMetrics.efficiency_score}%</h4>
                            <p class="mb-0">Effizienz-Score</p>
                        </div>
                        <div class="col-md-4">
                            <h4 class="text-${this.getScoreColor(efficiencyMetrics.stability_score)}">${efficiencyMetrics.stability_score}%</h4>
                            <p class="mb-0">Stabilitäts-Score</p>
                        </div>
                        <div class="col-md-4">
                            <h4 class="text-${this.getScoreColor(efficiencyMetrics.optimality_score)}">${efficiencyMetrics.optimality_score}%</h4>
                            <p class="mb-0">Optimalitäts-Score</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>Abweichung vom Optimum:</strong> ${efficiencyMetrics.deviation_from_optimal} kPa<br>
                        <strong>Effizienz-Level:</strong> <span class="badge bg-${efficiencyMetrics.efficiency_level === 'high' ? 'success' : efficiencyMetrics.efficiency_level === 'medium' ? 'warning' : 'danger'}">${efficiencyMetrics.efficiency_level}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // NEU: Risiko-Bewertung rendern
    renderRiskAssessment(riskAssessment) {
        if (!riskAssessment.risks || riskAssessment.risks.length === 0) {
            return `
                <div class="card border-success mb-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Risiko-Bewertung</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0 text-success"><i class="fas fa-check-circle me-2"></i>Keine Risiken erkannt - VPD ist sicher!</p>
                    </div>
                </div>
            `;
        }

        const risksHtml = riskAssessment.risks.map(risk => `
            <div class="alert alert-${risk.severity === 'critical' ? 'danger' : 'warning'} mb-2">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>${risk.description}</h6>
                <div class="row">
                    <div class="col-md-4">
                        <strong>Wahrscheinlichkeit:</strong> ${risk.probability}
                    </div>
                    <div class="col-md-4">
                        <strong>Auswirkung:</strong> ${risk.impact}
                    </div>
                    <div class="col-md-4">
                        <strong>Schweregrad:</strong> <span class="badge bg-${risk.severity === 'critical' ? 'danger' : 'warning'}">${risk.severity}</span>
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="card border-warning mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Risiko-Bewertung (${riskAssessment.risk_count} Risiken)</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Gesamtrisiko:</strong> <span class="badge bg-${riskAssessment.overall_risk === 'critical' ? 'danger' : riskAssessment.overall_risk === 'moderate' ? 'warning' : 'success'}">${riskAssessment.overall_risk}</span>
                    </div>
                    ${risksHtml}
                </div>
            </div>
        `;
    }

    // NEU: Trend-Analyse rendern
    renderTrendAnalysis(trendAnalysis) {
        const trendIcon = trendAnalysis.trend === 'increasing_needed' ? 'arrow-up' : 
                         trendAnalysis.trend === 'decreasing_needed' ? 'arrow-down' : 'equals';
        const trendColor = trendAnalysis.trend === 'stable' ? 'success' : 'warning';

        return `
            <div class="card border-${trendColor} mb-4">
                <div class="card-header bg-${trendColor} text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Trend-Analyse</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-${trendIcon} me-2"></i>Trend: ${trendAnalysis.message}</h6>
                            <p class="mb-0">
                                <strong>Vertrauen:</strong> ${(trendAnalysis.confidence * 100).toFixed(0)}%<br>
                                <strong>Optimal-Richtung:</strong> ${trendAnalysis.optimal_direction === 'increase' ? 'Erhöhen' : 'Reduzieren'}<br>
                                <strong>Distanz zum Optimum:</strong> ${trendAnalysis.distance_to_optimal} kPa
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="progress mb-2">
                                <div class="progress-bar bg-${trendColor}" style="width: ${trendAnalysis.confidence * 100}%"></div>
                            </div>
                            <small class="text-muted">Trend-Vertrauen: ${(trendAnalysis.confidence * 100).toFixed(0)}%</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // NEU: Historische Erkenntnisse rendern
    renderHistoricalInsights(historicalInsights) {
        if (!historicalInsights.available) {
            return `
                <div class="card border-secondary mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-history me-2"></i>Historische Erkenntnisse</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0 text-muted">${historicalInsights.message}</p>
                    </div>
                </div>
            `;
        }

        const recommendations = historicalInsights.recommendations.map(rec => 
            `<li class="list-group-item"><i class="fas fa-lightbulb me-2"></i>${rec}</li>`
        ).join('');

        return `
            <div class="card border-info mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Historische Erkenntnisse</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Datenpunkte:</strong> ${historicalInsights.data_points}<br>
                            <strong>Trend:</strong> ${historicalInsights.recent_trend}<br>
                            <strong>Stabilität:</strong> <span class="badge bg-${historicalInsights.stability === 'high' ? 'success' : historicalInsights.stability === 'medium' ? 'warning' : 'danger'}">${historicalInsights.stability}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Durchschnitt VPD:</strong> ${historicalInsights.average_vpd} kPa<br>
                            <strong>VPD-Bereich:</strong> ${historicalInsights.vpd_range.min} - ${historicalInsights.vpd_range.max} kPa
                        </div>
                    </div>
                    ${recommendations ? `
                        <div class="mt-3">
                            <strong>Empfehlungen:</strong>
                            <ul class="list-group list-group-flush">
                                ${recommendations}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Hauptfunktion: VPD-Optimierung laden
    async loadVPDOptimization() {
        if (!this.container) return;
        // Guidelines laden, falls noch nicht geschehen
        if (!VPDWidget.vpdGuidelinesLoaded) {
            await VPDWidget.loadVPDGuidelines();
        }


        
        // Aktuelle Werte aus Inputs holen
        this.updateInputValues();
        
        // Plant ID aus DOM oder URL holen
        let plantId = null;
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            plantId = plantIdElement.getAttribute('data-plant-id');
        } else {
            // Aus URL holen
            const urlParams = new URLSearchParams(window.location.search);
            plantId = urlParams.get('plant_id');
        }
        
        this.container.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Lade VPD-Optimierung...</div>';

        try {
            // NEU: Blatttemperatur an API übergeben, wenn vorhanden
            let url = `/api/vpd/optimization/${encodeURIComponent(this.currentPhase)}?temperature=${encodeURIComponent(this.temperature)}&humidity=${encodeURIComponent(this.humidity)}${plantId ? `&plant_id=${plantId}` : ''}`;
            if (this.leafTemperature !== null && this.leafTemperature !== undefined && this.leafTemperature !== '') {
                url += `&leaf_temperature=${encodeURIComponent(this.leafTemperature)}`;
            }
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error('VPD-Optimierung konnte nicht geladen werden');
            }
            
            const data = await response.json();
            this.renderVPDWidget(data);
            
        } catch (err) {
            this.container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler beim Laden der VPD-Optimierung: ${err.message}
                </div>
            `;
        }
    }

    // VPD-Widget rendern
    async renderVPDWidget(data) {
        // Strain-Type aus dem Template oder Widget holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        const html = `
            <div class="vpd-widget-card">
                <div class="vpd-widget-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fa-solid fa-droplet"></i>
                        VPD-Optimierung <span class="vpd-phase-name">${this.getFriendlyPhaseName(data.phase)}</span>
                        ${data.strain_specific ? '<span class="badge bg-info ms-2">Sortentyp-spezifisch</span>' : ''}
                        <span class="badge bg-success ms-2">${strainTypeDisplay}</span>
                    </div>
                    <button 
                        type="button" 
                        class="btn btn-sm btn-outline-light" 
                        data-bs-toggle="modal" 
                        data-bs-target="#vpd-guidelines-modal"
                        title="VPD-Richtlinien & Faustregeln anzeigen"
                    >
                        <i class="fas fa-book-open"></i>
                    </button>
                </div>
                <div class="vpd-widget-body">
                    ${(data.guidelines_used && data.guidelines) ? this.renderStrainSpecificValues(data) : ''}
                    ${!data.guidelines_used ? this.renderRecommendedValues(data.recommendations) : ''}
                    ${this.renderCurrentConditions()}
                    ${data.current ? this.renderCalculatedValues(data.current) : ''}
                    ${(data.optimization) ? this.renderOptimizationRecommendations(data.optimization) : ''}
                    ${this.renderGuidelinesPreview()}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        
        // Tooltips nach dem Rendern initialisieren
        this.initTooltips();
        
        // Guidelines-Modal erstellen
        this.createGuidelinesModal();
        // NEU: VPD-Historie-Chart laden
        await this.renderVPDHistoryChart();
    }
    
    // Tooltips für dynamisch erstellte Elemente initialisieren
    initTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
    
        } else {
            // Fallback: Warten und erneut versuchen
            setTimeout(() => this.initTooltips(), 500);
        }
    }

    // Empfohlene Werte rendern
    renderRecommendedValues(recommendations) {
        return `
            <div class="vpd-section-title vpd-recommended">
                <i class="fa-solid fa-check-circle me-2"></i>Empfohlene Werte
            </div>
            <div class="vpd-section-content vpd-recommended-content">
                <div><span class="vpd-label">VPD-Bereich:</span> <b>${recommendations.range}</b></div>
                <div><span class="vpd-label">Optimal:</span> <b>${recommendations.optimal}</b></div>
                <div><span class="vpd-label">Beschreibung:</span> ${recommendations.description}</div>
                <div><span class="vpd-label">Temperatur:</span> ${recommendations.temperature_range.min}–${recommendations.temperature_range.max}°C</div>
                <div><span class="vpd-label">Luftfeuchte:</span> ${recommendations.humidity_range.min}–${recommendations.humidity_range.max}%</div>
            </div>
        `;
    }

    // Strain-spezifische Werte rendern
    renderStrainSpecificValues(data) {
        const targets = data.targets;
        const guidelines = data.guidelines;
        return `
            <div class="vpd-section-title vpd-recommended" style="background:rgba(23,162,184,0.12);border-left:4px solid #17a2b8;">
                <i class="fa-solid fa-check-circle me-2"></i>Sortentyp-spezifische Empfehlungen
                <span class="badge bg-info ms-2">Wissenschaftlich fundiert</span>
            </div>
            <div class="vpd-section-content vpd-recommended-content" style="background:rgba(23,162,184,0.07);">
                <div class="vpd-strain-specific">
                    <i class="fa fa-leaf me-2"></i>
                    <strong>Phase:</strong> ${guidelines.description}
                </div>
                <div><span class="vpd-label">VPD-Zielbereich:</span> <b>${targets.vpd.min}-${targets.vpd.max} kPa</b></div>
                <div><span class="vpd-label">Temperatur-Ziel:</span> <b>${targets.temperature.min}-${targets.temperature.max}°C</b></div>
                <div><span class="vpd-label">Luftfeuchte-Ziel:</span> <b>${targets.humidity.min}-${targets.humidity.max}%</b></div>
                <div><span class="vpd-label">Blatttemperatur-Offset:</span> <b>+${guidelines.leaf_temp_offset || 1}°C</b></div>
                ${guidelines.notes && guidelines.notes.length > 0 ? `
                    <div class="vpd-notes" style="background:rgba(255,235,59,0.13);border-radius:8px;padding:8px 12px;margin-top:10px;">
                        <strong>Wichtige Hinweise:</strong>
                        <ul class="vpd-notes-list">
                            ${guidelines.notes.map(note => `<li style='color:#ffc107;'>${note}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Optimierungsempfehlungen rendern
    renderOptimizationRecommendations(optimization) {
        return `
            <div class="vpd-section-title vpd-optimization" style="background:rgba(255,193,7,0.12);border-left:4px solid #ffc107;">
                <i class="fa-solid fa-sliders-h me-2"></i>Optimierungsempfehlungen
            </div>
            <div class="vpd-section-content vpd-optimization-content" style="background:rgba(255,193,7,0.07);">
                <div class="vpd-optimization-item">
                    <strong>VPD-Anpassung:</strong> ${optimization.vpd_adjustment.action}
                </div>
                <div class="vpd-optimization-item">
                    <strong>Temperatur-Anpassung:</strong> ${optimization.temperature_adjustment.action}
                </div>
                <div class="vpd-optimization-item">
                    <strong>Luftfeuchte-Anpassung:</strong> ${optimization.humidity_adjustment.action}
                </div>
            </div>
        `;
    }

    // Aktuelle Umgebungsbedingungen rendern
    renderCurrentConditions() {
        return `
            <div class="vpd-section-title vpd-current">
                <i class="fa-solid fa-thermometer-half me-2"></i>Aktuelle Umgebungsbedingungen
            </div>
            <div class="vpd-section-content vpd-current-content">
                <div class="vpd-input-grid">
                    <div class="vpd-input-group">
                        <label for="temperature-input">Temperatur (°C)</label>
                        <input type="number" min="0" max="50" step="0.1" 
                               id="temperature-input" value="${this.temperature}" 
                               class="vpd-input">
                    </div>
                    <div class="vpd-input-group">
                        <label for="humidity-input">Luftfeuchte (%)</label>
                        <input type="number" min="0" max="100" step="1" 
                               id="humidity-input" value="${this.humidity}" 
                               class="vpd-input">
                    </div>
                    <div class="vpd-input-group">
                        <label for="leaf-temperature-input">Blatttemperatur (°C) <span class="text-muted" style="font-size:0.85em;">(optional)</span></label>
                        <input type="number" min="0" max="50" step="0.1" 
                               id="leaf-temperature-input" value="${this.leafTemperature !== null && this.leafTemperature !== undefined ? this.leafTemperature : ''}" 
                               class="vpd-input">
                    </div>
                </div>
                <div class="vpd-calc-section">
                    <div class="d-flex gap-2 mb-2">
                        <button id="vpd-calc-btn" class="btn btn-sm btn-primary">
                            <i class="fa fa-calculator"></i> VPD Berechnen
                        </button>
                        <button id="vpd-advanced-analysis-btn" class="btn btn-sm btn-outline-info">
                            <i class="fa fa-chart-line"></i> Erweiterte Analyse
                        </button>
                        <button id="vpd-govee-setup-btn" class="btn btn-sm btn-outline-success">
                            <i class="fa fa-thermometer-half"></i> Govee Sensor
                        </button>
                    </div>
                    <span class="vpd-input-hint">
                        <i class="fa fa-info-circle"></i> (Thermometer/Hygrometer, Blatttemperatur optional)
                    </span>
                </div>
            </div>
        `;
    }

    // Berechnete Werte rendern
    renderCalculatedValues(current) {
        // VPD-Grenzwerte und Risiken prüfen
        let vpdWarningHtml = '';
        let vpdValue = parseFloat(current.vpd);
        let guidelines = VPDWidget.vpdGuidelines;
        if (guidelines && guidelines.grenzwerte) {
            const low = guidelines.grenzwerte.lowVpd;
            const high = guidelines.grenzwerte.highVpd;
            if (vpdValue < low.threshold) {
                vpdWarningHtml = `<div class="alert alert-danger mt-2"><i class="fa fa-exclamation-triangle me-2"></i><b>Achtung: VPD zu niedrig (&lt; ${low.threshold} kPa)!</b><ul>${low.risks.map(r=>`<li>${r}</li>`).join('')}</ul></div>`;
            } else if (vpdValue > high.threshold) {
                vpdWarningHtml = `<div class="alert alert-danger mt-2"><i class="fa fa-exclamation-triangle me-2"></i><b>Achtung: VPD zu hoch (&gt; ${high.threshold} kPa)!</b><ul>${high.risks.map(r=>`<li>${r}</li>`).join('')}</ul></div>`;
            }
        }
        // NEU: Blatttemperatur-Offset anzeigen, wenn vorhanden
        let leafTempHtml = '';
        if (current.leaf_temperature !== undefined && current.leaf_temperature !== null) {
            const offset = (parseFloat(current.leaf_temperature) - parseFloat(current.temperature)).toFixed(2);
            leafTempHtml = `<div><span class="vpd-label">Blatttemperatur:</span> <span class="vpd-value">${current.leaf_temperature}°C</span> <span class="text-muted ms-2">(Offset: ${offset}°C)</span></div>`;
        }
        return `
            <div class="vpd-section-title vpd-calculated">
                <i class="fa-solid fa-chart-line me-2"></i>Berechnete Werte
            </div>
            <div class="vpd-section-content vpd-calculated-content">
                <div><span class="vpd-label">Temperatur:</span> <span class="vpd-value">${current.temperature}°C</span></div>
                <div><span class="vpd-label">Luftfeuchte:</span> <span class="vpd-value">${current.humidity}%</span></div>
                ${leafTempHtml}
                <div><span class="vpd-label">VPD:</span> <span class="vpd-value">${current.vpd} kPa</span></div>
                <div class="vpd-status ${current.status.status}">
                    <i class="fa fa-${this.getStatusIcon(current.status.status)}"></i>
                    ${current.status.message}
                </div>
                ${vpdWarningHtml}
            </div>
        `;
    }

    // Status-Icon basierend auf VPD-Status
    getStatusIcon(status) {
        const icons = {
            'optimal': 'check-circle',
            'high': 'exclamation-triangle',
            'low': 'exclamation-triangle',
            'critical': 'times-circle'
        };
        return icons[status] || 'info-circle';
    }

    // Phase aktualisieren (für externe Aufrufe)
    updatePhase(newPhase) {
        this.currentPhase = newPhase;
        this.loadVPDOptimization();
    }

    // Guidelines-Vorschau rendern
    renderGuidelinesPreview() {
        // Strain-Type aus dem Template oder Widget holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        return `
            <div class="vpd-section-title vpd-guidelines">
                <i class="fa-solid fa-lightbulb me-2"></i>Wichtige VPD-Faustregeln
                <span class="badge bg-info ms-2">${strainTypeDisplay}</span>
            </div>
            <div class="vpd-section-content vpd-guidelines-content">
                <div class="vpd-guidelines-preview">
                    <ul class="vpd-guidelines-list">
                        <li><strong>VPD steigt:</strong> bei sinkender Luftfeuchte oder steigender Temperatur</li>
                        <li><strong>Zu hoher VPD:</strong> Pflanzen verdunsten zu viel Wasser</li>
                        <li><strong>Zu niedriger VPD:</strong> Schimmelgefahr und Sauerstoffmangel</li>
                        <li><strong>Ideal:</strong> Blatttemperatur ~1–2 °C höher als Raumtemperatur</li>
                        ${isAutoflower ? '<li><strong>Autoflower-spezifisch:</strong> Empfindlicher gegen VPD-Schwankungen</li>' : ''}
                    </ul>
                    <div class="vpd-guidelines-more">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#vpd-guidelines-modal"
                        >
                            <i class="fas fa-book-open"></i> Alle Richtlinien anzeigen
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Guidelines-Modal erstellen
    createGuidelinesModal() {
        // Prüfen ob Modal bereits existiert
        if (document.getElementById('vpd-guidelines-modal')) {
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="vpd-guidelines-modal" tabindex="-1" aria-labelledby="vpd-guidelines-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="vpd-guidelines-modal-label">
                                <i class="fa-solid fa-droplet me-2"></i>VPD-Richtlinien & Faustregeln
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="vpd-guidelines-content">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fa-solid fa-list-check me-2"></i>Faustregeln</h6>
                                        <ul class="vpd-guidelines-list">
                                            <li>VPD steigt bei sinkender Luftfeuchte oder steigender Temperatur.</li>
                                            <li>Zu hoher VPD → Pflanzen verdunsten zu viel Wasser.</li>
                                            <li>Zu niedriger VPD → Schimmelgefahr und Sauerstoffmangel in Wurzeln.</li>
                                            <li>Ideal: Blatttemperatur ~1–2 °C höher als Raumtemperatur.</li>
                                            <li>Nacht-VPD sollte nicht drastisch von Tagwerten abweichen (max. ±0.3 kPa).</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fa-solid fa-chart-line me-2"></i>Phase-Einfluss</h6>
                                        <div class="vpd-phase-influence">
                                            <div class="vpd-phase-item">
                                                <strong>Vegetation:</strong> Niedrigere VPD-Werte für optimales Wachstum. Fokus auf Wurzelaufbau und Blattentwicklung.
                                            </div>
                                            <div class="vpd-phase-item">
                                                <strong>Blüte:</strong> Höhere VPD-Werte für bessere Transpiration. Unterstützt Nährstoffaufnahme und Harzproduktion.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6><i class="fa-solid fa-leaf me-2"></i>Sortentyp-spezifische Empfehlungen</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="vpd-strain-section">
                                            <h6 class="text-primary">
                                                <i class="fa-solid fa-sun me-2"></i>Photoperiodische Sorten
                                            </h6>
                                            <ul class="vpd-strain-list">
                                                <li>Höhere VPD-Werte verträglich (0.4-1.6 kPa)</li>
                                                <li>Breitere Temperaturbereiche möglich</li>
                                                <li>Niedrigere Luftfeuchte verträglich</li>
                                                <li>Bessere Anpassung an VPD-Schwankungen</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="vpd-strain-section">
                                            <h6 class="text-success">
                                                <i class="fa-solid fa-clock me-2"></i>Autoflower-Sorten
                                            </h6>
                                            <ul class="vpd-strain-list">
                                                <li>Niedrigere VPD-Werte optimal (0.4-1.5 kPa)</li>
                                                <li>Konservativere Temperaturbereiche</li>
                                                <li>Höhere Luftfeuchte für bessere Transpiration</li>
                                                <li>Höhere Empfindlichkeit gegen VPD-Schwankungen</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6><i class="fa-solid fa-exclamation-triangle me-2"></i>Grenzwerte & Risiken</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="vpd-risk-section">
                                            <h6 class="text-danger">
                                                <i class="fa-solid fa-arrow-down me-2"></i>Zu niedriger VPD (< 0.4 kPa)
                                            </h6>
                                            <ul class="vpd-risk-list">
                                                <li>Schimmelgefahr durch stehende Feuchte</li>
                                                <li>Nährstoffaufnahme reduziert</li>
                                                <li>Stomata schließen sich → Wachstumsstopp</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="vpd-risk-section">
                                            <h6 class="text-warning">
                                                <i class="fa-solid fa-arrow-up me-2"></i>Zu hoher VPD (> 1.6 kPa)
                                            </h6>
                                            <ul class="vpd-risk-list">
                                                <li>Blattverbrennungen, insbesondere an Rändern</li>
                                                <li>Erhöhter Wasserverlust → Stress</li>
                                                <li>Verzögerte Budreife oder Notblüte</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="vpd-sources">
                                    <h6><i class="fa-solid fa-link me-2"></i>Quellen</h6>
                                    <p class="text-muted small">
                                        Diese Empfehlungen basieren auf wissenschaftlichen Erkenntnissen von:
                                    </p>
                                    <ul class="vpd-sources-list">
                                        <li>Pulse Grow - VPD für Pflanzen erklärt</li>
                                        <li>Coco for Cannabis - VPD Calculator</li>
                                        <li>Royal Queen Seeds - VPD beim Cannabisanbau</li>
                                    </ul>
                                    <p class="text-muted small mt-2">
                                        <i class="fa-solid fa-calendar me-1"></i>Stand: 12.07.2025 | 
                                        <i class="fa-solid fa-user me-1"></i>Erstellt von: Grow-Experte (GPT)
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // NEU: VPD-Eintrag löschen
    async deleteVPDEntry(entryId) {
        // Modal für Bestätigung anzeigen
        this.showDeleteConfirmationModal(entryId);
    }

    // NEU: Lösch-Bestätigungs-Modal anzeigen
    showDeleteConfirmationModal(entryId) {
        // Prüfen ob Modal bereits existiert
        let modal = document.getElementById('vpd-delete-confirmation-modal');
        if (modal) {
            modal.remove();
        }

        const modalHtml = `
            <div class="modal fade" id="vpd-delete-confirmation-modal" tabindex="-1" aria-labelledby="vpd-delete-confirmation-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="vpd-delete-confirmation-modal-label">
                                <i class="fas fa-exclamation-triangle me-2"></i>VPD-Eintrag löschen
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p class="mb-0">
                                <strong>Möchtest Du diesen VPD-Eintrag wirklich löschen?</strong>
                            </p>
                            <p class="text-muted small mt-2">
                                Diese Aktion kann nicht rückgängig gemacht werden.
                            </p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Abbrechen
                            </button>
                            <button type="button" class="btn btn-danger" id="vpd-confirm-delete-btn" data-entry-id="${entryId}">
                                <i class="fas fa-trash me-1"></i>Löschen
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Modal anzeigen
        const modalElement = document.getElementById('vpd-delete-confirmation-modal');
        const bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.show();

        // Event-Listener für Lösch-Button
        const confirmBtn = document.getElementById('vpd-confirm-delete-btn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', async () => {
                // Modal schließen
                bootstrapModal.hide();
                
                // Löschung durchführen
                await this.performVPDEntryDeletion(entryId);
            });
        }

        // Modal nach dem Schließen entfernen
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });
    }

    // NEU: Tatsächliche Löschung durchführen
    async performVPDEntryDeletion(entryId) {
        try {
            const response = await fetch(`/api/vpd/history/delete/${entryId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Fehler beim Löschen des Eintrags');
            }
            
            const result = await response.json();
            
            if (result.success) {
                // Erfolgsmeldung anzeigen
                this.showNotification('VPD-Eintrag erfolgreich gelöscht', 'success');
                
                // Chart komplett neu laden
                await this.reloadVPDHistoryChart();
            } else {
                throw new Error(result.message || 'Unbekannter Fehler');
            }
            
        } catch (error) {
            console.error('Fehler beim Löschen des VPD-Eintrags:', error);
            this.showNotification(`Fehler beim Löschen: ${error.message}`, 'error');
        }
    }

    // NEU: Benachrichtigung anzeigen
    showNotification(message, type = 'info') {
        // Einfache Benachrichtigung erstellen
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Automatisch nach 3 Sekunden entfernen
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // NEU: VPD-Historie-Chart laden
    async renderVPDHistoryChart() {
        // Plant ID aus DOM holen
        let plantId = null;
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            plantId = plantIdElement.getAttribute('data-plant-id');
        } else {
            // Aus URL holen
            const urlParams = new URLSearchParams(window.location.search);
            plantId = urlParams.get('plant_id');
        }
        if (!plantId) return;
        
        // Chart-Container einfügen
        const chartSection = document.createElement('div');
        chartSection.className = 'vpd-history-section';
        chartSection.innerHTML = `
            <div class="vpd-section-title vpd-history-title" style="margin-top:18px;background:rgba(33,150,243,0.10);border-left:4px solid #2196f3;">
                <i class="fa-solid fa-chart-area me-2"></i>VPD-Historie (letzte 20 Einträge)
            </div>
            <div class="vpd-section-content vpd-history-content" style="background:rgba(33,150,243,0.04);">
                <canvas id="vpd-history-chart" height="120"></canvas>
                <div id="vpd-entries-list" class="mt-3"></div>
            </div>
        `;
        
        // Unterhalb der berechneten Werte einfügen
        const calculatedSection = this.container.querySelector('.vpd-section-content.vpd-calculated-content');
        if (calculatedSection && calculatedSection.parentNode) {
            calculatedSection.parentNode.insertAdjacentElement('afterend', chartSection);
        } else {
            this.container.appendChild(chartSection);
        }
        
        // Daten laden und Chart erstellen
        await this.loadVPDChartData(plantId);
    }

    // NEU: VPD-Chart-Daten laden und Chart erstellen
    async loadVPDChartData(plantId) {
        try {
            const resp = await fetch(`/api/vpd/history/${plantId}`);
            if (!resp.ok) return;
            const json = await resp.json();
            if (!json.history || !Array.isArray(json.history) || json.history.length === 0) {
                // Keine Daten - Container leeren
                const entriesListContainer = document.getElementById('vpd-entries-list');
                if (entriesListContainer) {
                    entriesListContainer.innerHTML = '<p class="text-muted text-center">Keine VPD-Einträge vorhanden</p>';
                }
                return;
            }
            
            // Einträge-Liste mit Lösch-Buttons anzeigen
            this.renderVPDEntriesList(json.history);
            
            // Daten für Chart.js aufbereiten
            const labels = json.history.map(e => (e.entry_date ? e.entry_date.split('T')[0] : ''));
            const vpdData = json.history.map(e => e.vpd_value);
            const tempData = json.history.map(e => e.temperature);
            const humData = json.history.map(e => e.humidity);
            
            // Chart.js initialisieren
            if (window.Chart) {
                const chartCanvas = document.getElementById('vpd-history-chart');
                if (chartCanvas) {
                    // Altes Chart zerstören falls vorhanden
                    if (this.vpdChart) {
                        this.vpdChart.destroy();
                    }
                    
                    this.vpdChart = new window.Chart(chartCanvas.getContext('2d'), {
                        type: 'line',
                        data: {
                            labels: labels.reverse(),
                            datasets: [
                                {
                                    label: 'VPD (kPa)',
                                    data: vpdData.reverse(),
                                    borderColor: '#00bcd4',
                                    backgroundColor: 'rgba(0,188,212,0.08)',
                                    tension: 0.3,
                                    yAxisID: 'y',
                                    pointRadius: 2,
                                    fill: true
                                },
                                {
                                    label: 'Temperatur (°C)',
                                    data: tempData.reverse(),
                                    borderColor: '#ff9800',
                                    backgroundColor: 'rgba(255,152,0,0.07)',
                                    tension: 0.3,
                                    yAxisID: 'y1',
                                    pointRadius: 2,
                                    fill: false
                                },
                                {
                                    label: 'Luftfeuchte (%)',
                                    data: humData.reverse(),
                                    borderColor: '#4caf50',
                                    backgroundColor: 'rgba(76,175,80,0.07)',
                                    tension: 0.3,
                                    yAxisID: 'y2',
                                    pointRadius: 2,
                                    fill: false
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { display: true }
                            },
                            scales: {
                                y: {
                                    type: 'linear',
                                    position: 'left',
                                    title: { display: true, text: 'VPD (kPa)' },
                                    min: 0, max: 2.5
                                },
                                y1: {
                                    type: 'linear',
                                    position: 'right',
                                    title: { display: true, text: 'Temperatur (°C)' },
                                    grid: { drawOnChartArea: false },
                                    min: 10, max: 40
                                },
                                y2: {
                                    type: 'linear',
                                    position: 'right',
                                    title: { display: true, text: 'Luftfeuchte (%)' },
                                    grid: { drawOnChartArea: false },
                                    min: 0, max: 100,
                                    offset: true
                                }
                            }
                        }
                    });
                }
            }
        } catch (e) {
            console.error('Fehler beim Laden der VPD-Chart-Daten:', e);
        }
    }

    // NEU: VPD-Chart komplett neu laden (nach Löschen)
    async reloadVPDHistoryChart() {
        // Plant ID aus DOM holen
        let plantId = null;
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            plantId = plantIdElement.getAttribute('data-plant-id');
        } else {
            // Aus URL holen
            const urlParams = new URLSearchParams(window.location.search);
            plantId = urlParams.get('plant_id');
        }
        if (!plantId) return;
        
        // Altes Chart zerstören
        if (this.vpdChart) {
            this.vpdChart.destroy();
            this.vpdChart = null;
        }
        
        // Daten neu laden
        await this.loadVPDChartData(plantId);
    }

    // NEU: VPD-Einträge-Liste mit Lösch-Buttons rendern
    renderVPDEntriesList(entries) {
        const entriesListContainer = document.getElementById('vpd-entries-list');
        if (!entriesListContainer) return;
        
        if (entries.length === 0) {
            entriesListContainer.innerHTML = '<p class="text-muted text-center">Keine VPD-Einträge vorhanden</p>';
            return;
        }
        
        const entriesHtml = entries.map(entry => {
            const date = entry.entry_date ? entry.entry_date.split('T')[0] : 'Unbekannt';
            const time = entry.entry_date ? entry.entry_date.split('T')[1]?.split('.')[0] : '';
            const leafTempHtml = entry.leaf_temperature ? ` | Blatt: ${entry.leaf_temperature}°C` : '';
            
            return `
                <div class="vpd-entry-item d-flex justify-content-between align-items-center p-2 border-bottom" style="background:rgba(255,255,255,0.5);">
                    <div class="vpd-entry-info">
                        <small class="text-muted">${date} ${time}</small><br>
                        <strong>VPD: ${entry.vpd_value} kPa</strong> | 
                        Temp: ${entry.temperature}°C | 
                        Luft: ${entry.humidity}%${leafTempHtml}
                        <br>
                        <small class="text-muted">Phase: ${entry.phase} | ${entry.notes || 'Automatisch gespeichert'}</small>
                    </div>
                    <button 
                        class="btn btn-sm btn-outline-danger vpd-delete-entry-btn" 
                        data-entry-id="${entry.id}"
                        title="VPD-Eintrag löschen"
                    >
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
        }).join('');
        
        entriesListContainer.innerHTML = `
            <div class="vpd-entries-container" style="max-height:200px;overflow-y:auto;">
                ${entriesHtml}
            </div>
        `;
    }

    // Widget zerstören (Cleanup)
    destroy() {
        // Chart zerstören falls vorhanden
        if (this.vpdChart) {
            this.vpdChart.destroy();
            this.vpdChart = null;
        }
        
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    // NEU: Govee Sensor Integration Functions (Global)
    
    // Govee Setup durchführen
    async setupGoveeIntegration() {
        try {
            // Globalen Govee Sensor Manager verwenden
            if (window.goveeSensorManager) {
                window.goveeSensorManager.showSetupModal();
                
                // Als Subscriber registrieren
                window.goveeSensorManager.subscribe(this.containerId, (reading) => {
                    this.updateFromGoveeReading(reading);
                });
            } else {
                // Fallback: Eigenes Setup-Modal
                this.showGoveeSetupModal();
            }
        } catch (error) {
            console.error('Fehler beim Govee Setup:', error);
            this.showNotification('Fehler beim Govee Setup', 'error');
        }
    }
    
    // Govee-Daten verarbeiten (Callback für globalen Manager)
    updateFromGoveeReading(reading) {
        if (!reading) return;
        
        // Werte in VPD-Widget setzen
        this.temperature = reading.temperature;
        this.humidity = reading.humidity;
        
        // Input-Felder aktualisieren
        const tempInput = document.getElementById('temperature-input');
        const humInput = document.getElementById('humidity-input');
        
        if (tempInput) tempInput.value = this.temperature;
        if (humInput) humInput.value = this.humidity;
        
        // VPD automatisch berechnen
        this.calculateVPD();
        
        // Status anzeigen
        this.showGoveeStatus(reading);
    }
    
    // Govee Setup Modal anzeigen
    showGoveeSetupModal() {
        // Prüfen ob Modal bereits existiert
        let modal = document.getElementById('govee-setup-modal');
        if (modal) {
            modal.remove();
        }

        const modalHtml = `
            <div class="modal fade" id="govee-setup-modal" tabindex="-1" aria-labelledby="govee-setup-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="govee-setup-modal-label">
                                <i class="fas fa-thermometer-half me-2"></i>Govee Sensor Integration
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="govee-setup-content">
                                <div class="text-center">
                                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                                    <p class="mt-2">Lade Govee Setup...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Modal anzeigen
        const modalElement = document.getElementById('govee-setup-modal');
        const bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.show();

        // Setup-Content laden
        this.loadGoveeSetupContent();

        // Modal nach dem Schließen entfernen
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });
    }
    
    // Govee Setup Content laden
    async loadGoveeSetupContent() {
        const content = document.getElementById('govee-setup-content');
        if (!content) return;

        try {
            // Status prüfen
            const statusResponse = await fetch('/api/sensor/govee/status');
            const statusData = await statusResponse.json();
            
            if (statusData.success && statusData.status.connected) {
                // Bereits verbunden - Geräte anzeigen
                await this.showGoveeDevices();
            } else {
                // Setup erforderlich
                this.showGoveeApiKeyForm();
            }
        } catch (error) {
            console.error('Fehler beim Laden des Govee Status:', error);
            this.showGoveeApiKeyForm();
        }
    }
    
    // Govee API Key Form anzeigen
    showGoveeApiKeyForm() {
        const content = document.getElementById('govee-setup-content');
        if (!content) return;

        content.innerHTML = `
            <div class="govee-setup-step">
                <h6><i class="fas fa-key me-2"></i>Schritt 1: Govee API Key einrichten</h6>
                <p class="text-muted">
                    Um Dein Govee Hygrometer zu verbinden, benötigen wir Deinen Govee API Key.
                </p>
                
                <div class="alert alert-info">
                    <strong>So bekommst Du Deinen API Key:</strong>
                    <ol class="mb-0 mt-2">
                        <li>Gehe zu <a href="https://developer.govee.com" target="_blank">developer.govee.com</a></li>
                        <li>Erstelle ein kostenloses Konto</li>
                        <li>Erstelle eine neue App</li>
                        <li>Kopiere den API Key</li>
                    </ol>
                </div>
                
                <div class="mb-3">
                    <label for="govee-api-key" class="form-label">Govee API Key</label>
                    <input type="password" class="form-control" id="govee-api-key" 
                           placeholder="Dein Govee API Key">
                    <div class="form-text">Der API Key wird sicher gespeichert und nur für die Verbindung verwendet.</div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" id="govee-test-connection">
                        <i class="fas fa-plug me-2"></i>Verbindung testen
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Abbrechen
                    </button>
                </div>
            </div>
        `;

        // Event-Listener für Test-Button
        const testBtn = document.getElementById('govee-test-connection');
        if (testBtn) {
            testBtn.addEventListener('click', () => this.testGoveeConnection());
        }
    }
    
    // Govee Verbindung testen
    async testGoveeConnection() {
        const apiKeyInput = document.getElementById('govee-api-key');
        const testBtn = document.getElementById('govee-test-connection');
        
        if (!apiKeyInput || !testBtn) return;
        
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            this.showNotification('Bitte gib einen API Key ein', 'error');
            return;
        }
        
        // Button deaktivieren
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Teste Verbindung...';
        
        try {
            const response = await fetch('/api/sensor/govee/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ api_key: apiKey })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(`Verbindung erfolgreich! ${data.hygrometers_found} Hygrometer gefunden.`, 'success');
                // API Key speichern und Geräte anzeigen
                await this.saveGoveeApiKey(apiKey);
                await this.showGoveeDevices();
            } else {
                this.showNotification(`Verbindung fehlgeschlagen: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Fehler beim Testen der Govee Verbindung:', error);
            this.showNotification('Fehler beim Testen der Verbindung', 'error');
        } finally {
            // Button wieder aktivieren
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-plug me-2"></i>Verbindung testen';
        }
    }
    
    // Govee API Key speichern
    async saveGoveeApiKey(apiKey) {
        try {
            const response = await fetch('/api/sensor/govee/setup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ api_key: apiKey })
            });
            
            const data = await response.json();
            if (data.success) {
                this.goveeEnabled = true;
                return true;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Fehler beim Speichern des API Keys:', error);
            this.showNotification('Fehler beim Speichern des API Keys', 'error');
            return false;
        }
    }
    
    // Govee Geräte anzeigen
    async showGoveeDevices() {
        const content = document.getElementById('govee-setup-content');
        if (!content) return;

        try {
            const response = await fetch('/api/sensor/govee/devices');
            const data = await response.json();
            
            if (data.success && data.hygrometers.length > 0) {
                const devicesHtml = data.hygrometers.map(device => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="govee-device" 
                                       id="device-${device.device_id}" value="${device.device_id}" 
                                       data-model="${device.model}">
                                <label class="form-check-label" for="device-${device.device_id}">
                                    <strong>${device.name}</strong><br>
                                    <small class="text-muted">
                                        ID: ${device.device_id} | Model: ${device.model}
                                    </small>
                                </label>
                            </div>
                        </div>
                    </div>
                `).join('');

                content.innerHTML = `
                    <div class="govee-setup-step">
                        <h6><i class="fas fa-thermometer-half me-2"></i>Schritt 2: Hygrometer auswählen</h6>
                        <p class="text-muted">
                            Wähle das Hygrometer aus, das Du für die VPD-Messung verwenden möchtest.
                        </p>
                        
                        <div class="mb-3">
                            ${devicesHtml}
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="govee-auto-update">
                            <label class="form-check-label" for="govee-auto-update">
                                <strong>Automatische Updates aktivieren</strong><br>
                                <small class="text-muted">Daten alle 30 Sekunden automatisch aktualisieren</small>
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="govee-save-device">
                                <i class="fas fa-save me-2"></i>Gerät speichern
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                Abbrechen
                            </button>
                        </div>
                    </div>
                `;

                // Event-Listener für Speichern-Button
                const saveBtn = document.getElementById('govee-save-device');
                if (saveBtn) {
                    saveBtn.addEventListener('click', () => this.saveGoveeDevice());
                }
            } else {
                content.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Keine Hygrometer gefunden</strong><br>
                        Stelle sicher, dass Dein Govee Hygrometer mit der Govee App verbunden ist.
                    </div>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Schließen
                    </button>
                `;
            }
        } catch (error) {
            console.error('Fehler beim Laden der Govee-Geräte:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Fehler beim Laden der Geräte</strong><br>
                    ${error.message}
                </div>
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    Schließen
                </button>
            `;
        }
    }
    
    // Govee Gerät speichern
    async saveGoveeDevice() {
        const selectedDevice = document.querySelector('input[name="govee-device"]:checked');
        const autoUpdate = document.getElementById('govee-auto-update').checked;
        
        if (!selectedDevice) {
            this.showNotification('Bitte wähle ein Hygrometer aus', 'error');
            return;
        }
        
        const deviceId = selectedDevice.value;
        const model = selectedDevice.getAttribute('data-model');
        
        // Gerät speichern
        this.goveeDevice = {
            id: deviceId,
            model: model
        };
        this.goveeAutoUpdate = autoUpdate;
        
        // Automatische Updates starten falls aktiviert
        if (autoUpdate) {
            this.startGoveeAutoUpdate();
        }
        
        // Modal schließen
        const modal = bootstrap.Modal.getInstance(document.getElementById('govee-setup-modal'));
        if (modal) {
            modal.hide();
        }
        
        this.showNotification('Govee Hygrometer erfolgreich verbunden!', 'success');
        
        // VPD-Widget mit aktuellen Daten aktualisieren
        await this.updateFromGovee();
    }
    
    // Govee automatische Updates starten
    startGoveeAutoUpdate() {
        // Automatische Updates deaktiviert
        console.log('💨 VPD-Widget: Automatische Govee-Updates deaktiviert');
        
        // Hinweis anzeigen
        this.showUpdateNotice('Govee Integration: Automatische Updates deaktiviert. Daten werden nur bei Bedarf aktualisiert.');
    }
    
    /**
     * Update-Hinweis anzeigen
     */
    showUpdateNotice(message) {
        const noticeElement = document.createElement('div');
        noticeElement.className = 'alert alert-info alert-sm mt-2';
        noticeElement.innerHTML = `
            <i class="fa-solid fa-info-circle me-2"></i>
            <small>${message}</small>
        `;
        
        // Hinweis in den Widget-Container einfügen
        if (this.container) {
            this.container.appendChild(noticeElement);
        }
    }
    
    // Govee automatische Updates stoppen
    stopGoveeAutoUpdate() {
        if (this.goveeUpdateInterval) {
            clearInterval(this.goveeUpdateInterval);
            this.goveeUpdateInterval = null;
        }
    }
    
    // Daten von Govee abrufen und VPD-Widget aktualisieren
    async updateFromGovee() {
        if (!this.goveeDevice) return;
        
        try {
            const response = await fetch(`/api/sensor/govee/reading/${this.goveeDevice.id}?model=${this.goveeDevice.model}`);
            const data = await response.json();
            
            if (data.success && data.reading) {
                const reading = data.reading;
                
                // Werte in VPD-Widget setzen
                this.temperature = reading.temperature;
                this.humidity = reading.humidity;
                
                // Input-Felder aktualisieren
                const tempInput = document.getElementById('temperature-input');
                const humInput = document.getElementById('humidity-input');
                
                if (tempInput) tempInput.value = this.temperature;
                if (humInput) humInput.value = this.humidity;
                
                // VPD automatisch berechnen
                await this.calculateVPD();
                
                // Status anzeigen
                this.showGoveeStatus(reading);
            }
        } catch (error) {
            console.error('Fehler beim Abrufen der Govee-Daten:', error);
        }
    }
    
    // Govee Status anzeigen
    showGoveeStatus(reading) {
        // Status-Badge im VPD-Widget anzeigen
        let statusElement = document.getElementById('govee-status-badge');
        if (!statusElement) {
            // Status-Badge erstellen falls nicht vorhanden
            const header = this.container.querySelector('.vpd-widget-header');
            if (header) {
                statusElement = document.createElement('span');
                statusElement.id = 'govee-status-badge';
                statusElement.className = 'badge bg-success ms-2';
                statusElement.innerHTML = '<i class="fas fa-thermometer-half"></i> Govee';
                header.appendChild(statusElement);
            }
        }
        
        if (statusElement) {
            statusElement.innerHTML = `
                <i class="fas fa-thermometer-half"></i> 
                Govee: ${reading.temperature}°C / ${reading.humidity}%
                <small class="ms-1">(${reading.battery}%)</small>
            `;
        }
    }
}

// Schritt 1: Guidelines laden und cachen
VPDWidget.vpdGuidelines = null;
VPDWidget.vpdGuidelinesLoaded = false;
VPDWidget.loadVPDGuidelines = async function() {
    if (VPDWidget.vpdGuidelinesLoaded) return VPDWidget.vpdGuidelines;
    try {
        const response = await fetch('/static/data/vpd-guidelines.json');
        if (response.ok) {
            const json = await response.json();
            VPDWidget.vpdGuidelines = json.vpdGuidelines;
            VPDWidget.vpdGuidelinesLoaded = true;
            return VPDWidget.vpdGuidelines;
        }
    } catch (e) {}
    return null;
};

// Schritt 2: Im Widget die Guidelines laden und bei renderCalculatedValues prüfen

// Schritt 3: Stelle sicher, dass beim ersten Rendern die Guidelines geladen werden, bevor das Widget angezeigt wird.
// Dazu passe loadVPDOptimization an:

// Automatische Initialisierung entfernt - wird jetzt über Widget Manager verwaltet

// Global verfügbar machen für Widget Manager
window.VPDWidget = VPDWidget;
window.VPDOptimizerWidget = VPDWidget; // Alias für Kompatibilität

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VPDWidget;
} 