/**
 * Flowering Predictive Analytics - Verwaltet Vorhersagen und Analytics
 */

class FloweringPredictiveAnalytics {
    constructor(widget) {
        this.widget = widget;
        this.predictiveAnalyticsInitialized = false;
        this.smartSchedulingInitialized = false;
    }

    /**
     * Initialisiert Predictive Analytics
     */
    setupPredictiveAnalytics() {
        if (!window.predictiveAnalytics) {
            console.warn('🌸 Blüte-Widget: PredictiveAnalytics nicht verfügbar');
            return;
        }
        
        // Predictive Analytics nur beim ersten <PERSON>den initialisieren
        if (!this.predictiveAnalyticsInitialized) {
            this.predictiveAnalyticsInitialized = true;
            
            // Analytics mit Verzögerung generieren
            setTimeout(() => {
                this.generateGrowthPrediction();
                this.predictProblems();
                this.generateHarvestPrediction();
            }, 3000);
        }
    }

    /**
     * Initialisiert Smart Scheduling
     */
    setupSmartScheduling() {
        if (!window.smartScheduler) {
            console.warn('🌸 Blüte-Widget: SmartScheduler nicht verfügbar');
            return;
        }
        
        // Smart Scheduling nur beim ersten Laden initialisieren
        if (!this.smartSchedulingInitialized) {
            this.smartSchedulingInitialized = true;
            
            // Benutzer-Präferenzen laden
            const userPreferences = window.smartScheduler.getUserPreferences(this.widget.currentPlantId);
            
            // Intelligente Zeitpläne mit Verzögerung generieren
            setTimeout(() => {
                this.generateSmartSchedule(userPreferences);
            }, 3000);
        }
    }

    /**
     * Generiert Wachstumsprognose
     */
    async generateGrowthPrediction() {
        if (!window.predictiveAnalytics) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.widget.floweringData?.flowering_status?.current_day || 0
            };
            
            // Wachstumsprognose generieren
            const prediction = await window.predictiveAnalytics.generateGrowthPrediction(
                this.widget.currentPlantId,
                plantData,
                this.widget.lightingManager.lightingData,
                7 // 7 Tage Vorhersage
            );
            
            if (prediction.available) {
                // Prognose anzeigen
                this.showGrowthPrediction(prediction);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Wachstumsprognose:', error);
        }
    }

    /**
     * Zeigt Wachstumsprognose an
     */
    showGrowthPrediction(prediction) {
        // Prüfen ob bereits eine Prognose Card existiert
        let predictionCard = this.widget.element.querySelector('.growth-prediction-card');
        
        if (!predictionCard) {
            // Neue Prognose Card erstellen
            predictionCard = document.createElement('div');
            predictionCard.className = 'lighting-card growth-prediction-card';
            this.widget.element.appendChild(predictionCard);
        }
        
        // Card-Inhalt aktualisieren
        predictionCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-chart-line"></i>
                <h3>Wachstumsprognose</h3>
                <div class="prediction-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">7-Tage Vorhersage</span>
                </div>
            </div>
            <div class="prediction-content">
                <div class="prediction-metrics">
                    <div class="metric">
                        <div class="metric-label">Erwartetes Wachstum</div>
                        <div class="metric-value">${prediction.expected_growth}%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Blütenentwicklung</div>
                        <div class="metric-value">${prediction.bud_development}%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Konfidenz</div>
                        <div class="metric-value">${prediction.confidence}%</div>
                    </div>
                </div>
                <div class="prediction-chart">
                    <canvas id="growthPredictionChart" width="300" height="150"></canvas>
                </div>
            </div>
        `;
        
        // Prognose Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && predictionCard.parentElement !== phase6Container) {
            phase6Container.appendChild(predictionCard);
        }
    }

    /**
     * Vorhersage von Problemen
     */
    async predictProblems() {
        if (!window.predictiveAnalytics) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.widget.floweringData?.flowering_status?.current_day || 0
            };
            
            // Umwelt-Daten (vereinfacht)
            const environmentalData = {
                temperature: 25, // Beispiel-Wert
                humidity: 60,    // Beispiel-Wert
                co2: 400         // Beispiel-Wert
            };
            
            // Probleme vorhersagen
            const problems = await window.predictiveAnalytics.predictProblems(
                this.widget.currentPlantId,
                plantData,
                environmentalData,
                this.widget.lightingManager.lightingData
            );
            
            if (problems.length > 0) {
                // Probleme anzeigen
                this.showProblemPredictions(problems);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Problemvorhersage:', error);
        }
    }

    /**
     * Zeigt Problem-Vorhersagen an
     */
    showProblemPredictions(problems) {
        // Prüfen ob bereits eine Problem Card existiert
        let problemCard = this.widget.element.querySelector('.problem-prediction-card');
        
        if (!problemCard) {
            // Neue Problem Card erstellen
            problemCard = document.createElement('div');
            problemCard.className = 'lighting-card problem-prediction-card';
            this.widget.element.appendChild(problemCard);
        }
        
        // Card-Inhalt aktualisieren
        problemCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-exclamation-triangle"></i>
                <h3>Problem-Vorhersage</h3>
                <div class="prediction-status">
                    <span class="status-indicator warning"></span>
                    <span class="status-text">${problems.length} potentielle Probleme</span>
                </div>
            </div>
            <div class="prediction-content">
                <div class="problems-list">
                    ${problems.map(problem => `
                        <div class="problem-item severity-${problem.severity}">
                            <div class="problem-header">
                                <div class="problem-title">${problem.title}</div>
                                <div class="problem-probability">${problem.probability}%</div>
                            </div>
                            <div class="problem-description">${problem.description}</div>
                            <div class="problem-prevention">
                                <strong>Vorbeugung:</strong> ${problem.prevention}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Problem Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && problemCard.parentElement !== phase6Container) {
            phase6Container.appendChild(problemCard);
        }
    }

    /**
     * Generiert Ernte-Prognose
     */
    async generateHarvestPrediction() {
        if (!window.predictiveAnalytics) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.widget.floweringData?.flowering_status?.current_day || 0
            };
            
            // Trichom-Daten (vereinfacht)
            const trichomeData = {
                clear: 30,
                milky: 50,
                amber: 20
            };
            
            // Ernte-Prognose generieren
            const harvestPrediction = await window.predictiveAnalytics.generateHarvestPrediction(
                this.widget.currentPlantId,
                plantData,
                trichomeData
            );
            
            if (harvestPrediction.available) {
                // Ernte-Prognose anzeigen
                this.showHarvestPrediction(harvestPrediction);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Ernte-Prognose:', error);
        }
    }

    /**
     * Zeigt Ernte-Prognose an
     */
    showHarvestPrediction(harvestPrediction) {
        // Prüfen ob bereits eine Ernte Card existiert
        let harvestCard = this.widget.element.querySelector('.harvest-prediction-card');
        
        if (!harvestCard) {
            // Neue Ernte Card erstellen
            harvestCard = document.createElement('div');
            harvestCard.className = 'lighting-card harvest-prediction-card';
            this.widget.element.appendChild(harvestCard);
        }
        
        // Card-Inhalt aktualisieren
        harvestCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-cut"></i>
                <h3>Ernte-Prognose</h3>
                <div class="prediction-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">Basierend auf Trichom-Analyse</span>
                </div>
            </div>
            <div class="prediction-content">
                <div class="harvest-timeline">
                    <div class="harvest-option optimal">
                        <div class="harvest-label">Optimal</div>
                        <div class="harvest-date">${harvestPrediction.optimal_date}</div>
                        <div class="harvest-days">Tag ${harvestPrediction.optimal_day}</div>
                    </div>
                    <div class="harvest-option early">
                        <div class="harvest-label">Früh</div>
                        <div class="harvest-date">${harvestPrediction.early_date}</div>
                        <div class="harvest-days">Tag ${harvestPrediction.early_day}</div>
                    </div>
                    <div class="harvest-option late">
                        <div class="harvest-label">Spät</div>
                        <div class="harvest-date">${harvestPrediction.late_date}</div>
                        <div class="harvest-days">Tag ${harvestPrediction.late_day}</div>
                    </div>
                </div>
                <div class="harvest-factors">
                    <div class="factor">
                        <span class="factor-label">Ertrag:</span>
                        <span class="factor-value">${harvestPrediction.yield_estimate}g</span>
                    </div>
                    <div class="factor">
                        <span class="factor-label">Qualität:</span>
                        <span class="factor-value">${harvestPrediction.quality_score}/10</span>
                    </div>
                    <div class="factor">
                        <span class="factor-label">Konfidenz:</span>
                        <span class="factor-value">${harvestPrediction.confidence}%</span>
                    </div>
                </div>
            </div>
        `;
        
        // Ernte Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && harvestCard.parentElement !== phase6Container) {
            phase6Container.appendChild(harvestCard);
        }
    }

    /**
     * Generiert intelligente Zeitpläne
     */
    async generateSmartSchedule(userPreferences = {}) {
        if (!window.smartScheduler) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.widget.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.widget.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.widget.floweringData?.flowering_status?.current_day || 0
            };
            
            // Intelligente Zeitpläne generieren
            const schedule = await window.smartScheduler.generateSmartSchedule(
                this.widget.currentPlantId,
                plantData,
                this.widget.lightingManager.lightingData,
                userPreferences
            );
            
            if (schedule) {
                // Zeitplan anzeigen
                this.showSmartSchedule(schedule);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Smart Scheduling:', error);
        }
    }

    /**
     * Zeigt Smart Schedule an
     */
    showSmartSchedule(schedule) {
        // Prüfen ob bereits eine Smart Schedule Card existiert
        let scheduleCard = this.widget.element.querySelector('.smart-schedule-card');
        
        if (!scheduleCard) {
            // Neue Smart Schedule Card erstellen
            scheduleCard = document.createElement('div');
            scheduleCard.className = 'lighting-card smart-schedule-card';
            this.widget.element.appendChild(scheduleCard);
        }
        
        // Card-Inhalt aktualisieren
        scheduleCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-calendar-alt"></i>
                <h3>Intelligenter Zeitplan</h3>
                <div class="schedule-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">KI-optimiert</span>
                </div>
            </div>
            <div class="schedule-content">
                <div class="schedule-recommendations">
                    ${schedule.recommendations.map(rec => `
                        <div class="schedule-item">
                            <div class="schedule-time">${rec.time}</div>
                            <div class="schedule-action">${rec.action}</div>
                            <div class="schedule-reason">${rec.reason}</div>
                        </div>
                    `).join('')}
                </div>
                <div class="schedule-benefits">
                    <div class="benefit">
                        <span class="benefit-label">Energieersparnis:</span>
                        <span class="benefit-value">${schedule.energy_savings}%</span>
                    </div>
                    <div class="benefit">
                        <span class="benefit-label">Ertragssteigerung:</span>
                        <span class="benefit-value">${schedule.yield_improvement}%</span>
                    </div>
                </div>
            </div>
        `;
        
        // Schedule Card in den Phase 6 Features Container einfügen
        const phase6Container = this.widget.element.querySelector('#phase6Features');
        if (phase6Container && scheduleCard.parentElement !== phase6Container) {
            phase6Container.appendChild(scheduleCard);
        }
    }
}
