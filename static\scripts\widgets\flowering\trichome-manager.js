/**
 * Flowering Trichome Manager - Verwaltet alle Trichom-bezogenen Funktionen
 */

class FloweringTrichomeManager {
    constructor(widget) {
        this.widget = widget;
        this.trichomeData = null;
        this.trichomeTriggerData = null;
        this.trichomeRecommendationData = null;
        this.trichomeProgressData = null;
        this.trichomeGuidelinesLoaded = false;
    }

    /**
     * Lädt alle Trichom-Daten
     */
    async loadTrichomeData() {
        try {
            await Promise.all([
                this.loadTrichomeStatus(),
                this.loadTrichomeTrigger(),
                this.loadTrichomeRecommendation(),
                this.loadTrichomeProgress(),
                this.loadTrichomeGuidelines()
            ]);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden der Trichom-Daten:', error);
        }
    }

    /**
     * Lädt Trichom-Status
     */
    async loadTrichomeStatus() {
        try {
            const response = await fetch(`/flowering/trichome-status/${this.widget.currentPlantId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // Speichere die Daten direkt
            this.trichomeData = data;
            
            // Aktualisiere UI nur wenn Daten vorhanden sind
            if (data && data.has_data) {
                this.updateTrichomeStatus(data);
                this.updateTrichomeBadge(data);
                
                // Beobachtungslog aktualisieren
                if (data.observations) {
                    this.updateObservationList(data.observations);
                } else {
                    this.updateObservationList([]);
                }
            } else {
                this.updateTrichomeNoData();
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Status:', error);
        }
    }

    /**
     * Lädt Trichom-Trigger-Daten
     */
    async loadTrichomeTrigger() {
        try {
            const response = await fetch(`/flowering/trichome-trigger/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeTriggerData = data;
            this.updateTrichomeTrigger(data);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Triggers:', error);
        }
    }

    /**
     * Lädt Trichom-Empfehlungen
     */
    async loadTrichomeRecommendation() {
        try {
            const response = await fetch(`/flowering/trichome-recommendation/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeRecommendationData = data;
            this.updateTrichomeRecommendation(data);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden der Trichom-Empfehlung:', error);
        }
    }

    /**
     * Lädt Trichom-Fortschritt
     */
    async loadTrichomeProgress() {
        try {
            const response = await fetch(`/flowering/trichome-progress/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeProgressData = data;
            this.updateTrichomeProgress(data);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Fortschritts:', error);
        }
    }

    /**
     * Lädt Trichom-Guidelines
     */
    async loadTrichomeGuidelines() {
        try {
            const response = await fetch(`/flowering/trichome-guidelines/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            if (data && data.guidelines) {
                this.loadGuidelinesForTab(data.guidelines, 'trichome', data.strain_type);
                this.trichomeGuidelinesLoaded = true;
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden der Trichom-Guidelines:', error);
        }
    }

    /**
     * Aktualisiert den Trichom-Status
     */
    updateTrichomeStatus(data) {
        if (!data || !data.has_data) {
            this.updateTrichomeNoData();
            return;
        }

        // Status-Badge mit erweiterten Daten
        this.updateTrichomeBadge(data);

        // Letzte Aktualisierung
        const lastUpdate = this.widget.getElementById('lastTrichomeUpdate');
        if (lastUpdate && data.latest_entry) {
            const date = new Date(data.latest_entry.date).toLocaleDateString('de-DE');
            lastUpdate.textContent = date;
        }

        // Verbesserte Trichom-Balken mit neuen Daten
        this.updateTrichomeSegments(data);

        // Flush-Alert mit erweiterten Daten
        this.updateFlushAlert(data);

        // Reifegrad mit erweiterten Daten
        const maturityLevel = this.widget.getElementById('maturityLevelValue');
        if (maturityLevel) {
            maturityLevel.textContent = this.getMaturityLevelText(data.maturity_level);
        }
    }

    /**
     * Aktualisiert die Trichom-Segmente
     */
    updateTrichomeSegments(data) {
        if (!data.current_values) return;

        const values = data.current_values;
        
        // Clear Trichome
        const clearBar = this.widget.getElementById('clearTrichomeBar');
        const clearValue = this.widget.getElementById('clearTrichomeValue');
        if (clearBar) clearBar.style.width = `${values.clear}%`;
        if (clearValue) clearValue.textContent = `${values.clear}%`;

        // Milky Trichome
        const milkyBar = this.widget.getElementById('milkyTrichomeBar');
        const milkyValue = this.widget.getElementById('milkyTrichomeValue');
        if (milkyBar) milkyBar.style.width = `${values.milky}%`;
        if (milkyValue) milkyValue.textContent = `${values.milky}%`;

        // Amber Trichome
        const amberBar = this.widget.getElementById('amberTrichomeBar');
        const amberValue = this.widget.getElementById('amberTrichomeValue');
        if (amberBar) amberBar.style.width = `${values.amber}%`;
        if (amberValue) amberValue.textContent = `${values.amber}%`;
    }

    /**
     * Aktualisiert das Trichom-Badge
     */
    updateTrichomeBadge(data) {
        const badge = this.widget.getElementById('trichomeBadge');
        if (!badge || !data.current_values) return;

        const values = data.current_values;
        badge.textContent = `${values.clear}% / ${values.milky}% / ${values.amber}%`;
        
        // Badge-Farbe basierend auf Reifegrad
        badge.className = 'badge';
        if (values.amber >= 20) {
            badge.classList.add('bg-warning');
        } else if (values.milky >= 70) {
            badge.classList.add('bg-success');
        } else {
            badge.classList.add('bg-info');
        }
    }

    /**
     * Aktualisiert den Flush-Alert
     */
    updateFlushAlert(data) {
        const flushAlert = this.widget.getElementById('flushAlert');
        if (!flushAlert) return;

        if (data.flush_recommended) {
            flushAlert.style.display = 'block';
            flushAlert.className = 'alert alert-warning';
            flushAlert.innerHTML = `
                <i class="fa-solid fa-exclamation-triangle"></i>
                <strong>Flush empfohlen!</strong> 
                Die Trichome zeigen optimale Reife für den Flush-Start.
            `;
        } else {
            flushAlert.style.display = 'none';
        }
    }

    /**
     * Zeigt "Keine Daten" Zustand an
     */
    updateTrichomeNoData() {
        // Alle Werte auf 0 setzen
        this.updateTrichomeSegments({
            current_values: { clear: 0, milky: 0, amber: 0 }
        });

        // Badge aktualisieren
        const badge = this.widget.getElementById('trichomeBadge');
        if (badge) {
            badge.textContent = 'Keine Daten';
            badge.className = 'badge bg-secondary';
        }

        // Letzte Aktualisierung
        const lastUpdate = this.widget.getElementById('lastTrichomeUpdate');
        if (lastUpdate) {
            lastUpdate.textContent = 'Nie';
        }

        // Flush-Alert verstecken
        const flushAlert = this.widget.getElementById('flushAlert');
        if (flushAlert) {
            flushAlert.style.display = 'none';
        }
    }

    /**
     * Öffnet das Beobachtungs-Modal
     */
    openObservationModal() {
        const modal = document.getElementById('trichomeObservationModal');
        if (modal) {
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    }

    /**
     * Sendet eine Trichom-Beobachtung
     */
    async submitObservation() {
        const form = document.getElementById('trichomeObservationForm');
        if (!form) return;

        const formData = new FormData(form);
        const observationData = {
            plant_id: this.widget.currentPlantId,
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes'),
            date: new Date().toISOString().split('T')[0]
        };

        try {
            const response = await fetch('/flowering/trichome-observations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(observationData)
            });

            if (response.ok) {
                this.widget.uiRenderer.showSuccess('Trichom-Beobachtung erfolgreich gespeichert');
                await this.loadTrichomeData();
                
                // Modal schließen
                const modal = bootstrap.Modal.getInstance(document.getElementById('trichomeObservationModal'));
                if (modal) modal.hide();
                
                // Formular zurücksetzen
                form.reset();
            } else {
                throw new Error('Fehler beim Speichern der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Speichern der Beobachtung:', error);
            this.widget.uiRenderer.showError('Fehler beim Speichern der Beobachtung');
        }
    }

    /**
     * Aktualisiert die Beobachtungsliste
     */
    updateObservationList(observations) {
        const observationsList = this.widget.getElementById('observationsList');
        if (!observationsList) return;

        if (!observations || observations.length === 0) {
            observationsList.innerHTML = '<div class="no-observations">Keine Beobachtungen vorhanden</div>';
            return;
        }

        observationsList.innerHTML = observations.map((obs, index) => `
            <div class="observation-item">
                <div class="observation-header">
                    <div class="observation-date">${obs.date}</div>
                    <div class="observation-actions">
                        <button class="btn btn-sm btn-outline-danger" onclick="floweringWidget.trichomeManager.deleteObservation(${index})">
                            <i class="fa-solid fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="observation-values">
                    <span class="clear-value">${obs.clear_percentage}% klar</span>
                    <span class="milky-value">${obs.milky_percentage}% milchig</span>
                    <span class="amber-value">${obs.amber_percentage}% bernstein</span>
                </div>
                ${obs.notes ? `<div class="observation-notes">${obs.notes}</div>` : ''}
            </div>
        `).join('');
    }

    /**
     * Löscht eine Beobachtung
     */
    async deleteObservation(index) {
        if (!confirm('Beobachtung wirklich löschen?')) return;

        try {
            const response = await fetch(`/flowering/trichome-observations/${this.widget.currentPlantId}/${index}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.widget.uiRenderer.showSuccess('Beobachtung erfolgreich gelöscht');
                await this.loadTrichomeData();
            } else {
                throw new Error('Fehler beim Löschen der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Löschen der Beobachtung:', error);
            this.widget.uiRenderer.showError('Fehler beim Löschen der Beobachtung');
        }
    }

    /**
     * Hilfsfunktionen
     */
    getMaturityLevelText(level) {
        const levels = {
            'early': 'Früh',
            'optimal': 'Optimal',
            'late': 'Spät',
            'overripe': 'Überreif'
        };
        return levels[level] || 'Unbekannt';
    }

    loadGuidelinesForTab(guidelines, prefix, strainType) {
        // Implementation für Guidelines-Loading
        // Wird in einem separaten Update hinzugefügt
    }

    updateTrichomeTrigger(data) {
        // Implementation für Trigger-Updates
        // Wird in einem separaten Update hinzugefügt
    }

    updateTrichomeRecommendation(data) {
        // Implementation für Empfehlungs-Updates
        // Wird in einem separaten Update hinzugefügt
    }

    updateTrichomeProgress(data) {
        // Implementation für Fortschritts-Updates
        // Wird in einem separaten Update hinzugefügt
    }

    performDelete(index) {
        return this.deleteObservation(index);
    }
}
