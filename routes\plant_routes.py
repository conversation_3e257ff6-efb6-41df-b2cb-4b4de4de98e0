"""
Plant Routes Module
Handles all plant-related routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash
from database_basic import db

# Create blueprint
plant_bp = Blueprint('plant', __name__)

def format_date(date_obj):
    """Datum im deutschen Format formatieren"""
    from datetime import datetime
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d')
        except ValueError:
            return date_obj
    return date_obj.strftime('%d.%m.%Y')

@plant_bp.route('/plant/add', methods=['POST'])
def add_plant():
    """Neue Pflanze hinzufügen"""
    try:
        data = request.form
        
        # Validierung
        if not data.get('plant_name'):
            flash('Pflanzenname ist erforderlich!', 'error')
            return redirect(url_for('index'))
        
        if not data.get('start_date'):
            flash('Startdatum ist erforderlich!', 'error')
            return redirect(url_for('index'))
        
        # Prüfen ob bereits eine Pflanze mit diesem Namen existiert
        existing_plants = db.get_all_plants()
        for plant in existing_plants:
            if plant['plant_name'].lower() == data.get('plant_name', '').lower():
                flash(f'Pflanze "{data.get("plant_name")}" existiert bereits!', 'error')
                return redirect(url_for('index'))
        
        plant_data = {
            'plant_name': data.get('plant_name', ''),
            'strain': data.get('strain', ''),
            'strain_type': data.get('strain_type', 'photoperiodic'),
            'start_date': data.get('start_date', ''),
            'note': data.get('note', '')
        }
        
        plant_id = db.add_plant(plant_data)
        flash(f'Pflanze "{plant_data["plant_name"]}" erfolgreich angelegt!', 'success')
        
    except Exception as e:
        flash(f'Fehler beim Anlegen der Pflanze: {str(e)}', 'error')
    
    return redirect(url_for('index'))

@plant_bp.route('/plant/<string:plant_id>')
def plant_detail(plant_id):
    """Pflanzendetails anzeigen"""
    plant = db.get_plant_by_id(plant_id)
    if not plant:
        flash('Pflanze nicht gefunden', 'error')
        return redirect(url_for('index'))
    
    entries = db.get_entries_by_plant(plant_id)
    external_data = db.get_external_data_by_plant(plant_id)
    current_phase = db.get_plant_current_phase(plant_id)
    phase_history = db.get_plant_phase_history(plant_id)
    next_phases = db.get_plant_next_phases(plant_id)
    
    return render_template('plant_detail.html', 
                         plant=plant, 
                         entries=entries,
                         external_data=external_data,
                         current_phase=current_phase,
                         phase_history=phase_history,
                         next_phases=next_phases,
                         format_date=format_date)

@plant_bp.route('/plant/<string:plant_id>/edit', methods=['GET', 'POST'])
def edit_plant(plant_id):
    """Pflanze bearbeiten"""
    if request.method == 'POST':
        try:
            data = request.form
            
            # Plant data für die neue Datenbankstruktur
            plant_data = {
                'plant_name': data.get('plant_name', ''),
                'strain': data.get('strain', ''),
                'strain_type': data.get('strain_type', 'photoperiodic'),
                'start_date': data.get('start_date', ''),
                'flower_start_date': data.get('flower_start_date', ''),
                'grow_end_date': data.get('grow_end_date', ''),
                'location': data.get('location', ''),
                'location_info': data.get('location_info', ''),
                'pot_size': data.get('pot_size', ''),
                'lighting_system': data.get('lighting_system', ''),
                'watering_system': data.get('watering_system', ''),
                'autopot_options': data.get('autopot_options', ''),
                'substrate': data.get('substrate', ''),
                'yield_total': float(data.get('yield_total', 0)) if data.get('yield_total') else 0,
                'note': data.get('note', ''),
                # Neue externe Datenfelder
                'marke': data.get('marke', ''),
                'eltern': data.get('eltern', ''),
                'genetik': data.get('genetik', ''),
                'bluetentyp': data.get('bluetentyp', ''),
                'bluetezeit': data.get('bluetezeit', ''),
                'thc': data.get('thc', ''),
                'cbd': data.get('cbd', ''),
                'innenhoehe': int(data.get('innenhoehe', 0)) if data.get('innenhoehe') else None,
                'aussenhoehe': int(data.get('aussenhoehe', 0)) if data.get('aussenhoehe') else None,
                'ertrag_innen': int(data.get('ertrag_innen', 0)) if data.get('ertrag_innen') else None,
                'ertrag_aussen': int(data.get('ertrag_aussen', 0)) if data.get('ertrag_aussen') else None,
                'gattung': data.get('gattung', '')
            }
            
            # Debug: Ausgabe der plant_data
            print(f"🔍 Debug - plant_data: {plant_data}")
            
            # Pflanze aktualisieren
            result = db.update_plant(plant_id, plant_data)
            print(f"🔍 Debug - update_plant result: {result}")
            
            # Debug: Pflanze nach dem Update laden und anzeigen
            updated_plant = db.get_plant_by_id(plant_id)
            print(f"🔍 Debug - Pflanze nach Update:")
            print(f"  marke: {updated_plant.get('marke', 'NICHT GEFUNDEN')}")
            print(f"  eltern: {updated_plant.get('eltern', 'NICHT GEFUNDEN')}")
            print(f"  genetik: {updated_plant.get('genetik', 'NICHT GEFUNDEN')}")
            print(f"  thc: {updated_plant.get('thc', 'NICHT GEFUNDEN')}")
            print(f"  cbd: {updated_plant.get('cbd', 'NICHT GEFUNDEN')}")
            
            # Externe Daten verarbeiten
            external_keys = request.form.getlist('external_key[]')
            external_values = request.form.getlist('external_value[]')
            
            # Alte externe Daten löschen
            old_external_data = db.get_external_data_by_plant(plant_id)
            for data in old_external_data:
                db.delete_external_data(data['id'])
            
            # Neue externe Daten hinzufügen
            for key, value in zip(external_keys, external_values):
                if key.strip() and value.strip():
                    db.add_external_data(plant_id, key.strip(), value.strip())
            
            flash('Pflanze erfolgreich aktualisiert', 'success')
            
            # Event für Frontend-Widgets auslösen (wird über JavaScript abgefangen)
            # Das Event wird über eine URL-Parameter oder Session-Flag kommuniziert
            return redirect(url_for('plant.plant_detail', plant_id=plant_id, updated='true'))
            
        except Exception as e:
            flash(f'Fehler beim Aktualisieren der Pflanze: {str(e)}', 'error')
    
    # Pflanze laden
    plant = db.get_plant_by_id(plant_id)
    if not plant:
        flash('Pflanze nicht gefunden', 'error')
        return redirect(url_for('index'))
    
    # Externe Daten laden
    external_data = db.get_external_data_by_plant(plant_id)
    
    # Gespeicherte Werte für Dropdowns laden
    saved_pot_sizes = db.get_saved_values('pot_size')
    saved_lighting_systems = db.get_saved_values('lighting_system')
    saved_watering_systems = db.get_saved_values('watering_system')
    saved_substrates = db.get_saved_values('substrate')
    
    return render_template('edit_plant.html', 
                         plant=plant,
                         external_data=external_data,
                         saved_pot_sizes=saved_pot_sizes,
                         saved_lighting_systems=saved_lighting_systems,
                         saved_watering_systems=saved_watering_systems,
                         saved_substrates=saved_substrates)

@plant_bp.route('/plant/<string:plant_id>/delete', methods=['POST'])
def delete_plant(plant_id):
    """Pflanze löschen"""
    try:
        db.delete_plant(plant_id)
        flash('Pflanze erfolgreich gelöscht', 'success')
    except Exception as e:
        flash(f'Fehler beim Löschen der Pflanze: {str(e)}', 'error')
    return redirect(url_for('index')) 