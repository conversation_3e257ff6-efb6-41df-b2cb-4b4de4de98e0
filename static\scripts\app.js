/**
 * Grow-Diary-Basic - Haupt-JavaScript-Modul
 * Basis-Funktionalität für das Grow-Tagebuch
 */

class GrowDiaryApp {
    constructor() {
        this.config = {
            features: {
                measurements: false,
                watering: false,
                fertilizer: false,
                sensors: false,
                analysis: false,
                chatgpt: false
            }
        };
        
        // Module instances
        this.modules = {};
        
        this.init();
    }

    /**
     * Initialisierung der Anwendung
     */
    init() {
        // Module initialisieren
        this.initModules();
        
        // Event-Listener registrieren
        this.setupEventListeners();
        
        // Konfiguration laden
        this.loadConfig();
        
        // UI initialisieren
        this.initUI();
    }
    
    /**
     * Module initialisieren
     */
    initModules() {
        // Modal Manager
        if (window.ModalManager) {
            this.modules.modalManager = new ModalManager();
        }
        
        // Plant Form
        if (window.PlantForm) {
            this.modules.plantForm = new PlantForm();
        }
        
        // Delete Modal
        if (window.DeleteModal) {
            this.modules.deleteModal = new DeleteModal();
        }
    }

    /**
     * Event-Listener einrichten
     */
    setupEventListeners() {
        // DOM Content Loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.onDOMReady();
        });

        // Formular-Submission
        document.addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });

        // Modal-Events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || 
                e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target.closest('.modal'));
            }
        });

        // Escape-Taste für Modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * Wird ausgeführt, wenn DOM bereit ist
     */
    onDOMReady() {
        // Navigation aktualisieren
        this.updateNavigation();
        
        // Feature-Flags anwenden
        this.applyFeatureFlags();
        
        // Tooltips initialisieren
        this.initTooltips();
        
        // Datums-Formatierung
        this.formatDates();
    }

    /**
     * Konfiguration laden
     */
    loadConfig() {
        try {
            const savedConfig = localStorage.getItem('growDiaryConfig');
            if (savedConfig) {
                this.config = { ...this.config, ...JSON.parse(savedConfig) };
            }
        } catch (error) {
            console.warn('Fehler beim Laden der Konfiguration:', error);
        }
    }

    /**
     * Konfiguration speichern
     */
    saveConfig() {
        try {
            localStorage.setItem('growDiaryConfig', JSON.stringify(this.config));
        } catch (error) {
            console.error('Fehler beim Speichern der Konfiguration:', error);
        }
    }

    /**
     * UI initialisieren
     */
    initUI() {
        // Loading-States entfernen
        this.removeLoadingStates();
        
        // Alerts initialisieren
        this.initAlerts();
        
        // Buttons mit Loading-States
        this.initLoadingButtons();
    }

    /**
     * Navigation aktualisieren
     */
    updateNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    }

    /**
     * Feature-Flags anwenden
     */
    applyFeatureFlags() {
        Object.entries(this.config.features).forEach(([feature, enabled]) => {
            const elements = document.querySelectorAll(`[data-feature="${feature}"]`);
            elements.forEach(element => {
                if (enabled) {
                    element.classList.remove('d-none');
                } else {
                    element.classList.add('d-none');
                }
            });
        });
    }

    /**
     * Tooltips initialisieren
     */
    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip();
            });
        });
    }

    /**
     * Tooltip anzeigen
     */
    showTooltip(element) {
        const tooltipText = element.getAttribute('data-tooltip');
        if (!tooltipText) return;

        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = tooltipText;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        setTimeout(() => tooltip.classList.add('show'), 10);
    }

    /**
     * Tooltip verstecken
     */
    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    /**
     * Datums-Formatierung
     */
    formatDates() {
        const dateElements = document.querySelectorAll('[data-date]');
        
        dateElements.forEach(element => {
            const dateString = element.getAttribute('data-date');
            if (dateString) {
                const date = new Date(dateString);
                element.textContent = this.formatDate(date);
            }
        });
    }

    /**
     * Datum im deutschen Format formatieren
     */
    formatDate(date) {
        return date.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    /**
     * Formular-Submission behandeln
     */
    handleFormSubmit(e) {
        const form = e.target;
        const formId = form.id || form.className;
        
        // Pflanzen-Formular nicht behandeln - es wird klassisch abgesendet
        if (formId === 'plant-form') {
            return true; // Lass das Formular normal ablaufen
        }
        
        // Loading-State für Button
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            this.setButtonLoading(submitBtn, true);
        }
        
        // Formular-spezifische Behandlung
        switch (formId) {
            case 'entry-form':
                this.handleEntryForm(form);
                break;
            case 'config-form':
                this.handleConfigForm(form);
                break;
            default:
                // Standard-Formular-Behandlung
                this.handleGenericForm(form);
        }
    }

    /**
     * Pflanzen-Formular behandeln
     */
    async handlePlantForm(form) {
        try {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch('/api/plants', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                this.showAlert('Pflanze erfolgreich erstellt!', 'success');
                form.reset();
                // Optional: Seite neu laden oder UI aktualisieren
                setTimeout(() => window.location.reload(), 1000);
            } else {
                throw new Error('Fehler beim Erstellen der Pflanze');
            }
        } catch (error) {
            this.showAlert('Fehler beim Erstellen der Pflanze: ' + error.message, 'error');
        } finally {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                this.setButtonLoading(submitBtn, false);
            }
        }
    }

    /**
     * Eintrags-Formular behandeln
     */
    async handleEntryForm(form) {
        try {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch('/api/entries', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                this.showAlert('Eintrag erfolgreich erstellt!', 'success');
                form.reset();
                // Modal schließen falls vorhanden
                const modal = form.closest('.modal');
                if (modal) {
                    this.closeModal(modal);
                }
            } else {
                throw new Error('Fehler beim Erstellen des Eintrags');
            }
        } catch (error) {
            this.showAlert('Fehler beim Erstellen des Eintrags: ' + error.message, 'error');
        } finally {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                this.setButtonLoading(submitBtn, false);
            }
        }
    }

    /**
     * Konfigurations-Formular behandeln
     */
    async handleConfigForm(form) {
        try {
            const formData = new FormData(form);
            const features = {};
            
            // Feature-Flags sammeln
            formData.forEach((value, key) => {
                if (key.startsWith('feature_')) {
                    const featureName = key.replace('feature_', '');
                    features[featureName] = value === 'on';
                }
            });
            
            // Konfiguration aktualisieren
            this.config.features = { ...this.config.features, ...features };
            this.saveConfig();
            
            // Feature-Flags anwenden
            this.applyFeatureFlags();
            
            this.showAlert('Konfiguration erfolgreich gespeichert!', 'success');
        } catch (error) {
            this.showAlert('Fehler beim Speichern der Konfiguration: ' + error.message, 'error');
        } finally {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                this.setButtonLoading(submitBtn, false);
            }
        }
    }

    /**
     * Generisches Formular behandeln
     */
    async handleGenericForm(form) {
        try {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch(form.action, {
                method: form.method || 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                this.showAlert('Formular erfolgreich gesendet!', 'success');
                form.reset();
            } else {
                throw new Error('Fehler beim Senden des Formulars');
            }
        } catch (error) {
            this.showAlert('Fehler beim Senden des Formulars: ' + error.message, 'error');
        } finally {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                this.setButtonLoading(submitBtn, false);
            }
        }
    }

    /**
     * Alert anzeigen
     */
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <span>${message}</span>
            <button type="button" class="alert-close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        alertContainer.appendChild(alert);
        
        // Auto-remove nach 5 Sekunden
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    /**
     * Alert-Container erstellen falls nicht vorhanden
     */
    createAlertContainer() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'alert-container';
        document.body.insertBefore(container, document.body.firstChild);
        return container;
    }

    /**
     * Modal öffnen
     */
    openModal(modalId) {
        if (this.modules.modalManager) {
            this.modules.modalManager.open(modalId);
        } else {
            // Fallback für direkte Modal-Öffnung
            const modal = document.getElementById(modalId);
            if (modal) {
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();
            } else {
                console.error(`Modal mit ID '${modalId}' nicht gefunden`);
            }
        }
    }

    /**
     * Modal schließen
     */
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    /**
     * Alle Modals schließen
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => this.closeModal(modal));
    }

    /**
     * Button Loading-State setzen
     */
    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.dataset.originalText = button.textContent;
            button.innerHTML = '<span class="spinner"></span> Wird verarbeitet...';
        } else {
            button.disabled = false;
            if (button.dataset.originalText) {
                button.textContent = button.dataset.originalText;
                delete button.dataset.originalText;
            }
        }
    }

    /**
     * Loading-States entfernen
     */
    removeLoadingStates() {
        const loadingElements = document.querySelectorAll('.loading');
        loadingElements.forEach(element => {
            element.classList.remove('loading');
        });
    }

    /**
     * Alerts initialisieren
     */
    initAlerts() {
        // Auto-remove für bestehende Alerts (außer Countdown-Alerts)
        const alerts = document.querySelectorAll('.alert:not(.countdown-alert)');
        alerts.forEach(alert => {
            // Prüfe auch auf data-no-auto-remove Attribut
            if (!alert.hasAttribute('data-no-auto-remove')) {
                setTimeout(() => {
                    if (alert.parentElement) {
                        alert.remove();
                    }
                }, 5000);
            }
        });
    }

    /**
     * Loading-Buttons initialisieren
     */
    initLoadingButtons() {
        const loadingButtons = document.querySelectorAll('.btn[data-loading]');
        loadingButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.setButtonLoading(button, true);
            });
        });
    }

    /**
     * Utility: Debounce-Funktion
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Utility: Throttle-Funktion
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Globale Instanz erstellen
window.growDiaryApp = new GrowDiaryApp();

// Utility-Funktionen global verfügbar machen
window.showAlert = (message, type) => window.growDiaryApp.showAlert(message, type);
window.openModal = (modalId) => window.growDiaryApp.openModal(modalId);
window.closeModal = (modal) => window.growDiaryApp.closeModal(modal); 