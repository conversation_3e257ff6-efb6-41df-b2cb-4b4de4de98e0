/**
 * Lighting Widget Module
 * Handles lighting plan functionality
 */

class LightingWidget {
    constructor(containerId = 'lighting-plan-box', options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        // Stelle sicher, dass currentPhase ein String ist
        let phase = options.currentPhase || window.currentPhaseKey || 'vegetative_early';
        if (typeof phase === 'object' && phase !== null) {
            phase = 'flowering_late'; // Fallback wenn es ein Objekt ist
        }
        this.currentPhase = phase;
        this.plantId = options.plantId || null; // Plant ID für Speicherung
        this.lampPower = 240;
        this.lampDistance = 25;
        this.lightHours = 18;
        this.measuredPPFD = null;
        // Neue Eigenschaften für erweiterte Lampenkonfiguration
        this.colorTemperature = 3500; // Kelvin (z.B. 3500K)
        this.additionalWavelengths = ''; // z.B. "660nm HyperRed"
        this.powerPercentage = 100; // Leistung in Prozent (1-100)
        this.initialized = false;
        
        if (this.container) {
            this.init();
        } else {
            console.warn(`Lighting Widget: Container ${containerId} nicht gefunden`);
        }
    }

    init() {
        this.loadSavedSettings().then(() => {
            this.loadLightingPlan();
            this.setupEventListeners();
        });
    }

    // Hilfsfunktion für benutzerfreundliche Phase-Namen
    getFriendlyPhaseName(phaseKey) {
        const phaseNames = {
            'germination': 'Keimung',
            'vegetative_early': 'Frühe Wachstumsphase',
            'vegetative_middle': 'Mittlere Wachstumsphase',
            'vegetative_late': 'Späte Wachstumsphase',
            'flowering_early': 'Frühe Blüte',
            'flowering_middle': 'Mittlere Blüte',
            'flowering_late': 'Späte Blüte',
            'flush': 'Flush'
        };
        return phaseNames[phaseKey] || phaseKey.replace('_', ' ');
    }

    // Event-Listener für Widget-Interaktionen
    setupEventListeners() {
        // Event-Delegation für dynamisch erstellte Elemente
        this.container.addEventListener('click', (e) => {
            if (e.target.id === 'ppfd-calc-btn') {
                this.calculatePPFD();
            }
            if (e.target.id === 'save-settings-btn') {
                this.saveSettings().then(success => {
                    if (success) {
                        this.showSaveMessage('Einstellungen gespeichert!', 'success');
                    } else {
                        this.showSaveMessage('Fehler beim Speichern!', 'error');
                    }
                });
            }
        });

        // Input-Event-Listener für Live-Updates und Auto-Save
        this.container.addEventListener('input', (e) => {
            if (e.target.id === 'lamp-power-input' || 
                e.target.id === 'lamp-distance-input' || 
                e.target.id === 'light-hours-input' ||
                e.target.id === 'color-temp-input' ||
                e.target.id === 'wavelengths-input' ||
                e.target.id === 'power-percent-input') {
                this.updateInputValues();
                // Auto-Save nach kurzer Verzögerung
                this.autoSaveSettings();
            }
        });

        // Event-Listener für Strain-Type-Änderungen
        document.addEventListener('strainTypeChanged', (e) => {
            this.loadLightingPlan();
        });
    }

    // Auto-Save mit Debouncing
    autoSaveSettings() {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }
        this.saveTimeout = setTimeout(() => {
            this.saveSettings();
        }, 2000); // 2 Sekunden Verzögerung
    }

    // Aktuelle Input-Werte aktualisieren
    updateInputValues() {
        const lampPowerInput = document.getElementById('lamp-power-input');
        const lampDistanceInput = document.getElementById('lamp-distance-input');
        const lightHoursInput = document.getElementById('light-hours-input');
        const ppfdInput = document.getElementById('ppfd-input');
        const colorTempInput = document.getElementById('color-temp-input');
        const wavelengthsInput = document.getElementById('wavelengths-input');
        const powerPercentInput = document.getElementById('power-percent-input');
        
        if (lampPowerInput) this.lampPower = parseInt(lampPowerInput.value) || 240;
        if (lampDistanceInput) this.lampDistance = parseInt(lampDistanceInput.value) || 25;
        if (lightHoursInput) this.lightHours = parseInt(lightHoursInput.value) || 18;
        if (ppfdInput) this.measuredPPFD = parseFloat(ppfdInput.value) || null;
        if (colorTempInput) this.colorTemperature = parseInt(colorTempInput.value) || 3500;
        if (wavelengthsInput) this.additionalWavelengths = wavelengthsInput.value || '';
        if (powerPercentInput) this.powerPercentage = parseInt(powerPercentInput.value) || 100;
    }

    // PPFD-Berechnung auslösen
    async calculatePPFD() {
        this.updateInputValues();
        await this.loadLightingPlan();
    }

    // Hauptfunktion: Beleuchtungsplan laden
    async loadLightingPlan() {
        if (!this.container) return;

        // Fallback aus DOM für Lampenleistung (nur beim ersten Laden)
        if (!this.initialized) {
            this.loadPlantDataFromDOM();
        }
        
        // Aktuelle Werte aus Inputs holen (überschreibt DOM-Werte)
        this.updateInputValues();

        // Photoperiode basierend auf Phase anpassen - NUR beim ersten Laden
        // UND nur für photoperiodische Pflanzen (nicht für Autoflowers)
        if (!this.initialized) {
            let isAutoflower = false;
            if (this.plantId) {
                try {
                    const plantResponse = await fetch(`/api/plants/${this.plantId}`);
                    if (plantResponse.ok) {
                        const plantData = await plantResponse.json();
                        const strainType = plantData.strain_type || 'photoperiodic';
                        isAutoflower = strainType.toLowerCase().includes('auto');
                    }
                } catch (error) {
                    console.warn('Konnte strain_type nicht prüfen:', error);
                }
            }
            // Nur für photoperiodische Pflanzen auf 12/12 umstellen
            if (!isAutoflower && (this.currentPhase.includes('flowering') || this.currentPhase === 'flush')) {
                this.lightHours = 12; // 12/12 für Blüte
            }
        }
        this.initialized = true;

        this.container.innerHTML = '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Lade Beleuchtungsplan...</div>';

        try {
            // Strain-Type direkt aus dem Template holen
            let strainType = 'photoperiodic'; // Standard
            
            // Versuche zuerst aus dem Template zu holen
            const strainTypeElement = document.querySelector('[data-plant-strain-type]');
            if (strainTypeElement) {
                strainType = strainTypeElement.getAttribute('data-plant-strain-type');
            } else {
                // Fallback: Suche nach dem Strain-Type-Widget
                const strainTypeWidget = document.querySelector('#strain-type-widget');
                if (strainTypeWidget) {
                    const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                    if (strainTypeValue) {
                        const strainTypeText = strainTypeValue.textContent.trim();
                        if (strainTypeText.toLowerCase().includes('auto')) {
                            strainType = 'autoflowering';
                        } else {
                            strainType = 'photoperiodic';
                        }
                    }
                }
            }
            
            let url = `/api/lighting/plan/${encodeURIComponent(this.currentPhase)}?strain_type=${encodeURIComponent(strainType)}&plant_id=${encodeURIComponent(this.plantId || '')}&lamp_power_w=${encodeURIComponent(this.lampPower)}&lamp_distance_cm=${encodeURIComponent(this.lampDistance)}&light_hours=${encodeURIComponent(this.lightHours)}&color_temperature_k=${encodeURIComponent(this.colorTemperature)}&power_percentage=${encodeURIComponent(this.powerPercentage)}`;
            
            // PPFD-Wert hinzufügen wenn vorhanden
            if (this.measuredPPFD) {
                url += `&ppfd_measured=${encodeURIComponent(this.measuredPPFD)}`;
            }
            
            // Zusatz-Wellenlängen hinzufügen wenn vorhanden
            if (this.additionalWavelengths) {
                url += `&additional_wavelengths=${encodeURIComponent(this.additionalWavelengths)}`;
            }
            
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error('Beleuchtungsplan konnte nicht geladen werden');
            }
            
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            
            this.renderLightingWidget(data);
            
        } catch (err) {
            this.container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    Fehler beim Laden des Beleuchtungsplans: ${err.message}
                </div>
            `;
        }
    }

    // Pflanzen-Daten aus DOM laden
    loadPlantDataFromDOM() {
        const lightingElement = document.querySelector('[data-plant-lighting]');
        if (lightingElement) {
            const lightingText = lightingElement.textContent.trim();
            if (lightingText && lightingText !== '-') {
                // Extrahiere Watt-Zahl aus "DIY-240W-KIT" oder ähnlich
                const wattMatch = lightingText.match(/(\d+)W/);
                if (wattMatch) {
                    this.lampPower = parseInt(wattMatch[1]);
                }
            }
        }
    }

    // Gespeicherte Einstellungen laden
    async loadSavedSettings() {
        if (!this.plantId) {
    
            return;
        }
        
        try {
            const response = await fetch(`/api/lighting/load-settings/${this.plantId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.settings) {
                    // Gespeicherte Einstellungen anwenden
                    this.lampPower = data.settings.lamp_power_w || this.lampPower;
                    this.lampDistance = data.settings.lamp_distance_cm || this.lampDistance;
                    this.lightHours = data.settings.light_hours || this.lightHours;
                    this.colorTemperature = data.settings.color_temperature_k || this.colorTemperature;
                    this.powerPercentage = data.settings.power_percentage || this.powerPercentage;
                    this.additionalWavelengths = data.settings.additional_wavelengths || this.additionalWavelengths;
                    this.measuredPPFD = data.settings.ppfd_measured || this.measuredPPFD;
                    
        
                }
            }
        } catch (error) {
            console.warn('Lighting Widget: Fehler beim Laden der gespeicherten Einstellungen:', error);
        }
    }

    // Einstellungen speichern
    async saveSettings() {
        if (!this.plantId) {
            console.warn('Lighting Widget: Keine Plant ID verfügbar, kann nicht speichern');
            return false;
        }
        
        try {
            const settings = {
                lamp_power_w: this.lampPower,
                lamp_distance_cm: this.lampDistance,
                light_hours: this.lightHours,
                color_temperature_k: this.colorTemperature,
                power_percentage: this.powerPercentage,
                additional_wavelengths: this.additionalWavelengths,
                ppfd_measured: this.measuredPPFD
            };
            
            const response = await fetch(`/api/lighting/save-settings/${this.plantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
        
                    return true;
                }
            }
        } catch (error) {
            console.error('Lighting Widget: Fehler beim Speichern der Einstellungen:', error);
        }
        
        return false;
    }

    // Beleuchtungs-Widget rendern
    renderLightingWidget(data) {
        const rec = data.recommendations;
        const phaseName = this.getFriendlyPhaseName(data.phase);
        // Guidelines holen
        const guidelines = LightingWidget.lightingGuidelines;
        let phaseGuideline = null;
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflower';
                    }
                }
            }
        }
        if (guidelines && guidelines.phases) {
            phaseGuideline = guidelines.phases.find(p => p.phase === data.phase);
        }
        // HTML für Guidelines-Box
        let guidelineBox = '';
        let dliWarnBox = '';
        let dliValue = null;
        let dliWarn = false;
        let ppfdWarn = false;
        let ppfdValue = null;
        let ppfdMin = null, ppfdMax = null, dliMin = null, dliMax = null;
        if (phaseGuideline) {
            const ppfd = phaseGuideline.ppfd[strainType] || {min: '?', max: '?'};
            const dli = phaseGuideline.dli[strainType] || {min: '?', max: '?'};
            const lightHours = phaseGuideline.lightHours[strainType] || '?';
            // Berechne DLI aus aktuellem PPFD (gemessen oder berechnet) und Stunden
            ppfdValue = (data.current && data.current.ppfd_measured) ? data.current.ppfd_measured : (data.current && data.current.ppfd_calculated) ? data.current.ppfd_calculated : null;
            if (ppfdValue && lightHours && !isNaN(ppfdValue) && !isNaN(lightHours)) {
                dliValue = (ppfdValue * lightHours * 0.0036).toFixed(1);
            }
            // Grenzwerte für Warnungen
            ppfdMin = ppfd.min; ppfdMax = ppfd.max; dliMin = dli.min; dliMax = dli.max;
            if (ppfdValue && ppfdMin && ppfdMax && !isNaN(ppfdMin) && !isNaN(ppfdMax)) {
                if (ppfdValue < ppfdMin || ppfdValue > ppfdMax) ppfdWarn = true;
            }
            if (dliValue && dliMin && dliMax && !isNaN(dliMin) && !isNaN(dliMax)) {
                if (dliValue < dliMin || dliValue > dliMax) dliWarn = true;
            }
            // Warnbox
            if (ppfdWarn || dliWarn) {
                dliWarnBox = `<div class="alert alert-danger mt-2"><i class="fa fa-exclamation-triangle me-2"></i><b>Achtung:</b> ${ppfdWarn ? `PPFD (${ppfdValue}) außerhalb des empfohlenen Bereichs (${ppfdMin}-${ppfdMax})!<br>` : ''}${dliWarn ? `DLI (${dliValue}) außerhalb des empfohlenen Bereichs (${dliMin}-${dliMax})!` : ''}</div>`;
            }
            guidelineBox = `
                <div class="lighting-section-title lighting-guidelines-phase">
                    <i class="fa-solid fa-lightbulb me-2"></i>Phasen-/Sortentyp-Empfehlung
                    <span class="badge bg-info ms-2">${this.getFriendlyPhaseName(data.phase)}</span>
                </div>
                <div class="lighting-section-content lighting-guidelines-phase-content">
                    <div><span class="lighting-label">PPFD:</span> <b>${ppfd.min}-${ppfd.max} µmol/m²/s</b></div>
                    <div><span class="lighting-label">DLI:</span> <b>${dli.min}-${dli.max} mol/m²/Tag</b></div>
                    <div><span class="lighting-label">Beleuchtungsstunden:</span> <b>${lightHours} h</b></div>
                    <div class="lighting-phase-notes">
                        <strong>Hinweise:</strong>
                        <ul class="lighting-phase-notes-list">
                            ${(phaseGuideline.notes||[]).map(note => `<li>${note}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
        const html = `
            <div class="lighting-widget-card">
                <div class="lighting-widget-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fa-solid fa-lightbulb"></i>
                        Beleuchtungsplan <span class="lighting-phase-name">${phaseName}</span>
                    </div>
                    <div class="lighting-widget-actions">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-info me-2" 
                            data-bs-toggle="modal" 
                            data-bs-target="#lighting-guidelines-modal"
                            title="Beleuchtungsrichtlinien und PPFD/DLI-Empfehlungen anzeigen"
                        >
                            <i class="fas fa-book"></i> Richtlinien
                        </button>
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-light" 
                            data-bs-toggle="tooltip" 
                            data-bs-placement="left"
                            title="Optimale Beleuchtungsstrategie basierend auf Wachstumsphase und Sortentyp. Berechnet PPFD, Photoperiode und Lampenabstand für maximales Wachstum und Blütenbildung."
                        >
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                </div>
                <div class="lighting-widget-body">
                    ${guidelineBox}
                    ${dliWarnBox}
                    ${dliValue ? `<div class='lighting-dli-calculated'><b>Berechneter DLI:</b> ${dliValue} mol/m²/Tag</div>` : ''}
                    ${this.renderRecommendedLighting(rec)}
                    ${this.renderLightingNotes(rec)}
                    ${this.renderLightingSettings()}
                    ${this.renderPPFDMeasurement()}
                    ${this.renderCurrentValues(data)}
                    ${this.renderGuidelinesPreview()}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        
        // Tooltips nach dem Rendern initialisieren
        this.initTooltips();
        
        // Guidelines-Modal erstellen falls nicht vorhanden
        this.createGuidelinesModal();
    }
    
    // Tooltips für dynamisch erstellte Elemente initialisieren
    initTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(this.container.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        } else {
            // Fallback: Warten und erneut versuchen
            setTimeout(() => this.initTooltips(), 500);
        }
    }

    // Empfohlene Beleuchtung rendern
    renderRecommendedLighting(rec) {
        // Strain-Type aus dem Template oder Widget holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        // Strain-spezifische Hinweise
        let strainSpecificNote = '';
        if (rec.strain_specific) {
            strainSpecificNote = `
                <div class="lighting-strain-specific">
                    <i class="fa fa-leaf me-2"></i>
                    <strong>Sortentyp-spezifische Empfehlungen:</strong> ${strainTypeDisplay}
                    ${isAutoflower ? 
                        '<br><small class="text-muted">Autoflowers benötigen niedrigere PPFD-Werte für optimales Wachstum ohne Stress.</small>' : 
                        '<br><small class="text-muted">Photoperiodische Sorten können höhere PPFD-Werte vertragen.</small>'
                    }
                </div>
            `;
        }
        
        // Beleuchtungsstunden sortentyp-spezifisch formatieren
        let lightHoursDisplay = `${rec.light_hours} Stunden`;
        if (isAutoflower) {
            lightHoursDisplay = `${rec.light_hours} Stunden (konstant)`;
        } else {
            // Für photoperiodische Sorten: Phasenabhängige Anzeige
            if (this.currentPhase.includes('vegetative') || this.currentPhase === 'germination') {
                lightHoursDisplay = `${rec.light_hours} Stunden (18/6 - vegetative Phase)`;
            } else if (this.currentPhase.includes('flowering') || this.currentPhase === 'flush') {
                lightHoursDisplay = `${rec.light_hours} Stunden (12/12 - Blütephase)`;
            }
        }
        
        return `
            <div class="lighting-section-title lighting-recommended">
                <i class="fa-solid fa-check-circle me-2"></i>Empfohlene Beleuchtung
                ${rec.strain_specific ? '<span class="badge bg-info ms-2">Sortentyp-spezifisch</span>' : ''}
                <span class="badge bg-success ms-2">Empfehlung für ${strainTypeDisplay}</span>
            </div>
            <div class="lighting-section-content lighting-recommended-content">
                ${strainSpecificNote}
                <div class="lighting-recommendation-type">
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>Empfehlungstyp:</strong> 
                    ${rec.strain_specific ? 
                        `<span class="text-success">Sortentyp-spezifisch (${strainTypeDisplay})</span>` : 
                        `<span class="text-warning">Allgemeine Empfehlung</span>`
                    }
                </div>
                <div><span class="lighting-label">PPFD-Ziel:</span> <span class="lighting-amount">${rec.ppfd_target} μmol/m²/s</span></div>
                <div><span class="lighting-label">PPFD-Bereich:</span> <span class="lighting-range">${rec.ppfd_range} μmol/m²/s</span></div>
                <div><span class="lighting-label">Beleuchtungsstunden:</span> <span class="lighting-hours">${lightHoursDisplay}</span></div>
                <div><span class="lighting-label">Intensität:</span> <span class="intensity-badge">${rec.intensity}</span></div>
                <div><span class="lighting-label">Lampenabstand:</span> <span class="lighting-distance">${rec.distance}</span></div>
                
                <div class="lighting-recommendations-explanation">
                    <h6><i class="fa fa-info-circle me-2"></i>Erklärung der Empfehlungen:</h6>
                    <div class="lighting-explanation-grid">
                        <div class="lighting-explanation-item">
                            <strong>PPFD-Ziel:</strong> <span>Optimaler Wert für diese Phase und Sortentyp</span>
                        </div>
                        <div class="lighting-explanation-item">
                            <strong>PPFD-Bereich:</strong> <span>Akzeptabler Bereich (Minimum-Maximum) für ${strainTypeDisplay.toLowerCase()}</span>
                        </div>
                        <div class="lighting-explanation-item">
                            <strong>Beleuchtungsstunden:</strong> <span>Empfohlene tägliche Beleuchtungsdauer (${isAutoflower ? 'konstant' : 'phasenabhängig'})</span>
                        </div>
                        <div class="lighting-explanation-item">
                            <strong>Intensität:</strong> <span>Lichtstärke-Empfehlung (low/medium/high)</span>
                        </div>
                        <div class="lighting-explanation-item">
                            <strong>Lampenabstand:</strong> <span>Optimaler Abstand zwischen Lampe und Pflanzen</span>
                        </div>
                    </div>
                    <div class="lighting-phase-tips">
                        <strong>Phase-spezifische Tipps für ${strainTypeDisplay}:</strong>
                        <ul>
                            ${this.getPhaseSpecificTips(isAutoflower)}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    // Phase-spezifische Tipps generieren
    getPhaseSpecificTips(isAutoflower = false) {
        const tips = {
            'germination': {
                photoperiodic: [
                    'Sehr sanftes Licht verwenden',
                    'Direkte Sonneneinstrahlung vermeiden',
                    'Lichtabstand: 60-80cm'
                ],
                autoflower: [
                    'Sehr sanftes Licht verwenden',
                    'Direkte Sonneneinstrahlung vermeiden',
                    'Lichtabstand: 60-80cm',
                    'Autoflowers: Konstante 18/6 Beleuchtung beibehalten'
                ]
            },
            'vegetative_early': {
                photoperiodic: [
                    'Langsam Intensität erhöhen',
                    'Lichtabstand: 40-60cm',
                    '18/6 Beleuchtung beibehalten'
                ],
                autoflower: [
                    'Langsam Intensität erhöhen (etwas vorsichtiger)',
                    'Lichtabstand: 40-60cm',
                    'Konstante 18/6 Beleuchtung beibehalten',
                    'Autoflowers: Niedrigere PPFD für sanfteres Wachstum'
                ]
            },
            'vegetative_middle': {
                photoperiodic: [
                    'Volle Intensität für optimales Wachstum',
                    'Lichtabstand: 30-50cm',
                    'Temperatur kontrollieren'
                ],
                autoflower: [
                    'Moderate Intensität für gleichmäßiges Wachstum',
                    'Lichtabstand: 30-50cm',
                    'Temperatur kontrollieren',
                    'Autoflowers: Reduzierte PPFD verhindert Stress'
                ]
            },
            'vegetative_late': {
                photoperiodic: [
                    'Pflanzen für Blüte vorbereiten',
                    'Lichtabstand: 30-50cm',
                    'Nächste Phase planen'
                ],
                autoflower: [
                    'Pflanzen für Blüte vorbereiten',
                    'Lichtabstand: 30-50cm',
                    'Autoflowers: Blüte beginnt automatisch',
                    'Konstante Beleuchtung beibehalten'
                ]
            },
            'flowering_early': {
                photoperiodic: [
                    'Lichtzyklus auf 12/12 umstellen',
                    'Lichtabstand: 30-50cm',
                    'Blütenbildung fördern'
                ],
                autoflower: [
                    'Konstante 18/6 Beleuchtung beibehalten',
                    'Lichtabstand: 30-50cm',
                    'Blütenbildung beginnt automatisch',
                    'Autoflowers: Sanftere PPFD für Blüteeinleitung'
                ]
            },
            'flowering_middle': {
                photoperiodic: [
                    'Optimale Beleuchtung für Blütenentwicklung',
                    'Lichtabstand: 30-50cm',
                    'Temperatur und Luftfeuchte kontrollieren'
                ],
                autoflower: [
                    'Optimale Beleuchtung für Blütenentwicklung',
                    'Lichtabstand: 30-50cm',
                    'Temperatur und Luftfeuchte kontrollieren',
                    'Autoflowers: Etwas reduzierte PPFD für optimale Entwicklung'
                ]
            },
            'flowering_late': {
                photoperiodic: [
                    'Langsam Intensität reduzieren',
                    'Lichtabstand: 40-60cm',
                    'Reifung fördern'
                ],
                autoflower: [
                    'Langsam Intensität reduzieren',
                    'Lichtabstand: 40-60cm',
                    'Reifung fördern',
                    'Autoflowers: Reduzierte PPFD fördert sanfte Reifung'
                ]
            },
            'flush': {
                photoperiodic: [
                    'Minimale Beleuchtung',
                    'Lichtabstand: 50-70cm',
                    'Finale Reifung'
                ],
                autoflower: [
                    'Minimale Beleuchtung',
                    'Lichtabstand: 50-70cm',
                    'Finale Reifung',
                    'Autoflowers: Konstante Beleuchtung beibehalten'
                ]
            }
        };
        
        const strainType = isAutoflower ? 'autoflower' : 'photoperiodic';
        const phaseTips = tips[this.currentPhase]?.[strainType] || 
                         tips[this.currentPhase]?.['photoperiodic'] || 
                         ['Standard-Beleuchtung verwenden'];
        
        return phaseTips.map(tip => `<li>${tip}</li>`).join('');
    }

    // Beleuchtungseinstellungen rendern
    renderLightingSettings() {
        return `
            <div class="lighting-section-title lighting-current">
                <i class="fa-solid fa-cog me-2"></i>Beleuchtungseinstellungen
            </div>
            <div class="lighting-section-content lighting-current-content">
                <div class="lighting-input-grid">
                    <div class="lighting-input-group">
                        <label for="lamp-power-input">Lampenleistung (W)</label>
                        <input type="number" min="1" step="1" 
                               id="lamp-power-input" value="${this.lampPower}" 
                               class="lighting-input">
                    </div>
                    <div class="lighting-input-group">
                        <label for="lamp-distance-input">Lampenabstand (cm)</label>
                        <input type="number" min="5" step="1" 
                               id="lamp-distance-input" value="${this.lampDistance}" 
                               class="lighting-input">
                    </div>
                </div>
                <div class="lighting-input-grid">
                    <div class="lighting-input-group">
                        <label for="color-temp-input">Farbtemperatur (K)</label>
                        <input type="number" min="2000" max="10000" step="100" 
                               id="color-temp-input" value="${this.colorTemperature}" 
                               class="lighting-input" placeholder="z.B. 3500">
                        <small class="lighting-input-hint">
                            <i class="fa fa-info-circle"></i> z.B. 3500K für warmes Licht
                        </small>
                    </div>
                    <div class="lighting-input-group">
                        <label for="power-percent-input">Leistung (%)</label>
                        <input type="number" min="1" max="100" step="1" 
                               id="power-percent-input" value="${this.powerPercentage}" 
                               class="lighting-input" placeholder="100">
                        <small class="lighting-input-hint">
                            <i class="fa fa-info-circle"></i> Aktuelle Dimmereinstellung
                        </small>
                    </div>
                </div>
                <div class="lighting-input-group">
                    <label for="wavelengths-input">Zusatz-Wellenlängen (optional)</label>
                    <input type="text" 
                           id="wavelengths-input" value="${this.additionalWavelengths}" 
                           class="lighting-input" placeholder="z.B. 660nm HyperRed, 730nm FarRed">
                    <small class="lighting-input-hint">
                        <i class="fa fa-info-circle"></i> Spezielle LEDs wie HyperRed, FarRed etc.
                    </small>
                </div>
                <div class="lighting-input-group">
                    <label for="light-hours-input">Beleuchtungsstunden</label>
                    <input type="number" min="1" max="24" step="1" 
                           id="light-hours-input" value="${this.lightHours}" 
                           class="lighting-input">
                </div>
                <div class="lighting-save-section">
                    <button id="save-settings-btn" class="btn btn-sm btn-primary">
                        <i class="fa fa-save"></i> Einstellungen speichern
                    </button>
                    <small class="lighting-input-hint">
                        <i class="fa fa-info-circle"></i> Einstellungen werden auch automatisch gespeichert
                    </small>
                </div>
            </div>
        `;
    }

    // PPFD-Messung rendern
    renderPPFDMeasurement() {
        return `
            <div class="lighting-section-title lighting-measurement">
                <i class="fa-solid fa-ruler me-2"></i>Gemessener PPFD
            </div>
            <div class="lighting-section-content lighting-measurement-content">
                <div class="lighting-ppfd-input-group">
                    <input type="number" min="0" step="1" 
                           id="ppfd-input" placeholder="PPFD in μmol/m²/s" 
                           value="${this.measuredPPFD || ''}" 
                           class="lighting-ppfd-input">
                    <button id="ppfd-calc-btn" class="btn btn-sm btn-warning">
                        <i class="fa fa-calculator"></i> Berechnen
                    </button>
                    <small class="lighting-input-hint">
                        <i class="fa fa-info-circle"></i> (optional, PAR-Meter)
                    </small>
                </div>
            </div>
        `;
    }

    // Aktuelle Werte rendern
    renderCurrentValues(data) {
        if (!data.current) return '';
        
        const current = data.current;
        const recommendations = data.recommendations;
        
        // Umfassende Status-Bewertung
        let issues = [];
        let statusClass = 'optimal';
        let statusMessage = 'Perfekte Lichtbedingungen!';
        let statusRecommendation = 'Alles im grünen Bereich – weiter so!';
        let icon = 'check-circle';
        
        // PPFD-Status berechnen (priorisiere berechnete PPFD)
        const ppfdRange = recommendations.ppfd_range;
        const calculatedPPFD = current.ppfd_calculated;
        const measuredPPFD = current.ppfd_measured;
        
        if (calculatedPPFD) {
            const rangeMatch = ppfdRange.match(/(\d+)-(\d+)/);
            if (rangeMatch) {
                const minPPFD = parseInt(rangeMatch[1]);
                const maxPPFD = parseInt(rangeMatch[2]);
                
                if (calculatedPPFD < minPPFD) {
                    issues.push(`PPFD zu niedrig (${calculatedPPFD} < ${minPPFD})`);
                    statusClass = 'low';
                    statusMessage = 'PPFD zu niedrig';
                    statusRecommendation = 'Lampenleistung erhöhen oder Abstand reduzieren';
                    icon = 'exclamation-triangle';
                } else if (calculatedPPFD > maxPPFD) {
                    issues.push(`PPFD zu hoch (${calculatedPPFD} > ${maxPPFD})`);
                    statusClass = 'high';
                    statusMessage = 'PPFD zu hoch';
                    statusRecommendation = 'Lampenleistung reduzieren oder Abstand erhöhen';
                    icon = 'exclamation-triangle';
                }
            }
        }
        
        // Beleuchtungsstunden prüfen
        const recommendedHours = recommendations.light_hours;
        if (current.light_hours !== recommendedHours) {
            issues.push(`Beleuchtungsstunden: ${current.light_hours}h statt empfohlenen ${recommendedHours}h`);
            if (statusClass === 'optimal') {
                statusClass = 'warning';
                statusMessage = 'Abweichung bei Beleuchtungsstunden';
                statusRecommendation = `Empfohlen: ${recommendedHours} Stunden`;
                icon = 'exclamation-circle';
            }
        }
        
        // Lampenabstand prüfen
        const distanceRange = recommendations.distance;
        if (distanceRange) {
            const distanceMatch = distanceRange.match(/(\d+)-(\d+)/);
            if (distanceMatch) {
                const minDistance = parseInt(distanceMatch[1]);
                const maxDistance = parseInt(distanceMatch[2]);
                const currentDistance = current.lamp_distance_cm;
                
                if (currentDistance < minDistance || currentDistance > maxDistance) {
                    issues.push(`Lampenabstand: ${currentDistance}cm außerhalb des empfohlenen Bereichs (${minDistance}-${maxDistance}cm)`);
                    if (statusClass === 'optimal') {
                        statusClass = 'warning';
                        statusMessage = 'Lampenabstand nicht optimal';
                        statusRecommendation = `Empfohlen: ${minDistance}-${maxDistance}cm`;
                        icon = 'exclamation-circle';
                    }
                }
            }
        }
        
        // Status-Nachricht anpassen wenn mehrere Probleme
        if (issues.length > 1) {
            statusMessage = `${issues.length} Abweichungen gefunden`;
            statusRecommendation = `<ul class="lighting-issues-list">${issues.map(issue => `<li>${issue}</li>`).join('')}</ul>`;
        } else if (issues.length === 1) {
            statusRecommendation = issues[0];
        }
        
        return `
            <div class="lighting-section-title lighting-current-values">
                <i class="fa-solid fa-chart-line me-2"></i>Aktuelle Werte
            </div>
            <div class="lighting-section-content lighting-current-values-content">
                <div><span class="lighting-label">Berechnete PPFD:</span> <span class="lighting-amount">${current.ppfd_calculated} μmol/m²/s</span></div>
                ${current.ppfd_measured ? `<div><span class="lighting-label">Gemessene PPFD:</span> <span class="lighting-amount">${current.ppfd_measured} μmol/m²/s</span></div>` : ''}
                <div><span class="lighting-label">Lampenleistung:</span> <span class="lighting-value">${current.lamp_power_w}W</span></div>
                <div><span class="lighting-label">Lampenabstand:</span> <span class="lighting-value">${current.lamp_distance_cm}cm</span></div>
                <div><span class="lighting-label">Beleuchtungsstunden:</span> <span class="lighting-value">${current.light_hours}h</span></div>
                ${current.color_temperature_k ? `<div><span class="lighting-label">Farbtemperatur:</span> <span class="lighting-value">${current.color_temperature_k}K</span></div>` : ''}
                ${current.power_percentage ? `<div><span class="lighting-label">Leistung:</span> <span class="lighting-value">${current.power_percentage}%</span></div>` : ''}
                ${current.additional_wavelengths ? `<div><span class="lighting-label">Zusatz-LEDs:</span> <span class="lighting-value">${current.additional_wavelengths}</span></div>` : ''}
                <div class="lighting-status ${statusClass}">
                    <i class="fa fa-${icon}"></i><br>
                    <b>${statusMessage}</b><br>
                    <span>${statusRecommendation}</span>
                </div>
            </div>
        `;
    }

    // Beleuchtungs-Hinweise rendern
    renderLightingNotes(rec) {
        return `
            <div class="lighting-section-title lighting-notes">
                <i class="fa-solid fa-info-circle me-2"></i>Hinweise
            </div>
            <div class="lighting-section-content lighting-notes-content">
                <div class="lighting-note">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    ${rec.note}
                </div>
            </div>
        `;
    }

    // Phase aktualisieren (für externe Aufrufe)
    updatePhase(newPhase) {
        this.currentPhase = newPhase;
        this.loadLightingPlan();
    }

    // Widget zerstören (Cleanup)
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    // Speicher-Nachricht anzeigen
    showSaveMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `lighting-save-message lighting-save-${type}`;
        messageDiv.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check' : 'exclamation'}-circle"></i>
            ${message}
        `;
        
        // Alte Nachricht entfernen
        const oldMessage = this.container.querySelector('.lighting-save-message');
        if (oldMessage) {
            oldMessage.remove();
        }
        
        // Neue Nachricht hinzufügen
        this.container.appendChild(messageDiv);
        
        // Nachricht nach 3 Sekunden ausblenden
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    // Guidelines-Vorschau rendern
    renderGuidelinesPreview() {
        // Strain-Type aus dem Template oder Widget holen
        let strainType = 'photoperiodic';
        const strainTypeElement = document.querySelector('[data-plant-strain-type]');
        if (strainTypeElement) {
            strainType = strainTypeElement.getAttribute('data-plant-strain-type');
        } else {
            const strainTypeWidget = document.querySelector('#strain-type-widget');
            if (strainTypeWidget) {
                const strainTypeValue = strainTypeWidget.querySelector('.strain-type-value');
                if (strainTypeValue) {
                    const strainTypeText = strainTypeValue.textContent.trim();
                    if (strainTypeText.toLowerCase().includes('auto')) {
                        strainType = 'autoflowering';
                    }
                }
            }
        }
        
        const isAutoflower = strainType.toLowerCase().includes('auto');
        const strainTypeDisplay = isAutoflower ? 'Autoflower' : 'Photoperiodisch';
        
        // Strain-spezifische Empfehlungen
        let ppfdRange, dliRange, photoperiod, spectrum;
        
        if (isAutoflower) {
            ppfdRange = "100-800 µmol/m²/s je Phase (reduziert)";
            dliRange = "13-45 mol/m²/Tag (konstante Beleuchtung)";
            photoperiod = "18/6 konstant (kein Lichtwechsel nötig)";
            spectrum = "Vollspektrum mit angepasster Intensität";
        } else {
            ppfdRange = "100-1000 µmol/m²/s je Phase";
            dliRange = "13-45 mol/m²/Tag für optimales Wachstum";
            photoperiod = "18/6 (Veg) → 12/12 (Blüte)";
            spectrum = "Blau (Veg) → Rot (Blüte) anpassen";
        }
        
        return `
            <div class="lighting-section-title lighting-guidelines">
                <i class="fa-solid fa-lightbulb me-2"></i>Wichtige PPFD/DLI-Empfehlungen
                <span class="badge bg-success ms-2">${strainTypeDisplay}</span>
            </div>
            <div class="lighting-section-content lighting-guidelines-content">
                <div class="lighting-guidelines-preview">
                    <ul class="lighting-guidelines-list">
                        <li><strong>PPFD-Bereich:</strong> ${ppfdRange}</li>
                        <li><strong>DLI-Ziel:</strong> ${dliRange}</li>
                        <li><strong>Photoperiode:</strong> ${photoperiod}</li>
                        <li><strong>Spektrum:</strong> ${spectrum}</li>
                    </ul>
                    <div class="lighting-guidelines-more">
                        <button 
                            type="button" 
                            class="btn btn-sm btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#lighting-guidelines-modal"
                        >
                            <i class="fas fa-book-open"></i> Alle Richtlinien anzeigen
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Guidelines-Modal erstellen
    createGuidelinesModal() {
        // Prüfen ob Modal bereits existiert
        if (document.getElementById('lighting-guidelines-modal')) {
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="lighting-guidelines-modal" tabindex="-1" aria-labelledby="lighting-guidelines-modal-label" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="lighting-guidelines-modal-label">
                                <i class="fa-solid fa-lightbulb me-2"></i>Beleuchtungsrichtlinien & PPFD/DLI-Empfehlungen
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="lighting-guidelines-content">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6><i class="fa-solid fa-chart-line me-2"></i>Phasenspezifische Empfehlungen</h6>
                                        <div class="lighting-phases-table">
                                            <div class="lighting-phase-item">
                                                <div class="lighting-phase-header">
                                                    <strong>Keimung (3-7 Tage)</strong>
                                                    <span class="lighting-phase-ppfd">PPFD: 100-200 µmol/m²/s</span>
                                                </div>
                                                <div class="lighting-phase-details">
                                                    <p><strong>DLI:</strong> 6-12 mol/m²/Tag | <strong>Stunden:</strong> 18h | <strong>Kelvin:</strong> 5000-6500K</p>
                                                    <p><strong>Spektrum:</strong> Blau 450nm, Kaltweiß | <strong>Zweck:</strong> Orientierung, nicht Wachstum</p>
                                                </div>
                                            </div>
                                            
                                            <div class="lighting-phase-item">
                                                <div class="lighting-phase-header">
                                                    <strong>Frühe Wachstumsphase</strong>
                                                    <span class="lighting-phase-ppfd">PPFD: 250-400 µmol/m²/s</span>
                                                </div>
                                                <div class="lighting-phase-details">
                                                    <p><strong>DLI:</strong> 12-20 mol/m²/Tag | <strong>Stunden:</strong> 18h | <strong>Kelvin:</strong> 4000-6500K</p>
                                                    <p><strong>Spektrum:</strong> Blau 450nm, Neutralweiß, Rot 660nm | <strong>Fokus:</strong> Kompaktes Wachstum</p>
                                                </div>
                                            </div>
                                            
                                            <div class="lighting-phase-item">
                                                <div class="lighting-phase-header">
                                                    <strong>Späte Wachstumsphase</strong>
                                                    <span class="lighting-phase-ppfd">PPFD: 400-600 µmol/m²/s</span>
                                                </div>
                                                <div class="lighting-phase-details">
                                                    <p><strong>DLI:</strong> 20-30 mol/m²/Tag | <strong>Stunden:</strong> 18h | <strong>Kelvin:</strong> 3500-5000K</p>
                                                    <p><strong>Spektrum:</strong> Blau 450nm, Rot 660nm, Weiß 4000K | <strong>Fokus:</strong> Stabile Pflanzenstruktur</p>
                                                </div>
                                            </div>
                                            
                                            <div class="lighting-phase-item">
                                                <div class="lighting-phase-header">
                                                    <strong>Frühe Blüte</strong>
                                                    <span class="lighting-phase-ppfd">PPFD: 600-800 µmol/m²/s</span>
                                                </div>
                                                <div class="lighting-phase-details">
                                                    <p><strong>DLI:</strong> 30-40 mol/m²/Tag | <strong>Stunden:</strong> 12h (Photo) / 18h (Auto) | <strong>Kelvin:</strong> 3000-3500K</p>
                                                    <p><strong>Spektrum:</strong> Rot 660nm, Far Red 730nm, Warmweiß | <strong>Fokus:</strong> Blüteeinleitung</p>
                                                </div>
                                            </div>
                                            
                                            <div class="lighting-phase-item">
                                                <div class="lighting-phase-header">
                                                    <strong>Volle Blüte</strong>
                                                    <span class="lighting-phase-ppfd">PPFD: 800-1000 µmol/m²/s</span>
                                                </div>
                                                <div class="lighting-phase-details">
                                                    <p><strong>DLI:</strong> 35-45 mol/m²/Tag | <strong>Stunden:</strong> 12h (Photo) / 18h (Auto) | <strong>Kelvin:</strong> 2700-3500K</p>
                                                    <p><strong>Spektrum:</strong> Deep Red 660nm, Far Red 730nm, Warmweiß | <strong>Fokus:</strong> Buddichte & Harzproduktion</p>
                                                </div>
                                            </div>
                                            
                                            <div class="lighting-phase-item">
                                                <div class="lighting-phase-header">
                                                    <strong>Spätblüte</strong>
                                                    <span class="lighting-phase-ppfd">PPFD: 600-800 µmol/m²/s</span>
                                                </div>
                                                <div class="lighting-phase-details">
                                                    <p><strong>DLI:</strong> 30-38 mol/m²/Tag | <strong>Stunden:</strong> 12h (Photo) / 18h (Auto) | <strong>Kelvin:</strong> 2700-3000K</p>
                                                    <p><strong>Spektrum:</strong> Deep Red, Far Red | <strong>Fokus:</strong> Reifung & Farbwechsel</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <h6><i class="fa-solid fa-tent me-2"></i>Tent-Empfehlungen</h6>
                                        <div class="lighting-tent-examples">
                                            <div class="lighting-tent-item">
                                                <strong>60x60cm</strong>
                                                <p>Veg: 80W, PPFD 300 | Blüte: 120W, PPFD 700</p>
                                                <small>Passive Kühlung, reflektierende Wände</small>
                                            </div>
                                            <div class="lighting-tent-item">
                                                <strong>80x80cm</strong>
                                                <p>Veg: 120W, PPFD 350 | Blüte: 200W, PPFD 800</p>
                                                <small>Gute Wahl für Anfänger, 2-4 Pflanzen</small>
                                            </div>
                                            <div class="lighting-tent-item">
                                                <strong>100x100cm</strong>
                                                <p>Veg: 200W, PPFD 400 | Blüte: 320W, PPFD 900</p>
                                                <small>Hohes Ertragspotenzial, CO₂ bei PPFD >800</small>
                                            </div>
                                        </div>
                                        
                                        <hr class="my-4">
                                        
                                        <h6><i class="fa-solid fa-calculator me-2"></i>DLI-Berechnung</h6>
                                        <div class="lighting-dli-info">
                                            <p><strong>Formel:</strong> DLI = PPFD × Photoperiode × 0,0036</p>
                                            <p><strong>Beispiel:</strong> 600 PPFD × 12h × 0,0036 = 26 mol/m²/Tag</p>
                                            <p><strong>Zielbereich:</strong> 12-45 mol/m²/Tag für optimales Wachstum</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="lighting-sources">
                                    <h6><i class="fa-solid fa-link me-2"></i>Quellen</h6>
                                    <p class="text-muted small">
                                        Diese Empfehlungen basieren auf wissenschaftlichen Erkenntnissen und LED-optimierten Growräumen:
                                    </p>
                                    <ul class="lighting-sources-list">
                                        <li>Migro Light - PPFD, DLI und PAR</li>
                                        <li>Royal Queen Seeds - Beste LED-Wachstumslichter</li>
                                        <li>Coco for Cannabis - Light Scheduler</li>
                                        <li>Lux Light Tech - LED-Optimierung</li>
                                        <li>Kind LED Grow Lights - Spektrum-Analyse</li>
                                    </ul>
                                    <p class="text-muted small mt-2">
                                        <i class="fa-solid fa-calendar me-1"></i>Stand: 12.07.2025 | 
                                        <i class="fa-solid fa-user me-1"></i>Erstellt von: Grow-Experte (GPT)
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Modal zum Body hinzufügen
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }
}

// Automatische Initialisierung entfernt - wird jetzt über Widget Manager verwaltet

// Global verfügbar machen für Widget Manager
window.LightingWidget = LightingWidget;

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LightingWidget;
}

// Guidelines laden und cachen
LightingWidget.lightingGuidelines = null;
LightingWidget.lightingGuidelinesLoaded = false;
LightingWidget.loadLightingGuidelines = async function() {
    if (LightingWidget.lightingGuidelinesLoaded) return LightingWidget.lightingGuidelines;
    try {
        const response = await fetch('/static/data/lighting-guidelines.json');
        if (response.ok) {
            const json = await response.json();
            LightingWidget.lightingGuidelines = json.lightingGuidelines;
            LightingWidget.lightingGuidelinesLoaded = true;
            return LightingWidget.lightingGuidelines;
        }
    } catch (e) {}
    return null;
};