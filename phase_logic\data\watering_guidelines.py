# Bewässerungs-Guidelines pro Sub-Phase (Basiswerte, können später erweitert werden)
WATERING_GUIDELINES = {
    'germination': {
        'amount_ml_per_liter': 50,
        'frequency': 'Alle 2-3 Tage, Substrat feucht halten',
        'note': 'Keimlinge nur leicht feucht halten, Staunässe vermeiden.'
    },
    'vegetative_early': {
        'amount_ml_per_liter': 100,
        'frequency': 'Alle 2 Tage, bei Bedarf täglich',
        'note': '<PERSON><PERSON><PERSON><PERSON> regelmäßig, aber nicht zu nass halten.'
    },
    'vegetative_middle': {
        'amount_ml_per_liter': 200,
        'frequency': 'Alle 2 Tage, bei warmem Wetter täglich',
        'note': 'Wurzelballen gut durchfeuchten, aber nicht dauerhaft nass.'
    },
    'vegetative_late': {
        'amount_ml_per_liter': 300,
        'frequency': 'Alle 2 Tage, ggf. täglich',
        'note': '<PERSON><PERSON>lan<PERSON> nehmen mehr Wasser auf, auf Trockenphasen achten.'
    },
    'flowering_early': {
        'amount_ml_per_liter': 250,
        'frequency': 'Alle 2-3 Tage',
        'note': 'Wasserbedarf bleibt hoch, aber Staunässe vermeiden.'
    },
    'flowering_middle': {
        'amount_ml_per_liter': 200,
        'frequency': 'Alle 3 Tage',
        'note': 'Wasserbedarf sinkt leicht, auf Schimmelgefahr achten.'
    },
    'flowering_late': {
        'amount_ml_per_liter': 150,
        'frequency': 'Alle 3-4 Tage',
        'note': 'Weniger gießen, Substrat vor Ernte leicht abtrocknen lassen.'
    },
    'flush': {
        'amount_ml_per_liter': 100,
        'frequency': 'Nach Bedarf, Substrat gut durchspülen',
        'note': 'Nur Wasser, keine Nährstoffe mehr.'
    }
}

def get_watering_recommendation(phase, substrate=None, pot_size_l=None):
    """
    Gibt die Bewässerungsempfehlung für eine Phase zurück.
    Args:
        phase (str): Sub-Phase-Key (z. B. 'vegetative_early')
        substrate (str, optional): Substrat-Typ
        pot_size_l (float, optional): Topfgröße in Litern
    Returns:
        dict: amount_ml_per_liter, frequency, note, (optional: amount_per_pot)
    """
    rec = WATERING_GUIDELINES.get(phase, {}).copy()
    if not rec:
        return None
    # Optionale Anpassung nach Topfgröße
    if pot_size_l:
        rec['amount_per_pot'] = int(rec['amount_ml_per_liter'] * pot_size_l)
    if substrate:
        rec['note'] += f' (Substrat: {substrate})'
    return rec 