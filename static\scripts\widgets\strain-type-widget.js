/**
 * Strain-Type-Widget
 * Verwaltet die Anzeige und Änderung des Sortentyps (photoperiodic/autoflowering)
 */

class StrainTypeWidget {
    constructor(containerId, plantId) {
        this.containerId = containerId;
        // plantId wird direkt als String übergeben
        this.plantId = plantId;
        this.currentStrainType = null;
        this.container = null;
        this.isInitialized = false;
        
        // Strain-Type-Widget initialisiert
        
        // Automatische Initialisierung
        this.init();
    }
    
    async init() {
        // Verhindere doppelte Initialisierung
        if (this.isInitialized) {
            return;
        }
        
        try {
            this.container = document.getElementById(this.containerId);
            if (!this.container) {
                console.error('Strain-Type-Widget: Container nicht gefunden:', this.containerId);
                return;
            }
            
            // Strain-Type aus Template lesen
            this.currentStrainType = this.getStrainTypeFromTemplate();
            
            // Widget aktualisieren (nicht neu rendern)
            this.updateWidget();
            
            // Event-Listener setzen
            this.setupEventListeners();
            
            this.isInitialized = true;
            
        } catch (error) {
            console.error('Strain-Type-Widget: Fehler bei Initialisierung:', error);
        }
    }
    
    getStrainTypeFromTemplate() {
        // Strain-Type aus verstecktem Input-Feld oder Data-Attribut lesen
        const strainTypeInput = document.querySelector('input[name="strain_type"]');
        const strainTypeData = document.querySelector('[data-strain-type]');
        
        if (strainTypeInput && strainTypeInput.value) {
            return strainTypeInput.value;
        } else if (strainTypeData && strainTypeData.dataset.strainType) {
            return strainTypeData.dataset.strainType;
        }
        
        // Fallback: Aus URL-Parameter oder Standard
        return 'autoflowering';
    }
    
    updateWidget() {
        const strainTypeInfo = this.getStrainTypeInfo(this.currentStrainType);
        
        // Widget wird aktualisiert
        
        // Nur die relevanten Teile aktualisieren, nicht das gesamte HTML überschreiben
        const strainTypeValue = this.container.querySelector('.strain-type-value');
        const strainTypeDetails = this.container.querySelector('.strain-type-details');
        const strainTypeCards = this.container.querySelectorAll('.strain-type-card');
        
        if (strainTypeValue) {
            strainTypeValue.textContent = strainTypeInfo.name.toUpperCase();
            strainTypeValue.className = `strain-type-value ${this.currentStrainType}`;
        }
        
        if (strainTypeDetails) {
            strainTypeDetails.textContent = strainTypeInfo.description;
        }
        
        // Karten-Status aktualisieren
        strainTypeCards.forEach(card => {
            const cardType = card.getAttribute('data-type');
            if (cardType === this.currentStrainType) {
                card.classList.add('active');
            } else {
                card.classList.remove('active');
            }
        });
        
        // Widget aktualisiert
    }
    
    getStrainTypeInfo(strainType) {
        const types = {
            'photoperiodic': {
                name: 'Photoperiodisch',
                description: 'Photoperiodische Sorten benötigen eine Änderung des Lichtzyklus von 18/6 auf 12/12 Stunden, um in die Blütephase zu wechseln. Sie bieten mehr Kontrolle über Wachstum und Blütezeitpunkt.',
                icon: '🌱',
                advantages: ['Mehr Kontrolle über Wachstum', 'Längere vegetative Phase möglich', 'Höhere Erträge möglich']
            },
            'autoflowering': {
                name: 'Autoflowering',
                description: 'Autoflowering-Sorten blühen automatisch nach 2-4 Wochen, unabhängig vom Lichtzyklus. Kürzere Gesamtdauer, einfacher zu handhaben.',
                icon: '⚡',
                advantages: ['Einfacher zu handhaben', 'Schnellerer Zyklus', 'Konsistente Beleuchtung']
            }
        };
        
        return types[strainType] || types['autoflowering'];
    }
    
    setupEventListeners() {
        // Edit-Button für Modal
        const editBtn = this.container.querySelector('.strain-type-edit-btn');
        if (editBtn) {
            editBtn.addEventListener('click', () => {
                this.openModal();
            });
        }
    }

    openModal() {
        // Modal-Template dynamisch erstellen
        const modalTemplate = this.createModalTemplate();
        if (!modalTemplate) {
            console.error('Strain-Type-Widget: Modal-Template konnte nicht erstellt werden');
            return;
        }

        // Modal-Overlay an body anhängen
        document.body.appendChild(modalTemplate);

        // Event-Listener für Modal setzen
        this.setupModalEventListeners(modalTemplate);

        // Modal anzeigen
        modalTemplate.style.display = 'flex';
        modalTemplate.classList.add('modal-visible');
    }
    
    createModalTemplate() {
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'strain-type-modal-overlay';
        modalOverlay.id = 'strainTypeModal';
        
        const currentStrainType = this.currentStrainType;
        
        modalOverlay.innerHTML = `
            <div class="strain-type-modal">
                <div class="strain-type-modal-header">
                    <h3>Sortentyp ändern</h3>
                    <button class="strain-type-modal-close">×</button>
                </div>
                <div class="strain-type-modal-content">
                    <div class="strain-type-info-section">
                        <div class="strain-type-info-text">
                            <i class="fas fa-info-circle me-2"></i>
                            Wähle zwischen Photoperiodic (lichtabhängig) und Autoflowering (automatisch blühend). Bestimmt die Beleuchtungsstrategie und Gesamtdauer des Grows.
                        </div>
                    </div>
                    <div class="strain-type-selection">
                        <div class="strain-type-option-card ${currentStrainType === 'photoperiodic' ? 'selected' : ''}" data-type="photoperiodic">
                            <i class="fa-solid fa-clock strain-type-option-icon"></i>
                            <div class="strain-type-option-title">PHOTOPERIODIC</div>
                            <div class="strain-type-option-description">10-16 Wochen<br>18/6 → 12/12</div>
                        </div>
                        <div class="strain-type-option-card ${currentStrainType === 'autoflowering' ? 'selected' : ''}" data-type="autoflowering">
                            <i class="fa-solid fa-bolt strain-type-option-icon"></i>
                            <div class="strain-type-option-title">AUTOFLOWERING</div>
                            <div class="strain-type-option-description">8-12 Wochen<br>18/6 oder 20/4</div>
                        </div>
                    </div>
                </div>
                <div class="strain-type-modal-actions">
                    <button class="strain-type-modal-btn cancel">Abbrechen</button>
                    <button class="strain-type-modal-btn save" id="saveStrainTypeBtn"><i class="fa-solid fa-check me-1"></i>Speichern</button>
                </div>
            </div>
        `;
        
        return modalOverlay;
    }
    
    setupModalEventListeners(modalElement) {
        // Schließen-Button
        const closeBtn = modalElement.querySelector('.strain-type-modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeModal(modalElement);
            });
        }

        // Abbrechen-Button
        const cancelBtn = modalElement.querySelector('.strain-type-modal-btn.cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.closeModal(modalElement);
            });
        }

        // Speichern-Button
        const saveBtn = modalElement.querySelector('.strain-type-modal-btn.save');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveStrainType(modalElement);
            });
        }

        // Option-Karten für Auswahl
        const optionCards = modalElement.querySelectorAll('.strain-type-option-card');
        optionCards.forEach(card => {
            card.addEventListener('click', () => {
                // Alle Karten deselektieren
                optionCards.forEach(c => c.classList.remove('selected'));
                // Diese Karte selektieren
                card.classList.add('selected');
            });
        });

        // ESC zum Schließen
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeModal(modalElement);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);

        // Klick außerhalb Modal schließt Modal
        modalElement.addEventListener('click', (e) => {
            if (e.target === modalElement) {
                this.closeModal(modalElement);
            }
        });
    }
    
    closeModal(modalElement) {
        if (modalElement && modalElement.parentNode) {
            modalElement.parentNode.removeChild(modalElement);
        }
    }
    
    async saveStrainType(modalElement) {
        // Ausgewählte Option finden
        const selectedOption = modalElement.querySelector('.strain-type-option-card.selected');
        if (!selectedOption) {
            this.showNotification('Bitte wähle einen Sortentyp aus', 'error');
            return;
        }
        
        const newStrainType = selectedOption.getAttribute('data-type');
        // Speichere neuen Strain-Type
        
        try {
            // API-Call zum Speichern
            const response = await fetch(`/api/plants/${this.plantId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    strain_type: newStrainType
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Strain-Type aktualisieren
                this.currentStrainType = newStrainType;
                
                // Widget neu rendern
                this.updateWidget();
                
                // Modal schließen
                this.closeModal(modalElement);
                
                // Event für andere Widgets auslösen
                this.triggerStrainTypeChange();
                
                // Benachrichtigung anzeigen
                this.showNotification('Sortentyp erfolgreich geändert', 'success');
            } else {
                this.showNotification('Fehler beim Speichern des Sortentyps', 'error');
            }
        } catch (error) {
            console.error('Strain-Type-Widget: Fehler beim Speichern:', error);
            this.showNotification('Fehler beim Speichern des Sortentyps', 'error');
        }
    }
    
    updateStrainType(newStrainType) {
        this.currentStrainType = newStrainType;
        this.updateWidget();
        
        // Event für andere Widgets auslösen
        this.triggerStrainTypeChange();
        
        // Benachrichtigung anzeigen
        this.showNotification('Sortentyp erfolgreich geändert', 'success');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Am Anfang der Seite einfügen
        const container = document.querySelector('.container') || document.body;
        container.insertBefore(notification, container.firstChild);
        
        // Automatisch entfernen nach 5 Sekunden
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    triggerStrainTypeChange() {
        // Custom Event auslösen für andere Widgets
        const event = new CustomEvent('strainTypeChanged', {
            detail: {
                strainType: this.currentStrainType,
                plantId: this.plantId
            }
        });
        document.dispatchEvent(event);
    }
    
    // Getter für andere Widgets
    getCurrentStrainType() {
        return this.currentStrainType;
    }
    
    // Methode zum Aktualisieren der Beleuchtungsempfehlungen
    updateLightingRecommendations() {
        if (this.currentStrainType === 'autoflowering') {
            // Autoflowering-spezifische Beleuchtungsempfehlungen
            return {
                photoperiod: '18/6 oder 20/4',
                intensity: 'Hoch (600-800 µmol/m²/s)',
                duration: '18-20 Stunden pro Tag',
                notes: 'Konsistente Beleuchtung während des gesamten Zyklus'
            };
        } else {
            // Photoperiodic-spezifische Beleuchtungsempfehlungen
            return {
                photoperiod: '18/6 → 12/12',
                intensity: 'Mittel-Hoch (400-600 µmol/m²/s)',
                duration: '18h vegetative, 12h Blüte',
                notes: 'Lichtzyklus-Änderung für Blüte erforderlich'
            };
        }
    }
    
    // Methode zum Aktualisieren der Phasen-Timeline
    updatePhaseTimeline() {
        if (this.currentStrainType === 'autoflowering') {
            return {
                germination: '3-7 Tage',
                vegetative: '2-4 Wochen',
                flowering: '6-8 Wochen',
                total: '8-12 Wochen'
            };
        } else {
            return {
                germination: '3-7 Tage',
                vegetative: '4-8 Wochen',
                flowering: '8-12 Wochen',
                total: '12-20 Wochen'
            };
        }
    }
    
    // Cleanup-Methode für Widget Manager
    destroy() {
        this.isInitialized = false;
        // Widget zerstört
    }
}

// Globale Funktionen für Widget Manager
function initStrainTypeWidget(containerId, plantId) {
    return new StrainTypeWidget(containerId, plantId);
}

function getStrainTypeWidget() {
    // Widget über Widget Manager abrufen falls verfügbar
    if (window.widgetManager) {
        return window.widgetManager.getWidget('strain-type', 'strainTypeContainer');
    }
    return null;
}

// Globale Verfügbarkeit
window.StrainTypeWidget = StrainTypeWidget;
window.initStrainTypeWidget = initStrainTypeWidget;
window.getStrainTypeWidget = getStrainTypeWidget;
