/* ===== PLANT CARDS COMPONENT STYLES ===== */

.plant-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.plant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Plant Card: Textfarbe überall korrekt, auch für alle Kinder */
.plant-card, .plant-card *, .plant-card .card-body, .plant-card .card-title, .plant-card .card-text, .plant-card .card-footer, .plant-card .card-header, .plant-card .entry-count, .plant-card .details-link, .plant-card .btn {
  color: var(--text) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plant-card .card-body {
    padding: 1rem;
  }
} 