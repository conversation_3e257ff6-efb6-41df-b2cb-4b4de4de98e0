"""
Govee Sensor Integration
Handles Govee hygrometer data retrieval and integration
"""

import requests
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

class GoveeIntegration:
    """Govee sensor integration for hygrometers"""
    
    def __init__(self):
        self.api_key = None
        self.base_url = "https://developer-api.govee.com/v1"
        self.devices = []
        self.last_reading = {}
        self.connection_status = False
        
    def setup_api_key(self, api_key: str) -> bool:
        """Setup Govee API key"""
        try:
            self.api_key = api_key
            # Test connection
            test_result = self.test_connection()
            self.connection_status = test_result
            return test_result
        except Exception as e:
            print(f"Fehler beim Setup der Govee API: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test Govee API connection"""
        if not self.api_key:
            return False
            
        try:
            headers = {
                'Govee-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.get(f"{self.base_url}/devices", headers=headers)
            return response.status_code == 200
        except Exception as e:
            print(f"Govee API Test fehlgeschlagen: {e}")
            return False
    
    def get_devices(self) -> List[Dict[str, Any]]:
        """Get all available Govee devices"""
        if not self.api_key:
            return []
            
        try:
            headers = {
                'Govee-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.get(f"{self.base_url}/devices", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                devices = []
                
                for device in data.get('data', {}).get('devices', []):
                    device_info = {
                        'device_id': device.get('device'),
                        'model': device.get('model'),
                        'name': device.get('deviceName'),
                        'controllable': device.get('controllable', False),
                        'retrievable': device.get('retrievable', False),
                        'type': self._get_device_type(device.get('model', ''))
                    }
                    devices.append(device_info)
                
                self.devices = devices
                return devices
            else:
                print(f"Fehler beim Abrufen der Geräte: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"Fehler beim Abrufen der Govee-Geräte: {e}")
            return []
    
    def _get_device_type(self, model: str) -> str:
        """Determine device type from model"""
        model_lower = model.lower()
        
        if 'h5179' in model_lower or 'h5101' in model_lower or 'h5102' in model_lower:
            return 'hygrometer'
        elif 'h7142' in model_lower or 'h7141' in model_lower:
            return 'humidifier'
        elif 'h6104' in model_lower or 'h6105' in model_lower:
            return 'thermometer'
        else:
            return 'unknown'
    
    def get_device_state(self, device_id: str, model: str) -> Optional[Dict[str, Any]]:
        """Get current state of a specific device"""
        if not self.api_key:
            return None
            
        try:
            headers = {
                'Govee-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            params = {
                'device': device_id,
                'model': model
            }
            
            response = requests.get(f"{self.base_url}/devices/state", headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                state_data = data.get('data', {})
                
                # Extract sensor data
                sensor_data = {}
                for prop in state_data.get('properties', []):
                    name = prop.get('name', '')
                    value = prop.get('value')
                    
                    if name == 'temperature':
                        sensor_data['temperature'] = value
                    elif name == 'humidity':
                        sensor_data['humidity'] = value
                    elif name == 'battery':
                        sensor_data['battery'] = value
                
                # Add timestamp
                sensor_data['timestamp'] = datetime.now().isoformat()
                sensor_data['device_id'] = device_id
                sensor_data['model'] = model
                
                # Store last reading
                self.last_reading[device_id] = sensor_data
                
                return sensor_data
            else:
                print(f"Fehler beim Abrufen des Geräte-Status: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Fehler beim Abrufen des Govee-Geräte-Status: {e}")
            return None
    
    def get_hygrometer_data(self, device_id: str, model: str) -> Optional[Dict[str, Any]]:
        """Get hygrometer data specifically"""
        state = self.get_device_state(device_id, model)
        
        if state and 'temperature' in state and 'humidity' in state:
            return {
                'temperature': state['temperature'],
                'humidity': state['humidity'],
                'battery': state.get('battery', 0),
                'timestamp': state['timestamp'],
                'device_id': device_id,
                'model': model
            }
        
        return None
    
    def get_last_reading(self, device_id: str) -> Optional[Dict[str, Any]]:
        """Get last known reading for a device"""
        return self.last_reading.get(device_id)
    
    def is_connected(self) -> bool:
        """Check if Govee API is connected"""
        return self.connection_status and self.api_key is not None
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get detailed connection status"""
        return {
            'connected': self.is_connected(),
            'api_key_set': self.api_key is not None,
            'devices_count': len(self.devices),
            'last_test': datetime.now().isoformat() if self.connection_status else None
        }

# Global instance
govee_integration = GoveeIntegration() 