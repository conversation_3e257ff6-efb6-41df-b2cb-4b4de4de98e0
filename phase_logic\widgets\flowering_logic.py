"""
Blüte-Zeitmanagement Logic Module
Handles flowering timeline, markers, and harvest predictions
"""

import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# Add parent directories to path for imports
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from database_basic import db
from phase_logic.core import PhaseLogic


class FloweringLogic:
    """Logik für Blüte-Zeitmanagement und Ernteprognose"""
    
    @staticmethod
    def load_flowering_guidelines() -> Dict:
        """Lädt die Blüte-Zeitmanagement-Guidelines aus der JSON-Datei."""
        try:
            guidelines_path = os.path.join('static', 'data', 'bluete-zeitmanagement-guidelines.json')
            with open(guidelines_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('floweringManagementGuidelines', {})
        except Exception as e:
            print(f"Fehler beim Laden der Blüte-Guidelines: {e}")
            return {}
    
    @staticmethod
    def calculate_bloom_day(flower_start_date: str) -> int:
        """Berechnet den aktuellen Blütetag basierend auf dem Blütebeginn."""
        try:
            if not flower_start_date:
                return 0
            
            start_date = datetime.fromisoformat(flower_start_date.replace('Z', '+00:00'))
            current_date = datetime.now()
            
            # Zeitzone-Unterschied berücksichtigen
            if start_date.tzinfo is None:
                start_date = start_date.replace(tzinfo=current_date.tzinfo)
            
            bloom_day = (current_date - start_date).days
            return max(0, bloom_day)
        except Exception as e:
            print(f"Fehler bei Blütetag-Berechnung: {e}")
            return 0
    
    @staticmethod
    def get_current_flowering_phase(bloom_day: int, strain_type: str = 'autoflowering') -> Dict:
        """Bestimmt die aktuelle Blütephase basierend auf dem Blütetag."""
        guidelines = FloweringLogic.load_flowering_guidelines()
        phases = guidelines.get('phasen', [])
        
        current_phase = None
        progress_percentage = 0
        
        for phase in phases:
            phase_start = phase['tage']['start']
            phase_duration = phase['tage']['dauer']
            phase_end = phase_start + phase_duration
            
            if bloom_day >= phase_start and bloom_day < phase_end:
                current_phase = phase
                # Fortschritt innerhalb der Phase berechnen
                days_in_phase = bloom_day - phase_start
                progress_percentage = (days_in_phase / phase_duration) * 100
                break
        
        if not current_phase:
            # Fallback: Letzte Phase
            current_phase = phases[-1] if phases else {}
            progress_percentage = 100
        
        return {
            'phase_id': current_phase.get('id', 'unknown'),
            'phase_name': current_phase.get('beschreibung', 'Unbekannte Phase'),
            'bloom_day': bloom_day,
            'progress_percentage': round(progress_percentage, 1),
            'phase_start': current_phase.get('tage', {}).get('start', 0),
            'phase_duration': current_phase.get('tage', {}).get('dauer', 0),
            'indicators': current_phase.get('indikatoren', []),
            'notes': current_phase.get('notizen', [])
        }
    
    @staticmethod
    def get_flowering_timeline(plant_id: str) -> Dict:
        """Erstellt eine vollständige Timeline der Blütephasen."""
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return {'error': 'Pflanze nicht gefunden'}
        
        flower_start_date = plant.get('flower_start_date')
        strain_type = plant.get('strain_type', 'autoflowering')
        bloom_day = FloweringLogic.calculate_bloom_day(flower_start_date)
        
        guidelines = FloweringLogic.load_flowering_guidelines()
        phases = guidelines.get('phasen', [])
        
        timeline = []
        total_duration = 0
        
        for phase in phases:
            phase_start = phase['tage']['start']
            phase_duration = phase['tage']['dauer']
            phase_end = phase_start + phase_duration
            total_duration = max(total_duration, phase_end)
            
            # Marker für diese Phase laden
            phase_markers = db.get_flowering_markers(plant_id, phase_start, phase_end)
            
            timeline.append({
                'phase_id': phase['id'],
                'phase_name': phase['beschreibung'],
                'start_day': phase_start,
                'end_day': phase_end,
                'duration': phase_duration,
                'is_current': bloom_day >= phase_start and bloom_day < phase_end,
                'is_completed': bloom_day >= phase_end,
                'markers': phase_markers
            })
        
        return {
            'plant_id': plant_id,
            'current_bloom_day': bloom_day,
            'total_duration': total_duration,
            'timeline': timeline,
            'strain_type': strain_type
        }
    
    @staticmethod
    def create_flowering_marker(plant_id: str, marker_data: Dict) -> Dict:
        """Erstellt einen neuen Flowering-Marker."""
        try:
            # Pflichtfelder prüfen
            required_fields = ['event_type', 'event_name', 'bloom_day']
            for field in required_fields:
                if field not in marker_data:
                    return {'success': False, 'error': f'{field} ist erforderlich'}
            
            # Marker in Datenbank speichern
            marker_id = db.create_flowering_marker(plant_id, marker_data)
            
            if marker_id:
                return {
                    'success': True,
                    'data': {
                        'marker_id': marker_id,
                        'message': 'Flowering-Marker erfolgreich erstellt'
                    }
                }
            else:
                return {'success': False, 'error': 'Fehler beim Speichern des Markers'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def get_harvest_prediction(plant_id: str) -> Dict:
        """Erstellt eine Ernteprognose basierend auf Strain und aktueller Entwicklung."""
        plant = db.get_plant_by_id(plant_id)
        if not plant:
            return {'error': 'Pflanze nicht gefunden'}
        
        strain_type = plant.get('strain_type', 'autoflowering')
        bloom_day = FloweringLogic.calculate_bloom_day(plant.get('flower_start_date'))
        
        guidelines = FloweringLogic.load_flowering_guidelines()
        harvest_recommendations = guidelines.get('harvestEmpfehlungen', {})
        strain_dependency = guidelines.get('strainAbhaengigkeit', {})
        
        # Strain-spezifische Empfehlungen
        if strain_type == 'autoflowering':
            # Autoflower: Kürzere Blütezeit
            optimal_range = harvest_recommendations.get('optimal', {}).get('tageNachBlüte', [63, 70])
            early_range = harvest_recommendations.get('frueh', {}).get('tageNachBlüte', [55, 62])
            late_range = harvest_recommendations.get('spaet', {}).get('tageNachBlüte', [71, 80])
        else:
            # Photoperiod: Längere Blütezeit
            optimal_range = [70, 80]
            early_range = [65, 69]
            late_range = [81, 90]
        
        # Flush-Empfehlung
        flush_duration = strain_dependency.get('flushVorErnteTage', {}).get('midFlowering', 10)
        flush_start = optimal_range[0] - flush_duration
        
        # Tage bis zur Ernte berechnen
        days_to_optimal = optimal_range[0] - bloom_day
        days_to_early = early_range[0] - bloom_day
        days_to_late = late_range[0] - bloom_day
        
        return {
            'plant_id': plant_id,
            'current_bloom_day': bloom_day,
            'harvest_windows': {
                'early': {
                    'range': early_range,
                    'days_until': max(0, days_to_early),
                    'effect': harvest_recommendations.get('frueh', {}).get('wirkung', 'Energetisch, zerebral'),
                    'trichome_target': harvest_recommendations.get('frueh', {}).get('trichome', {})
                },
                'optimal': {
                    'range': optimal_range,
                    'days_until': max(0, days_to_optimal),
                    'effect': harvest_recommendations.get('optimal', {}).get('wirkung', 'Balanced High'),
                    'trichome_target': harvest_recommendations.get('optimal', {}).get('trichome', {})
                },
                'late': {
                    'range': late_range,
                    'days_until': max(0, days_to_late),
                    'effect': harvest_recommendations.get('spaet', {}).get('wirkung', 'Sedierend, medizinisch'),
                    'trichome_target': harvest_recommendations.get('spaet', {}).get('trichome', {})
                }
            },
            'flush_recommendation': {
                'start_day': flush_start,
                'duration': flush_duration,
                'days_until_flush': max(0, flush_start - bloom_day),
                'status': 'pre_flush' if bloom_day < flush_start else 'ready_to_flush'
            },
            'recommendation': f"Optimaler Erntezeitpunkt in {max(0, days_to_optimal)} Tagen. Flush in {max(0, flush_start - bloom_day)} Tagen starten."
        }
    
    @staticmethod
    def get_flowering_status(plant_id: str) -> Dict:
        """Holt den vollständigen Flowering-Status einer Pflanze."""
        try:
            plant = db.get_plant_by_id(plant_id)
            if not plant:
                return {'error': 'Pflanze nicht gefunden'}
            
            flower_start_date = plant.get('flower_start_date')
            strain_type = plant.get('strain_type', 'autoflowering')
            bloom_day = FloweringLogic.calculate_bloom_day(flower_start_date)
            
            # Aktuelle Phase bestimmen
            current_phase = FloweringLogic.get_current_flowering_phase(bloom_day, strain_type)
            
            # Timeline erstellen
            timeline = FloweringLogic.get_flowering_timeline(plant_id)
            
            # Marker laden
            markers = db.get_flowering_markers(plant_id)
            
            # Ernteprognose
            harvest_prediction = FloweringLogic.get_harvest_prediction(plant_id)
            
            # Guidelines laden
            guidelines = FloweringLogic.load_flowering_guidelines()
            
            return {
                'plant_id': plant_id,
                'strain_type': strain_type,
                'bloom_start_date': flower_start_date,
                'current_bloom_day': bloom_day,
                'current_phase': current_phase,
                'timeline': timeline.get('timeline', []),
                'markers': markers,
                'harvest_prediction': harvest_prediction,
                'guidelines': {
                    'phases': guidelines.get('phasen', []),
                    'event_markers': guidelines.get('eventMarker', []),
                    'flush_logic': guidelines.get('flushLogik', {}),
                    'harvest_recommendations': guidelines.get('harvestEmpfehlungen', {}),
                    'strain_dependency': guidelines.get('strainAbhaengigkeit', {}),
                    'faustregeln': guidelines.get('faustregeln', [])
                }
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    @staticmethod
    def validate_marker_data(marker_data: Dict) -> Dict:
        """Validiert Marker-Daten vor dem Speichern."""
        errors = []
        
        if not marker_data.get('event_type'):
            errors.append('Event-Typ ist erforderlich')
        
        if not marker_data.get('event_name'):
            errors.append('Event-Name ist erforderlich')
        
        bloom_day = marker_data.get('bloom_day')
        if bloom_day is None or bloom_day < 0:
            errors.append('Gültiger Blütetag ist erforderlich')
        
        if errors:
            return {'valid': False, 'errors': errors}
        
        return {'valid': True}
    
    @staticmethod
    def get_event_marker_types() -> List[Dict]:
        """Gibt alle verfügbaren Event-Marker-Typen zurück."""
        guidelines = FloweringLogic.load_flowering_guidelines()
        event_markers = guidelines.get('eventMarker', [])
        
        return [
            {
                'type': marker['type'],
                'description': marker['beschreibung'],
                'trigger': marker.get('trigger', []),
                'significance': marker.get('bedeutung', '')
            }
            for marker in event_markers
        ] 