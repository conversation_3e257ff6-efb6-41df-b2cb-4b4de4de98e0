"""
Lighting Widget API Routes
Handles lighting plan and schedule API endpoints
"""

from flask import Blueprint, request, jsonify
from phase_logic.widgets.lighting_logic import LightingLogic
import json

# Create blueprint
lighting_bp = Blueprint('lighting', __name__, url_prefix='/api/lighting')

@lighting_bp.route('/plan/<phase>', methods=['GET'])
def lighting_plan(phase):
    """
    Beleuchtungsplan für eine bestimmte Phase
    """
    try:
        # Parameter aus Query-String holen
        strain_type = request.args.get('strain_type', default='photoperiodic')
        light_type = request.args.get('light_type', default='led')
        lamp_power_w = request.args.get('lamp_power_w', type=float, default=480.0)
        lamp_distance_cm = request.args.get('lamp_distance_cm', type=float, default=25.0)
        light_hours = request.args.get('light_hours', type=int, default=12)
        
        # Neue Parameter für erweiterte Lampenkonfiguration
        color_temperature_k = request.args.get('color_temperature_k', type=int, default=3500)
        power_percentage = request.args.get('power_percentage', type=int, default=100)
        additional_wavelengths = request.args.get('additional_wavelengths', default='')
        
        # Plant ID aus Query-String holen (falls vorhanden)
        plant_id = request.args.get('plant_id')
        
        # Strain-Type aus Datenbank laden falls nicht übergeben und Plant ID vorhanden
        if strain_type == 'photoperiodic' and plant_id:
            try:
                from database_basic import GrowDiaryBasicDB
                db = GrowDiaryBasicDB()
                conn = db.get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT strain_type, strain FROM plants WHERE id = ?", (plant_id,))
                row = cursor.fetchone()
                if row:
                    # Erst prüfen ob strain_type explizit gesetzt ist
                    if row[0]:  # strain_type
                        strain_type_db = row[0].lower()
                        # Prüfe auf Autoflower-Werte im strain_type Feld
                        auto_type_keywords = ['auto', 'autoflower', 'autoflowering', 'automatic']
                        if any(keyword in strain_type_db for keyword in auto_type_keywords):
                            strain_type = 'autoflower'
                            print(f"Lighting API - Detected autoflower from strain_type: {row[0]}")
                        else:
                            strain_type = strain_type_db
                            print(f"Lighting API - Loaded strain_type from database: {strain_type}")
                    # Dann nach Autoflower-Keywords in strain suchen
                    elif row[1]:  # strain
                        strain_text = row[1].lower()
                        auto_keywords = ['auto', 'autoflower', 'automatic', 'ruderalis']
                        if any(keyword in strain_text for keyword in auto_keywords):
                            strain_type = 'autoflower'
            except Exception as e:
                pass  # Silently handle database errors
        
        # Lighting-Logik initialisieren
        lighting_logic = LightingLogic()
        
        # PPFD-Wert extrahieren
        ppfd_measured = None
        if 'ppfd_measured' in request.args:
            try:
                ppfd_measured = float(request.args.get('ppfd_measured'))
            except (ValueError, TypeError):
                ppfd_measured = None
        
        # Beleuchtungsplan mit Guidelines berechnen
        result = lighting_logic.calculate_lighting_with_guidelines(
            phase, strain_type, light_type, 
            lamp_power_w, lamp_distance_cm, light_hours, 
            ppfd_measured, color_temperature_k, power_percentage, additional_wavelengths
        )
        
        return jsonify({
            'success': True,
            'phase': phase,
            **result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden des Beleuchtungsplans: {str(e)}'
        }), 500

@lighting_bp.route('/schedule/<plant_id>', methods=['GET'])
def lighting_schedule(plant_id):
    """
    Beleuchtungsplan für eine spezifische Pflanze
    """
    try:
        from database_basic import db
        
        # Pflanzendaten laden
        plant = db.get_plant(plant_id)
        if not plant:
            return jsonify({'success': False, 'message': 'Pflanze nicht gefunden'}), 404
        
        # Lighting-Logik initialisieren
        lighting_logic = LightingLogic()
        
        # Beleuchtungsplan für Pflanze berechnen
        result = lighting_logic.calculate_plant_lighting_schedule(plant)
        
        return jsonify({
            'success': True,
            'plant_id': plant_id,
            'schedule': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden des Beleuchtungsplans: {str(e)}'
        }), 500

@lighting_bp.route('/light-types', methods=['GET'])
def lighting_types():
    """
    Verfügbare Beleuchtungstypen
    """
    try:
        lighting_logic = LightingLogic()
        light_types = lighting_logic.get_available_light_types()
        
        return jsonify({
            'success': True,
            'light_types': light_types
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Beleuchtungstypen: {str(e)}'
        }), 500

@lighting_bp.route('/strain-recommendations', methods=['GET'])
def strain_recommendations():
    """
    Beleuchtungsempfehlungen basierend auf Sortentyp
    """
    try:
        strain_type = request.args.get('strain_type', default='photoperiodic')
        
        lighting_logic = LightingLogic()
        recommendations = lighting_logic.get_strain_lighting_recommendations(strain_type)
        
        return jsonify({
            'success': True,
            'strain_type': strain_type,
            'recommendations': recommendations
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Empfehlungen: {str(e)}'
        }), 500

@lighting_bp.route('/calculate', methods=['POST'])
def lighting_calculate():
    """
    Beleuchtungsberechnung mit benutzerdefinierten Werten
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not data.get('phase'):
            return jsonify({'success': False, 'message': 'Phase ist erforderlich'}), 400
        
        phase = data['phase']
        strain_type = data.get('strain_type', 'photoperiodic')
        light_type = data.get('light_type', 'led')
        
        # Lighting-Logik initialisieren
        lighting_logic = LightingLogic()
        
        # Beleuchtung berechnen
        result = lighting_logic.calculate_lighting_plan(phase, strain_type, light_type)
        
        return jsonify({
            'success': True,
            'photoperiod': result['photoperiod'],
            'intensity': result['intensity'],
            'schedule': result['schedule']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der Beleuchtungsberechnung: {str(e)}'
        }), 500

@lighting_bp.route('/guidelines', methods=['GET'])
def lighting_guidelines():
    """
    Beleuchtungsrichtlinien und PPFD/DLI-Empfehlungen laden
    """
    try:
        import os
        
        # JSON-Datei mit Guidelines laden
        guidelines_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'static', 'data', 'lighting-guidelines.json'
        )
        
        if not os.path.exists(guidelines_path):
            return jsonify({
                'success': False,
                'message': 'Beleuchtungsrichtlinien nicht gefunden'
            }), 404
        
        with open(guidelines_path, 'r', encoding='utf-8') as f:
            guidelines = json.load(f)
        
        return jsonify({
            'success': True,
            'guidelines': guidelines
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Beleuchtungsrichtlinien: {str(e)}'
        }), 500

@lighting_bp.route('/guidelines/<phase>', methods=['GET'])
def lighting_guidelines_for_phase(phase):
    """
    Spezifische Beleuchtungsrichtlinien für eine Phase
    """
    try:
        import os
        
        # JSON-Datei mit Guidelines laden
        guidelines_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'static', 'data', 'lighting-guidelines.json'
        )
        
        if not os.path.exists(guidelines_path):
            return jsonify({
                'success': False,
                'message': 'Beleuchtungsrichtlinien nicht gefunden'
            }), 404
        
        with open(guidelines_path, 'r', encoding='utf-8') as f:
            guidelines_data = json.load(f)
        
        # Phase finden
        guidelines = guidelines_data.get('lightingGuidelines', {})
        phases = guidelines.get('phases', [])
        
        matching_phase = None
        for phase_data in phases:
            if phase_data.get('phase') == phase:
                matching_phase = phase_data
                break
        
        if not matching_phase:
            return jsonify({
                'success': False,
                'message': f'Keine Richtlinien für Phase {phase} gefunden'
            }), 404
        
        return jsonify({
            'success': True,
            'phase': phase,
            'guidelines': matching_phase,
            'tent_examples': guidelines.get('tentExamples', []),
            'meta': guidelines.get('meta', {})
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Beleuchtungsrichtlinien: {str(e)}'
        }), 500

@lighting_bp.route('/calculate-dli', methods=['POST'])
def calculate_dli():
    """
    DLI (Daily Light Integral) berechnen
    """
    try:
        data = request.get_json()
        
        # Validierung
        if not data.get('ppfd'):
            return jsonify({'success': False, 'message': 'PPFD ist erforderlich'}), 400
        
        if not data.get('photoperiod'):
            return jsonify({'success': False, 'message': 'Photoperiode ist erforderlich'}), 400
        
        ppfd = float(data['ppfd'])
        photoperiod = float(data['photoperiod'])
        
        # DLI berechnen: PPFD × Photoperiode × 0,0036
        dli = ppfd * photoperiod * 0.0036
        
        return jsonify({
            'success': True,
            'ppfd': ppfd,
            'photoperiod': photoperiod,
            'dli': round(dli, 2),
            'formula': 'DLI = PPFD × Photoperiod × 0,0036'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler bei der DLI-Berechnung: {str(e)}'
        }), 500 

@lighting_bp.route('/save-settings/<plant_id>', methods=['POST'])
def save_lighting_settings(plant_id):
    """
    Beleuchtungsplan-Einstellungen für eine Pflanze speichern
    """
    try:
        from database_basic import db
        
        data = request.get_json()
        
        # Validierung
        if not data:
            return jsonify({'success': False, 'message': 'Keine Daten erhalten'}), 400
        
        # Beleuchtungsplan-Einstellungen speichern
        settings = {
            'lamp_power_w': data.get('lamp_power_w', 480),
            'lamp_distance_cm': data.get('lamp_distance_cm', 25),
            'light_hours': data.get('light_hours', 12),
            'color_temperature_k': data.get('color_temperature_k', 3500),
            'power_percentage': data.get('power_percentage', 100),
            'additional_wavelengths': data.get('additional_wavelengths', ''),
            'ppfd_measured': data.get('ppfd_measured', None)
        }
        
        # Als JSON in external_data speichern
        settings_json = json.dumps(settings)
        
        # Prüfen ob bereits Einstellungen existieren
        existing_data = db.get_external_data_by_plant(plant_id)
        existing_lighting_settings = None
        
        for data_item in existing_data:
            if data_item['data_key'] == 'lighting_settings':
                existing_lighting_settings = data_item
                break
        
        if existing_lighting_settings:
            # Update existing settings
            success = db.update_external_data(existing_lighting_settings['id'], settings_json)
        else:
            # Create new settings
            db.add_external_data(plant_id, 'lighting_settings', settings_json)
            success = True
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Beleuchtungsplan-Einstellungen gespeichert',
                'settings': settings
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Fehler beim Speichern der Einstellungen'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Speichern der Beleuchtungsplan-Einstellungen: {str(e)}'
        }), 500

@lighting_bp.route('/load-settings/<plant_id>', methods=['GET'])
def load_lighting_settings(plant_id):
    """
    Beleuchtungsplan-Einstellungen für eine Pflanze laden
    """
    try:
        from database_basic import db
        
        # Beleuchtungsplan-Einstellungen aus external_data laden
        external_data = db.get_external_data_by_plant(plant_id)
        
        lighting_settings = None
        for data_item in external_data:
            if data_item['data_key'] == 'lighting_settings':
                try:
                    lighting_settings = json.loads(data_item['data_value'])
                    break
                except json.JSONDecodeError:
                    continue
        
        if lighting_settings:
            return jsonify({
                'success': True,
                'settings': lighting_settings
            })
        else:
            # Default-Einstellungen zurückgeben
            default_settings = {
                'lamp_power_w': 480,
                'lamp_distance_cm': 25,
                'light_hours': 12,
                'color_temperature_k': 3500,
                'power_percentage': 100,
                'additional_wavelengths': '',
                'ppfd_measured': None
            }
            return jsonify({
                'success': True,
                'settings': default_settings,
                'message': 'Keine gespeicherten Einstellungen gefunden, verwende Standardwerte'
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Fehler beim Laden der Beleuchtungsplan-Einstellungen: {str(e)}'
        }), 500 